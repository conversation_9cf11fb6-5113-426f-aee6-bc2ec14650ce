{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("We've detected violations of the Boost Terms &amp; Conditions") }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        {% if LANGUAGE == "pl" %}
            <PERSON><PERSON><PERSON> dobry,
        {% else %}
            {{ _("Hi,") }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ email_text_template.format(boost_terms_url=boost_terms_url) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Please bear in mind that non-compliance with the Boost Terms &amp; Conditions will result in Boost suspension.") }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("This is an automatically generated message. Please do not reply. If you have any questions, feel free to contact us via email at <a href='mailto:{support_email}' target='_blank'>{support_email}</a>.").format(support_email=support_email) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Here for you,") }}<br/>
        Booksy
    {% endcall %}
{% endblock email_content %}
