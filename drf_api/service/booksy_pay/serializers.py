import typing as t
from decimal import Decimal

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework.fields import get_attribute

from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger
from lib.pos.utils import pos_refactor_stage2_enabled
from lib.serializers import RelativedeltaField
from lib.tools import (
    json_to_relativedelta_converter,
    relativedelta_to_json_converter,
    sget_v2,
    tznow,
)
from lib.x_version_compatibility import BooksyPayCompatibility
from service.booking.tools import AppointmentData, check_tokenized_payments_v2
from webapps.adyen.typing import DeviceDataDict
from webapps.booking.appointment_checkout import AppointmentCheckout
from webapps.booksy_pay.utils import is_booksy_pay_payment_window_open
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort
from webapps.pos.deposit import create_booksy_pay_transaction
from webapps.pos.enums import PaymentProviderEnum, PaymentTypeEnum
from webapps.pos.models import POS
from webapps.pos.provider import get_payment_provider
from webapps.pos.serializers import (
    BaseCustomerAppointmentTransactionSerializer,
    TipChoicesSerializer,
)
from webapps.pos.services import PaymentRowService, TransactionService


class AvailabilityInfoSerializer(serializers.Serializer):
    available = serializers.BooleanField()
    payment_window_open = serializers.BooleanField()


class PaymentInfoSerializer(serializers.Serializer):
    is_paid = serializers.BooleanField()
    refundable = serializers.BooleanField()
    is_auto_refund_possible = serializers.BooleanField()
    late_cancellation_window = RelativedeltaField()


class BooksyPayResponseSerializer(serializers.Serializer):
    appointment_id = serializers.IntegerField()
    appointment_uid = serializers.IntegerField()
    availability_info = AvailabilityInfoSerializer()
    payment_info = PaymentInfoSerializer()


class BooksyPaySerializer(BaseCustomerAppointmentTransactionSerializer):
    BOOKSY_PAY_NOT_AVAILABLE = 'booksy_pay_not_available'
    BOOKSY_PAY_TIME_WINDOW_IS_CLOSED = 'booksy_pay_time_window_is_closed'
    default_error_messages = {
        BOOKSY_PAY_NOT_AVAILABLE: _('Booksy Pay is not available.'),
        BOOKSY_PAY_TIME_WINDOW_IS_CLOSED: _(
            'Booksy Pay is allowed only in the specific time window.'
        ),
    }

    booksy_pay = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)
    booksy_pay_tax = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)
    booksy_pay_total = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)

    def validate(self, attrs) -> t.Dict:
        appointment_data: AppointmentData = self.context.get('appointment_data')

        # Deprecated, we should check compatibility earlier
        if BooksyPayCompatibility(self.context.get('request')) is False:
            raise self.fail(self.BOOKSY_PAY_NOT_AVAILABLE)

        # Needs to be run first
        attrs = super().validate(attrs)
        if not self.context['is_booksy_pay_available']:
            raise self.fail(self.BOOKSY_PAY_NOT_AVAILABLE)
        if not is_booksy_pay_payment_window_open(
            booked_from=appointment_data.booked_from,
            booked_till=appointment_data.booked_till,
            now=tznow(),
        ):
            raise self.fail(self.BOOKSY_PAY_TIME_WINDOW_IS_CLOSED)

        attrs.update(self._get_extra_input_data())
        attrs.update(self._calculate_tip(attrs))
        attrs['booksy_pay_total'] = self._calculate_booksy_pay_total(attrs)

        if not attrs['dry_run']:
            self._validate_payment_methods(attrs)

        return attrs

    def get_to_pay_later(self, obj):
        checkout: AppointmentCheckout = self.context['appointment_checkout']

        if checkout.total.value is None:
            return None

        return str(
            checkout.total.value - checkout.booksy_pay.value - self._get_booksy_pay_tax_value()
        )

    def _get_booksy_pay_tax_value(self) -> Decimal:
        business = self.context['business']
        checkout: AppointmentCheckout = self.context['appointment_checkout']

        if not business.pos_enabled:
            return Decimal(0)

        service_fee = business.pos.service_fee if business.pos.service_fee else Decimal(0)
        return checkout.booksy_pay.excluded_tax + service_fee

    def _get_extra_input_data(self):
        update_attrs = super()._get_extra_input_data()

        checkout: AppointmentCheckout = self.context['appointment_checkout']
        booksy_pay = checkout.booksy_pay
        update_attrs['booksy_pay'] = booksy_pay.value

        if update_attrs['booksy_pay'] == 0:
            update_attrs['booksy_pay_tax'] = 0
            update_attrs['tip_base'] = 0
        else:
            update_attrs['booksy_pay_tax'] = self._get_booksy_pay_tax_value()
            # update_attrs['booksy_pay_total'] - set later after calculating tip
            update_attrs['tip_base'] = checkout.booksy_pay_tip_base

        return update_attrs

    @property
    def no_fees(self) -> dict:
        no_fees = super().no_fees
        no_fees.update(
            {
                'booksy_pay': Decimal(0),
                'booksy_pay_tax': Decimal(0),
                'booksy_pay_total': Decimal(0),
            }
        )

        return no_fees

    def _calculate_tip(self, attrs):
        """
        Note: Booksy Pay should allow for tips as well.
        """
        tips_data = super()._calculate_tip(attrs)

        if tips_data and attrs['booksy_pay'] == 0:
            tips_data['tip']['rate'] = 0
            tips_data['tip']['amount'] = 0

        return tips_data

    def _validate_payment_methods(self, attrs: dict) -> None:
        payment_method = attrs.get('payment_method')
        external_payment_method = attrs.get('external_payment_method')

        if not bool(payment_method) and not bool(external_payment_method):  # XOR
            raise serializers.ValidationError(
                'Payment_method xor external_payment_method ' 'should be provided',
                code='missing_method',
            )

        if payment_method and payment_method.provider != PaymentProviderEnum.FAKE_PROVIDER:
            business_payment_provider_code = (
                PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                    TransactionService.get_payment_provider_code(
                        pos=self.context['pos'], payment_type_code=PaymentTypeEnum.PAY_BY_APP
                    )
                )
            )

            if payment_method.provider != business_payment_provider_code:
                raise serializers.ValidationError(
                    'Wrong card is used.', code='wrong_payment_provider'
                )
        if tokenized_pm_id := sget_v2(payment_method, ['tokenized_pm_id']):
            validation_response = PaymentProvidersPaymentPort.validate_tokenized_pm(
                tokenized_pm_id=tokenized_pm_id,
            )
            if errors := [x for x in validation_response.errors if x is not None]:
                error = errors[0]
                raise serializers.ValidationError(
                    detail=error.label,
                    code=error.value,
                )

        pos = self.context.get('pos')
        payment_possibilities = check_tokenized_payments_v2(
            appointment_data=self.context.get('appointment_data'),
            stripe_enabled=pos.force_stripe_pba if pos else False,
            allow_blik=True,
        )

        if (
            attrs.get('external_payment_method')
            and not payment_possibilities[
                get_attribute(attrs, ['external_payment_method', 'card_type'])
            ]
        ):
            raise serializers.ValidationError(
                _('Payment is not possible'), code='payment_not_possible'
            )

    def _calculate_booksy_pay_total(self, attrs: dict):
        return (
            attrs['booksy_pay']
            + attrs['booksy_pay_tax']
            + (attrs['tip']['amount'] if attrs.get('tip') else Decimal(0))
        )

    def save(self) -> None:
        """
        Creates transaction with Booksy Pay using TransactionSerializer.
        """
        if not self.validated_data.get('booksy_pay_total'):
            return

        device_data = DeviceDataDict(
            fingerprint=self.context.get('device_fingerprint', ''),
            phone_number=self.context.get('cell_phone', ''),
            user_agent=self.context.get('user_agent', ''),
        )
        extra_data = self.context.get('extra_data')
        payment_method = self._get_payment_method()
        appointment_data: AppointmentData = self.context.get('appointment_data')
        transaction = create_booksy_pay_transaction(
            appointment_id=appointment_data.appointment_id,
            subbookings_ids=appointment_data.subbookings_ids,
            booksy_pay_total=self.context['appointment_checkout'].booksy_pay.total,
            amount=self.validated_data['booksy_pay_total'],
            pos=self.context_pos,
            tip=self._get_tip_by_hand(),
            user=self.context.get('user'),
        )
        # Only 1 PR should exist
        booksy_pay_row = transaction.latest_receipt.payment_rows.get()

        if pos_refactor_stage2_enabled(self.context_pos):
            provider = get_payment_provider(
                codename=payment_method.provider,
                txn=transaction,
            )
            provider.make_payment(
                transaction=transaction,
                payment_method=payment_method,
                payment_row=booksy_pay_row,
                device_data=device_data,
                extra_data=extra_data,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__BOOKSY_PAY,
            )

            TransactionService.check_booksy_pay_auth_success(txn=transaction)
        else:
            # This scenario should never happen as Booksy Pay is available on Stripe only.
            # This exception is just an extra level of protection.
            raise RuntimeError('Unexpected call for Booksy Pay')

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if not Decimal(ret.get('booksy_pay_total') or '0') and 'tip_choices' in ret:
            del ret['tip_choices']

        if not self.instance:  # it is NOT for GET http method
            TipChoicesSerializer.mark_as_main_tip(ret)

        return ret


def get_booksy_pay_late_cancellation_labels():
    before_the_appointment_string = _("before the appointment")
    valid_booksy_pay_late_cancellation_window_options = {
        relativedelta(hours=1): " ".join([_("1 hour"), before_the_appointment_string]),
        relativedelta(hours=2): " ".join([_("2 hours"), before_the_appointment_string]),
        relativedelta(hours=3): " ".join([_("3 hours"), before_the_appointment_string]),
        relativedelta(hours=6): " ".join([_("6 hours"), before_the_appointment_string]),
        relativedelta(hours=8): " ".join([_("8 hours"), before_the_appointment_string]),
        relativedelta(hours=12): " ".join([_("12 hours"), before_the_appointment_string]),
        relativedelta(days=1): " ".join([_("1 day"), before_the_appointment_string]),
        relativedelta(days=2): " ".join([_("2 days"), before_the_appointment_string]),
        relativedelta(days=3): " ".join([_("3 days"), before_the_appointment_string]),
        relativedelta(days=4): " ".join([_("4 days"), before_the_appointment_string]),
        relativedelta(days=5): " ".join([_("5 days"), before_the_appointment_string]),
        relativedelta(days=7): " ".join([_("7 days"), before_the_appointment_string]),
    }
    return valid_booksy_pay_late_cancellation_window_options


class BooksyPayLateCancellationWindowSerializer(serializers.Serializer):
    def to_internal_value(self, data):
        try:
            value = json_to_relativedelta_converter(data['value'])
        except:
            raise ValidationError()

        return self.validate(value)

    def validate(self, value):
        if value not in get_booksy_pay_late_cancellation_labels().keys():
            raise ValidationError()

        return value

    def to_representation(self, instance):
        if instance == relativedelta():
            instance = relativedelta(**settings.BOOKSY_PAY_LATE_CANCELLATION_WINDOW_DEFAULT)

        data = {
            'value': relativedelta_to_json_converter(instance),
            'label': get_booksy_pay_late_cancellation_labels()[instance],
        }
        return data


class BooksyPayLateCancellationSerializer(serializers.ModelSerializer):
    booksy_pay_late_cancellation_window = BooksyPayLateCancellationWindowSerializer()
    booksy_pay_late_cancellation_window_options = serializers.SerializerMethodField()

    def get_booksy_pay_late_cancellation_window_options(self, _):
        serializer = BooksyPayLateCancellationWindowSerializer(
            get_booksy_pay_late_cancellation_labels().keys(), many=True
        )
        return serializer.data

    class Meta:
        model = POS
        fields = [
            'booksy_pay_late_cancellation_window',
            'booksy_pay_late_cancellation_window_options',
        ]
