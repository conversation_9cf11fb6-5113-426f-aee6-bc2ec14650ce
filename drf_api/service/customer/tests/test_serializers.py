import unittest
from unittest.mock import PropertyMock, patch

import pytest
from django.test import override_settings
from model_bakery import baker
from parameterized import parameterized
from rest_framework.serializers import ValidationError

from drf_api.service.customer.enums import SelectedForYouTriggerSource
from drf_api.service.customer.serializers import (
    BusinessPaymentOptionsSerializer,
    CustomerPreferencesSerializer,
    SelectedForYouRequestSerializer,
)
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import BusinessCategory
from webapps.pos.models import POS
from webapps.user.const import Gender
from webapps.user.models import User

pytestmark = pytest.mark.django_db

# pylint: disable=redefined-outer-name


@pytest.fixture
def business():
    return business_recipe.make()


@pytest.fixture
def pos(business):
    return baker.make(POS, business=business)


def test_payment_options_serializer(pos):
    serializer = BusinessPaymentOptionsSerializer(instance=pos, context={'business': pos.business})
    assert serializer.data == {
        'external_partners': {'google_pay': False, 'apple_pay': False, 'blik': False},
        'force_stripe_pba': False,
        'transaction_merchant_account': 'BooksyNET',
    }


@pytest.mark.parametrize('force_stripe_pba', [True, False])
def test_payment_options_serializer_pba(pos, force_stripe_pba):
    with patch.object(POS, 'force_stripe_pba', new_callable=PropertyMock) as mocked_pba:
        mocked_pba.return_value = force_stripe_pba
        serializer = BusinessPaymentOptionsSerializer(
            instance=pos, context={'business': pos.business}
        )
        assert serializer.data['force_stripe_pba'] == force_stripe_pba


@override_settings(
    ADYEN_MERCHANT_ACCOUNT='MERCHANT',
    ADYEN_MARKET_PAY_MERCHANT_ACCOUNT='MP',
)
@pytest.mark.parametrize('use_marketpay', [True, False])
def test_payment_options_transaction_merchant_account(pos, use_marketpay):
    pos.marketpay_enabled = use_marketpay
    pos.save()
    serializer = BusinessPaymentOptionsSerializer(instance=pos, context={'business': pos.business})

    assert serializer.data['transaction_merchant_account'] == (
        'MP' if use_marketpay else 'MERCHANT'
    )


def test_preferences_serializer_incorrect_gender():
    serializer = CustomerPreferencesSerializer(data={"gender": "A"})
    with pytest.raises(ValidationError):
        serializer.is_valid(raise_exception=True)


def test_preferences_serializer_incorrect_categories_type():
    treatment = baker.make(BusinessCategory, type=BusinessCategory.TREATMENT)
    serializer = CustomerPreferencesSerializer(data={"category_ids": [treatment.id]})
    with pytest.raises(ValidationError) as e:
        serializer.is_valid(raise_exception=True)
    assert f"Invalid category IDs: [{treatment.id}]" in str(e.value)


def test_preferences_serializer_incorrect_category_type():
    serializer = CustomerPreferencesSerializer(data={"category_ids": [100]})
    with pytest.raises(ValidationError) as e:
        serializer.is_valid(raise_exception=True)
    assert "Invalid category IDs: [100]" in str(e.value)


def test_preferences_serializer_correct_data():
    category_1 = baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)
    category_2 = baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)
    serializer = CustomerPreferencesSerializer(
        data={"gender": Gender.Both, "category_ids": [category_1.id, category_2.id]}
    )
    serializer.is_valid(raise_exception=True)
    assert serializer.validated_data['gender'] == Gender.Both.value
    assert serializer.validated_data['category_ids'] == [category_1.id, category_2.id]


def test_preferences_serializer_update_gender():
    user = baker.make(User, gender=Gender.Male)
    serializer = CustomerPreferencesSerializer(data={"gender": Gender.Both})
    serializer.is_valid(raise_exception=True)
    serializer.update_gender(user, Gender.Both)
    assert user.gender == Gender.Both


class TestSelectedForYouRequestSerializer(unittest.TestCase):
    def setUp(self):
        """Set up base data for tests."""
        self.base_data = {
            'gender': Gender.Male,
            'categories_ids': [1, 2, 3],
            'location_geo': '52.232989, 20.990810',
            'location_id': 123,
            'trigger_source': SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY,
        }

    def test_valid_data_not_authenticated(self):
        serializer = SelectedForYouRequestSerializer(
            data=self.base_data,
            context={'is_authenticated': False},
        )
        assert serializer.is_valid()
        assert serializer.validated_data['gender'] == Gender.Male
        assert serializer.validated_data['categories_ids'] == [1, 2, 3]
        assert serializer.validated_data['location_geo'] == {'lat': 52.232989, 'lon': 20.99081}
        assert serializer.validated_data['location_id'] == 123
        assert (
            serializer.validated_data['trigger_source']
            == SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY
        )

    def test_valid_data_authenticated(self):
        data = self.base_data.copy()
        data.pop('categories_ids')
        data.pop('gender')
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': True})
        assert serializer.is_valid()
        assert serializer.validated_data['categories_ids'] == []
        assert serializer.validated_data['location_geo'] == {'lat': 52.232989, 'lon': 20.99081}
        assert serializer.validated_data['location_id'] == 123
        assert (
            serializer.validated_data['trigger_source']
            == SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY
        )

    def test_missing_required_fields_not_authenticated(self):
        data = {'location_geo': '52.232989, 20.990810'}
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert not serializer.is_valid()
        assert 'gender' in serializer.errors
        assert 'categories_ids' in serializer.errors

    def test_invalid_gender(self):
        data = self.base_data.copy()
        data['gender'] = 'invalid_gender'
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert not serializer.is_valid()
        assert 'gender' in serializer.errors

    def test_invalid_categories_ids_type(self):
        data = self.base_data.copy()
        data['categories_ids'] = ['not_an_integer']
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert not serializer.is_valid()
        assert 'categories_ids' in serializer.errors

    def test_empty_categories_ids(self):
        data = self.base_data.copy()
        data['categories_ids'] = []
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert not serializer.is_valid()
        assert 'categories_ids' in serializer.errors

    def test_too_many_categories_ids(self):
        data = self.base_data.copy()
        data['categories_ids'] = [1, 2, 3, 4, 5, 6]  # 6 items, exceeds max_length=5
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert not serializer.is_valid()
        assert 'categories_ids' in serializer.errors

    def test_invalid_location_id_type(self):
        data = self.base_data.copy()
        data['location_id'] = 'not_an_integer'
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert not serializer.is_valid()
        assert 'location_id' in serializer.errors

    def test_invalid_trigger_source(self):
        data = self.base_data.copy()
        data['trigger_source'] = 'invalid_trigger_source'
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert not serializer.is_valid()
        assert 'trigger_source' in serializer.errors

    @parameterized.expand(
        [
            (SelectedForYouTriggerSource.DEEPLINK,),
            (SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY,),
            (SelectedForYouTriggerSource.SECOND_APP_LAUNCH,),
            (SelectedForYouTriggerSource.ONBOARDING,),
        ]
    )
    def test_valid_trigger_source_values(self, trigger_source):
        data = self.base_data.copy()
        data['trigger_source'] = trigger_source
        serializer = SelectedForYouRequestSerializer(data=data, context={'is_authenticated': False})
        assert serializer.is_valid()
        assert serializer.validated_data['trigger_source'] == trigger_source
