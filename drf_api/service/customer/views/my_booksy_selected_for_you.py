import logging

from bo_obs.datadog.enums import BooksyTeams
from elasticsearch_dsl import AttrDict
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.permissions.authentication import OptionalLogin
from drf_api.service.customer.enums import SelectedForYouTriggerSource
from drf_api.service.customer.serializers import SelectedForYouRequestSerializer
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.elasticsearch.consts import ESDocType
from lib.feature_flag.adapter import UserData
from lib.feature_flag.consts import CUSTOMER_BOOKING_SOURCE_TO_CLIENT_APP
from lib.feature_flag.enums import AppDomains, ClientApp, ExperimentVariants, SubjectType
from lib.feature_flag.experiment.customer import (
    SelectedForYouPortfolioImagesExperiment,
    SelectedForYouV4RandomizedScoringExperiment,
)
from lib.feature_flag.feature.customer import (
    CustomerRecommendedNewFlag,
    PortfolioImagesExperimentOnFlag,
    S4UServiceVariantCountFixFlag,
    SimpleSerchableInRecommendedForYouFlag,
    UseLastBookingLocationInSelectedForYouFlag,
)
from lib.searchables.common import IdsSearchable
from webapps.business.searchables.business.galleries.selected_for_you import (
    SelectedForYourPortfolioImagesExperimentSearchable,
    SelectedForYouSearchable,
    SelectedForYouSearchableV4,
    SelectedForYouSimpleSearchable,
)
from webapps.business.searchables.serializers import (
    BusinessDetailsHitSerializer,
    BusinessTreatmentHitSerializer,
)
from webapps.structure.models import Region
from webapps.user.const import Gender

logger = logging.getLogger('booksy.recommended_new_px')


class CustomerMyBooksySelectedForYouView(  # nosemgrep: no-is-authenticated-permission-for-drf
    BaseBooksySessionGenericAPIView
):
    permission_classes = (OptionalLogin,)
    serializer_class = SelectedForYouRequestSerializer
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @using_db_for_reads(READ_ONLY_DB)
    def get(self, request, *_, **kwargs):
        """
        Get recommended new providers from last 14 days<br>
        Fields *categories_ids* and *gender* are required for not authenticated users
        <br>
        *Example values*:<br>
        *   *gender=M*<br>
        *   *categories_ids=1,2,3*
        *   *location_geo=52.232989,20.990810*<br>
        *   *location_id=30692*<br>
        """
        flag_data = self._get_flag_data()
        is_authenticated = bool(self.user.id)
        validated_data = self._get_validated_data(is_authenticated)
        data = self._prepare_search_data(validated_data, flag_data, is_authenticated)
        if flag_data['is_required_location'] and not data.get('location_geo'):
            return self._get_empty_response()
        result_limit = flag_data['business_limit']
        min_business_count = flag_data['min_business_count']
        if self.is_customer_mobile and PortfolioImagesExperimentOnFlag(
            UserData(subject_key=self.fingerprint, subject_type=SubjectType.FINGERPRINT)
        ):
            businesses_with_portfolio = self._get_businesses_with_portfolio_images(
                data,
                max(result_limit, min_business_count),
                flag_data['treatment_limit'],
            )
            if businesses_with_portfolio.hits:
                experiment_variant = SelectedForYouPortfolioImagesExperiment(
                    UserData(
                        subject_key=self.fingerprint,
                        subject_type=SubjectType.FINGERPRINT,
                        is_experiment=True,
                        app_domain=AppDomains.CUSTOMER,
                    )
                )
                businesses = (
                    self._remove_inspiration_photos_from_response(businesses_with_portfolio)
                    if experiment_variant == ExperimentVariants.CONTROL
                    else businesses_with_portfolio
                )
                response = {
                    'businesses': self._process_response(
                        businesses, min_business_count, result_limit
                    ),
                }
                return Response(response, status=status.HTTP_200_OK)
            logger.warning(
                'No businesses with portfolio images found - selected_4_you searchable.',
                extra={'max_size': result_limit} | data,
            )

        is_variant = self._is_experiment_variant(
            self.fingerprint,
            validated_data.get('trigger_source'),
            self.request.booking_source.name,
        )
        businesses = self._get_businesses(
            data,
            max(result_limit, min_business_count),
            flag_data['treatment_limit'],
            is_variant,
        )
        response = {
            'businesses': self._process_response(businesses, min_business_count, result_limit),
        }
        return Response(response, status=status.HTTP_200_OK)

    @staticmethod
    def _process_response(businesses, min_business_count, result_limit):
        return [] if len(businesses) < min_business_count else businesses[:result_limit]

    @staticmethod
    def _remove_inspiration_photos_from_response(businesses):
        business_data = []
        for doc in businesses.hits:
            del doc['images']['inspiration']
            business_data.append(doc)
        return business_data

    def _prepare_search_data(self, data, flag_data, is_authenticated):
        result = {
            'min_treatment_count': flag_data['min_treatment_count'],
            'max_km_distance': flag_data['max_km_distance'],
            'max_active_days': flag_data['max_active_days'],
        }
        if location_id := data.get('location_id'):
            if coordinates := self._get_location_coordinates(location_id):
                result['location_geo'] = coordinates
        elif geo := data.get('location_geo'):
            result['location_geo'] = geo
        elif UseLastBookingLocationInSelectedForYouFlag() and (
            geo := self._get_last_booking_location()
        ):
            result['location_geo'] = geo
        if is_authenticated:
            result['gender'] = self.user.gender or Gender.Both
            qset = self.user.customerfavoritecategory_set.values_list('category_id', flat=True)
            result['business_categories'] = list(qset) or data['categories_ids']
        else:
            result['gender'] = data['gender']
            result['business_categories'] = data['categories_ids']

        return result

    @staticmethod
    def _get_flag_data():
        if not (flag_data := CustomerRecommendedNewFlag()):
            raise PermissionDenied(detail='Disabled view by feature flag')

        defaults = {
            'min_treatment_count': 1,
            'treatment_limit': 3,
            'business_limit': 5,
            'min_business_count': 0,
            'is_required_location': True,
            'max_km_distance': 50,
            'max_active_days': 300,
        }
        return defaults | flag_data

    @staticmethod
    def _get_businesses_with_portfolio_images(search_data, result_limit, treatment_limit):
        context_data = {'data': {**search_data, 'treatment_limit': treatment_limit}}
        result = (
            SelectedForYourPortfolioImagesExperimentSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessTreatmentHitSerializer(context=context_data),
            )
            .params(size=result_limit)
            .search(search_data)
            .execute()
        )
        return result

    def _get_businesses(self, search_data, result_limit, treatment_limit, is_experiment):
        if SimpleSerchableInRecommendedForYouFlag():
            context_data = {'data': {**search_data, 'treatment_limit': treatment_limit}}
            result = (
                SelectedForYouSimpleSearchable(
                    ESDocType.BUSINESS,
                    serializer=BusinessTreatmentHitSerializer(context=context_data),
                )
                .params(size=result_limit)
                .search(search_data)
                .execute()
            )
            if not result.hits:
                logger.warning(
                    'Empty response for seleted_4_you searchable in dedicated endpoint',
                    extra={'max_size': result_limit} | search_data,
                )
            return result

        if is_experiment:
            result = (
                SelectedForYouSearchableV4(
                    ESDocType.BUSINESS, serializer=BusinessDetailsHitSerializer
                )
                .params(size=result_limit)
                .search(search_data)
                .execute()
            )
        else:
            result = (
                SelectedForYouSearchable(
                    ESDocType.BUSINESS, serializer=BusinessDetailsHitSerializer
                )
                .params(size=result_limit)
                .search(search_data)
                .execute()
            )

        gender = search_data['gender']
        docs = []
        categories_ids = set(search_data['business_categories'])
        for doc in result.hits:
            doc['top_services'] = doc['top_services'][gender]
            doc.treatment_services = self._get_treatment_services(
                categories_ids, doc.service_categories, treatment_limit
            )
            del doc.service_categories
            docs.append(doc)
        if not result.hits:
            logger.warning(
                'Empty response for seleted_4_you searchable',
                extra={'max_size': result_limit} | search_data,
            )
        return docs

    def _get_location_coordinates(self, location_id):
        qset = Region.objects.filter(id=location_id).values_list('latitude', 'longitude')
        if (coordinates := qset.last()) and all(coordinates):
            return {'lat': coordinates[0], 'lon': coordinates[1]}

    def _get_validated_data(self, is_authenticated):
        serializer = self.get_serializer(
            data=self.request.query_params.dict(),
            context={'is_authenticated': is_authenticated},
        )
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    @staticmethod
    def _get_empty_response():
        return Response({'businesses': []}, status=status.HTTP_200_OK)

    @staticmethod
    def _get_treatment_services(treatment_ids, service_categories, treatment_limit):
        service_with_matching_treatment = []
        service_without_matching_treatment = []
        for category in service_categories:
            for s in category.services:
                data = AttrDict({'category_name': category.name, **s.to_dict()})
                if 'treatment_id' in s and {s.treatment_id, s.treatment_parent_id} & treatment_ids:
                    service_with_matching_treatment.append(data)
                else:
                    service_without_matching_treatment.append(data)

        services = service_with_matching_treatment or service_without_matching_treatment
        services = services[:treatment_limit]
        if S4UServiceVariantCountFixFlag():
            left = treatment_limit - len(services)
            for service in services:
                service.variants = service.variants[: left + 1]
                left -= len(service.variants) - 1

        return services

    def _get_last_booking_location(self) -> dict:
        if user_id := self.user.id:
            if result := IdsSearchable(ESDocType.USER).params(size=1).execute({'ids': [user_id]}):
                return result[0].last_booking_location

    @staticmethod
    def _is_experiment_variant(fingerprint, trigger, booking_source):
        if trigger != SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY:
            return False

        variant = SelectedForYouV4RandomizedScoringExperiment(
            UserData(
                subject_key=fingerprint,
                subject_type=SubjectType.FINGERPRINT,
                is_experiment=True,
                app=CUSTOMER_BOOKING_SOURCE_TO_CLIENT_APP.get(booking_source, ClientApp.DEFAULT),
                app_domain=AppDomains.CUSTOMER,
            )
        )
        return variant == ExperimentVariants.VARIANT_A
