# pylint: disable=too-many-statements
from datetime import timed<PERSON><PERSON>

import pytest
from django.shortcuts import reverse
from model_bakery import baker
from rest_framework import status

from conftest_helpers import clean_elastisearch_index_helper
from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from drf_api.service.customer.enums import SelectedForYouTriggerSource
from lib.elasticsearch.consts import ESIndex
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.customer import (
    SelectedForYouPortfolioImagesExperiment,
    SelectedForYouV4RandomizedScoringExperiment,
)
from lib.feature_flag.feature.customer import (
    CustomerRecommendedNewFlag,
    PortfolioImagesExperimentOnFlag,
    SimpleSerchableInRecommendedForYouFlag,
    UseLastBookingLocationInSelectedForYouFlag,
)
from lib.test_utils import create_subbooking
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from service.tests import BaseAsyncHTTPTest
from webapps.business.baker_recipes import (
    business_recipe,
    category_recipe,
    service_category_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    treatment_recipe,
)
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import Business
from webapps.consts import ANDROID, WEB
from webapps.elasticsearch.elastic import ELASTIC
from webapps.elasticsearch.tests.elasticsearch_test_helpers import ElasticSearchTestCaseMixin
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.search_engine_tuning.models import UserTuning
from webapps.structure.models import Region
from webapps.user.baker_recipes import customer_user
from webapps.user.const import Gender
from webapps.user.models import CustomerFavoriteCategory


@pytest.mark.django_db
class TestCustomerMyBooksySelectedForYouView(CustomerAPITestCase, ElasticSearchTestCaseMixin):

    def setUp(self):
        self.user = customer_user.make(email='<EMAIL>')
        self.url = reverse('customer_my_booksy_selected_for_you')
        clean_elastisearch_index_helper(ESIndex.BUSINESS)
        self.index = ELASTIC.indices[ESIndex.BUSINESS]
        self.bussiness_categories = self._create_business_categories()
        super().setUp()

    @staticmethod
    def _create_business_categories():
        categories = [
            BusinessCategoryEnum.BARBERS,
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.DENTAL,
        ]

        return {
            category: category_recipe.make(full_name=category.value, internal_name=category.value)
            for category in categories
        }

    def get(self, data):
        for key, value in data.items():
            if isinstance(value, list):
                data[key] = ','.join(str(v) for v in value)
        return self.client.get(self.url, data=data)

    def _create_business(
        self,
        active_x_days,
        has_cover_photo,
        categories_names,
        services_count,
        location_geo,
        promoted=False,
        number_of_portfolio_photos=0,
    ):
        latitude, longitude = location_geo or (None, None)
        business = business_recipe.make(
            active_from=active_x_days and tznow() - timedelta(days=active_x_days),
            latitude=latitude,
            longitude=longitude,
            boost_status=(
                Business.BoostStatus.ENABLED if promoted else Business.BoostStatus.DISABLED
            ),
        )
        if location_geo:
            Region.objects.filter(id=business.region_id).update(
                latitude=latitude + 0.05,  # move region 3.5km from business coordinates
                longitude=longitude + 0.05,
            )
        baker.make(
            Image,
            image_url='http://image.url/image.jpg',
            business=business,
            is_cover_photo=has_cover_photo,
        )
        categories = [
            self.bussiness_categories[category_name] for category_name in categories_names
        ]
        business.categories.add(*categories)
        if number_of_portfolio_photos:
            baker.make(
                Image,
                business=business,
                is_cover_photo=False,
                category=ImageTypeEnum.INSPIRATION,
                _quantity=number_of_portfolio_photos,
            )
        business.save()
        assert (
            business.boosted == promoted
        ), "The logic in the save method can overwrite boost_status"
        service_category = service_category_recipe.make(business=business)
        staffer = staffer_recipe.make(business=business)
        for i in range(services_count):
            category = categories[i % len(categories_names)]
            treatment = treatment_recipe.make(parent=category, name=f'T-{category.internal_name}')
            service = service_recipe.make(
                business=business,
                name=f'Service-{category.internal_name}',
                note=f'test note {i}',
                description=f'test description {i}',
                service_category=service_category,
                treatment=treatment,
            )
            service_variant_recipe.make(service=service, _quantity=i * 2 + 1)
            service.add_staffers([staffer])
        business_document = business.get_document()
        business_document.save()
        return business

    def _remove_login_token(self):
        self.client.credentials(HTTP_X_API_KEY=self.customer_booking_src.api_key)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {}})
    def test_get_disable_ff(self):
        response = self.get({})

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.json(), {'detail': 'Disabled view by feature flag'})

    @override_eppo_feature_flag(
        {
            CustomerRecommendedNewFlag.flag_name: {
                'is_required_location': True,
                'min_business_count': 1,
                'business_limit': 1,
            }
        }
    )
    def test_get_not_logged_in(self):
        self._remove_login_token()
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        services_names = [f'Service-{c}' for c in categories_names]
        business_0 = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 21.032610],
        )
        _business_1_to_old = self._create_business(
            active_x_days=50,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
            promoted=True,
        )
        _business_2_too_low_services = self._create_business(
            active_x_days=50,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=1,
            location_geo=[52.232989, 20.990810],
        )
        _business_3_wrong_categories = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.DENTAL],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        _business_4_no_cover = self._create_business(
            active_x_days=1,
            has_cover_photo=False,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        _business_5_too_far_away = business_by_region = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[51.792989, 20.990810],  # 49 km from [51.513989, 20.990810]
            promoted=True,
        )
        _business_6_no_start_day = self._create_business(
            active_x_days=None,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
            promoted=True,
        )
        _business_7_no_location = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=None,
            promoted=True,
        )
        self.index.refresh()
        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': categories_ids[:2],
            'location_id': '',
        }

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'business_limit': 1,
                    'treatment_limit': 3,
                }
            }
        ):
            response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 1)
        self.assertEqual(businesses[0]['id'], business_0.id)
        self.assertEqual(
            {s['name'] for s in businesses[0]['treatment_services']}, set(services_names[:2])
        )
        self.assertEqual(sum(len(s['variants']) for s in businesses[0]['treatment_services']), 3)
        self.assertEqual(len(businesses[0]['images']['cover']), 1)
        self.assertEqual(businesses[0]['images']['cover'][0]['image'], 'http://image.url/image.jpg')

        data = {
            'location_id': business_by_region.region_id,
            'gender': Gender.Male,
            'categories_ids': categories_ids[:2],
        }

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'business_limit': 1,
                }
            }
        ):
            response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 1)
        self.assertEqual({b['id'] for b in businesses}, {business_by_region.id})
        self.assertEqual(
            {s['name'] for s in businesses[0]['treatment_services']}, set(services_names[:2])
        )
        self.assertEqual(len(businesses[0]['images']['cover']), 1)
        self.assertEqual(businesses[0]['images']['cover'][0]['image'], 'http://image.url/image.jpg')

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 2,
                    'business_limit': 2,
                    'treatment_limit': 2,
                }
            }
        ):
            response = self.get(data=data)

        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 2)
        self.assertEqual(businesses[0]['id'], business_by_region.id)
        self.assertEqual(businesses[1]['id'], business_0.id)
        self.assertEqual(sum(len(s['variants']) for s in businesses[0]['treatment_services']), 2)
        self.assertEqual(sum(len(s['variants']) for s in businesses[1]['treatment_services']), 2)
        # smaller distance
        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'business_limit': 2,
                    'max_km_distance': 10,
                }
            }
        ):
            response = self.get(data=data)

        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 1)
        self.assertEqual(businesses[0]['id'], business_by_region.id)

        # only for today created business
        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'max_active_days': 0,
                }
            }
        ):
            response = self.get(data=data)

        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 0)

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 6,  # more than we have
                    'business_limit': 3,
                }
            }
        ):
            response = self.get(data=data)

        self.assertEqual(len(response.json()['businesses']), 0)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_logged_in(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        business_0 = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        self.index.refresh()
        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'location_id': '',
            'gender': Gender.Male,
            'categories_ids': categories_ids,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)
        self.assertEqual(ids, {business_0.id})
        self.assertEqual(len(businesses[0]['treatment_services']), 1)
        self.assertEqual(
            businesses[0]['treatment_services'][0]['name'],
            f'Service-{BusinessCategoryEnum.HAIR_SALONS}',
        )

        self.user.customerfavoritecategory_set.all().delete()

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)

    @staticmethod
    def _create_appointment_with_location(latitude, longitude, user):
        business_with_location = baker.make(
            'business.Business',
            latitude=latitude,
            longitude=longitude,
        )
        create_subbooking(
            business=business_with_location,
            booking_kws=dict(
                booked_for__user=user,
                updated_by=user,
                _save_related=True,
            ),
        )
        UserTuning.update_multiple_tunings([user.id])
        user.get_document().save()
        ELASTIC.indices[ESIndex.USER].refresh()

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_logged_in_get_last_booking_location(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        self.index.refresh()
        self._create_appointment_with_location(52, 21, self.user)

        data = {
            'location_geo': '',
            'location_id': '',
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[name].id for name in categories_names],
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json()['businesses'], [])

    @override_eppo_feature_flag(
        {CustomerRecommendedNewFlag.flag_name: {'business_limit': 1, 'min_treatment_count': 1}}
    )
    def test_get_logged_in_no_matching_favorite_service(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        business_0 = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.BARBERS],
        )
        self.index.refresh()
        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': categories_ids,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)
        self.assertEqual(ids, {business_0.id})
        # no matching favorite services so return other services
        self.assertEqual(len(businesses[0]['treatment_services']), 2)
        self.assertEqual(
            {s['name'] for s in businesses[0]['treatment_services']},
            {f'Service-{c}' for c in categories_names[:2]},
        )

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_with_user_empty(self):
        data = {
            'location_geo': [52.232989, 20.990810],
            'limit': 10,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

        data['categories_ids'] = []
        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

        data['categories_ids'] = ''
        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

    def _assert_response(self, data: dict, is_empty: bool):
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['businesses']), 0 if is_empty else 1)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_required_location(self):
        self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.BARBERS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.BARBERS],
        )
        self.index.refresh()
        flag_data = {
            'is_required_location': True,
            'min_business_count': 1,
        }
        flags = {CustomerRecommendedNewFlag.flag_name: flag_data}

        with override_eppo_feature_flag(flags):
            self._assert_response(data={}, is_empty=True)

        flag_data['is_required_location'] = False
        with override_eppo_feature_flag(flags):
            self._assert_response(data={}, is_empty=False)

        self._remove_login_token()
        data = {
            'gender': Gender.Female,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.BARBERS].id],
        }
        flag_data['is_required_location'] = True
        with override_eppo_feature_flag(flags):
            self._assert_response(data=data, is_empty=True)

        flag_data['is_required_location'] = False
        with override_eppo_feature_flag(flags):
            self._assert_response(data=data, is_empty=False)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_invalid_without_user(self):
        self._remove_login_token()
        data = {
            'location_geo': [52.232989, 20.990810],
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'gender',
                        'description': 'This field is required.',
                        'code': 'required',
                    },
                    {
                        'field': 'categories_ids',
                        'description': 'This field is required.',
                        'code': 'required',
                    },
                ],
            },
        )

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': 'invalid',
            'categories_ids': ['a'],
            'limit': 20,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'gender',
                        'description': '"invalid" is not a valid choice.',
                        'code': 'invalid_choice',
                    },
                    {
                        'field': 'categories_ids.0',
                        'description': 'A valid integer is required.',
                        'code': 'invalid',
                    },
                ],
            },
        )
        error_min_1_element = {
            'field': 'categories_ids',
            'description': 'Ensure this field has at least 1 elements.',
            'code': 'min_length',
        }

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Female,
            'categories_ids': [],
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'errors': [error_min_1_element]})

        data['categories_ids'] = ''
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'errors': [error_min_1_element]})

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_no_cover_photo(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        self._create_business(
            active_x_days=2,
            has_cover_photo=False,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 21.032610],
        )
        self.index.refresh()

        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': categories_ids[:2],
            'location_id': '',
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 0)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_request_parameters_validation(self):
        self._remove_login_token()
        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.BARBERS].id],
            'location_id': 123,
            'trigger_source': SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY,
        }

        # Test with invalid gender
        invalid_data = data.copy()
        invalid_data['gender'] = 'invalid_gender'
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid_choice')

        # Test with invalid categories_ids (non-integer)
        invalid_data = data.copy()
        invalid_data['categories_ids'] = ['not_an_integer']
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid')

        # Test with invalid location_id (non-integer)
        invalid_data = data.copy()
        invalid_data['location_id'] = 'not_an_integer'
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid')

        # Test with invalid trigger_source
        invalid_data = data.copy()
        invalid_data['trigger_source'] = 'invalid_trigger_source'
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid_choice')


@override_eppo_feature_flag({SimpleSerchableInRecommendedForYouFlag.flag_name: True})
class TestCustomerMyBooksySelectedForYouViewOptimized(TestCustomerMyBooksySelectedForYouView): ...


@override_eppo_feature_flag(
    {
        CustomerRecommendedNewFlag.flag_name: {'business_limit': 5},
        PortfolioImagesExperimentOnFlag.flag_name: True,
    }
)
class TestCustomerMyBooksySelectedForYouPortfolioImagesExperiment(
    TestCustomerMyBooksySelectedForYouView, BaseAsyncHTTPTest
):
    def test_on_web_default_logic(self):
        self.customer_booking_src.name = WEB
        self.customer_booking_src.save()

        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=3,
        )
        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=4,
        )
        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=6,
        )
        self.index.refresh()

        category_id = self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS].id

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': [category_id],
            'location_id': '',
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 3)
        self.assertNotIn('inspiration', businesses[0]['images'])
        self.assertNotIn('inspiration', businesses[1]['images'])
        self.assertNotIn('inspiration', businesses[2]['images'])

    def test_no_px_with_min_inspiration_photos_default_logic(self):
        self.customer_booking_src.name = ANDROID
        self.customer_booking_src.save()

        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=3,
        )
        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=0,
        )
        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=2,
        )
        self.index.refresh()

        category_id = self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS].id

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': [category_id],
            'location_id': '',
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 3)
        self.assertNotIn('inspiration', businesses[0]['images'])
        self.assertNotIn('inspiration', businesses[1]['images'])
        self.assertNotIn('inspiration', businesses[2]['images'])

    @override_eppo_feature_flag(
        {SelectedForYouPortfolioImagesExperiment.flag_name: ExperimentVariants.CONTROL}
    )
    def test_control_group(self):
        self.customer_booking_src.name = ANDROID
        self.customer_booking_src.save()

        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=3,  # below min portfolio images
        )
        business_2 = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=4,
        )
        business_3 = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=6,
        )
        self.index.refresh()

        category_id = self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS].id

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': [category_id],
            'location_id': '',
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 2)
        self.assertIn(businesses[0]['id'], [business_2.id, business_3.id])
        self.assertIn(businesses[1]['id'], [business_2.id, business_3.id])
        self.assertEqual(len(businesses[0]['images']['cover']), 1)
        self.assertEqual(len(businesses[1]['images']['cover']), 1)
        self.assertNotIn('inspiration', businesses[0]['images'])
        self.assertNotIn('inspiration', businesses[1]['images'])

    @override_eppo_feature_flag(
        {SelectedForYouPortfolioImagesExperiment.flag_name: ExperimentVariants.VARIANT_A}
    )
    def test_variant_a(self):
        self.customer_booking_src.name = ANDROID
        self.customer_booking_src.save()

        self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=3,  # below min portfolio images
        )
        business_2 = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=4,
        )
        business_3 = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
            number_of_portfolio_photos=6,
        )
        self.index.refresh()

        category_id = self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS].id

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': [category_id],
            'location_id': '',
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 2)
        self.assertIn(businesses[0]['id'], [business_2.id, business_3.id])
        self.assertIn(businesses[1]['id'], [business_2.id, business_3.id])
        self.assertEqual(len(businesses[0]['images']['inspiration']), 4)
        self.assertEqual(len(businesses[1]['images']['inspiration']), 4)


@override_eppo_feature_flag({UseLastBookingLocationInSelectedForYouFlag.flag_name: True})
class TestCustomerMyBooksySelectedForYouViewFF(TestCustomerMyBooksySelectedForYouView):

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_logged_in_get_last_booking_location(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        business_0 = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        self.index.refresh()
        self._create_appointment_with_location(52, 21, self.user)

        data = {
            'location_geo': '',
            'location_id': '',
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[name].id for name in categories_names],
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)
        self.assertEqual(ids, {business_0.id})


@override_eppo_feature_flag(
    {
        CustomerRecommendedNewFlag.flag_name: {'business_limit': 3},
        SelectedForYouV4RandomizedScoringExperiment.flag_name: ExperimentVariants.VARIANT_A,
    }
)
class TestCustomerMyBooksySelectedForYouV4(TestCustomerMyBooksySelectedForYouView):

    def _prepare_businesses(self, count, categories_names, **kwargs):
        default_params = {
            'active_x_days': 10,
            'has_cover_photo': True,
            'services_count': 3,
            'location_geo': [52.232989, 20.990810],
            'promoted': False,
        }
        default_params.update(kwargs)

        businesses = []
        for _ in range(count):
            business = self._create_business(categories_names=categories_names, **default_params)
            businesses.append(business)

        self.index.refresh()
        return businesses

    def _setup_customer_favorite_categories(self, categories_names):
        for category_name in categories_names:
            CustomerFavoriteCategory.objects.create(
                user=self.authorized_user,
                category=self.bussiness_categories[category_name],
            )

    def _prepare_request_data(
        self,
        *,
        location_geo=None,
        categories_names=None,
        gender=None,
        trigger_source=None,
    ):
        return {
            'location_geo': location_geo or [52.232989, 20.990810],
            'gender': gender or Gender.Male,
            'categories_ids': [self.bussiness_categories[name].id for name in categories_names],
            'trigger_source': trigger_source or SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY,
        }

    def test_randomized_scoring_different_orderings_results(self):
        categories = [BusinessCategoryEnum.HAIR_SALONS]
        self._setup_customer_favorite_categories(categories)

        # more businesses for better variation
        self._prepare_businesses(count=10, categories_names=categories)
        data = self._prepare_request_data(categories_names=categories)

        results = []
        for _ in range(15):
            response = self.get(data=data)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            business_ids = [b['id'] for b in response.json()['businesses']]
            results.append(business_ids)

        unique_results = set(tuple(result) for result in results)
        # This is conservative but should help to avoid flakiness
        self.assertGreaterEqual(len(unique_results), 3)

    def test_promoted_business_penalty(self):
        categories = [BusinessCategoryEnum.HAIR_SALONS]
        self._setup_customer_favorite_categories(categories)

        self._prepare_businesses(count=3, categories_names=categories, promoted=True)
        regular_businesses = self._prepare_businesses(
            count=3, categories_names=categories, promoted=False
        )
        self.index.refresh()

        data = self._prepare_request_data(categories_names=categories)

        regular_first_count = 0
        total_runs = 30  # More runs for better statistics

        for _ in range(total_runs):
            response = self.get(data=data)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            businesses = response.json()['businesses']
            self.assertEqual(len(businesses), 3)

            # Check if a regular business is in first position
            first_business_id = businesses[0]['id']
            regular_ids = {biz.id for biz in regular_businesses}

            if first_business_id in regular_ids:
                regular_first_count += 1

        # Conservative threshold to avoid flakiness while still testing the penalty
        regular_first_percentage = regular_first_count / total_runs
        self.assertGreater(regular_first_percentage, 0.4)

    def test_reviews_count_scoring_randomization(self):
        categories = [BusinessCategoryEnum.HAIR_SALONS]
        self._setup_customer_favorite_categories(categories)

        businesses = self._prepare_businesses(count=3, categories_names=categories)

        # Set very different review counts to make the effect more visible when applied
        high_reviews_business = businesses[0]
        high_reviews_doc = high_reviews_business.get_document()
        high_reviews_doc.reviews_count = 50  # Many reviews = lower boost when applied
        high_reviews_doc.save()

        low_reviews_business = businesses[1]
        low_reviews_doc = low_reviews_business.get_document()
        low_reviews_doc.reviews_count = 1  # Few reviews = higher boost when applied
        low_reviews_doc.save()

        medium_reviews_business = businesses[2]
        medium_reviews_doc = medium_reviews_business.get_document()
        medium_reviews_doc.reviews_count = 10  # Medium reviews
        medium_reviews_doc.save()

        self.index.refresh()

        data = self._prepare_request_data(categories_names=categories)

        orderings = []
        total_runs = 25  # More runs for better statistical reliability

        for _ in range(total_runs):
            response = self.get(data=data)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            businesses_response = response.json()['businesses']
            self.assertEqual(len(businesses_response), 3)

            # Record the ordering
            business_ids = [b['id'] for b in businesses_response]
            orderings.append(tuple(business_ids))

        # Also verify that the low-review business appears in different positions
        # due to the randomized application of review scoring
        low_review_positions = []
        for ordering in orderings:
            if low_reviews_business.id in ordering:
                position = list(ordering).index(low_reviews_business.id)
                low_review_positions.append(position)

        # The low-review business should appear in at least 2 different positions
        # due to randomization (when review factor applied vs not applied)
        unique_positions = set(low_review_positions)
        self.assertGreaterEqual(len(unique_positions), 2)
