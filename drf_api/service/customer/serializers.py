from hashlib import md5

from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.db import IntegrityError
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from country_config import Country
from drf_api.base_serializers import BooksyValidationMixin, CustomValidationErrorModelSerializer
from drf_api.service.customer.constants import COUNTRY_TERMS_EMAILS_MAP, HTML
from drf_api.service.customer.enums import SelectedForYouTriggerSource
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.email_internal import generate_private_email
from lib.feature_flag.feature.customer import (
    PhoneRegistrationNameFlag,
    SkipFavoriteCategoryUpdateFlag,
)
from lib.fields.geo_location import GeoLocationSerializerField
from lib.fields.phone_number import BooksyCellPhoneCountrySpecificField
from lib.serializers import CommaS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RequiredContextMixin
from service.customer.consts import PEOPLE_ALSO_BOOKED_ALGORITHM_NAME_BASIC
from webapps.business.models import BusinessCategory, Service, ServiceVariant
from webapps.business.serializers.fields import ServicePriceField
from webapps.notification.models import NotificationEmailCodes
from webapps.photo.serializers import ServicePhotoSerializer
from webapps.pos.models import POS
from webapps.pos.serializers import ExternalPartners
from webapps.user.const import FIXED_DEV_SMS_CODE, GENDERS, Gender
from webapps.user.enums import RegistrationSource
from webapps.user.models import CustomerFavoriteCategory, User, UserProfile
from webapps.user.serializers import BaseCustomerCreateSerializer, GDPRUserAgreementSerializer


class BusinessPaymentOptionsSerializer(serializers.Serializer):
    external_partners = ExternalPartners(source='*', read_only=True)
    force_stripe_pba = serializers.BooleanField(read_only=True)
    transaction_merchant_account = serializers.SerializerMethodField()

    @staticmethod
    def get_transaction_merchant_account(pos: POS):
        return (
            settings.ADYEN_MARKET_PAY_MERCHANT_ACCOUNT
            if pos.marketpay_enabled
            else settings.ADYEN_MERCHANT_ACCOUNT
        )


class ServiceSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    active = serializers.BooleanField()
    combo_type = serializers.CharField()
    photos = ServicePhotoSerializer(many=True)

    class Meta:
        model = Service
        fields = (
            'id',
            'name',
            'active',
            'combo_type',
            'photos',
        )


class ServiceVariantSerializer(serializers.ModelSerializer):
    service = ServiceSerializer()
    duration = DurationField(source='service_duration')
    service_price = ServicePriceField()

    class Meta:
        model = ServiceVariant
        fields = (
            'id',
            'duration',
            'service_price',
            'service',
        )


class PeopleAlsoBookedSerializer(serializers.Serializer):
    business = serializers.SerializerMethodField()
    service_variant = ServiceVariantSerializer(source='*')

    def get_business(self, service_variant):
        businesses = self.context['businesses']

        return businesses[service_variant.business_id]


class PeopleAlsoBookedResponseSerializer(serializers.Serializer):
    people_also_booked = PeopleAlsoBookedSerializer(many=True, source='*')
    algorithm_name = serializers.ReadOnlyField(default=PEOPLE_ALSO_BOOKED_ALGORITHM_NAME_BASIC)


def _get_message_already_registered() -> str:
    return _('Phone number already registered. Please use email or social login.')


class CustomerQuickSignInUpRequestSerializer(
    BooksyValidationMixin,
    serializers.Serializer,
):
    cell_phone = BooksyCellPhoneCountrySpecificField(
        required=True,
        help_text='Cell phone number with country code',
    )
    sms_code = serializers.CharField(
        required=True,
        help_text='SMS code (for confirming cell phone number)',
    )
    user_agreements = GDPRUserAgreementSerializer(
        required=False,
        allow_null=True,
        many=True,
        help_text='GDPR user agreement used only in sign up',
    )
    creation_token = serializers.CharField(
        required=False,
        allow_null=True,
        max_length=32,
        help_text='Token require for sign in. Obtained from sign up response',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if PhoneRegistrationNameFlag():
            self.fields['name'] = serializers.CharField(
                required=False,
                allow_null=True,
                allow_blank=True,
                help_text='Name of the user - first and last name',
            )

    def validate(self, attrs):
        super().validate(attrs)
        cell_phone = attrs['cell_phone']
        attrs['email'] = generate_private_email(cell_phone)
        if cell_phone and not User.objects.filter(email=attrs['email']).exists():
            # We store in db phone in smart_nice format which can be local_nice or global_nice
            # look at implementation in lib/fields/phone_number.py::to_python
            phones = {cell_phone.local_nice, cell_phone.global_nice}

            if User.objects.filter(
                cell_phone__in=phones,
                internal_data__registration_source=RegistrationSource.PHONE_NUMBER,
            ).exists():
                raise serializers.ValidationError(
                    _get_message_already_registered(),
                    'quick_sign_in_up_not_allowed',
                )

        return attrs


def generate_creation_token(user):
    created = user.created.isoformat()
    return md5(created.encode()).hexdigest()


class CustomerQuickSignUpSerializer(
    BooksyValidationMixin,
    RequiredContextMixin,
    BaseCustomerCreateSerializer,
):
    required_context = ['booking_source']
    name = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = User
        fields = (
            'cell_phone',
            'email',
            'name',
            'sms_code',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.context['profile_type'] = UserProfile.Type.CUSTOMER

    def create(self, validated_data):
        validated_data['password'] = self._generate_unusable_password()
        if PhoneRegistrationNameFlag():
            if name := validated_data.pop('name', False):
                first_name, last_name = self._split_name(name)
                validated_data['first_name'] = first_name
                validated_data['last_name'] = last_name
        user = super().create(validated_data)
        if gdpr_agreements := self.context['user_agreements']:
            user.create_or_update_gdpr_agreements(gdpr_agreements, created=True)
        return user

    def update(self, instance, validated_data):
        raise NotImplementedError()

    @staticmethod
    def _generate_unusable_password():
        return make_password(None)

    @staticmethod
    def _split_name(name: str):
        parts = name.strip().split(' ', 1)
        if len(parts) == 2:
            return parts[0], parts[1]
        return parts[0], ''


class CustomerQuickSignInSerializer(CustomerQuickSignUpSerializer):
    creation_token = serializers.CharField(
        required=True,
        allow_null=False,
        min_length=32,
        max_length=32,
        error_messages={
            'null': _get_message_already_registered(),
            'required': _get_message_already_registered(),
        },
    )

    class Meta:
        model = User
        fields = (
            'cell_phone',
            'creation_token',
            'sms_code',
        )

    def create(self, validated_data):
        raise NotImplementedError()

    def validate_creation_token(self, value):
        valid_creation_token = generate_creation_token(self.instance)
        if value != valid_creation_token:
            raise serializers.ValidationError(_('Incorrect verification token'))

        return value


class BaseNotificationEmailCodesSerializer(CustomValidationErrorModelSerializer):
    class Meta:
        model = NotificationEmailCodes
        fields = ('email_address', 'confirmation_code')

    @property
    def _user(self) -> User:
        return self.context['request'].user


class CustomerEmailChangeSerializer(BaseNotificationEmailCodesSerializer):
    def validate(self, attrs: dict) -> dict:
        super().validate(attrs)
        email = attrs['email_address']
        confirmation_code = attrs['confirmation_code']
        if not settings.LIVE_DEPLOYMENT and confirmation_code == FIXED_DEV_SMS_CODE:
            return attrs

        if not NotificationEmailCodes.is_valid(
            self._user.id,
            email,
            confirmation_code,
            settings.EMAIL_CONFIRMATION_CODE_LIFETIME_MINUTES,
        ):
            raise serializers.ValidationError(
                {'confirmation_code': _("Incorrect verification code")},
            )

        return attrs

    def update(self, instance: User, validated_data: dict) -> User:
        instance.email = validated_data['email_address']
        try:
            instance.save(update_fields=['email'])
        except IntegrityError as exc:
            raise serializers.ValidationError(
                {
                    'errors': [
                        {
                            'code': 'invalid',
                            'description': _("Email already exists!"),
                            'field': 'email_address',
                        }
                    ]
                }
            ) from exc

        return instance


class NotificationEmailCodesSerializer(BaseNotificationEmailCodesSerializer):
    def validate_confirmation_code(self, value):
        if NotificationEmailCodes.objects.filter(user_id=self._user.id).count() > 10:
            raise serializers.ValidationError(
                detail=_('Too many requests. Wait an hour until next request')
            )

        return value

    def validate_email_address(self, value: str) -> str:
        if self._user.businesses.filter(active=True, deleted__isnull=True).exists():
            raise serializers.ValidationError(
                detail=_(
                    'For security reasons, business owners can only change their email address '
                    'via Customer Support.'
                )
            )

        if value == self._user.email:
            raise serializers.ValidationError(
                detail=_('New email should be different than your current email address.')
            )

        if User.objects.filter(email=value.lower()).exists():
            raise serializers.ValidationError(detail=_('Email already exists!'))

        return value

    def create(self, validated_data: dict):
        return NotificationEmailCodes.add(user=self._user, **validated_data)


class BusinessContactInfoSerializer(serializers.Serializer):
    business_id = serializers.IntegerField()


class BusinessContactInfoPostResponseSerializer(serializers.Serializer):
    email = serializers.CharField()


class BooksyOmnibusConsentContentGetResponseSerializer(serializers.Serializer):
    title = serializers.CharField(allow_null=True)
    html = serializers.SerializerMethodField()

    @staticmethod
    def get_html(_instance):
        country_code = settings.API_COUNTRY
        if country_code not in Country.get_eu_countries():
            return None
        country_terms, country_email = COUNTRY_TERMS_EMAILS_MAP.get(country_code)
        return HTML.format(country_email=country_email, country_terms=country_terms)


class BusinessCategorySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    icon = serializers.URLField(allow_null=True)

    class Meta:
        model = BusinessCategory
        fields = ('id', 'name', 'icon')


class BusinessCategoriesResponseSerializer(serializers.Serializer):
    categories = BusinessCategorySerializer(many=True)


class BaseCustomerFavoriteCategorySerializer(serializers.Serializer):
    category_ids = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_empty=True
    )

    def validate_category_ids(self, category_ids: list[int]) -> list[int]:
        with using_db_for_reads(READ_ONLY_DB):
            existing_category_ids = BusinessCategory.objects.filter(
                id__in=category_ids, type=BusinessCategory.CATEGORY
            ).values_list('id', flat=True)

        if invalid_ids := set(category_ids) - set(existing_category_ids):
            raise serializers.ValidationError(
                detail=_(f"Invalid category IDs: {list(invalid_ids)}")
            )
        return category_ids


class CustomerPreferencesSerializer(BaseCustomerFavoriteCategorySerializer):
    gender = serializers.ChoiceField(choices=Gender, required=False)

    def validate_category_ids(self, category_ids: list[int]) -> list[int]:
        category_ids = super().validate_category_ids(category_ids)
        # max 5 category ids are accepted but no error should be returned if more provided
        accepted_category_ids = category_ids[:5]
        return accepted_category_ids

    @staticmethod
    def update_gender(user: User, new_gender: Gender, skip_exists: bool = False) -> User:
        if SkipFavoriteCategoryUpdateFlag():
            if skip_exists and not user.gender:
                user.gender = new_gender
                user.save(update_fields=['gender'])
            return user

        user.gender = new_gender
        user.save(update_fields=['gender'])
        return user


class CustomerFavoriteCategorySerializer(
    BooksyValidationMixin, BaseCustomerFavoriteCategorySerializer
):
    def validate_category_ids(self, category_ids: list[int]) -> list[int]:
        category_ids = super().validate_category_ids(category_ids)
        if not 1 <= len(category_ids) <= 5:
            raise serializers.ValidationError(detail=_('Expected length of category_ids: 1-5'))

        return category_ids

    def save(self, **kwargs):
        validated_data = {**self.validated_data, **kwargs}

        user_id = self.context.get('user_id')
        category_ids = validated_data.get('category_ids')

        CustomerFavoriteCategory.update_categories_for_user(user_id, category_ids)


class CustomerFavoriteCategoryResponseSerializer(serializers.Serializer):
    favorite_categories = serializers.ListField(
        child=serializers.IntegerField(), help_text='Business Categories chosen by user'
    )


class NullableIntegerField(serializers.IntegerField):
    def to_internal_value(self, data):
        return None if data == '' else super().to_internal_value(data)


class SelectedForYouRequestSerializer(BooksyValidationMixin, serializers.Serializer):
    gender = serializers.ChoiceField(
        choices=GENDERS,
        required=True,
    )
    categories_ids = CommaSeparatedListField(
        child=serializers.IntegerField(),
        allow_empty_string=True,
        required=True,
        min_length=1,
        max_length=5,
    )
    location_geo = GeoLocationSerializerField(required=False, allow_blank=True)
    location_id = NullableIntegerField(required=False, default=None)
    trigger_source = serializers.ChoiceField(
        choices=SelectedForYouTriggerSource.choices(),
        required=False,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.context.get('is_authenticated'):  # used also by swagger without context
            self._make_fields_optional(('gender', 'categories_ids'))
            self.fields['categories_ids'].default = []

    def _make_fields_optional(self, fields_name):
        for field_name in fields_name:
            field = self.fields[field_name]
            field.validators = []
            field.required = False
            field.allow_null = True
