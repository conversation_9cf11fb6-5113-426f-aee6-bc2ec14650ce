import datetime
from decimal import Decimal
from unittest import mock
from unittest.mock import (
    PropertyMock,
    patch,
)

import pytest
import stripe
from dateutil import tz as dateutil_tz
from django.conf import settings
from django.test import override_settings
from django.urls import reverse
from freezegun import freeze_time
from model_bakery import baker
from rest_framework import status
from stripe.error import InvalidRequestError

from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.payments.enums import PaymentProviderCode
from lib.tools import tznow
from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe
from webapps.kill_switch.models import KillSwitch
from webapps.payment_gateway.models import (
    WalletFeeSettings,
)
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import AccountHolder, StripeAccountHolder
from webapps.pos.baker_recipes import pos_recipe
from webapps.stripe_integration.enums import (
    FastPayoutStatus,
    StripeAccountStatus,
    StripeExternalAccountType,
    StripeFeeType,
    StripePayoutMethodType,
    StripePayoutStatus,
)
from webapps.stripe_integration.models import (
    StripeAccount,
    StripePayout,
)
from webapps.stripe_integration.tasks import trigger_fast_payout_paid_fees_task
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_account_retrieve,
    mock_stripe_balance_retrieve,
    mock_stripe_balancetransaction_list,
    mock_stripe_payout_create,
    mock_stripe_payout_retrieve,
    mock_stripe_transfer_create,
)


class FastPayoutCreateViewSetBaseTestCase(BusinessOwnerAPITestCase):
    endpoint_name = 'stripe_fast_payout_create'

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.business = business_recipe.make()
        cls.user = cls.business.owner
        cls.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key='biz_key',
        )
        cls.pos = pos_recipe.make(
            business=cls.business,
            stripe_terminal_enabled=True,
            fast_payouts_visible=True,
        )
        cls.account_holder = baker.make(AccountHolder)
        cls.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            cls.business.id,
            statement_name=cls.business.name,
        )[0]
        cls.stripe_account_holder = baker.make(
            StripeAccountHolder,
            account_holder_id=cls.business_wallet.account_holder_id,
            external_id='test_intent',
        )
        cls.stripe_account = baker.make(
            StripeAccount,
            pos=cls.pos,
            status=StripeAccountStatus.VERIFIED,
            charges_enabled=True,
            payouts_enabled=True,
            blocked=False,
        )
        cls.stripe_wallet_fee_settings = baker.make(
            WalletFeeSettings,
            default=True,
            payment_provider_code=PaymentProviderCode.STRIPE,
            fast_payout_provision_percentage=Decimal(2.60),
            fast_payout_provision_fee=30,
        )
        cls.url = reverse(
            cls.endpoint_name,
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    def make_post(self, amount, description=None, dry_run=None):
        payload = {
            'amount': amount,
        }
        if description:
            payload['description'] = description
        if dry_run:
            payload['dry_run'] = dry_run

        return self.client.post(
            self.url,
            data=payload,
        )


@pytest.mark.django_db
@patch(
    'webapps.stripe_integration.models.StripeAccount.fast_payouts_status',
    new_callable=PropertyMock,
    return_value=FastPayoutStatus.AVAILABLE,
)
class FastPayoutCreateViewSetTestCase(FastPayoutCreateViewSetBaseTestCase):
    def setUp(self):
        super().setUp()
        baker.make(KillSwitch, name=KillSwitch.System.FAST_PAYOUTS_DAILY_LIMIT, is_killed=True)

    @override_settings(POS__FAST_PAYOUTS=True)
    def test_amount_too_low(self, _):
        resp = self.make_post(amount=12.33)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(resp.data['errors'][0]['field'], 'amount')
        self.assertEqual(
            resp.data['errors'][0]['description'], 'Payout amount is lower than limit: 50.00'
        )

    @override_settings(POS__FAST_PAYOUTS=True)
    def test_amount_too_high(self, _):
        resp = self.make_post(amount=100_000.00)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(resp.data['errors'][0]['field'], 'amount')
        self.assertEqual(
            resp.data['errors'][0]['description'],
            'Payout amount is higher than limit: 5000.00',
        )

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_payout_retrieve(stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_payout(
        self,
        account_retrieve_mock,
        balance_retrieve_mock,
        payout_retrieve_mock,
        payout_create_mock,
        transfer_create_mock,
        balancetransaction_list_mock,
    ):
        resp = self.make_post(amount=100.00, description='This i. OK?!,/')
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        payout_create_mock.assert_called_with(
            stripe_account=self.stripe_account.external_id,
            amount=9717,
            currency='USD',
            method=StripePayoutMethodType.INSTANT,
            expand=['destination'],
        )
        transfer_create_mock.assert_not_called()
        payout = StripePayout.objects.get(external_id='payout_id')
        self.assertTrue(payout.destination)
        self.assertEqual(payout.destination['id'], 'card_id')

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock.patch(
        'stripe.Payout.create',
        side_effect=InvalidRequestError(message='error_mssg', code='error_code', param='sum param'),
    )
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    @mock_stripe_payout_retrieve(stripe.Card(id='card_id'))
    def test_stripe_internal_error(
        self,
        payout_retrieve_mock,
        account_retrieve_mock,
        balance_retrieve_mock,
        transfer_create_mock,
        balancetransaction_list_mock,
    ):
        resp = self.make_post(amount=100.00, description='This i. OK?!,')
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(resp.data, {'code': 'error_code', 'message': 'error_mssg'})
        transfer_create_mock.assert_not_called()
        self.assertFalse(StripePayout.objects.filter(external_id='payout_id').exists())

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination='destination_id')
    @mock_stripe_payout_retrieve(stripe.Card(id='asdf'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_payout_no_destination(
        self,
        account_retrieve_mock,
        balance_retrieve_mock,
        payout_retrieve_mock,
        payout_create_mock,
        transfer_create_mock,
        balancetransaction_list_mock,
    ):
        resp = self.make_post(amount=100.00, description='a' * 22)
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        payout_create_mock.assert_called_with(
            stripe_account=self.stripe_account.external_id,
            amount=9717,
            currency='USD',
            method=StripePayoutMethodType.INSTANT,
            expand=['destination'],
        )
        transfer_create_mock.assert_not_called()
        payout = StripePayout.objects.get(external_id='payout_id')
        self.assertTrue(payout.destination)
        self.assertEqual(payout.destination['id'], 'asdf')

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create()
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    @mock_stripe_payout_retrieve(stripe.Card(id='card_id'))
    def test_payout_no_desc(
        self,
        payout_retrieve_mock,
        account_retrieve_mock,
        balance_retrieve_mock,
        payout_create_mock,
        transfer_create_mock,
        balancetransaction_list_mock,
    ):
        resp = self.make_post(amount=100.00)
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        payout_create_mock.assert_called_with(
            stripe_account=self.stripe_account.external_id,
            amount=9717,
            currency='USD',
            method=StripePayoutMethodType.INSTANT,
            expand=['destination'],
        )
        transfer_create_mock.assert_not_called()

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000)
    def test_payout_description_with_wrong_characters(
        self,
        balance_retrieve_mock,
        payout_create_mock,
        transfer_create_mock,
    ):
        resp = self.make_post(amount=100.00, description=r'aafafasas<>\\f####')
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            resp.json(),
            {
                'errors': [
                    {
                        'code': 'invalid',
                        'description': r'Description can only contain English'
                        r' letters, digits, a space and some punctuation (.,?!/()#-+^=][{}&~).',
                        'field': 'description',
                    }
                ]
            },
        )
        payout_create_mock.assert_not_called()
        transfer_create_mock.assert_not_called()

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000)
    def test_payout_description_too_long(
        self,
        balance_retrieve_mock,
        payout_create_mock,
        transfer_create_mock,
    ):
        resp = self.make_post(amount=100.00, description='a' * 23)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            resp.json(),
            {
                'errors': [
                    {
                        'code': 'max_length',
                        'description': 'Ensure this field has no more than 22 characters.',
                        'field': 'description',
                    }
                ]
            },
        )
        payout_create_mock.assert_not_called()
        transfer_create_mock.assert_not_called()

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_dry_run(
        self,
        account_retrieve_mock,
        balance_retrieve_mock,
    ):
        response = self.make_post(100.00, dry_run=True)
        self.assertDictEqual(
            response.json(),
            {
                'total_amount': '100.00',
                'fast_payout_provision': '2.53',
                'fast_payout_txn_fee': '0.30',
                'total_fee_amount': '2.83',
                'total_payout_amount': '97.17',
            },
        )

    @override_settings(POS__FAST_PAYOUTS=False)
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 10000, 10)
    def test_settings_disabled(self, balance_retrieve_mock):
        response = self.make_post(amount=100.00, dry_run=True)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            response.data,
            {
                'errors': [
                    {
                        'code': 'invalid',
                        'description': 'Fast Payouts are not available in this country.',
                        'field': 'non_field_errors',
                    }
                ]
            },
        )

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    @mock_stripe_payout_retrieve(stripe.Card(id='card_id'))
    def test_payout_paid_fee(
        self,
        payout_retrieve_mock,
        account_retrieve_mock,
        balance_retrieve_mock,
        payout_create_mock,
        transfer_create_mock,
        balancetransaction_list_mock,
    ):
        resp = self.make_post(amount=100.00, description='descr')
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        transfer_create_mock.assert_not_called()
        payout: StripePayout = StripePayout.objects.get(external_id='payout_id')
        payout_create_mock.assert_called_with(
            stripe_account=self.stripe_account.external_id,
            amount=payout.fast_payout_splits['total_payout_amount'],
            currency='USD',
            method=StripePayoutMethodType.INSTANT,
            expand=['destination'],
        )
        self.assertTrue(payout.destination)
        self.assertEqual(payout.destination['id'], 'card_id')
        self.assertEqual(payout.status, StripePayoutStatus.PENDING)

        payout.status = StripePayoutStatus.PAID
        payout.save()

        trigger_fast_payout_paid_fees_task.delay()
        transfer_create_mock.assert_called_with(
            amount=payout.fast_payout_splits['total_fee_amount'],
            currency=settings.CURRENCY_CODE,
            destination=settings.STRIPE_ACCOUNT_ID,
            description=StripeFeeType.FAST_PAYOUT_FEE.label,
            metadata={'payout': payout.external_id},
            stripe_account=payout.account.external_id,
        )
        self.assertEqual(transfer_create_mock.call_count, 1)
        payout.refresh_from_db()
        self.assertEqual(payout.status, StripePayoutStatus.PAID)
        self.assertTrue(payout.provision_charged)

        trigger_fast_payout_paid_fees_task.delay()
        self.assertEqual(transfer_create_mock.call_count, 1)


@pytest.mark.django_db
@patch(
    'webapps.stripe_integration.models.StripeAccount.fast_payouts_status',
    new_callable=PropertyMock,
    return_value=FastPayoutStatus.AVAILABLE,
)
class FastPayoutCreateViewSetTestKillSwtichNotKilledCase(FastPayoutCreateViewSetBaseTestCase):
    def setUp(self):
        super().setUp()
        baker.make(KillSwitch, name=KillSwitch.System.FAST_PAYOUTS_DAILY_LIMIT, is_killed=False)

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    @mock_stripe_payout_retrieve(stripe.Card(id='card_id'))
    def test_payout(self, _, __, ___, payout_create_mock, transfer_create_mock, ____):
        resp = self.make_post(amount=100.00, description='This i. OK?!,/')
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        payout_create_mock.assert_called_with(
            stripe_account=self.stripe_account.external_id,
            amount=9717,
            currency='USD',
            method=StripePayoutMethodType.INSTANT,
            expand=['destination'],
        )
        transfer_create_mock.assert_not_called()
        payout = StripePayout.objects.get(external_id='payout_id')
        self.assertTrue(payout.destination)
        self.assertEqual(payout.destination['id'], 'card_id')

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_payout_not_available(
        self,
        account_retrieve_mock,
        balance_retrieve_mock,
        payout_create_mock,
        transfer_create_mock,
        balancetransaction_list_mock,
    ):
        previous_payout = baker.make(
            StripePayout,
            account=self.stripe_account,
            status=StripePayoutStatus.PENDING,
            method=StripePayoutMethodType.INSTANT,
            payout_created=tznow(),
        )

        resp = self.make_post(amount=100.00, description='This i. OK?!,/')
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        dict_assert(
            resp.json()['errors'][0],
            {
                'field': 'non_field_errors',
                'description': (
                    'Next Fast Payout will be available after full settlement'
                    ' of the last Fast Payout'
                ),
                'code': 'invalid',
            },
        )
        assert not StripePayout.objects.all().exclude(id=previous_payout.id).exists()

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    @mock_stripe_payout_retrieve(stripe.Card(id='card_id'))
    def test_payout_available_because_previous_has_failed(
        self,
        _,
        __,
        ___,
        payout_create_mock,
        transfer_create_mock,
        ____,
    ):
        baker.make(
            StripePayout,
            account=self.stripe_account,
            status=StripePayoutStatus.FAILED,
            method=StripePayoutMethodType.INSTANT,
            payout_created=tznow(),
        )

        resp = self.make_post(amount=100.00, description='This i. OK?!,/')
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        payout_create_mock.assert_called_with(
            stripe_account=self.stripe_account.external_id,
            amount=9717,
            currency='USD',
            method=StripePayoutMethodType.INSTANT,
            expand=['destination'],
        )
        transfer_create_mock.assert_not_called()
        payout = StripePayout.objects.get(external_id='payout_id')
        self.assertTrue(payout.destination)
        self.assertEqual(payout.destination['id'], 'card_id')

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_payout_not_available_previous_has_provision_charged_false_less_than_3_hours_ago(
        self, *_
    ):
        business_tz = dateutil_tz.gettz(self.business.time_zone_name)
        test_datetime = datetime.datetime(2222, 10, 2, 2, 0, tzinfo=business_tz)
        with freeze_time(test_datetime):
            previous_day_payout = baker.make(
                StripePayout,
                account=self.stripe_account,
                status=StripePayoutStatus.PENDING,
                method=StripePayoutMethodType.INSTANT,
                payout_created=test_datetime - datetime.timedelta(hours=2, minutes=30),
                provision_charged=False,
            )

            self.authorized_user = getattr(
                self.client,
                self.AUTH_METHOD,
            )(
                user=self.user,
                source=getattr(self, self.AUTH_BOOKING_SOURCE),
            )

            resp = self.make_post(amount=100.00, description='This i. OK?!,/')
            self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
            dict_assert(
                resp.json()['errors'][0],
                {
                    'field': 'non_field_errors',
                    'description': (
                        'Next Fast Payout will be available after'
                        ' full settlement of the last Fast Payout'
                    ),
                    'code': 'invalid',
                },
            )
            assert not StripePayout.objects.all().exclude(id=previous_day_payout.id).exists()

    @override_settings(POS__FAST_PAYOUTS=True)
    @mock_stripe_balancetransaction_list
    @mock_stripe_transfer_create
    @mock_stripe_payout_create(payout_id='payout_id', destination=stripe.Card(id='card_id'))
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 300000, 300000)
    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    @mock_stripe_payout_retrieve(stripe.Card(id='card_id'))
    def test_payout_available_previous_has_provision_charged_false_more_than_3_hours_ago(self, *_):
        business_tz = dateutil_tz.gettz(self.business.time_zone_name)
        test_datetime = datetime.datetime(2222, 10, 2, 2, 0, tzinfo=business_tz)
        with freeze_time(test_datetime):
            previous_day_payout = baker.make(
                StripePayout,
                account=self.stripe_account,
                status=StripePayoutStatus.PENDING,
                method=StripePayoutMethodType.INSTANT,
                payout_created=test_datetime - datetime.timedelta(hours=3, minutes=10),
                provision_charged=False,
            )

            self.authorized_user = getattr(
                self.client,
                self.AUTH_METHOD,
            )(
                user=self.user,
                source=getattr(self, self.AUTH_BOOKING_SOURCE),
            )

            resp = self.make_post(amount=100.00, description='This i. OK?!,/')
            self.assertEqual(resp.status_code, status.HTTP_200_OK)
            assert StripePayout.objects.all().exclude(id=previous_day_payout.id).exists()
