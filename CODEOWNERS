[French Certification][1] @booksy/teams/french_certification
webapps/french_certification/
webapps/script_runner/scripts/script_bump_software_version__fr.py

[Subscriptions][1] @booksy/teams/px_conversion
webapps/billing/
webapps/braintree_app/
webapps/stripe_app/
webapps/purchase/
!webapps/purchase/models/subscription_buyer.py
!webapps/purchase/tests/test_subscription_buyer.py

[Cx Booking][1] @booksy/teams/cx_booking
cliapps/booking/
service/booking/
webapps/booking/
webapps/appointment_drafts/
domain_services/booking/
drf_api/service/family_and_friends/
service/customer/book_again.py
service/customer/timeslots.py
webapps/admin_extra/views/booking_staff.py
webapps/admin_extra/views/booking_remove.py

[Px Calendar][1] @booksy/teams/px_calendar
drf_api/service/business/calendar/
drf_api/service/firebase/
service/business/calendars.py
service/business/opening_hours.py
service/resources.py
service/schedule/
webapps/booking/calendar_importer/
webapps/business_calendar/
webapps/public_partners/
webapps/schedule/
webapps/stats_and_reports/reports/other/staff_performance_report.py
webapps/stats_and_reports/reports/other/staff_revenue_payment_methods_report.py
webapps/stats_and_reports/reports/revenue/staff_revenue_forecast.py
webapps/stats_and_reports/reports/staff
webapps/turntracker/
webapps/wait_list/

[Finance Automation][1] @booksy/teams/finance_automation
webapps/admin_extra/tasks/subscription_buyer_tools.py
webapps/admin_extra/tasks/dry_booksy_billing_invoices_report.py
webapps/admin_extra/tasks/dry_offline_invoicing.py
webapps/admin_extra/views/navision/
webapps/navision/
webapps/purchase/models/subscription_buyer.py
webapps/purchase/tests/test_subscription_buyer.py


^[Px Calendar Optional] @booksy/teams/px_calendar
# Resource model
webapps/business/models/models.py


[Px Engagement] @booksy/teams/px-engagement
booksy_pytest_doctest/
.doctestignore
