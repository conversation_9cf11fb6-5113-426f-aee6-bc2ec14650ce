core:
  image:
    repository: us-central1-docker.pkg.dev/bks-ar-pkg/images-prd/core
  deploy:
    celeryAllQueueWorker: true
    celery: false

  # autoscaling:
    # api:
    #   enabled: true
    #   minReplicas: 4
    #   maxReplicas: 8
    #   CPUTargetUtilizationPercentage: 50

  variables:
    apiUwsgiLazyApp: False
    apiUwsgiForkHooks: True
    apiUwsgiRssReload: True
    booksyVariant: live
    booksyCountryCode: ar
    booksyRedisDB: 5
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    authHost: auth-server.auth.svc.cluster.local:8010
    grpcClientProxyHost: http://grpc-ar-client-proxy.grpc-ar.svc.cluster.local:9911
    postgresql:
      masterHost: postgres-primary-lb-5432-cross-mesh.svc.cluster.local
      paymentsHost: *********
      draftsHost: **********
      LBslaveHosts: postgres-replica-lb-5432-cross-mesh.svc.cluster.local
      LBslaveReportsHosts: postgres-lb-reports-replica-cross-mesh.svc.cluster.local
      pgcat:
        enabled: false
    elasticsearch:
      host: elasticsearch-load-balancer-cross-mesh.svc.cluster.local
      port: 9200
      numerOfReplicas: 2
      numerOfShards: 3
    redis:
      celeryBackendHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBeatHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBrokerHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBulkCacheHost: bks-prd-2-us-c1-v2-redis-email-bulk.svc.cluster.local
      riverRedisHost: bks-prd-2-us-c1-redis-river.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: bks-prd-2-us-c1-redis-throttling.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: bks-prd-2-us-c1-v2-redis-email-bulk.svc.cluster.local
      subdomainsRedisHost: bks-prd-2-us-c1-redis-core-subdomains.svc.cluster.local
      simplifiedBookingRedisHost: bks-prd-2-us-c1-redis-simplified-booking.svc.cluster.local

    boostClaims:
      GCSProjectID: bks-prd-2-us-c1

    workloadIdentity:
      projectID: bks-prd-workloads-1

    captcha:
      projectID: bks-prd-workloads-1

    kafka:
      brokerBootstrapServers: seed-312ca77d.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-8c152181.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:30081

      resourcesSecretManager:
        projectID: bks-kafka-prd-2-us-c1

  booksyWorkloadPriority:
    admin: p5
    api: p5
    celeryBeat: p5
    celeryAllQueueWorker: p5
    celeryEta: p5
    grpcApi: p5
    publicApi: p5
    reportsApi: p5
    searchApi: p5
    readOnlyApi: p5
    pubsubWorkerNotificationsWebhooks: p5
    pubsubWorkerProviderCalendarImporter: p5
    tools: p5
    checkQueue: p5
    init: p1
    dbInit: p1
    elasticInit: p1
    executeScriptsInit: p1
    otherInit: p1
