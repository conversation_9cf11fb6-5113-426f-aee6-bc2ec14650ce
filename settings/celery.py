#!/usr/bin/env python
# pylint: disable=too-many-lines
import os
from datetime import (
    datetime,
    timedelta,
    timezone,
)
from itertools import chain

import pytz
from celery.schedules import crontab
from kombu import Queue, Exchange

from django.conf import settings
from django.utils.functional import classproperty

from country_config import DEFAULT_TIME_ZONES
from country_config.enums import Country
from lib.rivers import River
from lib.utils import str_to_bool


CRONTAB_SHIFT_PER_COUNTRY = {
    # <country_code>: <minutes>,
    # american server
    'us': 0,
    'ar': 6,
    'au': 8,
    'br': 10,
    'ca': 12,
    'mx': 14,
    'jp': 16,
    'my': 18,
    'ph': 20,
    'hk': 22,
    'co': 24,
    'cl': 26,
    # european server
    'pl': 0,
    'fi': 4,
    'ie': 6,
    'za': 8,
    'de': 10,
    'es': 12,
    'se': 14,
    'gb': 16,
    'nl': 18,
    'ru': 20,
    'pt': 22,
    'dk': 24,
    'fr': 26,
    'it': 28,
    've': 30,
    'ng': 32,
    # indian server
    'in': 0,
    # singapore server
    'sg': 0,
}


def queue(name):
    return Queue(name, Exchange(name, type='direct'), routing_key=name)


def shifted_crontab(hour, minute):
    """Create a crontab() object shifted by some minutes based on country.

    It is used to prevent spikes on farms with shared resources.

    """
    shift = CRONTAB_SHIFT_PER_COUNTRY.get(settings.API_COUNTRY, 0)
    minute += shift
    if minute >= 60:
        hour += minute // 60
    minute %= 60
    hour %= 24
    return crontab(hour=hour, minute=minute)


def crontab_in_timezone(hour: int, *args, target_timezone: timezone, **kwargs) -> crontab:
    """
    Returns crontab shifted for the local timezone, that will run at specific
    time in the provided `target_timezone`.

    Example: if the provided arguments are hour=10 and timezone='Europe/Warsaw'
    and the local machine stands in `US/Pacific` timezone, then the result
    crontab will be run at 1AM local time.
    """
    target_timezone_now = datetime.now(tz=target_timezone).replace(hour=hour)
    country = settings.API_COUNTRY
    local_timezone = pytz.timezone(DEFAULT_TIME_ZONES.get(country) or 'UTC')
    shifted_hour = target_timezone_now.astimezone(local_timezone).hour
    return crontab(hour=shifted_hour, *args, **kwargs)


CELERY_QUEUES_BY_PRIORITY = {
    'segment': [
        'segment',
    ],
    'index': [
        'es_index',
        'es_index_bci',
        'es_index_region',
        'es_delete_object',
    ],
    'push_notifications': [
        'push_notifications',
        'custom_push_notification',
    ],
    'high': [
        'registration_sms',
        'email',
        'email_batch',
        'payment_gateway_high',
        'otp_notifications',
    ],
    'normal': [
        'adyen',
        'celery',
        'celery_other',
        'billing',
        'booking',
        'ecommerce',
        'repeatingbooking',
        'business',
        'business_related',
        'import',
        'review',
        'rating_import',
        'purchase',
        'user',
        'scenarios_generic',
        'scenarios_instant',
        'scenarios_single',
        # an alternative to 'scenarios_single' for notifications
        # that need to be sent instantly:
        'scenarios_single_priority',
        'scenarios_beat',
        'availability',
        'photos',
        'external_apis',
        'reports',
        'pos',
        'payment_gateway',
        'payment_providers',
        'payments',
        'stripe',
        'wallet',
        'voucher',
        'marketing',
        'statistics',
        'statistics_reports',
        'google_feeds',
        'google_feeds_live_updates',
        'instagram_feeds',
        'groupon_feeds',
        'facebook_feeds',
        'admin_extra',
        'search',
        'pattern',
        'images_index_image',
        'images_index_comment',
        'images_index_like',
        'images_thumbnails',
        'images_business',
        'service_questions',
        'service_promotions',
        'stripe_app',
        'structure',
        'stripe_terminal',
        'pop_up_notification',
        'r_and_d',
        'marketplace',
        'marketplace_crons',
        'marketplace_priority',
        'boost',
        'boost_crons',
        'c2b_referral',
        'notifications_maintenance',
        'push_notifications_save_history',
        'waitlist',
        'consents',
        'migrate_to_s3',
        'market_pay',
        'invoice_indexing',
        'navision',
        'notification_signal',
        'notification_maintenance',
        'notification_async',
        'notification_event',
        'async_events',
        'lazy_events',
        'b2b_referral',
        'utt',
        'script_runner',
        'profile_completeness',
        'public_api',
        'message_blast',
        'history',
        'df_creator',
        'family_and_friends',
        'best_of_booksy',
        'versum_migration',
        'french_certification',
        'sms_notifiaction_save_history',
        'subdomains',
        'appointment_analytics',
        'fast_payouts',
        'ba_deposit',
    ],
    'low': [
        'seo_cms',
    ],
}
CELERY_QUEUES_BY_APP = {
    'celery-all-queue-worker': (
        # does all priority queues
        CELERY_QUEUES_BY_PRIORITY['high']
        + CELERY_QUEUES_BY_PRIORITY['normal']
        + CELERY_QUEUES_BY_PRIORITY['low']
        + CELERY_QUEUES_BY_PRIORITY['push_notifications']
        + CELERY_QUEUES_BY_PRIORITY['segment']
        + CELERY_QUEUES_BY_PRIORITY['index']
    ),
    'celery-priority-worker': (
        # does only high and normal priority queues
        CELERY_QUEUES_BY_PRIORITY['high']
        + CELERY_QUEUES_BY_PRIORITY['normal']
    ),
    'celery-regular-worker': (
        # does all priority queues
        CELERY_QUEUES_BY_PRIORITY['high']
        + CELERY_QUEUES_BY_PRIORITY['normal']
        + CELERY_QUEUES_BY_PRIORITY['low']
    ),
    'celery-push-worker': CELERY_QUEUES_BY_PRIORITY['push_notifications'],
    'celery-segment-worker': CELERY_QUEUES_BY_PRIORITY['segment'],
    'celery-index-worker': CELERY_QUEUES_BY_PRIORITY['index'],
}

CELERY_QUEUES = list(chain.from_iterable(CELERY_QUEUES_BY_PRIORITY.values()))

CELERY_ROUTES = {
    'drf_api.service.facebook.tasks.upload_facebook_photo_task': {
        'queue': 'user',
    },
    # region celery other
    'webapps.admin_extra.tasks.utils.debug_celery_exceptions': {
        'queue': 'celery_other',
    },
    'webapps.admin_extra.tasks.utils.debug_celery_result_task': {
        'queue': 'celery_other',
    },
    'webapps.admin_extra.tasks.utils.set_admin_permissions_task': {
        'queue': 'celery_other',
    },
    'lib.probes.celery_probes.celery_beat_health_check_task': {
        'queue': 'celery_other',
    },
    'lib.tasks.eta_scheduler.move_eta_tasks_to_queues_task': {
        'queue': 'celery_other',
    },
    'service.management.commands.generate_swagger.generate_swagger_task': {
        'queue': 'celery_other',
    },
    # endregion
    # region booking
    'webapps.booking.tasks.schedule_bulk_update_appointments_task': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.post_business_update_appointment_task': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.update_subbooking_internal_service_data_task': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.update_any_mobile_customer_appointments_task': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.bulk_update_appointments': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.set_cache_after_bulk_update_appointments': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.add_coordinates_to_traveling_appointments_task': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.regenerate_deeplinks_task': {
        'queue': 'booking',
    },
    'webapps.admin_extra.tasks.change_bookings.change_bookings_staffer_task': {
        'queue': 'booking',
    },
    'webapps.booking.experiments.compare_new_slots.tasks.compare_with_timeslot_service_slots_for_sv': {  # pylint: disable=line-too-long
        'queue': 'booking',
    },
    'webapps.booking.experiments.compare_new_slots.tasks.compare_with_timeslot_service_slots': {
        'queue': 'booking',
    },
    'webapps.booking.experiments.record_benchmark_time_slots.tasks.send_benchmark_time_slots': {
        'queue': 'booking',
    },
    'webapps.booking.v2.domains.appointment.tasks.drafts.remove_old_appointment_drafts': {
        'queue': 'booking',
    },
    'webapps.booking.tasks.update_repeating_by_parent_booking': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.check_booking_conflicts_and_send_report_task': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.replan_repeating_bookings': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.replan_all_repeating_bookings': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.split_repeating_bookings': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.split_all_repeating_bookings': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.split_new_repeating_task': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.replan_new_repeating_task': {
        'queue': 'repeatingbooking',
    },
    'webapps.booking.tasks.create_late_cancellation_notifications_task': {
        'queue': 'pop_up_notification',
    },
    'webapps.booking.tasks.'
    'short_review_pop_up_notification_delete_for_noshow_booking': {
        'queue': 'pop_up_notification',
    },
    'webapps.booking.tasks.update_bci_service_questions_task': {
        'queue': 'service_questions',
    },
    'webapps.booking.tasks.compare_with_time_slots_v2_task': {
        'queue': 'booking',
    },
    'service.booking.experiment_cx_incentives_task.send_experiment_event_if_applicable': {
        'queue': 'booking',
    },
    # endregion
    # region business
    'webapps.business.tasks.business_trial_till_blocking_task': {
        'queue': 'business',
    },
    'webapps.business.business_categories.tasks.assign_treatments_task': {
        'queue': 'business',
    },  # TODO: Move to utt queue when UTT2 will be fully operational
    'webapps.business.business_categories.tasks.assign_business_treatments_task': {
        'queue': 'business',
    },  # TODO: Move to utt queue when UTT2 will be fully operational
    'webapps.business.business_categories.tasks.assign_services_treatment_task': {
        'queue': 'business',
    },
    'webapps.business.business_categories.tasks.refresh_images_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.business_replan_repeating_bookings': {
        'queue': 'business',
    },
    'webapps.business.tasks.business_blocked_overdue_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.business_sms_notification_status_change_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.send_change_details_venue_email': {
        'queue': 'business',
    },
    'webapps.business.tasks.shift_bookings_to_timezone_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.trusted_clients_batch_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.trust_clients_by_business_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.trust_clients_by_group_business_task': {
        'queue': 'business',
    },
    'webapps.admin_extra.tasks.business_updates.business_freeze_switch_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.compute_es_weight_business_category_task': {
        'queue': 'business',
    },
    'webapps.admin_extra.tasks.business_updates.business_churn_switch_task': {
        'queue': 'business',
    },
    'webapps.purchase.tasks.churn.business_churn_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.set_sms_limit_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.cleanup_unused_tags_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.business_visible_delay_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.post_business_activate_task': {
        'queue': 'business',
    },
    'webapps.notification.tasks.retrial.retrial_reset_sms_usage_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.block_spammer_accounts': {
        'queue': 'business',
    },
    'webapps.business.tasks.business_hidden_in_search_turn_off_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.hide_test_businesses_on_marketplace_task': {
        'queue': 'business',
    },
    'webapps.business.tasks.clean_associations_deleted_variants_staffers_task': {
        'queue': 'business',
    },
    'webapps.business_related.tasks.calculate_business_accept_booksy_gift_cards_task': {
        'queue': 'business_related',
    },
    'webapps.business_related.tasks.calculate_single_business_accept_booksy_gift_cards_task': {
        'queue': 'business_related',
    },
    'webapps.business.business_categories.tasks.calculate_businesscategory_enable_booksy_gift_cards_task': {  # pylint: disable=line-too-long
        'queue': 'business',
    },
    'webapps.business.business_categories.tasks.calculate_single_businesscategory_enable_booksy_gift_cards_task': {  # pylint: disable=line-too-long
        'queue': 'business',
    },
    'webapps.business.v2.business_services.entrypoints.tasks.create_business_services_from_url_task': {  # pylint: disable=line-too-long
        'queue': 'business',
    },
    'webapps.business.v2.business_services.entrypoints.tasks.upload_screenshot_to_bucket_task': {  # pylint: disable=line-too-long
        'queue': 'business',
    },
    # endregion
    # business_related
    'webapps.business_related.tasks.send_pdf_with_safety_rules': {
        'queue': 'business_related',
    },
    'webapps.business_related.tasks.update_old_business_invitaion_deeplinks': {
        'queue': 'business_related',
    },
    'webapps.business.actions.after_registration.'
    'instantly_invite_clients_after_business_activation': {
        'queue': 'business_related',
    },
    # END business_related
    # region feedback
    'webapps.feedback.tasks.SendFeedBackFormEmail': {
        'queue': 'review',
    },
    # endregion
    # region imports
    'webapps.business.tasks.business_import_json_customers': {
        'queue': 'import',
    },
    'webapps.business.tasks.parse_and_import_customers': {
        'queue': 'import',
    },
    'webapps.business.tasks.business_import_customers_task': {
        'queue': 'import',
    },
    'webapps.business.tasks.import_customers_dry_run_with_email_report_task': {
        'queue': 'import',
    },
    'webapps.business.tasks.delete_imported_cutomers_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.versum.versum_file_parser_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.enterprise_import_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.add_photo_to_business_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.vagaro_import.vagaro_file_import_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.calendars_import.google_calendar_import_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.calendars_import.i_calendar_import_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.styleseat_import.styleseat_import_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.styleseat_import.'
    'styleseat_import_single_note_task': {
        'queue': 'import',
    },
    'webapps.warehouse.tasks.business_import_warehouse_commodities_task': {
        'queue': 'import',
    },
    'webapps.warehouse.tasks.parse_and_import_warehouse_commodities': {
        'queue': 'import',
    },
    'webapps.warehouse.tasks.import_wholesaler_commodities_task': {
        'queue': 'import',
    },
    'webapps.warehouse.tasks.import_commodities_dry_run_with_email_report_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.wholesaler.'
    'wholesaler_commodities_file_import_task': {
        'queue': 'import',
    },
    'webapps.business.tasks.extend_businesses_trial_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.batch_update_services_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.batch_update_resources_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.'
    'batch_update_business_visibility_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.'
    'batch_update_business_logos_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.'
    'batch_update_business_security_settings_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.'
    'batch_update_business_opening_hours_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.'
    'batch_update_image_bundles_task': {
        'queue': 'import',
    },
    'webapps.business.tasks.import_business_gdpr_data_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.subscription_import.subscription_import_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.subscription_import.'
    'subscription_batch_edit_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.subscription_buyer_tools.batch_update_subscription_buyers_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.subscription_buyer_tools.import_subscription_buyers_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.batch_update_about_us_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.enterprise_data.calendar_visibility_edit_task': {
        'queue': 'import',
    },
    'webapps.business.tasks.boost_switch_availability_task': {
        'queue': 'import',
    },
    'webapps.business.tasks.boost_status_change_task': {
        'queue': 'import',
    },
    'webapps.business.tasks.mass_claim_processing': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.switch_merchants_to_billing.'
    'mass_switch_merchants_to_billing_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_billing_business_offer.mass_billing_business_offer_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_billing_purchase_flow_switcher.'
    'mass_billing_purchase_flow_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_sms_price_and_limit_changer.mass_billing_sms_changer_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.old_subscription_products.mass_subscription_product_import_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_billing_merchants_switcher.'
    'mass_billing_merchants_switcher_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_business_old_fizjo_disable.'
    'mass_business_old_fizjo_disable_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_business_extend_trial_by_given_days.'
    'mass_business_extend_trial_by_given_days_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_billing_offer_purchase.mass_billing_offer_purchase_task': {
        'queue': 'import',
    },
    'webapps.best_of_booksy.tasks.create_business_booksy_awards': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_switch_merchants_payment_processor.'
    'mass_switch_merchants_payment_processor_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_offline_to_billing_switch.mass_offline_to_billing_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.mass_revert_business_from_invalid.'
    'mass_business_revert_from_invalid_status_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.trial_end_helpers.trial_end_change_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.tasks.payment_providers.import_stripe_transfer_funds.'
    'import_stripe_transfer_funds_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.views.blisting_import.import_b_listings_task': {
        'queue': 'import',
    },
    'webapps.admin_extra.views.blisting_import.setup_data_and_run_import_task': {
        'queue': 'import',
    },
    # endregion
    # region celery
    'webapps.best_of_booksy.tasks.create_best_of_booksy_certificates': {
        'queue': 'best_of_booksy',
    },
    # endregion
    # region stripe_app
    'webapps.stripe_app.tasks.event_handler_task': {
        'queue': 'stripe_app',
    },
    # endregion
    # region review
    'webapps.reviews.tasks.recompute_business_reviews_task': {
        'queue': 'review',
    },
    'webapps.reviews.tasks.post_customer_review_task': {
        'queue': 'review',
    },
    'webapps.reviews.tasks.recompute_umbrella_reviews_task': {
        'queue': 'review',
    },
    'webapps.reviews.import_reviews.import_reviews_task': {
        'queue': 'review',
    },
    'webapps.reviews.import_reviews.dry_run_with_email_report_task': {
        'queue': 'review',
    },
    'webapps.reviews.import_reviews.setup_data_and_run_import_task': {
        'queue': 'review',
    },
    # endregion
    # region user
    'webapps.user.tasks.business_customer_info.user_claim_bci_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.business_customer_info.user_reindex_bci_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.comments.reindex_user_comments': {
        'queue': 'user',
    },
    'webapps.user.tasks.business_customer_info.update_user_bci': {
        'queue': 'user',
    },
    'webapps.user.tasks.family_and_friends.update_member_profile_task': {
        'queue': 'user',
    },
    'webapps.business.tasks.match_users_task': {
        'queue': 'user',
    },
    'webapps.business.tasks.match_business_customers_task': {
        'queue': 'user',
    },
    'webapps.business.tasks.invite_customers_to_booksy': {
        'queue': 'user',
    },
    'webapps.business.tasks.invite_again_existing_customers_task': {
        'queue': 'user',
    },
    'webapps.business.tasks.quick_invite_customer_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.sync.sync_user_booksy_auth_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.sync.sync_user_booksy_auth_from_replica_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.sync.sync_user_email_in_analytics': {
        'queue': 'user',
    },
    'webapps.user.tasks.booking_score.update_users_booking_score': {
        'queue': 'user',
    },
    'webapps.user.tasks.booking_score.bulk_update_user_booking_score_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.cleanup.email_token_cleanup_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.cleanup.session_cleanup_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.apple.authorize_apple_identities_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.apple.authorize_apple_user_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.profile.bulk_update_profile_language_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.user_deletion.send_emails_about_user_deletion_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.user_deletion.send_email_with_deletion_cancellation_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.user_deletion.user_deletion_periodic_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.sync.user_imported_sync_booksy_auth_task': {
        'queue': 'user',
    },
    'webapps.user.tasks.user_deletion.remove_photos_from_buckets': {
        'queue': 'user',
    },
    # endregion
    # region family_and_friends
    'webapps.family_and_friends.tasks.member_business_customer_info.'
    'update_inactive_member_bci_task': {
        'queue': 'family_and_friends',
    },
    'webapps.family_and_friends.tasks.member_business_customer_info.update_bci_relations_task': {
        'queue': 'family_and_friends',
    },
    'webapps.family_and_friends.tasks.expired_invite_notifications.'
    'send_expired_invite_notification_task': {
        'queue': 'family_and_friends',
    },
    # endregion
    # region es_index
    'webapps.elasticsearch.tasks.document_river_task': {
        'queue': 'es_index',
    },
    'webapps.elasticsearch.tasks.document_index_task': {
        'queue': 'es_index',
    },
    'webapps.elasticsearch.tasks.document_update_task': {
        'queue': 'es_index',
    },
    'webapps.structure.tasks.region_index_task': {
        'queue': 'es_index_region',
    },
    'webapps.images.tasks.image_index_task': {
        'queue': 'es_index',
    },
    'webapps.images.tasks.update_image_order_task': {
        'queue': 'es_index',
    },
    # endregion
    # region celery_es_delete_object
    'webapps.elasticsearch.tasks.es_delete_object': {
        'queue': 'es_delete_object',
    },
    'webapps.business.tasks.business_customer_info_delete_task': {
        'queue': 'es_delete_object',
    },
    'webapps.elasticsearch.tasks.fix_out_of_sync_docs_task': {
        'queue': 'es_delete_object',
    },
    'webapps.elasticsearch.tasks.fix_out_of_sync_single_doc_type_task': {
        'queue': 'es_delete_object',
    },
    'lib.elasticsearch.tasks.delete_document_with_backoff_task': {
        'queue': 'es_delete_object',
    },
    # endregion
    # region scenarios
    'lib.email.tasks.send_email_task': {
        'queue': 'email',
    },
    'webapps.notification.tasks.set_email_notification_status': {
        'queue': 'email',
    },
    'webapps.notification.tasks.set_sms_notification_status': {
        'queue': 'scenarios_generic',
    },
    'lib.email.tasks.send_emails_task': {
        'queue': 'email_batch',
    },
    'lib.email.tasks.send_otp_email_task': {
        'queue': 'otp_notifications',
    },
    'lib.email.tasks.send_emails_batch_task': {
        'queue': 'email_batch',
    },
    'webapps.warehouse.tasks.send_email_to_supplier_task': {
        'queue': 'email',
    },
    'webapps.user.tasks.gdpr.send_disclosure_obligation_agreement_email_task': {
        'queue': 'scenarios_generic',
    },
    'webapps.notification.tasks.instant_notification_schedule_task': {
        'queue': 'scenarios_instant',
    },
    'webapps.notification.tasks.bulk_sms_send_task': {
        'queue': 'scenarios_generic',
    },
    'webapps.notification.tasks.single_notification_schedule_task': {
        'queue': 'scenarios_single',
    },
    'webapps.notification.tasks.process_notification_schedules_task_with_cache': {
        'queue': 'scenarios_beat',
    },
    'webapps.notification.tasks.notifications_cleanup_task': {
        'queue': 'scenarios_beat',
    },
    'webapps.notification.tasks.custom.reminder_hour_old_values_cleanup': {
        'queue': 'scenarios_beat',
    },
    'webapps.notification.tasks.smscodes_cleanup_task': {
        'queue': 'scenarios_beat',
    },
    'webapps.notification.tasks.email_codes_cleanup_task': {
        'queue': 'scenarios_beat',
    },
    'webapps.notification.tasks.retrieve_stuck_started_notification_schedules_task': {
        'queue': 'scenarios_beat',
    },
    'webapps.notification.tasks.retrieve_stuck_received_notification_schedules_task': {
        'queue': 'scenarios_beat',
    },
    # endregion
    # region TWILIO NOTIFICATIONS MAINTENANCE
    'webapps.notification.tasks.replace_banned_twilio_number': {
        'queue': 'notifications_maintenance',
    },
    'webapps.notification.tasks.twilio_resend_registration_code': {
        'queue': 'notifications_maintenance',
    },
    'webapps.notification.tasks.async_send_notification_task': {
        'queue': 'notification_async',
    },
    # endregion
    # region availability
    'celery.starmap': {  # TODO check if starmap is used https://redmine.booksy.pm/issues/86537
        # celery.starmap is generated by Task.chunks().delay()
        'queue': 'availability',
    },
    'webapps.elasticsearch.tasks.BusinessAvailabilityRiverTask': {
        'queue': 'availability',
    },
    'webapps.elasticsearch.tasks.CleanOldBusinessAvailabilityTask': {
        'queue': 'availability',
    },
    'webapps.elasticsearch.tasks.all_business_availability_task': {
        'queue': 'availability',
    },
    'webapps.elasticsearch.tasks.update_business_availability_task': {
        'queue': 'availability',
    },
    'webapps.elasticsearch.tasks.bulk_update_business_availability_task': {
        'queue': 'availability',
    },
    # endregion
    # region pos
    'webapps.pos.tasks.ReleaseDepositOnPayment': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.ReleaseDepositOnCancel': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.CancelBookingOnDepositFail': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.POSActivityChange': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.CloseAllCashRegisters': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.ReleaseAllDepositOnServicesAndBookings': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.CancelAllTransactionsOlderThan30days': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.SendReceiptToCustomer': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.ClosePrepaymentTransactionAtTime': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.CloseBooksyPayTransactionAtTime': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.ChargeCancellationFeeTransactions': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.FetchAutoChargePOSes': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.SwitchToFraud': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.CheckoutPrepaidTransaction': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.auto_refund_transaction': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.CheckoutBooksyPayTransaction': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.DisablePrepayments': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.recalculate_pos_plans': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.recalculate_pos_plans_for_payment_type': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.cancel_cfp_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.execute_pending_refunds_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.batch_change_default_payment_method_to_pba_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.initialize_basket_payments': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.remove_square_payment_method_for_selected_pos_ids': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.batch_change_pos_plans_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.autopay_for_business_prepayment_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.disable_square_integration_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.reissue_gift_card_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.handle_no_show_booksy_gift_card_task': {
        'queue': 'pos',
    },
    'webapps.pos.actions.basket_payment_details_updated_event_handler_task': {
        'queue': 'pos',
    },
    # endregion
    # region payment_gateway
    'webapps.payment_gateway.tasks.initialize_fee_task': {
        'queue': 'payment_gateway',
    },
    'webapps.payment_gateway.tasks.update_payment_fee_amount_task': {
        'queue': 'payment_gateway',
    },
    'webapps.payment_gateway.tasks.synchronize_booksy_wallet_balance_transactions_task': {
        'queue': 'payment_gateway',
    },
    # endregion
    # region wallet
    'webapps.user.tasks.wallet.update_customer_wallet_task': {
        'queue': 'wallet',
    },
    'webapps.business.tasks.update_business_wallet_task': {
        'queue': 'wallet',
    },
    # endregion
    # region payment_gateway_high
    'webapps.user.tasks.wallet.get_or_create_customer_wallet_task': {
        'queue': 'payment_gateway_high',
    },
    # endregion
    # region stripe
    'webapps.stripe_integration.tasks.charge_refund_fee_task': {
        'queue': 'stripe',
    },
    'webapps.stripe_integration.tasks.reset_stripe_tip_settings_task': {
        'queue': 'stripe',
    },
    'webapps.stripe_integration.tasks.charge_fast_payout_paid_fee_task': {
        'queue': 'stripe',
    },
    'webapps.stripe_integration.tasks.trigger_fast_payout_paid_fees_task': {
        'queue': 'stripe',
    },
    'webapps.stripe_integration.tasks.synchronize_stripe_payout_task': {
        'queue': 'stripe',  # it's a POS refactor sync, do not confuse with tasks above
    },
    'webapps.business_consents.tasks.consent_stripe_account_restricted_soon_turn_back_visibility': {
        'queue': 'stripe',
    },
    # endregion
    # <editor-fold desc="public_partners">
    'webapps.public_partners.tasks.notification.notify_client_task': {
        'queue': 'public_api',
    },
    'webapps.public_partners.tasks.notification.notify_client_cmd_task': {
        'queue': 'public_api',
    },
    'webapps.public_partners.tasks.notification.notify_business_task': {
        'queue': 'public_api',
    },
    'webapps.public_partners.tasks.oauth2.clear_oauth2_tokens_task': {
        'queue': 'public_api',
    },
    'webapps.public_partners.tasks.webhook.send_signal_public_api_task': {
        'queue': 'public_api',
    },
    'webapps.public_partners.tasks.webhook.schedule_webhooks_task': {
        'queue': 'public_api',
    },
    'webapps.public_partners.tasks.webhook.deliver_webhook_task': {
        'queue': 'public_api',
    },
    'webapps.public_partners.tasks.partner_data.create_business_partner_data_task': {
        'queue': 'import',
    },
    # </editor-fold>
    # region payments
    'webapps.voucher.tasks.update_voucher_status_task': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.log_voucher_changes_task': {
        'queue': 'pos',
    },
    'webapps.pos.tasks.log_transaction_commission_changes_task': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.migrate_existing_voucher_templates_to_all_services_task': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.migrate_vouchers_templates_to_same_value_and_price': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.migrate_single_business_vouchers_templates_to_same_value_and_price': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.migrate_existing_voucher_templates_valid_till_values_task': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.fill_sold_vouchers_before_migration_threshold_task': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.fix_invalid_voucher_templates_services_task': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.migrate_existing_voucher_templates_valid_till_to_never_task': {
        'queue': 'pos',
    },
    'webapps.voucher.tasks.remove_unused_redundant_voucher_templates_task': {
        'queue': 'pos',
    },
    # endregion
    # region payments
    'webapps.purchase.tasks.DeletePurchaseRequestsTask': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.apple.apple_subscriptions_check_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.offline.switch_agreement_type_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.offline.offline_subscriptions_sync_status_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.brain_tree.BraintreeFetchPlansTask': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.churn.'
    'cancel_subscriptions_before_churn_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.brain_tree.reassign_payment_methods_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.InvoiceMassSend': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.InvoiceCreate': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.google.acknowledge_google_play_purchase_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.google.RestoreExpiryNoneSubscription': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.renewing_subscription.unify_renewing_subscription_status_paid': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.renewing_subscription.renew_one_business': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.offline.import_offline_transactions_task': {
        'queue': 'purchase',
    },
    'lib.celery_utils.debug_tasks.rdb_index_task': {
        'queue': 'es_index',
    },
    'lib.celery_utils.debug_tasks.rdb_high_task': {
        'queue': 'email',
    },
    'webapps.purchase.tasks.renewing_subscription.'
    'unify_renewing_subscription_status_overdue_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.renewing_subscription.'
    'unify_renewing_subscription_non_churned_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.renewing_subscription.'
    'unify_renewing_subscription_status_mismatched_task': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.webhook.ProcessSingleSubscription': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.reports.send_double_subscriptions_report': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.brain_tree.delete_old_card_verification_attempts': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.churn.clean_cancellation_reasons': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.brain_tree.refresh_braintree_subscriptions': {
        'queue': 'purchase',
    },
    'webapps.purchase.tasks.apple_google_subs_migration.migrate_apple_google_subscriptions_task': {
        'queue': 'navision',
    },
    'webapps.purchase.tasks.apple_google_subs_migration.send_apple_google_subscriptions_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.invoice_details_business_settings.invoice_details_confirmed_task': {
        'queue': 'navision',
    },
    'webapps.billing.tasks.auto_switch_payment_processor_for_inactive_businesses_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.switch_cycle_single_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.switch_billing_cycles_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.auto_churn': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.requested_churn': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.pending_requested_churn': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.auto_retry_charges': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.close_subscriptions': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.delete_stripe_payment_method_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.mismatched_report_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.update_not_refreshed_businesses_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.purchase_subscription_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.retry_charge_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.add_or_update_billing_info_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.setup_stripe_payment_method_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.finalize_stripe_payment_method_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.cancel_stripe_payment_intent_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.cancel_abandoned_purchase_request_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.cancel_abandoned_retry_charge_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.create_one_off_transaction_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.compute_business_status_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.migrate_stripe_payment_methods_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.billing_refund_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.initialize_purchase_subscription_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.finalize_purchase_subscription_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.initialize_retry_charge_subscription_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.finalize_retry_charge_subscription_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.renew_long_subscription_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.batch_boost_overdue_charge_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.finish_batch_boost_overdue_charge_task': {
        'queue': 'billing',
    },
    'webapps.billing.tasks.migrated_subscription_initial_task': {
        'queue': 'billing',
    },
    'webapps.admin_extra.tasks.stripe_migration_tool.'
    'mass_stripe_migration_tool': {
        'queue': 'billing',
    },
    # endregion
    # region marketing
    'webapps.marketing.tasks.clean_sms_invitation_events_task': {
        'queue': 'marketing',
    },
    # endregion
    # region statistics
    'webapps.statistics.tasks.aggregate_statistics_river_task': {
        'queue': 'statistics',
    },
    'webapps.statistics.tasks.aggregate_statistics_batch_task': {
        'queue': 'statistics',
    },
    'webapps.statistics.tasks.aggregate_statistics_daily_task': {
        'queue': 'statistics',
    },
    'webapps.statistics.tasks.aggregate_statistics_single_task': {
        'queue': 'statistics',
    },
    'webapps.statistics.tasks.generate_statistics_report': {
        'queue': 'statistics_reports',
    },
    'webapps.statistics.tasks.generate_summary_report': {
        'queue': 'statistics_reports',
    },
    'webapps.purchase.tasks.mrr_reports.calculate_mmr_reports': {
        'queue': 'statistics_reports',
    },
    'webapps.stats_and_reports.tasks.send_report_by_email_task': {
        'queue': 'statistics_reports',
    },
    # endregion
    # region google_feeds
    'webapps.feeds.google.tasks.notify_conversion_task': {
        'queue': 'google_feeds_live_updates',
    },
    # endregion
    # region groupon_feeds
    'webapps.feeds.groupon.tasks.update_redemption_status_task': {
        'queue': 'groupon_feeds',
    },
    # endregion
    # region facebook_feeds
    'webapps.feeds.facebook.tasks.update_fb_services_task': {
        'queue': 'facebook_feeds',
    },
    'webapps.feeds.facebook.tasks.disconnect_business_from_fbe_task': {
        'queue': 'facebook_feeds',
    },
    # endregion
    # region admin_extra other
    'webapps.admin_extra.tasks.appsflyer.appsflyer_shortcut_link_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.appsflyer.'
    'appsflyer_shortcut_generic_link_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.send_sms.sms_send_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.send_sms.sms_send_tasks': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.versum.import_data_versum_task': {
        'queue': 'admin_extra',
    },
    'webapps.user.tasks.gdpr.gdpr_customer_data_export_task': {
        'queue': 'admin_extra',
    },
    'webapps.business.tasks.gdpr_business_data_export_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.images_import.images_import_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.zip_codes_import.zip_codes_areas_import_task': {
        'queue': 'admin_extra',
    },
    'webapps.experiment_v3.tasks.update_old_experiment_slots_with_experiment': {
        'queue': 'admin_extra',
    },
    'webapps.experiment_v3.tasks.clear_experiment_activity_status_cache': {
        'queue': 'admin_extra',
    },
    'webapps.experiment_v3.tasks.check_experiment_watchers': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.reports.report_inappropriate_content_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.reports.report_inappropriate_content_dsa_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.utils.set_query_count_in_cache': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.business_updates.update_padding_time_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.mass_billing_business_discounts'
    '.mass_billing_business_discounts_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.mass_business_retrial_switch.mass_business_retrial_switch_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.mass_billing_offers_changer.mass_billing_offers_changer_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_offline_invoicing.boost_offline_dry_invoice_businesses_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_offline_invoicing.boost_offline_dry_invoicing_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_offline_invoicing.saas_offline_dry_invoice_subscriptions_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_offline_invoicing.saas_offline_dry_invoicing_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_offline_invoicing.dry_invoicing_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_booksy_billing_invoices_report'
    '.dry_booksy_billing_invoices_report_task': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_booksy_billing_invoices_report.process_one_day_items': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.dry_booksy_billing_invoices_report.prepare_email': {
        'queue': 'admin_extra',
    },
    'webapps.admin_extra.tasks.mail_pdf_sender.send_emails_with_pdf_task': {
        'queue': 'admin_extra',
    },
    'webapps.business.tasks.change_bci_web_consents_to_false_task': {
        'queue': 'admin_extra',
    },
    # endregion
    # region search_engine_tuning
    'webapps.search_engine_tuning.tasks.update_business_tuning_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_businesses_tuning_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_selected_businesses_tuning_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_availability_from_es': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_availability_bulk_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_users_tuning_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_users_tuning_fast_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_user_tuning_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.'
    'tasks.update_business_category_tunings_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.'
    'tasks.update_category_tunings_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_addon_tunings_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_commodity_tunings_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.update_service_tunings_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.'
    'update_service_variant_tunings_params_task': {
        'queue': 'search',
    },
    'webapps.search_engine_tuning.tasks.'
    'update_business_customer_tunings_params_task': {
        'queue': 'search',
    },
    # endregion
    # region segment
    'webapps.segment.tasks.merchant_activity_bulk': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.merchant_activity_weekly_report_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_api_appointment_booked_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_api_customer_invitation_sent': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_api_subscription_reactivated': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_api_subscription_expired': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_api_overdue_status_started': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_status_change_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_intercom_status_update': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.segment_intercom_status_update_businesses': {
        'queue': 'segment',
    },
    'webapps.purchase.tasks.segment.SegmentStatusChange': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_registration_started_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_registration_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_customer_registration_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_customer_search_query_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_location_entered_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_info_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_created_for_customer_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.send_analytics_energy_cb_created_for_customer_to_facebook': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.send_analytics_1st_cb_created_for_business_to_facebook': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_subscription_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_import_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.save_used_app_version_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.save_100_used_app_version_records_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_customer_app_opened_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_kyc_failure_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_app_opened_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_finished_for_business_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_finished_for_customer_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_gtm_onboarding_business_go_live_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_onboarding_business_go_live_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_created_for_business_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_created_count_in_days_for_business_segment_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_created_count_for_business_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_energy_cb_created_for_customer_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_1st_paid_status_achieved_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_boost_on_off_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_boost_on_off_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_boost_on_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_boost_off_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_categories_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_customer_info_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_contact_preferences_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_contact_preferences_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_staffer_created_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_user_language_set': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_customer_user_language_set': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_pos_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_1st_paid_status_achieved': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_1st_paid_status_achieved_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_onboarding_delay_set_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_kyc_success_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_adyen_kyc_success_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_terminal_ordered_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_review_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_protection_service_enabled_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_basket_payment_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_payment_transaction_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_status_updated_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_continuous_discovery_business_created_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_pba_enabled_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_1st_no_show_for_business_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bb_no_show_for_business_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_no_show_for_business_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_cb_started_for_customer': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_invite_all_clicked_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_invite_process_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_invite_process_completed_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_postponed_invites_queued_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_postponed_invites_sent': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_quick_invite_sent_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_report_generated_to_email': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.create_delayed_gtm_event_auth_data_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.forget_email_gdpr': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_view_item_list_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_pending_account_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_verified_account_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_not_verified_account_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_not_verified_account_segment_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_pending_account_segment_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_verified_account_segment_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_terminal_ordered_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_terminal_ordered_segment_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_order_received_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_order_received_segment_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_reset_account_verify_gtm_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_bcr_reset_account_verify_segment_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_checkout_transaction_completed_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_re_trial_eligible_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_special_offer_create_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_business_offline_migration_started_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_subscription_payment_succeeded_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_paid_status_achieved_branchio_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_invite_process_completed_branchio_task': {
        'queue': 'segment',
    },
    'lib.facebook.task.business_facebook_conversion_event_task': {
        'queue': 'segment',
    },
    'webapps.segment.tasks.analytics_churn_reason_updated_task': {
        'queue': 'segment',
    },
    # endregion
    # region stripe_terminal
    'webapps.stripe_terminal.tasks.update_order_status_task': {
        'queue': 'stripe_terminal',
    },
    'webapps.stripe_terminal.tasks.update_order_payment_status_task': {
        'queue': 'stripe_terminal',
    },
    'webapps.stripe_terminal.tasks.fetch_hardware_orders_shipment_tracking': {
        'queue': 'stripe_terminal',
    },
    'webapps.stripe_terminal.tasks.update_order_history_task': {
        'queue': 'stripe_terminal',
    },
    'webapps.stripe_terminal.tasks.update_payment_intent_device_type_task': {
        'queue': 'stripe_terminal',
    },
    # endregion
    # region structure
    'webapps.structure.tasks.map_regions_to_categories_task': {
        'queue': 'structure',
    },
    # endregion
    # region images
    'webapps.images.tasks.migrate_photo_to_s3': {
        'queue': 'migrate_to_s3',
    },
    'webapps.images.tasks.publish_photo_to_portfolio_task': {
        'queue': 'images_business',
    },
    # endregion
    # region seo cms
    'webapps.marketplace.cms.tasks.refresh_region_category_task': {
        'queue': 'seo_cms',
    },
    'webapps.marketplace.cms.tasks.refresh_region_homepage_task': {
        'queue': 'seo_cms',
    },
    # endregion seo cms
    # region marketplace
    'webapps.marketplace.tasks.pay_marketplace_transaction_task': {
        'queue': 'marketplace_priority',
    },
    'webapps.marketplace.tasks.pay_marketplace_chunk_transaction_task': {
        'queue': 'marketplace_priority',
    },
    'webapps.marketplace.tasks.retry_marketplace_transaction_task': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.retry_marketplace_chunk_transaction': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.retry_boost_claims_task': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.refresh_boost_braintree_processing_transactions_task': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.recalculate_commissions_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.push_waiting_businesses_task': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.resend_push_waiting_businesses_task': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.update_transacion_row_amount': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.set_chargeable_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.boost_celebration_moments_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.import_b_listings_from_lead_db': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.make_sure_merchant_can_pay_for_boost_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.update_martketplace_transaction_log_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.prepare_boost_trial_notification': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.mass_braintree_find_task': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.find_merchant_in_braintree_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.set_claimed_multiple_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.boost_pay_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.boost_refund_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.set_has_braintree_task': {
        'queue': 'marketplace',
    },
    'webapps.marketplace.tasks.mass_set_chargeable_payable_task': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.mass_check_chargeable': {
        'queue': 'marketplace_crons',
    },
    'webapps.marketplace.tasks.rerun_not_enabled_boost': {
        'queue': 'marketplace_crons',
    },
    'webapps.business.tasks.boost_set_switch_date_task': {
        'queue': 'marketplace',
    },
    'webapps.business.tasks.boost_status_change_with_date_task': {
        'queue': 'marketplace_crons',
    },
    # endregion
    # region boost
    'webapps.boost.tasks.boost_payment_source_change_task': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.mass_commission_change_task': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.mass_commission_close_task': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.mass_revert_or_delete_boost_transactions_task': {
        'queue': 'boost_crons',
    },
    'webapps.boost.tasks.set_claim_status_task': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.mark_old_clients_as_exempted': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.apply_boost_bans': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.end_boost_bans_for_today': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.end_boost_bans': {
        'queue': 'boost',
    },
    'webapps.boost.tasks.update_boost_ban_sms_status_task': {
        'queue': 'boost',
    },
    # endregion
    # region profile completeness
    'webapps.profile_completeness.tasks.update_profile_completeness_stats_task': {
        'queue': 'profile_completeness',
    },
    # endregion
    # region r_and_d tasks
    'webapps.r_and_d.tasks.calculate_median_of_businesses': {
        'queue': 'r_and_d',
    },
    'webapps.r_and_d.tasks.churned_merchant_user_reactivation': {
        'queue': 'r_and_d',
    },
    'webapps.r_and_d.tasks.start_scenario_for_found_businesses': {
        'queue': 'r_and_d',
    },
    'webapps.r_and_d.tasks.high_volume_start_scenario': {
        'queue': 'r_and_d',
    },
    'webapps.r_and_d.tasks.follow_up_start_scenario': {
        'queue': 'r_and_d',
    },
    # TODO: Refactor/remove with DG-342
    # 'webapps.r_and_d.tasks.propagating_big_query_to_cloud_storage': {
    #     'queue': 'r_and_d',
    # },
    # 'webapps.r_and_d.tasks.extract_country_cloud_storage_data': {
    #     'queue': 'r_and_d',
    # },
    # endregion
    # region booking_reactivation
    'webapps.r_and_d.tasks.check_booking_reactivation': {
        'queue': 'r_and_d',
    },
    'webapps.r_and_d.tasks.reactivate_booking_sms_task': {
        'queue': 'r_and_d',
    },
    'lib.tasks.new_yaml_tasks.task_test_new_celery_type': {
        'queue': 'r_and_d',
    },
    # endregion
    # region pop up
    'webapps.pop_up_notification.tasks.pop_up_cleanup_task': {
        'queue': 'r_and_d',
    },
    'webapps.pop_up_notification.tasks.create_what_is_new_family_and_friends_popup_for_all_users': {
        'queue': 'pop_up_notification',
    },
    # endregion
    # region C2B Referral
    'webapps.c2b_referral.tasks.send_mail_to_cs_task': {
        'queue': 'c2b_referral',
    },
    'webapps.c2b_referral.tasks.change_reward_status_task': {
        'queue': 'c2b_referral',
    },
    'webapps.c2b_referral.tasks.handle_c2b_reward': {
        'queue': 'c2b_referral',
    },
    # endregion
    # region IO BOUND PUSH `NOTIFICATIONS
    'webapps.notification.tasks.send_iterable_push_sent_notification': {
        'queue': 'push_notifications',
    },
    'webapps.notification.tasks.push.fcm_android_push_task': {
        'queue': 'push_notifications',
    },
    'webapps.notification.tasks.push.apns_push_task': {
        'queue': 'push_notifications',
    },
    'webapps.notification.tasks.push._save_history_task': {
        'queue': 'push_notifications_save_history',
    },
    'webapps.notification.tasks.push.retry_send_push_notification_task': {
        'queue': 'push_notifications',
    },
    'webapps.notification.tasks.push.delete_expired_push_tokens_task': {
        'queue': 'push_notifications',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'query_and_send_customer_push_task': {
        'queue': 'custom_push_notification',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'send_mass_customer_push_task': {
        'queue': 'custom_push_notification',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'send_test_custom_push_email_notification': {
        'queue': 'custom_push_notification',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'send_notifications_packet': {
        'queue': 'custom_push_notification',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'send_notifications_chunk': {
        'queue': 'custom_push_notification',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'create_notification_history': {
        'queue': 'custom_push_notification',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'push_sender_task': {
        'queue': 'custom_push_notification',
    },
    'webapps.admin_extra.tasks.push_and_notification.'
    'send_manual_admin_notification': {
        'queue': 'custom_push_notification',
    },
    'webapps.wait_list.tasks.waitlist_scenario_task': {
        'queue': 'waitlist',
    },
    'webapps.wait_list.tasks.event_query_task': {
        'queue': 'waitlist',
    },
    'webapps.wait_list.tasks.match_waitlist_to_booking': {
        'queue': 'waitlist',
    },
    # endregion
    # region SERVICE PROMOTIONS
    'webapps.business.tasks.disable_expired_flash_sale_promotions_task': {
        'queue': 'service_promotions',
    },
    'webapps.business.tasks.last_minute_incentive_task': {
        'queue': 'service_promotions',
    },
    'webapps.business.tasks.happy_hours_incentive_task': {
        'queue': 'service_promotions',
    },
    'webapps.business.tasks.send_happy_hours_incentive_task': {
        'queue': 'service_promotions',
    },
    # endregion
    # region MARKET PAY
    'webapps.market_pay.tasks.update_account_holder_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.transfer_funds_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.transfer_fund_and_payout_account_holder': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.send_welcome_email_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.marketplace_accounting_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.create_payouts_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.signal_payouts_ids_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.handle_marketpay_payout_report_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.create_payouts_and_signal_them_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.send_standalone_manual_payout_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.send_manual_reward_payout_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.update_signatory_document': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.upload_adyen_document_task': {
        'queue': 'market_pay',
    },
    'webapps.market_pay.tasks.restore_payout_data_task': {
        'queue': 'market_pay',
    },
    # endregion
    # region PAYMENT PROVIDERS
    'webapps.payment_providers.tasks.capture_payment_task': {
        'queue': 'payment_providers',
    },
    'webapps.payment_providers.tasks.mark_cards_invalid_because_of_payment_history': {
        'queue': 'payment_providers',
    },
    'webapps.payment_providers.tasks.check_and_update_card_expiry_status_task': {
        'queue': 'payment_providers',
    },
    'webapps.payment_providers.tasks.check_tokenized_payment_method_menu_warnings_for_customer': {
        'queue': 'payment_providers',
    },
    # endregion
    # region PAYMENTS
    'webapps.payments.tasks.send_basket_payment_details_via_email': {
        'queue': 'payments',
    },
    # endregion
    # region S3 INVOICES INDEXING
    'webapps.invoice.tasks.find_invoice_objects_to_index_task': {
        'queue': 'invoice_indexing',
    },
    'webapps.invoice.tasks.filter_objects_to_index_task': {
        'queue': 'invoice_indexing',
    },
    'webapps.invoice.tasks.index_invoice_objects_task': {
        'queue': 'invoice_indexing',
    },
    'webapps.invoice.tasks.save_invoice_object_task': {
        'queue': 'invoice_indexing',
    },
    # endregion
    # region CONSENTS
    'webapps.consents.tasks.update_appointment_consents_task': {
        'queue': 'consents',
    },
    'webapps.consents.tasks.create_consents_task': {
        'queue': 'consents',
    },
    'webapps.consents.tasks.dismiss_consents_task': {
        'queue': 'consents',
    },
    'webapps.consents.tasks.render_consent_pdf': {
        'queue': 'consents',
    },
    'webapps.consents.tasks.send_consent_business_email_task': {
        'queue': 'consents',
    },
    'webapps.consents.tasks.send_consent_customer_email_task': {
        'queue': 'consents',
    },
    # endregion
    # region Events
    'webapps.celery.tasks.async_signal_task_for_receiver': {
        'queue': 'async_events',
    },
    'webapps.celery.tasks.lazy_signal_task_for_receiver': {
        'queue': 'lazy_events',
    },
    'webapps.celery.tasks.periodic_event_task': {
        'queue': 'lazy_events',
    },
    # endregion
    # region B2B REFERRAL
    'webapps.b2b_referral.tasks.create_invite_notification_for_setting_task': {
        'queue': 'b2b_referral',
    },
    'webapps.b2b_referral.tasks.create_reward_notification_task': {
        'queue': 'b2b_referral',
    },
    'webapps.b2b_referral.tasks.create_signup_notification_task': {
        'queue': 'b2b_referral',
    },
    'webapps.b2b_referral.tasks.create_dyk_notification_task': {
        'queue': 'b2b_referral',
    },
    'webapps.b2b_referral.tasks.create_finish_invited_notification_task': {
        'queue': 'b2b_referral',
    },
    'webapps.b2b_referral.tasks.create_finish_referrer_notification_task': {
        'queue': 'b2b_referral',
    },
    # endregion
    # region UTT
    'webapps.utt.tasks.calculate_predictions_confirmation_priority': {
        'queue': 'utt',
    },
    'webapps.utt.tasks.update_predictions_confirmation_priority': {
        'queue': 'utt',
    },
    # endregion
    'webapps.adyen.tasks.capture_authorized_transaction_task': {
        'queue': 'adyen',
    },
    'webapps.adyen.tasks.handle_report_task': {
        'queue': 'adyen',
    },
    'webapps.adyen.tasks.bulk_transfer_funds': {
        'queue': 'adyen',
    },
    'webapps.adyen.tasks.cancel_outdated_3ds_bookings': {
        'queue': 'adyen',
    },
    'webapps.zoom.tasks.create_missing_zoom_meetings_task': {
        'queue': 'external_apis',
    },
    # region SCRIPT_RUNNER
    'webapps.booking.tasks.add_reminder_notification_task': {
        'queue': 'script_runner',
    },
    'webapps.notification.tasks.add_receiver_filter_to_notification_index_mapping': {
        'queue': 'script_runner',
    },
    'webapps.stripe_integration.tasks.sync_account_standard_payouts': {
        'queue': 'script_runner',
    },
    # endregion
    # region MESSAGE_BLAST
    'webapps.message_blast.tasks.process_message_blast_templates_task': {
        'queue': 'message_blast',
    },
    'webapps.message_blast.tasks.process_message_blast_schedules_task': {
        'queue': 'message_blast',
    },
    'webapps.message_blast.tasks.reset_to_default_message_blast_templates_task': {
        'queue': 'message_blast',
    },
    'webapps.message_blast.tasks.process_message_blast_templates_batch_task': {
        'queue': 'message_blast',
    },
    'webapps.message_blast.tasks.welcome_new_client_river_task': {
        'queue': 'message_blast',
    },
    'webapps.message_blast.tasks.send_message_blast_lazy': {
        'queue': 'message_blast',
    },
    'webapps.message_blast.tasks.check_msg_for_spam': {
        'queue': 'message_blast',
    },
    'webapps.notification.tasks.sms_history.save_sms_history_task': {
        'queue': 'sms_notifiaction_save_history',
    },
    # endregion
    # <editor-fold desc="sms">
    'webapps.notification.tasks.sms_codes.send_sms_registration_code_task': {
        'queue': 'registration_sms',
    },
    # </editor-fold>
    # region Navision
    'webapps.navision.tasks.api.create_invoice_in_navision': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.api.create_not_synced_merchant_in_navision': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.api.create_or_update_merchant_in_navision': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.api.send_not_synced_invoices_to_navision': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.api.sync_invoices_with_errors_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.api.sync_changed_invoice_items_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.subscription_buyer.update_buyer_with_business_data_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.subscription_buyer.create_buyers_based_on_data_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.subscription_buyer.update_buyers_based_on_data_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.subscription_buyer.verify_active_providers_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.subscription_buyer.fill_subscription_buyer_data_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.subscription_buyer.update_entity_type_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.base.schedule_invoicing_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoices_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.saas_online_based_on_billing_cycle.'
    'create_saas_online_invoice_based_on_billing_cycle_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.saas_online_based_on_billing_cycle.'
    'create_saas_online_invoices_based_on_billing_cycle_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.saas_online_based_on_billing_cycle.'
    'update_invoice_item_as_charged_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.api.send_to_new_sandbox_test_invoices_bc_start_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.boost_online.create_boost_online_invoice_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.boost_online.create_boost_online_invoices_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.errors.process_error_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.errors.process_online_invoice_error_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.errors.process_offline_invoice_error_task': {
        'queue': 'navision',
    },
    # pylint: disable='line-too-long'
    'webapps.navision.tasks.invoicing_summaries.approve_invoices_in_selected_invoice_summary_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.invoicing_summaries.remove_test_invoices_from_invoicing_summary_task': {
        'queue': 'navision'
    },
    'webapps.navision.tasks.invoicing_summaries.remove_unapproved_invoices_from_summaries_task': {
        'queue': 'navision'
    },
    'webapps.navision.tasks.offline.create_offline_batch_boost_invoice_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_batch_saas_invoice_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_boost_invoice_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_boost_invoices_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_boost_invoice_for_migration_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_boost_invoices_for_migration_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_saas_invoice_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_saas_invoices_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_sms_invoice_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.offline.create_offline_sms_invoices_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.tax_rates.fetch_tax_rate_for_zipcode_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.tax_rates.update_tax_rates_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.tax_rates.force_update_tax_rate': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.tax_rates.update_tax_rate_per_state_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.tax_rates.fill_tax_rate_table_from_business_zipcode_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.buyer_merchant_integration.sync_subscription_buyers_with_merchant_task': {
        'queue': 'navision',
    },
    'webapps.navision.tasks.buyer_merchant_integration.sync_buyer_with_merchant_task': {
        'queue': 'navision',
    },
    # endregion
    # region HISTORY
    'lib.history_model.tasks.save_with_history': {
        'queue': 'history',
    },
    'lib.history_model.tasks.update_with_history': {
        'queue': 'history',
    },
    'lib.history_model.tasks.add_to_history': {
        'queue': 'history',
    },
    'lib.es_history.tasks.index_history_records_task': {
        'queue': 'history',
    },
    # endregion
    # region DF_CREATOR
    'webapps.df_creator.tasks.push_digital_flyer_availability': {
        'queue': 'df_creator',
    },
    # endregion
    # region VERSUM_MIGRATION
    'webapps.versum_migration.tasks.versum_migration_customer_registration_completed_task': {
        'queue': 'versum_migration',
    },
    'webapps.versum_migration.tasks.schedule_appointments_sync': {
        'queue': 'versum_migration',
    },
    'webapps.versum_migration.tasks.fill_versum_subscription_information_task': {
        'queue': 'versum_migration',
    },
    # endregion
    # region FRENCH_CERTIFICATION
    'webapps.french_certification.tasks.close_periods_for_businesses_task': {
        'queue': 'french_certification',
    },
    'webapps.french_certification.tasks.batch_close_periods_for_businesses_task': {
        'queue': 'french_certification',
    },
    # endregion
    'webapps.google_sign_in.tasks.upload_google_photo_task': {
        'queue': 'user',
    },
    'webapps.subdomain_grpc.tasks.create_subdomains_regenerate_deeplinks_task': {
        'queue': 'subdomains',
    },
    'webapps.booking.tasks.appointment_analytics_task': {'queue': 'appointment_analytics'},
    'webapps.booking.v2.domains.analytics.outer.tasks.send_draft_created_task': {
        'queue': 'appointment_analytics'
    },
    'webapps.ecommerce.tasks.send_notification_campaign': {'queue': 'ecommerce'},
    # region fast_payouts
    'service.auto_enable_fast_payouts.tasks.try_enable_fast_payouts_in_batches_task': {
        'queue': 'fast_payouts',
    },
    'service.auto_enable_fast_payouts.tasks.try_enable_fast_payouts_task': {
        'queue': 'fast_payouts',
    },
    # endregion
    # region ba_deposit
    'service.auto_enable_ba_deposit.tasks.try_enable_ba_deposit_in_batches_task': {
        'queue': 'ba_deposit',
    },
    'service.auto_enable_ba_deposit.tasks.try_enable_ba_deposit_task': {
        'queue': 'ba_deposit',
    },
    'service.auto_enable_ba_deposit.tasks.try_enable_kip_payment_method_task': {
        'queue': 'ba_deposit',
    },
    # endregion
}

CELERY_BEAT_SCHEDULE = {
    # 'ecommerce-send-notification-capmaign-task': {  # ECOMM-588 ver.2
    #     'task': 'webapps.ecommerce.tasks.send_notification_campaign',
    #     'schedule': timedelta(hours=1),
    #     'options': {
    #         'expires': 30 * 60,
    #     },
    # },
    # region BUSINESS
    'business-sms-notification-status': {
        'task': 'webapps.business.tasks.business_sms_notification_status_change_task',
        'schedule': crontab(hour='*/8', minute=47),
        'options': {
            'expires': 8 * 60 * 60 - 1,
        },
    },
    'trust-clients': {
        'task': 'webapps.business.tasks.trusted_clients_batch_task',
        'schedule': shifted_crontab(hour=0, minute=30),
    },
    'assign-services-treatment-task': {
        'task': 'webapps.business.business_categories.tasks.assign_services_treatment_task',
        'schedule': timedelta(minutes=5),
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'hide-test-businesses-in-marketplace': {
        'task': 'webapps.business.tasks.hide_test_businesses_on_marketplace_task',
        'schedule': crontab(minute=0, hour=0, day_of_week='monday'),
    },
    # endregion
    # region BOOKING
    'booking-auto-finishing-and-declining-even': {
        'task': 'webapps.booking.tasks.schedule_bulk_update_appointments_task',
        'schedule': crontab(
            minute='0,2,4,6,8,10,12,14,16,18,20,22,24,26,28,30,32,34,36,38,40,42,44,46,48,50,52,54,56,58'  # pylint: disable=line-too-long
        ),
        'kwargs': {'mod_result': 0},
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'booking-auto-finishing-and-declining-uneven': {
        'task': 'webapps.booking.tasks.schedule_bulk_update_appointments_task',
        'schedule': crontab(
            minute='1,3,5,7,9,11,13,15,17,19,21,23,25,27,29,31,33,35,37,39,41,43,45,47,49,51,53,55,57,59'  # pylint: disable=line-too-long
        ),
        'kwargs': {'mod_result': 1},
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'booking-auto-finishing-and-declining-eod-in-case-of-failing': {
        'task': 'webapps.booking.tasks.schedule_bulk_update_appointments_task',
        'schedule': crontab(
            hour='23', minute='37'
        ),  # we don't expect a lot of traffic at this time
        'kwargs': {'workers': 5, 'daily_check': True},
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'replan-all-repeating-bookings': {
        'task': 'webapps.booking.tasks.replan_all_repeating_bookings',
        'schedule': crontab(hour='*/4', minute=45),
        'options': {
            'expires': 4 * 60 * 60 - 1,
        },
    },
    'split-all-repeating-bookings': {
        'task': 'webapps.booking.tasks.split_all_repeating_bookings',
        'schedule': crontab(hour='*/4', minute=15),
        'options': {
            'expires': 4 * 60 * 60 - 1,
        },
    },
    'remove-old-drafts': {
        'task': 'webapps.booking.v2.domains.appointment.tasks.drafts.remove_old_appointment_drafts',
        'schedule': timedelta(minutes=5),
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    # endregion
    # region ELASTICSEARCH TASKS
    'business-availability-river': {
        'task': 'webapps.elasticsearch.tasks.BusinessAvailabilityRiverTask',
        'schedule': crontab(minute='5,20,35,50'),
        'options': {
            'expires': 15 * 60 - 1,
        },
    },
    'all-business-availability': {
        'task': 'webapps.elasticsearch.tasks.all_business_availability_task',
        'schedule': shifted_crontab(hour=0, minute=20),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    # TODO: Remove when UTT2 will be fully operational
    'update-category_documents': {
        'task': 'webapps.business.tasks.compute_es_weight_business_category_task',
        'schedule': shifted_crontab(hour=2, minute=30),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    # 'clean-old-business-availability': {
    #     'task': 'webapps.elasticsearch.tasks.'
    #             'CleanOldBusinessAvailabilityTask',
    #     'schedule': shifted_crontab(hour=1, minute=20),
    #     'options': {
    #         'expires': 24 * 60 * 60 - 1,
    #     },
    # },
    # endregion
    # region ES RIVERS
    'business-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.BUSINESS,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'business-customer-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.BUSINESS_CUSTOMER,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'open-hours-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.OPEN_HOURS,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'resource-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.RESOURCE,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'external-listing-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=5 * 60),
        'args': (River.EXTERNAL_BUSINESS,),
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'review-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.REVIEW,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'image-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.IMAGE,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'image-comment-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.IMAGE_COMMENT,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'seo-metadata-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=60 * 60),
        'args': (River.SEO_METADATA,),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'user-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=1 * 60),
        'args': (River.USER,),
        'options': {
            'expires': 1 * 60 - 1,
        },
    },
    'business-category-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=5 * 60),
        'args': (River.BUSINESS_CATEGORY,),
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'service-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=60),
        'args': (River.SERVICE,),
        'options': {
            'expires': 60 - 1,
        },
    },
    'service-variant-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=60),
        'args': (River.SERVICE_VARIANT,),
        'options': {
            'expires': 60 - 1,
        },
    },
    'addon-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=60),
        'args': (River.ADDON,),
        'options': {
            'expires': 60 - 1,
        },
    },
    'commodity-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=60),
        'args': (River.COMMODITY,),
        'options': {
            'expires': 60 - 1,
        },
    },
    'commodity-category-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=60),
        'args': (River.COMMODITY_CATEGORY,),
        'options': {
            'expires': 60 - 1,
        },
    },
    'region-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=5 * 60),
        'args': (River.REGION,),
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'cms-content-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=60 * 60),
        'args': (River.CMS_CONTENT,),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'business-account-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.BUSINESS_ACCOUNT,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'seo-feature-flag-river': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.SEO_FEATURE_FLAG,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'seo-cms-content-data': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.SEO_CMS_CONTENT_DATA,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'seo-regions-homepage': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.SEO_REGION_HOMEPAGE,),
        'options': {
            'expires': 15 - 1,
        },
    },
    'seo-regions-category': {
        'task': 'webapps.elasticsearch.tasks.document_river_task',
        'schedule': timedelta(seconds=15),
        'args': (River.SEO_REGION_CATEGORY,),
        'options': {
            'expires': 15 - 1,
        },
    },
    # endregion
    # region NotificationSchedule
    'notification-schedule': {
        'task': 'webapps.notification.tasks.process_notification_schedules_task_with_cache',
        'schedule': timedelta(seconds=30),
        'options': {
            'expires': 120,
        },
    },
    'retrieve-stuck-started-notification-schedules-task': {
        'task': 'webapps.notification.tasks.retrieve_stuck_started_notification_schedules_task',
        'schedule': timedelta(minutes=7),
        'options': {
            'expires': 60 * 7 - 1,
        },
    },
    'retrieve-stuck-received-notification-schedules-task': {
        'task': 'webapps.notification.tasks.retrieve_stuck_received_notification_schedules_task',
        'schedule': timedelta(minutes=10),
        'options': {
            'expires': 60 * 10 - 1,
        },
    },
    'notifications_cleanup_task': {
        'task': 'webapps.notification.tasks.notifications_cleanup_task',
        'schedule': shifted_crontab(hour=2, minute=20),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'reminder_hour_old_values_cleanup': {
        'task': 'webapps.notification.tasks.custom.reminder_hour_old_values_cleanup',
        'schedule': shifted_crontab(hour=2, minute=20),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'delete_expired_push_tokens_task': {
        'task': 'webapps.notification.tasks.push.delete_expired_push_tokens_task',
        'schedule': timedelta(minutes=15),
        'options': {
            'expires': 1 * 60 * 60 - 1,
        },
    },
    'send_expired_invite_notification_task': {
        'task': (
            'webapps.family_and_friends.tasks.expired_invite_notifications.'
            'send_expired_invite_notification_task'
        ),
        'schedule': timedelta(minutes=15),
        'options': {
            'expires': 15 * 60 - 1,
        },
    },
    # endregion
    # region BUSINESS STATUS FLOW
    'business-trial-till-blocking': {
        'task': 'webapps.business.tasks.business_trial_till_blocking_task',
        'schedule': timedelta(minutes=5),
        'options': {
            'expires': 60 - 1,
        },
    },
    'business_visible_delay': {
        'task': 'webapps.business.tasks.business_visible_delay_task',
        'schedule': timedelta(minutes=10),
        'options': {
            'expires': 10 * 60 - 1,
        },
    },
    'business_blocked_overdue': {
        'task': 'webapps.business.tasks.business_blocked_overdue_task',
        'schedule': crontab(minute=7, hour='0,8,16'),
        'options': {
            'expires': 8 * 60 * 60 - 1,
        },
    },
    'business_delayed_churn': {
        'task': 'webapps.purchase.tasks.churn.business_churn_task',
        'schedule': crontab(hour='*/12', minute=15),
        'options': {
            'expires': 12 * 60 * 60 - 1,
        },
    },
    'cancel_subscriptions_before_churn': {
        'task': 'webapps.purchase.tasks.churn.cancel_subscriptions_before_churn_task',
        'schedule': crontab(hour='*/8', minute=22),
        'options': {
            'expires': 8 * 60 * 60 - 1,
        },
    },
    # endregion
    # region SUBSCRIPTIONS
    'delete-purchase-requests': {
        'task': 'webapps.purchase.tasks.DeletePurchaseRequestsTask',
        'schedule': crontab(hour='*/12', minute=21),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'delete-old-card-verification-attempts': {
        'task': 'webapps.purchase.tasks.brain_tree.delete_old_card_verification_attempts',
        'schedule': crontab(hour=3, minute=0),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'google-play-restore-expiry-none-subscription': {
        'task': 'webapps.purchase.tasks.google.RestoreExpiryNoneSubscription',
        'schedule': crontab(hour='*/12', minute=0),
        'options': {
            'expires': 8 * 60 * 60 - 1,
        },
    },
    'unify-restore-subscription-paid': {
        'task': (
            'webapps.purchase.tasks.renewing_subscription.unify_renewing_subscription_status_paid'
        ),
        'schedule': crontab(hour='*/8', minute=9),
        'options': {
            'expires': 8 * 60 * 60 - 1,
        },
    },
    'unify-restore-subscription-overdue': {
        'task': (
            'webapps.purchase.tasks.renewing_subscription.'
            'unify_renewing_subscription_status_overdue_task'
        ),
        'schedule': crontab(hour='*/8', minute=8),
        'options': {
            'expires': 8 * 60 * 60 - 1,
        },
    },
    'unify-restore-subscription-weekly': {
        'task': (
            'webapps.purchase.tasks.renewing_subscription.'
            'unify_renewing_subscription_non_churned_task'
        ),
        'schedule': crontab(day_of_week='sunday', hour=5, minute=7),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'unify-renewing-subscription-status-mismatched': {
        'task': (
            'webapps.purchase.tasks.renewing_subscription.'
            'unify_renewing_subscription_status_mismatched_task'
        ),
        'schedule': crontab(hour='*/6', minute=11),
        'options': {
            'expires': 6 * 60 * 60 - 1,
        },
    },
    'offline-subscriptions-sync-status': {
        'task': 'webapps.purchase.tasks.offline.offline_subscriptions_sync_status_task',
        'schedule': crontab(hour='*', minute=13),
        'options': {
            'expires': 60 - 1,
        },
    },
    'switch-billing-cycles': {
        'task': 'webapps.billing.tasks.switch_billing_cycles_task',
        'schedule': crontab(minute=9, hour='4,8,12,16,20'),
        'options': {
            'expires': 4 * 60 * 60 - 1,
        },
    },
    'auto-switch-billing-payment-processor-for-inactive-businesses': {
        'task': 'webapps.billing.tasks.auto_switch_payment_processor_for_inactive_businesses_task',
        'schedule': crontab(hour=4),
        'options': {
            'expires': 1 * 60 * 60 - 1,
        },
    },
    'pending-requested-churn': {
        'task': 'webapps.billing.tasks.pending_requested_churn',
        'schedule': crontab(minute=39, hour='1,5,9,13,17,21'),
        'options': {
            'expires': 4 * 60 * 60 - 1,
        },
    },
    'auto-retry-charges': {
        'task': 'webapps.billing.tasks.auto_retry_charges',
        'schedule': crontab(minute=21, hour=7),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'cancel-abandoned-purchase-requests': {
        'task': 'webapps.billing.tasks.cancel_abandoned_purchase_request_task',
        'schedule': crontab(hour=3),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'cancel-abandoned-retry-charge': {
        'task': 'webapps.billing.tasks.cancel_abandoned_retry_charge_task',
        'schedule': crontab(hour=4),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'update_not_refreshed_businesses_task': {
        'task': 'webapps.billing.tasks.update_not_refreshed_businesses_task',
        'schedule': crontab(hour='*/2', minute=17),
        'options': {
            'expires': 2 * 60 * 60 - 1,
        },
    },
    'calculate-mmr-reports': {
        'task': 'webapps.purchase.tasks.mrr_reports.calculate_mmr_reports',
        'schedule': crontab(hour=1, minute=0),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'braintree-fetch-plans': {
        'task': 'webapps.purchase.tasks.brain_tree.BraintreeFetchPlansTask',
        'schedule': timedelta(hours=1),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'send-double-subscriptions-report': {
        'task': 'webapps.purchase.tasks.reports.send_double_subscriptions_report',
        'schedule': crontab(hour=6, minute=15),
        'options': {
            'expires': 24 * 60 - 1,
        },
    },
    # endregion
    # region MARKETING
    'delete-old-sms-invitation-events': {
        'task': 'webapps.marketing.tasks.clean_sms_invitation_events_task',
        'schedule': crontab(hour=3, minute=0),
        'options': {
            'expires': 3 * 60 * 60 - 1,
        },
    },
    # endregion
    # region ADMIN-EXTRA
    # Restore CancelAllTransactionsOlderThan30days
    # after resolving https://booksy.atlassian.net/browse/PY-1458
    # 'cancel-all-transactions-older-than-30-days': {
    #     'task': 'webapps.pos.tasks.CancelAllTransactionsOlderThan30days',
    #     'schedule': crontab(hour=1, minute=0),
    #     'options': {
    #         'expires': 1 * 60 * 60 - 1,
    #     },
    # },
    'set_admin_permissions_task': {
        'task': 'webapps.admin_extra.tasks.utils.set_admin_permissions_task',
        'schedule': (
            timedelta(minutes=15) if settings.ENABLED_GOOGLE_SIGN_IN else timedelta(hours=6)
        ),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    # endregion
    # region TUNING SEARCH
    # must be fired after all_business_availability_task
    'business-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_businesses_tuning_params_task',
        'schedule': crontab(hour=3, minute=00),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'selected-businesses-search': {
        'task': (
            'webapps.search_engine_tuning.tasks.update_selected_businesses_tuning_params_task'
        ),
        'schedule': timedelta(minutes=5),
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    'user-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_users_tuning_task',
        'schedule': crontab(hour=5, minute=00),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'fast-user-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_users_tuning_fast_task',
        # to low schedule and expire time will result in discarding tasks
        'schedule': timedelta(seconds=15),
        'options': {
            'expires': 15 - 1,
        },
    },
    'business-category-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_business_category_tunings_params_task',
        'schedule': crontab(day_of_week=0, hour=2, minute=00),
        'options': {
            'expires': 7 * 24 * 60 * 60 - 1,
        },
    },
    'category-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_category_tunings_params_task',
        'schedule': crontab(day_of_week=0, hour=2, minute=5),
        'options': {
            'expires': 7 * 24 * 60 * 60 - 1,
        },
    },
    'addon-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_addon_tunings_params_task',
        'schedule': crontab(hour=2, minute=10),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'commodity-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_commodity_tunings_params_task',
        'schedule': crontab(hour=2, minute=15),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'service-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_service_tunings_params_task',
        'schedule': crontab(hour=2, minute=20),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'service-variant-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_service_variant_tunings_params_task',
        'schedule': crontab(hour=2, minute=25),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'business-customer-search': {
        'task': 'webapps.search_engine_tuning.tasks.update_business_customer_tunings_params_task',
        'schedule': crontab(hour=2, minute=30),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    # endregion
    # region POS
    'prepayment-auto-checkout': {
        'task': 'webapps.pos.tasks.ClosePrepaymentTransactionAtTime',
        'schedule': crontab(hour='*', minute=1),
    },
    'cancellation-fee-auto-checkout': {
        'task': 'webapps.pos.tasks.FetchAutoChargePOSes',
        'schedule': crontab(hour=23, minute=50),
    },
    'booksy-pay-auto-checkout': {
        'task': 'webapps.pos.tasks.CloseBooksyPayTransactionAtTime',
        'schedule': crontab(hour='*', minute=5),
    },
    'calculate-pos-plans': {
        'task': 'webapps.pos.tasks.recalculate_pos_plans',
        'schedule': shifted_crontab(hour=0, minute=14),
    },
    'cancel-cfp': {
        'task': 'webapps.pos.tasks.cancel_cfp_task',
        'schedule': shifted_crontab(hour=23, minute=54),
    },
    'execute-refunds': {
        'task': 'webapps.pos.tasks.execute_pending_refunds_task',
        'schedule': crontab(hour='*', minute=30),
    },
    'auto_enable_fast_payouts': {
        'task': 'service.auto_enable_fast_payouts.tasks.try_enable_fast_payouts_in_batches_task',
        'schedule': crontab(hour='3', minute='0'),
    },
    'auto_enable_ba_deposit': {
        'task': 'service.auto_enable_ba_deposit.tasks.try_enable_ba_deposit_in_batches_task',
        'schedule': crontab(hour='4', minute='0'),
    },
    # endregion
    # region VOUCHER
    'update-voucher-status': {
        'task': 'webapps.voucher.tasks.update_voucher_status_task',
        'schedule': crontab(hour='*', minute=13),
    },
    'remove-unused-redundant-voucher-templates': {
        'task': 'webapps.voucher.tasks.remove_unused_redundant_voucher_templates_task',
        'schedule': crontab(hour=3, minute=0, day_of_week=0),
    },
    # endregion
    # region STATISTICS
    'aggregate-statistics-daily': {
        'task': 'webapps.statistics.tasks.aggregate_statistics_daily_task',
        'schedule': crontab(hour=0, minute=5),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'aggregate-statistics-river': {
        'task': 'webapps.statistics.tasks.aggregate_statistics_river_task',
        'schedule': timedelta(minutes=5),
        'options': {
            'expires': 5 * 60 - 1,
        },
    },
    # endregion
    # region SEO CMS
    'refresh_region_homepage_task_daily': {
        'task': 'webapps.marketplace.cms.tasks.refresh_region_homepage_task',
        'schedule': crontab(hour=4, minute=7),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'refresh_region_category_task_daily': {
        'task': 'webapps.marketplace.cms.tasks.refresh_region_category_task',
        'schedule': crontab(hour=4, minute=7),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    # endregion SEO CMS
    # region MARKETPLACE
    'pay_marketplace_transaction_task': {
        'task': 'webapps.marketplace.tasks.pay_marketplace_transaction_task',
        'schedule': crontab(hour=0, minute=15),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'retry_marketplace_transaction': {
        'task': 'webapps.marketplace.tasks.retry_marketplace_transaction_task',
        'schedule': timedelta(hours=2),
        'options': {
            'expires': 2 * 60 * 60 - 1,
        },
    },
    'retry_boost_claims': {
        'task': 'webapps.marketplace.tasks.retry_boost_claims_task',
        'schedule': crontab(hour=2, minute=25),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'refresh_processing_marketplace_transactions': {
        'task': 'webapps.marketplace.tasks.refresh_boost_braintree_processing_transactions_task',
        'schedule': timedelta(minutes=30),
        'options': {
            'expires': 30 * 60 - 1,
        },
    },
    'resend-push-waiting-businesses-task': {
        'task': 'webapps.marketplace.tasks.resend_push_waiting_businesses_task',
        'schedule': timedelta(minutes=30),
        'options': {
            'expires': 30 * 60 - 1,
        },
    },
    'import-b-listings-from-lead-db': {
        'task': 'webapps.marketplace.tasks.import_b_listings_from_lead_db',
        'schedule': crontab(hour=1, minute=0),
    },
    'mass_braintree_find_task': {
        'task': 'webapps.marketplace.tasks.mass_braintree_find_task',
        'schedule': crontab(hour=1, minute=15),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'mass_set_chargeable_payable_task': {
        'task': 'webapps.marketplace.tasks.mass_set_chargeable_payable_task',
        'schedule': crontab(hour='*/2', minute=4),
    },
    'mass_check_chargeable': {
        'task': 'webapps.marketplace.tasks.mass_check_chargeable',
        'schedule': crontab(hour=0, minute=2),
    },
    'rerun_not_enabled_boost': {
        'task': 'webapps.marketplace.tasks.rerun_not_enabled_boost',
        'schedule': crontab(hour=0, minute=17),
    },
    'boost_status_change_with_date_task': {
        'task': 'webapps.business.tasks.boost_status_change_with_date_task',
        'schedule': crontab(hour=2, minute=48),
    },
    # endregion
    # region BOOST
    'mass_revert_or_delete_boost_transactions_task': {
        'task': 'webapps.boost.tasks.mass_revert_or_delete_boost_transactions_task',
        'schedule': timedelta(minutes=20),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'end_boost_bans_for_today': {
        'task': 'webapps.boost.tasks.end_boost_bans_for_today',
        'schedule': crontab(hour=10, minute=0),
    },
    'update_boost_ban_sms_status_task': {
        'task': 'webapps.boost.tasks.update_boost_ban_sms_status_task',
        'schedule': crontab(hour='*/2', minute=20),
    },
    # endregion
    # region STRIPE_TERMINAL
    'stripe-fetch-hardware-order-shipment-tracking': {
        'task': 'webapps.stripe_terminal.tasks.fetch_hardware_orders_shipment_tracking',
        'schedule': timedelta(hours=4),
    },
    # endregion
    # region FAST PAYOUTS
    'stripe-charge-fast-payout-paid-fees': {
        'task': 'webapps.stripe_integration.tasks.trigger_fast_payout_paid_fees_task',
        'schedule': timedelta(minutes=6),
    },
    # endregion
    # region STRIPE
    'consent-stripe-account-restricted-soon-visibility': {
        'task': (
            'webapps.business_consents.tasks.'
            'consent_stripe_account_restricted_soon_turn_back_visibility'
        ),
        'schedule': timedelta(minutes=30),
    },
    # endregion STRIPE
    # region STRUCTURE
    'map-regions-to-categories': {
        'task': 'webapps.structure.tasks.map_regions_to_categories_task',
        'schedule': shifted_crontab(hour=2, minute=5),
    },
    # endregion
    # region SEGMENT
    'green-merchant': {
        'task': 'webapps.segment.tasks.merchant_activity_weekly_report_task',
        'schedule': crontab(hour=1, minute=0, day_of_week="monday"),
    },
    # endregion
    # region BATCH EMAIL
    'batch-email-send': {
        'task': 'lib.email.tasks.send_emails_batch_task',
        'schedule': timedelta(seconds=15),
        'options': {
            'expires': 15 - 1,
        },
    },
    # endregion
    # region R&D
    'calculate-median-of-businesses': {
        'task': 'webapps.r_and_d.tasks.calculate_median_of_businesses',
        'schedule': crontab(hour=1, minute=0),
    },
    'business_hidden_in_search_turn_off_task': {
        'task': 'webapps.business.tasks.business_hidden_in_search_turn_off_task',
        'schedule': timedelta(minutes=60),
        'options': {
            'expires': 15 * 60 - 1,
        },
    },
    'update_users_booking_score': {
        'task': 'webapps.user.tasks.booking_score.update_users_booking_score',
        'schedule': crontab(hour=1, minute=0),
    },
    # endregion
    # region SERVICE PROMOTIONS
    # FLASH SALE
    'disable_expired_flash_sale_promotions_task': {
        'task': 'webapps.business.tasks.disable_expired_flash_sale_promotions_task',
        'schedule': crontab(hour='*', minute='*/15'),
    },
    # Temporary disabled https://redmine.booksy.pm/issues/71347
    # HAPPY HOURS MORNING NOTIFICATIONS
    # 'morning_happy_hours_incentive_task': {
    #     'task': 'webapps.business.tasks.happy_hours_incentive_task',
    #     'schedule': crontab(hour='*', minute=3),
    #     'options': {
    #         'expires': 1 * 60 * 60 - 1,
    #     },
    #     'args': ('8:00',),
    # },
    # HAPPY HOURS AFTERNOON NOTIFICATIONS
    # 'afternoon_happy_hours_incentive_task': {
    #     'task': 'webapps.business.tasks.happy_hours_incentive_task',
    #     'schedule': crontab(hour='*', minute=3),
    #     'options': {
    #         'expires': 1 * 60 * 60 - 1,
    #     },
    #     'args': ('12:00',),
    # },
    # endregion
    # region MARKET PAY
    'transfer_funds_task': {
        'task': 'webapps.market_pay.tasks.transfer_funds_task',
        'schedule': crontab(hour='*', minute='15'),
    },
    # endregion
    # region CONSENTS
    'create_consents_task': {
        'task': 'webapps.consents.tasks.create_consents_task',
        'schedule': timedelta(hours=2),
        'options': {
            'expires': 2 * 60 * 60 - 1,
        },
    },
    'dismiss_consents_task': {
        'task': 'webapps.consents.tasks.dismiss_consents_task',
        'schedule': timedelta(hours=1),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    # endregion
    # region CLEANUP
    'smscodes_cleanup_task': {
        'task': 'webapps.notification.tasks.smscodes_cleanup_task',
        'schedule': crontab(hour='*', minute='*/8'),
        'options': {
            'expires': 8 * 60 - 1,
        },
    },
    'email_codes_cleanup_task': {
        'task': 'webapps.notification.tasks.email_codes_cleanup_task',
        'schedule': crontab(hour='*', minute='*/8'),
        'options': {
            'expires': 8 * 60 - 1,
        },
    },
    'email_token_cleanup_task': {
        'task': 'webapps.user.tasks.cleanup.email_token_cleanup_task',
        'schedule': crontab(hour=2, minute=50),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'session_cleanup_task': {
        'task': 'webapps.user.tasks.cleanup.session_cleanup_task',
        'schedule': crontab(hour=2, minute=55),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'pop_up_cleanup_task': {
        'task': 'webapps.pop_up_notification.tasks.pop_up_cleanup_task',
        'schedule': crontab(hour=3, minute=0),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'cleanup_unused_tags_task': {
        'task': 'webapps.business.tasks.cleanup_unused_tags_task',
        'schedule': crontab(hour=3, minute=10),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    # region ZOOM
    'create-missing-zoom-meetings-task': {
        'task': 'webapps.zoom.tasks.create_missing_zoom_meetings_task',
        'schedule': crontab(hour=4, minute=0),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    # endregion
    # region periodic events
    'month_start_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=1, minute=5, day_of_month=1),
        'args': ('month_start_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'month_end_event_odd': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=15, minute=5, day_of_month=31),
        'args': ('month_start_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'month_end_event_even': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=15, minute=5, month_of_year='4,6,9,11', day_of_month=30),
        'args': ('month_start_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'month_end_event_feb': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=15, minute=5, month_of_year='2', day_of_month=28),
        'args': ('month_start_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_14_days_sunday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=12, minute=5, day_of_week='sunday'),
        'kwargs': {
            'event_type': 'every_14_days_sunday_event',
            'every_two_weeks': True,
        },
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_14_days_tuesday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=7, minute=5, day_of_week='tuesday'),
        'kwargs': {
            'event_type': 'every_14_days_tuesday_event',
            'every_two_weeks': True,
        },
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_14_days_wednesday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=7, minute=5, day_of_week='wednesday'),
        'kwargs': {
            'event_type': 'every_14_days_wednesday_event',
            'every_two_weeks': True,
        },
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_14_days_thursday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=7, minute=5, day_of_week='thursday'),
        'kwargs': {
            'event_type': 'every_14_days_thursday_event',
            'every_two_weeks': True,
        },
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_sunday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=13, minute=5, day_of_week='sunday'),
        'args': ('every_sunday_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_tuesday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=13, minute=5, day_of_week='tuesday'),
        'args': ('every_tuesday_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_monday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=5, minute=5, day_of_week='monday'),
        'args': ('every_monday_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_saturday_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=22, minute=5, day_of_week='saturday'),
        'args': ('every_saturday_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    'every_working_morning_event': {
        'task': 'webapps.celery.tasks.periodic_event_task',
        'schedule': crontab(hour=6, minute=5, day_of_week='mon-fri'),
        'args': ('every_working_morning_event',),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    },
    # endregion
    # region UTT
    'update-predictions-confirmation-priority': {
        'task': 'webapps.utt.tasks.update_predictions_confirmation_priority',
        'schedule': crontab(hour=3, minute=0),
    },
    # endregion
    # region User
    'apple_user_clean_up': {
        'task': 'webapps.user.tasks.apple.authorize_apple_identities_task',
        'schedule': timedelta(minutes=15),
        'options': {
            'expires': 15 * 60 - 1,
        },
    },
    'user_deletion_periodic_task': {
        'task': 'webapps.user.tasks.user_deletion.user_deletion_periodic_task',
        'schedule': crontab(hour='*', minute=0),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    # endregion
    # region MESSAGES_BLAST
    'process_message_blast_templates_task': {
        'task': 'webapps.message_blast.tasks.process_message_blast_templates_task',
        'schedule': crontab(hour="*", minute=5),
    },
    'process_message_blast_schedules_task': {
        'task': 'webapps.message_blast.tasks.process_message_blast_schedules_task',
        'schedule': crontab(hour="*", minute=5),
    },
    'reset_to_default_message_blast_templates_task': {
        'task': 'webapps.message_blast.tasks.reset_to_default_message_blast_templates_task',
        'schedule': crontab(hour=3, minute=15),
    },
    'welcome_new_client_river_task': {
        'task': 'webapps.message_blast.tasks.welcome_new_client_river_task',
        'schedule': timedelta(minutes=15),
        'options': {
            'expires': 15 * 60 - 1,
        },
    },
    # endregion
    # region Profile Completeness
    'update_profile_completeness_stats_task': {
        'task': 'webapps.profile_completeness.tasks.update_profile_completeness_stats_task',
        'schedule': timedelta(minutes=15),
        'options': {
            'expires': 15 * 60 - 1,
        },
    },
    # endregion
    # segment
    'save_used_app_version': {
        'task': 'webapps.segment.tasks.save_used_app_version_task',
        'schedule': crontab(hour=5, minute=17),
        'options': {
            'expires': 8 * 60 * 60 - 1,
        },
    },
    # endregion
    'cancel_outdated_3ds_bookings': {
        'task': 'webapps.adyen.tasks.cancel_outdated_3ds_bookings',
        'schedule': crontab(
            hour='*',
            minute='*',
        ),
    },
    # region DF_CREATOR
    'push_digital_flyer_availability': {
        'task': 'webapps.df_creator.tasks.push_digital_flyer_availability',
        'schedule': crontab(hour=10, minute=0),
    },
    # endregion
    # region Navision
    'create_not_synced_merchant_in_navision': {
        'task': 'webapps.navision.tasks.api.create_not_synced_merchant_in_navision',
        'schedule': crontab(minute='0', hour='*/1'),  # every one hour
    },
    'schedule_invoicing_task': {
        'task': 'webapps.navision.tasks.base.schedule_invoicing_task',
        'schedule': crontab(minute='9', hour='9,10'),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'send_not_synced_invoices_to_navision': {
        'task': 'webapps.navision.tasks.api.send_not_synced_invoices_to_navision',
        'schedule': crontab(minute='39'),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'sync_invoices_with_errors_task': {
        'task': 'webapps.navision.tasks.api.sync_invoices_with_errors_task',
        'schedule': crontab(minute='*/15'),
        'options': {
            'expires': 60 * 15 - 1,
        },
    },
    'sync_subscription_buyers_with_merchant_task': {
        'task': (
            'webapps.navision.tasks.buyer_merchant_integration.'
            'sync_subscription_buyers_with_merchant_task'
        ),
        'schedule': crontab(minute='0', hour='*/1'),  # every one hour
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'update_tax_rates_task': {
        'task': 'webapps.navision.tasks.tax_rates.update_tax_rates_task',
        'schedule': crontab(hour='7', minute='19'),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'fill_tax_rate_table_from_business_zipcode_task': {
        'task': 'webapps.navision.tasks.tax_rates.fill_tax_rate_table_from_business_zipcode_task',
        'schedule': crontab(hour='*/4', minute=19),
        'options': {
            'expires': 4 * 60 * 60 - 1,
        },
    },
    'sync_changed_invoice_items_task': {
        'task': 'webapps.navision.tasks.api.sync_changed_invoice_items_task',
        'schedule': crontab(minute='0', hour='*/1'),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'send_to_new_sandbox_test_invoices_bc_start_task': {
        'task': 'webapps.navision.tasks.api.send_to_new_sandbox_test_invoices_bc_start_task',
        'schedule': crontab(minute='9', hour='14'),
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'verify_active_providers_task': {
        'task': 'webapps.navision.tasks.subscription_buyer.verify_active_providers_task',
        'schedule': crontab(minute=37, hour='*/6'),  # every six hours
        'options': {
            'expires': 60 * 60 - 1,
        },
    },
    'fill_subscription_buyer_data_task': {
        'task': 'webapps.navision.tasks.subscription_buyer.fill_subscription_buyer_data_task',
        'schedule': crontab(hour=12, minute=0),
    },
    'update_buyer_with_business_data_task': {
        'task': 'webapps.navision.tasks.subscription_buyer.update_buyer_with_business_data_task',
        'schedule': crontab(minute='0', hour='*/4'),
        'options': {
            'expires': 2 * 60 * 60 - 1,
        },
    },
    # endregion
    # region History
    'index-business-history': {
        'task': 'lib.es_history.tasks.index_history_records_task',
        'schedule': timedelta(seconds=15),
        'args': ('business.Business',),
    },
    # endregion
    'clear_experiment_activity_status_cache': {
        'task': 'webapps.experiment_v3.tasks.clear_experiment_activity_status_cache',
        'schedule': timedelta(minutes=5),
    },
    'check_experiment_watchers': {
        'task': 'webapps.experiment_v3.tasks.check_experiment_watchers',
        'schedule': timedelta(minutes=5),
    },
    # endregion
    # region ES Sync
    'fix_out_of_sync_docs_task': {
        'task': 'webapps.elasticsearch.tasks.fix_out_of_sync_docs_task',
        'schedule': crontab(hour=0, minute=0),
    },
    # endregion
    # region VERSUM_MIGRATION
    'fill_versum_subscription_information_task': {
        'task': 'webapps.versum_migration.tasks.fill_versum_subscription_information_task',
        'schedule': crontab(hour=0, minute=0),
    },
    # endregion
    'celery_beat_health_check': {
        'task': 'lib.probes.celery_probes.celery_beat_health_check_task',
        'schedule': timedelta(seconds=20),
    },
    # region PublicAPI
    'clear_public_api_oauth2_tokens': {
        'task': 'webapps.public_partners.tasks.oauth2.clear_oauth2_tokens_task',
        'schedule': crontab(minute='0', hour='*/1'),  # every one hour
    },
    # endregion
    # region PAYMENT_PROVIDERS
    'check-and-update-card-expiry-status': {
        'task': 'webapps.payment_providers.tasks.check_and_update_card_expiry_status_task',
        'schedule': crontab(hour=3, minute=30, day_of_month=1),
    },
    # endregion
}

if not str_to_bool(os.getenv("CELERY_ETA", "False")):
    # region celery_internal
    CELERY_BEAT_SCHEDULE['move-eta-tasks-to-queues-task'] = {
        'task': 'lib.tasks.eta_scheduler.move_eta_tasks_to_queues_task',
        'schedule': timedelta(seconds=5),
        'options': {
            'expires': 30,
        },
    }

# region C2B Referral
if settings.LIVE_DEPLOYMENT and settings.C2B_REFERRAL:
    CELERY_BEAT_SCHEDULE['send-c2b-referral-summary'] = {
        'task': 'webapps.c2b_referral.tasks.send_mail_to_cs_task',
        'schedule': shifted_crontab(hour=1, minute=28),
        'options': {
            'expires': 24 * 60 * 60 - 1,
        },
    }
    CELERY_BEAT_SCHEDULE['c2b-referral-reward-status-change'] = {
        'task': 'webapps.c2b_referral.tasks.change_reward_status_task',
        'schedule': timedelta(minutes=30),
        'options': {
            'expires': 60 - 1,
        },
    }

if not settings.LIVE_DEPLOYMENT:
    CELERY_BEAT_SCHEDULE['cancellation-fee-auto-checkout'] = {
        'task': 'webapps.pos.tasks.FetchAutoChargePOSes',
        'schedule': timedelta(minutes=15),
        'options': {
            'expires': 15 * 60 - 1,
        },
    }
# endregion


# TODO: Refactor/remove with DG-342
# region Importing data from BQ to CS
# if settings.LIVE_DEPLOYMENT and settings.API_COUNTRY == Country.US:
#     CELERY_BEAT_SCHEDULE['propagating_BQ_to_CS:28d_conversion_old'] = {
#         'task': 'webapps.r_and_d.tasks.propagating_big_query_to_cloud_storage',
#         'schedule': shifted_crontab(hour=0, minute=0),
#         'kwargs': {
#             'dataset_id': 'analytics_181732434',
#             'project': 'api-project-785785666615',
#             'table_id': 'conversion_on_search_28d',
#             'destination_uri': 'gs://28d_conversion/conversion_on_search28d',
#         },
#     }
#     CELERY_BEAT_SCHEDULE['propagating_BQ_to_CS:28d_conversion'] = {
#         'task': 'webapps.r_and_d.tasks.propagating_big_query_to_cloud_storage',
#         'schedule': shifted_crontab(hour=0, minute=0),
#         'kwargs': {
#             'dataset_id': 'analytics_181732434',
#             'project': 'api-project-785785666615',
#             'table_id': 'conversion_on_search_28d',
#             'destination_uri': (
#                 'gs://live_periodic_tasks/conversion_on_search28d/conversion_on_search28d'
#             ),
#         },
#     }
#     CELERY_BEAT_SCHEDULE['propagating_BQ_to_CS:user_interest_views'] = {
#         'task': 'webapps.r_and_d.tasks.propagating_big_query_to_cloud_storage',
#         'schedule': shifted_crontab(hour=0, minute=0),
#         'kwargs': {
#             'dataset_id': 'analytics_181732434',
#             'project': 'api-project-785785666615',
#             'table_id': 'User_Interests_Views',
#             'destination_uri': 'gs://live_periodic_tasks/user_interest_views/user_interest_views',
#         },
#     }
#     CELERY_BEAT_SCHEDULE['propagating_BQ_to_CS:user_interest_searches'] = {
#         'task': 'webapps.r_and_d.tasks.propagating_big_query_to_cloud_storage',
#         'schedule': shifted_crontab(hour=0, minute=0),
#         'kwargs': {
#             'dataset_id': 'analytics_181732434',
#             'project': 'api-project-785785666615',
#             'table_id': 'User_Interests_Searches',
#             'destination_uri': (
#                 'gs://live_periodic_tasks/user_interest_searches/user_interest_searches'
#             ),
#         },
#     }
# CELERY_BEAT_SCHEDULE['extract_country_CS_data:conversion'] = {
#     'task': 'webapps.r_and_d.tasks.extract_country_cloud_storage_data',
#     'schedule': shifted_crontab(hour=1, minute=0),
#     'kwargs': {
#         'booksy_dataset_name': 'conversion',
#     },
# }
# CELERY_BEAT_SCHEDULE['extract_country_CS_data:user_interest_searches'] = {
#     'task': 'webapps.r_and_d.tasks.extract_country_cloud_storage_data',
#     'schedule': shifted_crontab(hour=1, minute=0),
#     'kwargs': {
#         'booksy_dataset_name': 'interest_searches',
#     },
# }
# CELERY_BEAT_SCHEDULE['extract_country_CS_data:user_interest_views'] = {
#     'task': 'webapps.r_and_d.tasks.extract_country_cloud_storage_data',
#     'schedule': shifted_crontab(hour=1, minute=0),
#     'kwargs': {
#         'booksy_dataset_name': 'interest_views',
#     },
# }
# endregion

# for testing on s.booksy.net
# disable till iOS app available https://pm.booksy.net/issues/52764
if settings.LOCAL_DEPLOYMENT:
    CELERY_BEAT_SCHEDULE['happy_hours_incentive_task'] = {
        'task': 'webapps.business.tasks.happy_hours_incentive_task',
        'schedule': timedelta(minutes=5),
        'options': {
            'expires': 1 * 60 * 60 - 1,
        },
        'args': ('8:00',),
    }

if settings.INVOICES_ENABLED:
    CELERY_BEAT_SCHEDULE['find_invoice_objects_to_index_task'] = {
        'task': 'webapps.invoice.tasks.find_invoice_objects_to_index_task',
        'schedule': crontab(hour='*', minute='37'),
        'options': {
            'expires': 60 * 60 - 1,
        },
    }

if settings.API_COUNTRY == Country.BR:
    CELERY_BEAT_SCHEDULE['block_spammer_accounts'] = {
        'task': 'webapps.business.tasks.block_spammer_accounts',
        'schedule': crontab(minute='0', hour='*'),
        'options': {
            'expires': 60 * 60 - 1,
        },
    }

if settings.API_COUNTRY == Country.PL:
    CELERY_BEAT_SCHEDULE['migrated_subscription_initial_task'] = {
        'task': 'webapps.billing.tasks.migrated_subscription_initial_task',
        'schedule': timedelta(seconds=30),
        'options': {
            'expires': 30 - 1,
        },
    }

if settings.API_COUNTRY == Country.FR:
    CELERY_BEAT_SCHEDULE['close_periods_for_businesses_task'] = {
        'task': 'webapps.french_certification.tasks.close_periods_for_businesses_task',
        'schedule': crontab_in_timezone(
            hour=3,
            minute=0,
            target_timezone=pytz.timezone('Europe/Paris'),
        ),  # at 3 a.m. every day France timezone
        'options': {
            'expires': 12 * 60 * 60,
        },
    }
    # endregion

if settings.API_COUNTRY in (Country.FR, Country.PL):
    CELERY_BEAT_SCHEDULE['user_imported_sync_booksy_auth_task'] = {
        'task': 'webapps.user.tasks.sync.user_imported_sync_booksy_auth_task',
        'schedule': timedelta(minutes=10),
        'options': {
            'expires': 10 * 60 - 1,
        },
    }


class BasicCeleryConfig:
    """Basic default celery config."""

    task_serializer = 'json'
    result_serializer = 'json'
    accept_content = ['json', 'pickle']

    redis_retry_on_timeout = True
    redis_socket_connect_timeout = 10

    broker_transport_options = {
        'confirm_publish': True,
        'fanout_prefix': True,
        'fanout_patterns': True,
        'visibility_timeout': 60 * 60 * 24,  # max task processing time
        'socket_timeout': 50,
        'socket_connect_timeout': 10,
        'retry_on_timeout': True,
        'health_check_interval': 50,
    }
    broker_connection_retry_on_startup = True
    result_backend = 'disabled'
    task_create_missing_queues = True
    task_default_queue = 'celery'
    task_default_exchange = 'celery'
    task_default_exchange_type = 'direct'
    task_default_routing_key = 'task.celery'
    task_ignore_result = False
    worker_hijack_root_logger = False
    worker_max_tasks_per_child = 500
    worker_max_memory_per_child = 600_000  # kilobytes == 600 mb
    worker_prefetch_multiplier = 1
    worker_proc_alive_timeout = 8.0
    worker_redirect_stdouts = True
    worker_send_task_events = True
    task_compression = 'zlib'
    timezone = settings.COUNTRY_CONFIG.default_time_zone

    task_soft_time_limit = 5 * 60
    task_time_limit = 6 * 60

    task_always_eager = settings.CELERY_ALWAYS_EAGER
    task_eager_propagates = settings.CELERY_ALWAYS_EAGER
    worker_cancel_long_running_tasks_on_connection_loss = True
    worker_logfile = 'ext://sys.stdout'

    worker_consumer = 'lib.celery_tools:RepeatableUnregisteredCeleryConsumer'

    @classproperty
    def task_queues(cls):  # pylint: disable=no-self-argument
        return [queue(name) for name in cls.queues]

    task_routes = CELERY_ROUTES
    beat_schedule = CELERY_BEAT_SCHEDULE


class BooksyCeleryConfig(BasicCeleryConfig):
    """Booksy specifig celery config."""

    beat_scheduler = 'lib.redbeat_celery.RedBeatSchedulerReloadOnStart'
    redbeat_redis_url = settings.CELERY_BEAT_URL
    redbeat_lock_timeout = 60
    beat_max_loop_interval = 20
    broker_url = settings.BROKER_URL
    result_backend = settings.CELERY_BACKEND_URL
    task_ignore_result = True
    result_expires = 3600


class AllQueuesCeleryConfig(BooksyCeleryConfig):
    queues = CELERY_QUEUES_BY_APP['celery-all-queue-worker']


class PriorityQueuesCeleryConfig(BooksyCeleryConfig):
    queues = CELERY_QUEUES_BY_APP['celery-priority-worker']


class PushNotificationsQueuesCeleryConfig(BooksyCeleryConfig):
    queues = CELERY_QUEUES_BY_APP['celery-push-worker']
    # worker_max_tasks_per_child = 5000


class RegularQueuesCeleryConfig(BooksyCeleryConfig):
    queues = CELERY_QUEUES_BY_APP['celery-regular-worker']


class SegmentQueuesCeleryConfig(BooksyCeleryConfig):
    queues = CELERY_QUEUES_BY_APP['celery-segment-worker']


class IndexQueuesCeleryConfig(BooksyCeleryConfig):
    queues = CELERY_QUEUES_BY_APP['celery-index-worker']


class EmptyQueuesCeleryConfig(BooksyCeleryConfig):
    # App with an empty queue that handles beat scheduler.
    queues = []
