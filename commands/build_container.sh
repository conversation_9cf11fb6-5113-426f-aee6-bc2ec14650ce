#!/usr/bin/env bash
set -x
CONTAINER_NAME=$1
COUNTRY_CODE=$2

CURRENT_UID=$(id -u)
CURRENT_GUID=$(id -g)

if ! [[ -f ~/.config/gcloud/application_default_credentials.json ]]; then
  echo " "
  echo -e "\033[0;31mYou need to configure application default credentials before building the image!\033[0m"
  echo "See https://gitlab.com/booksy/apps/core/-/wikis/Installation-Guide"
  echo " "
  echo "Please run: gcloud auth application-default login"
  echo " "
  exit
fi

if [[ "$COUNTRY_CODE" == 'us' ]];then
    # us docker-compose file must be default
    FILE_NAME="docker-compose.yml"
elif [[ "$COUNTRY_CODE" == 'pl' ]]; then
   FILE_NAME="docker-compose-pl.yml"
else
    echo " "
    echo "You need to specify country code: us or pl"
    echo " "
    exit
fi

if [[ "$CONTAINER_NAME" == 'api' ]] ||
   [[ "$CONTAINER_NAME" == 'admin' ]] ||
   [[ "$CONTAINER_NAME" == 'api_grpc' ]]; then
    FULL_CONTAINER_NAME="${CONTAINER_NAME}_${COUNTRY_CODE}"
else
  FULL_CONTAINER_NAME="$CONTAINER_NAME"
fi

mkdir -p ./.docker_volume/smtp_sink_emails

echo '***************'
echo "Build $FULL_CONTAINER_NAME"
echo '***************'
docker-compose -f "$FILE_NAME" \
    build \
    --build-arg CURRENT_UID="${CURRENT_UID}" \
    --build-arg CURRENT_GUID="${CURRENT_GUID}" \
    "$FULL_CONTAINER_NAME"
