#!/usr/bin/env python
# pylint: disable=ungrouped-imports,wrong-import-position

import copy
import sys
import time
import typing as t
from concurrent import futures
from datetime import timedelta
from functools import lru_cache

import django
from django.db import reset_queries
from django.db.models import Count, Model
from django.utils.connection import ConnectionProxy
from pytz import UTC

django.setup()

from django.conf import settings

settings.DEBUG = True

from lib.elasticsearch.document import Document
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.business.elasticsearch.account import BusinessAccountDocument
from webapps.business.elasticsearch.business import BusinessDocument
from webapps.business.elasticsearch.business_category import BusinessCategoryDocument
from webapps.business.elasticsearch.business_customer import BusinessCustomerDocument
from webapps.business.elasticsearch.history import BusinessHistoryDocument
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.marketplace.cms.elasticsearch.content import SeoCmsContentDataDocument
from webapps.marketplace.cms.elasticsearch.feature_flag import SeoFeatureFlagDocument
from webapps.marketplace.cms.elasticsearch.region_category_listing import SeoRegionCategoryDocument
from webapps.marketplace.cms.elasticsearch.region_homepage import SeoRegionHomepageDocument
from webapps.marketplace.elasticsearch.elasticsearch import CmsContentDocument, SeoMetadataDocument
from webapps.user.elasticsearch.user import UserDocument


class QueriesCounter:
    def __init__(self, conn):
        self.conn = conn
        self._queries = []

    @property
    def queries_count(self) -> int:
        return len(self.conn.queries)

    def __enter__(self):
        reset_queries()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self._queries = copy.deepcopy(self.conn.queries)


def count_queries(conn: ConnectionProxy, document: t.Type[Document], obj: Model) -> dict:
    connections_count = {
        'get_queryset': 0,
        'to_representation': 0,
    }
    if not obj:
        return connections_count

    with QueriesCounter(conn) as counter:
        business = document.get_queryset([obj.id]).first()
        connections_count['get_queryset'] = counter.queries_count

    with QueriesCounter(conn) as counter:
        _ = getattr(document(), '_document').serializer.to_representation(business)
        connections_count['to_representation'] = counter.queries_count

    return connections_count


@lru_cache(maxsize=1)
def get_appointment_with_most_subbookings() -> Appointment:
    days_ago = tznow(UTC) - timedelta(days=90)
    return (
        Appointment.objects.filter(
            updated__gte=days_ago,
        )
        .annotate(
            subbookings_count=Count('bookings'),
        )
        .latest('subbookings_count')
    )


@lru_cache(maxsize=1)
def get_business_with_most_appointments() -> Business:
    return Business.objects.annotate(
        appointments_count=Count('appointments'),
    ).latest('appointments_count')


def check_business_document(conn) -> dict:
    business = get_business_with_most_appointments()
    return count_queries(conn, BusinessDocument, business)


def check_business_history_document(conn) -> dict:
    business = get_business_with_most_appointments()
    return count_queries(conn, BusinessHistoryDocument, business)


def check_business_category_document(conn) -> dict:
    business_category = BusinessCategoryDocument.get_queryset().last()
    return count_queries(conn, BusinessCategoryDocument, business_category)


def check_business_account_document(conn) -> dict:
    business = get_business_with_most_appointments()
    return count_queries(conn, BusinessAccountDocument, business)


def check_business_customer_document(conn) -> dict:
    days_ago = tznow(UTC) - timedelta(days=291)
    bci = (
        BusinessCustomerInfo.objects.filter(
            updated__gte=days_ago,
        )
        .annotate(
            appointments_count=Count('appointments'),
        )
        .latest('appointments_count')
    )

    return count_queries(conn, BusinessCustomerDocument, bci)


def check_cms_content_document(conn) -> dict:
    content = CmsContentDocument.get_queryset().last()
    return count_queries(conn, CmsContentDocument, content)


def check_seo_metadata_document(conn) -> dict:
    seo_metadata = SeoMetadataDocument.get_queryset().last()
    return count_queries(conn, SeoMetadataDocument, seo_metadata)


def check_user_document(conn):
    user = UserDocument.get_queryset().last()
    return count_queries(conn, UserDocument, user)


def check_seo_feature_flag_document(conn) -> dict:
    seo_feature_flag = SeoFeatureFlagDocument.get_queryset().last()
    return count_queries(conn, SeoFeatureFlagDocument, seo_feature_flag)


def check_seo_region_homepage_document(conn) -> dict:
    seo_region_homepage = SeoRegionHomepageDocument.get_queryset().last()
    return count_queries(conn, SeoRegionHomepageDocument, seo_region_homepage)


def check_cms_content_data_document(conn) -> dict:
    seo_cms_content_data = SeoCmsContentDataDocument.get_queryset().last()
    return count_queries(conn, SeoCmsContentDataDocument, seo_cms_content_data)


def check_seo_region_category_document(conn) -> dict:
    seo_region_category_document = SeoRegionCategoryDocument.get_queryset().last()
    return count_queries(conn, SeoRegionCategoryDocument, seo_region_category_document)


CHECKS = {
    BusinessAccountDocument: check_business_account_document,
    BusinessCategoryDocument: check_business_category_document,
    BusinessCustomerDocument: check_business_customer_document,
    BusinessDocument: check_business_document,
    BusinessHistoryDocument: check_business_history_document,
    CmsContentDocument: check_cms_content_document,
    SeoCmsContentDataDocument: check_cms_content_data_document,
    SeoFeatureFlagDocument: check_seo_feature_flag_document,
    SeoMetadataDocument: check_seo_metadata_document,
    SeoRegionCategoryDocument: check_seo_region_category_document,
    SeoRegionHomepageDocument: check_seo_region_homepage_document,
    UserDocument: check_user_document,
}


def count_queries_in_parallel():
    from django.db import connection

    with futures.ThreadPoolExecutor(max_workers=8) as executor:
        tasks = {doc: executor.submit(func, connection) for doc, func in CHECKS.items()}
    futures.as_completed(tasks.values())
    return {doc: future.result() for doc, future in tasks.items()}


def check_queries_count() -> bool:
    start = time.perf_counter()
    checks = count_queries_in_parallel()
    finish = time.perf_counter()

    has_extra_queries = False

    for doc_name, result in checks.items():
        status = '🟢'
        if result.get('get_queryset') == 0:
            status = '🟠'
        elif result.get('to_representation') > result.get('get_queryset'):
            status = '🔴'
            has_extra_queries = True

        print(f'{doc_name.__name__:<30} {status}: {result=}')

    print(f'{len(checks)} finished in {finish - start:0.3f}')
    return has_extra_queries


if __name__ == '__main__':
    if check_queries_count():
        sys.exit(1)
