import argparse

# pylint: disable=logging-fstring-interpolation
import logging
from io import BytesIO
from pathlib import PosixPath
import pandas as pd

import django


django.setup()  # noqa
# pylint: disable=wrong-import-position
from rest_framework import serializers
from tqdm import tqdm
from lib import gcs
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import SubjectType, ExperimentVariants
from lib.feature_flag.experiment.boost import BoostFraudSuspicionWarningExperiment
from lib.tools import tznow
from lib.tools import retry
from webapps.boost.enums import BoostFraudWarningType
from webapps.boost.models import BoostFraudSuspicion

logger = logging.getLogger('booksy.boost_warnings')


class BoostFraudSuspicionSerializer(serializers.ModelSerializer):
    class Meta:
        model = BoostFraudSuspicion
        fields = (
            'business_id',
            'warning_type',
            'warning_visible',
        )


def _get_data(filename, ticket_id):
    storage = gcs.GCSClient()

    filepath = str(PosixPath(ticket_id) / f'{filename}.csv')
    data = storage.get_data(filepath, as_bytes=True)
    if not data:
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f'The file {filepath} does not exist.'
        )

    return pd.read_csv(BytesIO(data), keep_default_na=False).to_dict(orient='records')


def _upload_report(filename, ticket_id, data):
    storage = gcs.GCSClient()

    time_ = str(tznow().timestamp()).replace('.', '')
    storage.save_data(str(PosixPath(ticket_id) / f'{filename}_report_{time_}.csv'), data)


def _get_parser():
    parser = argparse.ArgumentParser()
    parser.add_argument('filename')
    parser.add_argument('--ticket', type=str, required=True)
    parser.add_argument('--dry-run', action='store_true')
    parser.add_argument('--force-delete', action='store_true')
    parser.add_argument(
        '--warning-type',
        type=str,
        choices=['unfinished_appointments', 'price_manipulation'],
        help=(
            "Specify the type of warning"
            " (choose from 'unfinished_appointments' or 'price_manipulation')"
        ),
    )
    return parser


@retry(retries=2, exceptions=(ValueError,), delay=1)
def _assign_experiment_variant(business_id):
    experiment_variant = BoostFraudSuspicionWarningExperiment(
        UserData(
            subject_key=business_id,
            subject_type=SubjectType.BUSINESS_ID.value,
            is_experiment=True,
        )
    )
    logger.info(f'Assigned variant {experiment_variant} to business {business_id}')
    if not experiment_variant:
        raise ValueError('Could not assign experiment variant')
    return experiment_variant


def _save_in_db(data_row, warning_type):
    suspicious_id = data_row.pop('id', None)
    business_id = int(data_row['business_id'])
    suspicious_id = None if suspicious_id == '' else suspicious_id
    instance = (
        BoostFraudSuspicion.objects.filter(id=suspicious_id).first() if suspicious_id else None
    )
    experiment_variant = _assign_experiment_variant(business_id)
    data_row['warning_visible'] = experiment_variant == ExperimentVariants.VARIANT_A
    data_row['warning_type'] = warning_type

    serializer = BoostFraudSuspicionSerializer(instance=instance, data=data_row)
    serializer.is_valid(raise_exception=True)
    instance = serializer.save()
    data_row['id'] = instance.id


def _delete_from_db(data_row):
    business_id = str(data_row['business_id'])
    suspicious_to_delete = BoostFraudSuspicion.objects.filter(business_id=business_id)
    logger.info(f'Deleting: {suspicious_to_delete}...')
    suspicious_to_delete.delete()


def run():
    parser = _get_parser()
    args = parser.parse_args()

    data = _get_data(filename=args.filename, ticket_id=args.ticket)

    if not args.force_delete and args.warning_type is None:
        parser.error("The --warning-type argument is required when --force-delete is not used.")

    for row in tqdm(data):
        if args.dry_run:
            logger.info(f'[dry-run] Saving data row: {row}...')
        elif args.force_delete:
            logger.info(f'Deleting data row: {row}...')
            _delete_from_db(row)
        else:
            warning_type = {
                'unfinished_appointments': BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT,
                'price_manipulation': BoostFraudWarningType.PRICE_MANIPULATIONS,
            }[args.warning_type]

            logger.info(f'Saving data row: {row}; {warning_type=}...')

            _save_in_db(row, warning_type)
            _upload_report(
                filename=args.filename,
                ticket_id=args.ticket,
                data=pd.DataFrame(data).to_csv(index=False),
            )


if __name__ == '__main__':
    run()
