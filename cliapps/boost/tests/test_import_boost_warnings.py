import argparse
import random
import typing
from dataclasses import astuple, dataclass, fields
from unittest.mock import patch

import pytest
from mock.mock import Mock
from model_bakery import baker

from cliapps.boost.import_boost_warnings import run
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.boost import BoostFraudSuspicionWarningExperiment
from lib.tests.utils import override_eppo_feature_flag
from webapps.boost.enums import BoostFraudWarningType
from webapps.boost.models import BoostFraudSuspicion


@dataclass
class BoostFraudSuspicionData:
    business_id: int
    id: str = ''
    created: str = '2024-01-01'


def _get_row(boost_fraud_suspicious_data: BoostFraudSuspicionData):
    row = [str(field_value) for field_value in astuple(boost_fraud_suspicious_data)]

    return ','.join(row) + '\n'


def _get_mocked_input_file(boost_fraud_suspicious_list: typing.List[BoostFraudSuspicionData]):
    columns = [field.name for field in fields(BoostFraudSuspicionData)]
    input_file = f"{','.join(columns)}\n"
    for boost_fraud_suspicious in boost_fraud_suspicious_list:
        input_file += _get_row(boost_fraud_suspicious)

    return input_file.encode()


@patch('cliapps.boost.import_boost_warnings._get_data', Mock(return_value={}))
def test_import_requires_warning_type():
    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
        warning_type=None,
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        with pytest.raises(SystemExit):
            run()


@pytest.mark.django_db
def test_import_all_rows(gcs_client_mock):
    rows = [BoostFraudSuspicionData(business_id=random.randint(1, 1000)) for i in range(5)]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(rows))

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
        warning_type='price_manipulation',
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    assert BoostFraudSuspicion.objects.all().count() == 5


@pytest.mark.django_db
@override_eppo_feature_flag(
    {BoostFraudSuspicionWarningExperiment.flag_name: ExperimentVariants.VARIANT_A.value}
)
def test_import_warning_visible(gcs_client_mock):
    business_id = 1234
    rows = [BoostFraudSuspicionData(business_id=business_id)]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(rows))

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
        warning_type='unfinished_appointments',
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    assert BoostFraudSuspicion.objects.all().count() == 1
    assert BoostFraudSuspicion.objects.first().warning_visible is True
    assert BoostFraudSuspicion.objects.first().business_id == business_id
    assert (
        BoostFraudSuspicion.objects.first().warning_type
        == BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT
    )


@pytest.mark.django_db
@override_eppo_feature_flag(
    {BoostFraudSuspicionWarningExperiment.flag_name: ExperimentVariants.CONTROL.value}
)
def test_import_warning_not_visible(gcs_client_mock):
    business_id = 1234
    rows = [BoostFraudSuspicionData(business_id=business_id)]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(rows))

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
        warning_type='price_manipulation',
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    assert BoostFraudSuspicion.objects.all().count() == 1
    assert BoostFraudSuspicion.objects.first().warning_visible is False
    assert BoostFraudSuspicion.objects.first().business_id == business_id
    assert (
        BoostFraudSuspicion.objects.first().warning_type
        == BoostFraudWarningType.PRICE_MANIPULATIONS
    )


@pytest.mark.django_db
def test_update(gcs_client_mock):
    business_id = 1234
    business_fraud_suspicious = baker.make(
        BoostFraudSuspicion,
        business_id=business_id,
        warning_visible=True,
        warning_type=BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT,
    )
    updated_rows = [
        BoostFraudSuspicionData(business_id=business_id, id=business_fraud_suspicious.id)
    ]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(updated_rows))

    # Before update:
    assert BoostFraudSuspicion.objects.all().count() == 1
    assert BoostFraudSuspicion.objects.first().warning_visible is True
    assert BoostFraudSuspicion.objects.first().business_id == business_id
    assert (
        BoostFraudSuspicion.objects.first().warning_type
        == BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT
    )

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
        warning_type='price_manipulation',
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    # After update:
    assert BoostFraudSuspicion.objects.all().count() == 1
    assert BoostFraudSuspicion.objects.first().warning_visible is False
    assert BoostFraudSuspicion.objects.first().business_id == business_id
    assert (
        BoostFraudSuspicion.objects.first().warning_type
        == BoostFraudWarningType.PRICE_MANIPULATIONS
    )


@pytest.mark.django_db
def test_delete(gcs_client_mock):
    business_id = 1234
    business_fraud_suspicious = baker.make(
        BoostFraudSuspicion,
        business_id=business_id,
        warning_visible=True,
        warning_type=BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT,
    )
    rows_to_delete = [
        BoostFraudSuspicionData(business_id=business_id, id=business_fraud_suspicious.id)
    ]
    gcs_client_mock.return_value.get_data = Mock(
        return_value=_get_mocked_input_file(rows_to_delete)
    )

    # Before update:
    assert BoostFraudSuspicion.objects.all().count() == 1
    assert BoostFraudSuspicion.objects.first().warning_visible is True
    assert BoostFraudSuspicion.objects.first().business_id == business_id
    assert (
        BoostFraudSuspicion.objects.first().warning_type
        == BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT
    )

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=True,
        warning_type=None,
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    # After update:
    assert BoostFraudSuspicion.objects.all().count() == 0
