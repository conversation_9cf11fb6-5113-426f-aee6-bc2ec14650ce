from datetime import datetime, timedelta

import pytest
import pytz
from freezegun import freeze_time

from cliapps.boost.fix_chargeable_appointments import run, get_clients_requiring_fixing
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.boost import RefactorSetChargeableTaskFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.boost.enums import BoostClientCardStatus
from webapps.business.models import Business
from webapps.marketplace.models import BoostClientCard
from webapps.marketplace.tasks import set_chargeable_task
from webapps.marketplace.tests.utils import (
    ExpectedChargeablePayable,
    assert_appointment_chargeable_payable,
    assert_bci_and_appointment_setup,
    assert_bci_first_appointment,
    prepare_bci,
)

_BASE_DATETIME = datetime(2023, 4, 1, 1, tzinfo=pytz.utc)
_SUBBOOKING_DATA = {
    'booked_from': _BASE_DATETIME - timedelta(hours=10),
    'booked_till': _BASE_DATETIME - timedelta(hours=9),
}


def create_appointment_(bci, subbookings=None, source_chargeable=True, **kwargs):
    subbookings = [{**_SUBBOOKING_DATA}] if not subbookings else subbookings
    source = get_or_create_booking_source(chargeable=source_chargeable)
    common_kwargs = {
        'source': source,
        'status': Appointment.STATUS.FINISHED,
        'business': bci.business,
        'booked_for': bci,
        'chargeable': False,
        'updated_by': bci.business.owner,
        'type': Appointment.TYPE.CUSTOMER,
    }
    common_kwargs.update(kwargs)
    appointment = create_appointment(subbookings, **common_kwargs)

    if bci.first_appointment is None:
        bci.first_appointment = appointment
        bci.save()

    return appointment


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
@override_eppo_feature_flag({RefactorSetChargeableTaskFlag.flag_name: False})
def test_fix_appointment_that_should_be_chargeable_but_is_not():
    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_appointment_(bci)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    set_chargeable_task(appointment.id)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with freeze_time(_BASE_DATETIME + timedelta(weeks=30)):
        run(bci.business.id, bci.business.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_fix_appointment_that_should_not_be_chargeable_but_is():
    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_appointment_(bci)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag({RefactorSetChargeableTaskFlag.flag_name: False}):
        set_chargeable_task(appointment.id)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    new_date = _BASE_DATETIME + timedelta(weeks=30)
    with freeze_time(_BASE_DATETIME + timedelta(weeks=30)):
        subbooking_data = {
            'booked_from': new_date + timedelta(days=1, hours=1),
            'booked_till': new_date + timedelta(days=1, hours=2),
        }
        second_appointment = create_appointment_(bci, subbookings=[{**subbooking_data}])
        with override_eppo_feature_flag({RefactorSetChargeableTaskFlag.flag_name: True}):
            set_chargeable_task(second_appointment.id)

    assert_bci_and_appointment_setup(
        bci, second_appointment, ExpectedChargeablePayable(True, False)
    )

    run(bci.business.id, bci.business.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_do_not_modify_payable_appointment():
    bci = prepare_bci(Business.BoostStatus.ENABLED)
    appointment = create_appointment_(bci)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    set_chargeable_task(appointment.id)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(True, True))

    run(bci.business.id, bci.business.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, True))


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_skip_exempted_client():
    bci = prepare_bci(Business.BoostStatus.DISABLED)
    create_appointment_(bci)

    assert get_clients_requiring_fixing(bci.business.id, bci.business.id), 'wrong setup'

    bcc = BoostClientCard.get_or_create_related(bci)
    bcc.status = BoostClientCardStatus.EXEMPTED
    bcc.save()

    assert not get_clients_requiring_fixing(bci.business.id, bci.business.id)
