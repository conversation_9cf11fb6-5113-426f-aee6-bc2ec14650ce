id: AppointmentDetails
required:
  - _version
  - customer
  - subbookings
  - overbooking
  - _notify_about_reschedule
  - dry_run
properties:
    # two-way fields
    _version:
        type: integer
        description: (not required in POST) Appointment version number
    subbookings:
        type: array
        items:
            type: SubbookingDetails
        description: a list of subbookings
    customer:
        type: CustomerMultiMode
        description: multi-mode customer object (can be null in dry_run)
    business_note:
        type: string
        description: note added by Business
    business_secret_note:
        type: string
        description: secret note added by Business (visible only to Business)
    bci_agreements:
        type: BCIAgreementsDetails
        description: business customer agreements

    # read-only fields
    appointment_uid:
      type: integer
      description: (read-only) unique Appointment ID
    appointment_id:
        type: integer
        description: >
            (read-only) Appointment ID
            (unique only together with Appointment type)
            [will be deprecated soon:
            use appointment_uid instead of appointment_id and appointment_type]
    appointment_type:
        type: string
        description: (read-only) Appointment type
        enum:
          - single
          - multi
    booked_from:
        type: string
        format: date-time
        description: (read-only) calculated start of the Appointment
    booked_till:
        type: string
        format: date-time
        description: (read-only) calculated end of the Appointment
    status:
        type: string
        enum_from_const: webapps.booking.enums.AppointmentStatusChoices
        description: (read-only) Appointment status
    type:
        type: string
        enum_from_const: booking.Appointment.TYPE_CHOICES
        description: (read-only) customer or business originated booking
    actions:
        type: BookingActions
        description: (read-only) what actions are allowed
    payment_info:
        type: BookingPaymentInfo
        description: >
            (read-only) (can be null)
            payment and deposit info for POS enabled businesses
    repeating:
        type: AppointmentRepeatingInfo
        description: (read-only) info about repeating appointments
    repeating_series:
        type: AppointmentRepeatingInfo
        description: info about repeating series
    customer_note:
        type: string
        description: (read-only) note added by Customer
    total:
        type: string
        description: (read-only) formatted total price of all services
    external_source:
        type: string
        description:
            (read-only) (can be null)
            codename of external booking partner
        enum:
          - ""
          - google
    consent_forms:
        type: array
        items:
            type: AppointmentConsentForm
        description: list of required consent forms for the appointment
        readOnly: true

    # write-only fields
    overbooking:
        type: boolean
        description: (write-only) Should overbooking be allowed
    _notify_about_reschedule:
        type: boolean
        description: >
            (write-only)
            Should customer be notified about reschedule
            (it applies only if booking time was changed)
            (in forms initially set it to true for customer bookings
            and to false for business bookings)
    dry_run:
        type: boolean
        description: >
            (write-only)
            Dry Mode does not save anything, only validates and plans.
    _preserve_order:
        type: boolean
        defaultValue: False
        description: >
            (write-only)
            When doing dry run for multibooking try to preserve order
            of the subbookings.
    new_repeating:
        type: AppointmentRepeatingInfo
        description: (write-only) specification for new repeated appointments
    _update_future_bookings:
        type: boolean
        description: >
            (write-only)
            If there is an existing non-custom repeating booking then changes
            can be applied only to the current appointment or to the current
            appointment and all the appointments after it in the series.
        default: false
    _notification_enabled:
        type: boolean
        description: >
            (write-only)
            Should be available only in super-user mode in process of booking creation.
            If true no changes will be applied, otherwise no notification will be send
            ( that should be send during business booking creation, invite to customer etc.).
            WARNING DO nothing on PUT (EDIT appointment)
        default: true
    no_thumbs:
        type: boolean
    traveling:
        type: AppointmentTraveling
    from_promo:
        type: boolean
    partner_app_data:
        type: array
        items:
            type: object
            description: Partner app client_id and metadata
        description: (Read Only) List of external Partner apps metadata
    with_prepayment:
        type: boolean
        description: (optional) merchant will to charge prepayment if it is possible
    is_deposit_available:
        type: boolean
        description: (read-only) is deposit available for appointment
    is_booksy_gift_card_appointment:
        type: boolean
        description: >
            (read only) determines if Booksy Gift Card has been used as a payment
            method.
            
