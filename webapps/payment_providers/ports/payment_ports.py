import datetime
import uuid
from typing import (
    Optional,
)

from lib.payment_providers.entities import (
    AuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
    ConnectionTokenEntity,
    PaymentClientTokenEntity,
    PaymentTokenEntity,
    PaymentTokenExternalData,
    PortResponse,
    TokenizedPMExternalData,
)
from lib.payment_providers.enums import (
    PaymentMethodType,
    ResponseEntityType,
    TokenizedPaymentMethodType,
)
from lib.payment_providers.errors import (
    PaymentPortError,
    PaymentPortErrorCodes,
)
from lib.payments.enums import (
    PaymentProviderCode,
    PayoutType,
)
from webapps.payment_providers.exceptions.common import (
    MissingCustomer,
    ProviderNotSupportException,
    TokenizedPaymentMethodException,
)
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    Payment,
    Payout,
    SetupIntent,
    TokenizedPaymentMethod,
    TransferFund,
)
from webapps.payment_providers.services.common import (
    CommonPaymentMethodServices,
    CommonPaymentOperationServices,
    CommonPaymentServices,
    CommonPayoutServices,
    CommonTerminalServices,
    CommonTransferFundServices,
)


class PaymentProvidersPaymentPort:
    @staticmethod
    def initialize_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        account_holder_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
        payment_method_type: PaymentMethodType,
        amount: int,
        fee_amount: int,
        payment_token: str = None,
        auto_capture: bool = False,
        customer_id: uuid.UUID = None,
        metadata: dict = None,
        additional_data: dict = None,
    ) -> PortResponse:
        """
        Creates Payment object (money transfer from Customer to AccountHolder)

        :param account_holder_id: AccountHolder.id
        :param payment_provider_code: payment provider implementation to be used
        :param payment_method_type: Payment method
        :param amount: Payment amount
        :param fee_amount: Payment fee amount
        :param payment_token: payment token
        :param auto_capture: determines if Payment should be automatically captured
            after receiving proper webhook notification
        :param customer_id: Customer.id
        :param metadata: any custom data to be stored in Payment object
        :param additional_data: additional data that have to be passed due to concrete psp
            implementation (i.e. psp radar data like customers IP address)
        """
        customer = Customer.objects.get(id=customer_id) if customer_id else None
        account_holder = AccountHolder.objects.get(id=account_holder_id)
        payment = CommonPaymentServices.initialize_payment(
            customer=customer,
            account_holder=account_holder,
            payment_provider_code=payment_provider_code,
            payment_method_type=payment_method_type,
            amount=amount,
            fee_amount=fee_amount,
            payment_token=payment_token,
            metadata=metadata,
            additional_data=additional_data,
            auto_capture=auto_capture,
        )
        return PortResponse(
            entity=payment.entity,
            entity_type=ResponseEntityType.PAYMENT_ENTITY,
        )

    @staticmethod
    def get_payment(
        payment_id: uuid.UUID,
    ) -> PortResponse:
        """
        Returns Payment object entity

        :param payment_id: Payment.id
        """
        payment = Payment.objects.get(id=payment_id)
        payment_entity = payment.entity

        external_id = CommonPaymentServices.get_provider_payment_details(payment)
        payment_entity.provider_external_id = external_id
        if payment.tokenized_payment_method:
            payment_entity.tokenized_pm_id = payment.tokenized_payment_method.id

        return PortResponse(
            entity=payment_entity,
            entity_type=ResponseEntityType.PAYMENT_ENTITY,
        )

    @staticmethod
    def cancel_payment(payment_id: uuid.UUID) -> PortResponse:
        """
        Tries to cancel Payment

        :param payment_id: Payment.id
        """
        payment = Payment.objects.get(id=payment_id)
        CommonPaymentServices.cancel_payment(payment=payment)
        return PortResponse(
            entity=payment.entity,
            entity_type=ResponseEntityType.PAYMENT_ENTITY,
        )

    @staticmethod
    def modify_payment(payment_id: uuid.UUID, fee_amount: int) -> PortResponse:
        """
        Modifies fee amount

        :param payment_id: Payment.id
        :param fee_amount: fee amount
        """
        payment = Payment.objects.get(id=payment_id)
        payment = CommonPaymentServices.modify_payment(payment=payment, fee_amount=fee_amount)
        return PortResponse(
            entity=payment.entity,
            entity_type=ResponseEntityType.PAYMENT_ENTITY,
        )

    @staticmethod
    def set_default_tokenized_pm(
        customer_id: uuid.UUID,
        tokenized_payment_method_id: uuid.UUID,
    ) -> PortResponse:
        """
        Set existing TokenizedPaymentMethod as default
        Applies for given pm.payment_provider and pm.method_type
        Also set all other TokenizedPaymentMethods (for given pm type) as not default

        :param customer_id: Customer.id
        :param tokenized_payment_method_id: TokenizedPaymentMethod.id
        """
        customer = Customer.objects.get(id=customer_id)
        tokenized_pm = TokenizedPaymentMethod.objects.filter(
            customer=customer,
            id=tokenized_payment_method_id,
        ).last()
        if not tokenized_pm:
            raise TokenizedPaymentMethodException(
                "Couldn't find TokenizedPaymentMethod for given parameters",
            )

        CommonPaymentMethodServices.set_default_tokenized_pm(
            customer=customer,
            tokenized_payment_method=tokenized_pm,
        )
        return PortResponse(
            entity=tokenized_pm.entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )

    @staticmethod
    def initialize_setup_intent(
        customer_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
        method_type: PaymentMethodType = PaymentMethodType.CARD,
    ) -> PortResponse:
        """
        Initialize SetupIntent.
        Only for Stripe implementation.
        Used for Stripe Elements form (creating PaymentMethod/TokenizedPaymentMethod)

        :param customer_id: Customer.id
        :param payment_provider_code: payment provider implementation to be used
        :param method_type: PaymentMethodType
        """
        if payment_provider_code is not PaymentProviderCode.STRIPE:
            raise ProviderNotSupportException
        customer = Customer.objects.get(id=customer_id)
        setup_intent = CommonPaymentMethodServices.initialize_setup_intent(
            customer=customer,
            payment_provider_code=payment_provider_code,
            method_type=method_type,
        )
        return PortResponse(
            entity=setup_intent.entity,
            entity_type=ResponseEntityType.SETUP_INTENT_ENTITY,
        )

    @staticmethod
    def get_setup_intent(
        setup_intent_id: uuid.UUID,
        customer_id: uuid.UUID = None,
    ) -> PortResponse:
        """
        Returns SetupIntent object entity

        :param setup_intent_id: SetupIntent.id
        :param customer_id: Customer.id
        """
        setup_intent_qs = SetupIntent.objects.filter(
            id=setup_intent_id,
        )
        if customer_id:
            setup_intent_qs = setup_intent_qs.filter(
                customer_id=customer_id,
            )
        setup_intent = setup_intent_qs.first()
        return PortResponse(
            entity=setup_intent.entity if setup_intent else None,
            entity_type=ResponseEntityType.SETUP_INTENT_ENTITY,
        )

    @staticmethod
    def add_tokenized_pm(
        customer_id: uuid.UUID,
        external_data: TokenizedPMExternalData,
        payment_provider_code: PaymentProviderCode,
        method_type: TokenizedPaymentMethodType,
    ) -> PortResponse:
        """
        Creates instance of TokenizedPaymentMethod object

        :param customer_id: Customer.id
        :param external_data: tokenized data ie. for Stripe {'external_id': 'asd123'}
        :param payment_provider_code: payment provider implementation to be used
        :param method_type: PaymentMethodType
        """
        customer = Customer.objects.get(id=customer_id)
        tokenized_pm = CommonPaymentMethodServices.add_tokenized_pm(
            customer=customer,
            external_data=external_data,
            payment_provider_code=payment_provider_code,
            method_type=method_type,
        )

        return PortResponse(
            entity=tokenized_pm.entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )

    @staticmethod
    def remove_tokenized_pm(
        tokenized_payment_method_id: uuid.UUID,
        customer_id: uuid.UUID,
    ) -> PortResponse:
        """
        Removes TokenizedPaymentMethod object by soft deleting

        :param tokenized_payment_method_id: TokenizedPaymentMethod.id
        :param customer_id: Customer.id
        """
        errors = []
        try:
            tokenized_pm = TokenizedPaymentMethod.objects.get(
                id=tokenized_payment_method_id,
                customer_id=customer_id,
            )
        except TokenizedPaymentMethod.DoesNotExist:
            errors.append(
                PaymentPortError(
                    code=PaymentPortErrorCodes.OBJECT_DOES_NOT_EXISTS,
                    message='TokenizedPaymentMethod does not exists',
                ),
            )
        else:
            CommonPaymentMethodServices.remove_tokenized_pm(
                tokenized_payment_method=tokenized_pm,
            )
        return PortResponse(
            entity=None,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_OK,
            errors=errors,
        )

    @staticmethod
    def validate_tokenized_pm(
        tokenized_pm_id: uuid.UUID | None,
        expiration_date_validation: datetime.date = None,
    ):
        """
        Pre-validate customer payment card before request to Stripe provider.

        :param tokenized_pm_id: PaymentMethod field which relates to TokenizedPaymentMethod
        :param expiration_date_validation: date due to which to check card expiration date
        """
        if not tokenized_pm_id:
            return PortResponse(
                entity=None,
                entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_VALIDATION,
                errors=[None],
            )
        tokenized_pm = TokenizedPaymentMethod.objects.filter(
            id=tokenized_pm_id,
        ).first()
        if not tokenized_pm:
            return PortResponse(
                entity=None,
                entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_VALIDATION,
                errors=[None],
            )
        error = CommonPaymentMethodServices.validate_tokenized_pm(
            tokenized_pm=tokenized_pm,
            expiration_date_validation=expiration_date_validation,
        )
        return PortResponse(
            entity=None,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_VALIDATION,
            errors=[error],
        )

    @staticmethod
    def get_tokenized_pm(
        tokenized_payment_method_id: uuid.UUID,
        customer_id: uuid.UUID,
    ) -> PortResponse:
        """
        Get TokenizedPaymentMethod object

        :param tokenized_payment_method_id: TokenizedPaymentMethod.id
        :param customer_id: Customer.id
        """
        tokenized_pm = TokenizedPaymentMethod.objects.filter(
            id=tokenized_payment_method_id,
            customer_id=customer_id,
        ).first()
        if not tokenized_pm:
            raise TokenizedPaymentMethodException(
                "Couldn't find TokenizedPaymentMethod for given parameters",
            )
        return PortResponse(
            entity=tokenized_pm.entity,
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY,
        )

    @staticmethod
    def list_tokenized_pms(
        customer_id: uuid.UUID,
        payment_provider_code: Optional[PaymentProviderCode] = None,
        method_type: Optional[PaymentMethodType] = None,
        default: Optional[bool] = None,
    ) -> PortResponse:
        """
        Return list of TokenizedPaymentMethod objects for given criteria

        :param customer_id: Customer.id
        :param payment_provider_code: payment provider implementation to be used
        :param method_type: PaymentMethodType
        :param default: is default TokenizedPaymentMethod
        """
        customer = Customer.objects.get(id=customer_id)
        tokenized_pms = CommonPaymentMethodServices.list_tokenized_pms(
            customer=customer,
            payment_provider_code=payment_provider_code,
            method_type=method_type,
            default=default,
        )

        return PortResponse(
            entity=[tokenized_pm.entity for tokenized_pm in tokenized_pms],
            entity_type=ResponseEntityType.TOKENIZED_PAYMENT_METHOD_ENTITY_LIST,
        )

    @staticmethod
    def create_payment_token(
        payment_provider_code: PaymentProviderCode,
        external_data: PaymentTokenExternalData,
    ) -> PortResponse:
        """
        Creates provider payment token from received external_data

        :param payment_provider_code: payment provider implementation to be used
        :param external_data: external data based on provider
        """
        payment_token = CommonPaymentMethodServices.create_payment_token(
            payment_provider_code=payment_provider_code,
            external_data=external_data,
        )
        return PortResponse(
            entity=PaymentTokenEntity(id=payment_token),
            entity_type=ResponseEntityType.PAYMENT_TOKEN_ENTITY,
        )

    @staticmethod
    def authorize_payment(
        payment_id: uuid.UUID,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        additional_data: AuthAdditionalDataEntity | None = None,
        off_session: bool | None = None,
    ):
        """
        Tries to authorize Payment with given TokenizedPaymentMethod

        :param payment_id: Payment.id
        :param payment_method_data: Data related to payment method
        :param additional_data: additional data that have to be passed due to concrete psp
            implementation (i.e. psp radar data like customers IP address)
        :param off_session: Set to true to indicate that the customer
            is not in checkout flow during this payment attempt
        """
        payment = Payment.objects.get(id=payment_id)
        if (
            payment.payment_method
            not in (PaymentMethodType.BLIK, PaymentMethodType.KEYED_IN_PAYMENT)
            and not payment.customer
        ):
            raise MissingCustomer(f'To authorize payment_id: {payment.id} customer is required')

        payment = CommonPaymentServices.authorize_payment(
            payment=payment,
            payment_method_data=payment_method_data,
            additional_data=additional_data,
            off_session=off_session,
        )

        return PortResponse(
            entity=payment.entity,
            entity_type=ResponseEntityType.PAYMENT_ENTITY,
        )

    @staticmethod
    def capture_payment(
        payment_id: uuid.UUID,
    ):
        """
        Tries to capture Payment

        :param payment_id: Payment.id
        """
        payment = Payment.objects.get(id=payment_id)
        payment = CommonPaymentServices.capture_payment(
            payment,
        )

        return PortResponse(
            entity=payment.entity,
            entity_type=ResponseEntityType.PAYMENT_ENTITY,
        )

    @staticmethod
    def get_payment_client_token(payment_id: uuid.UUID) -> PortResponse:
        """
        Retrieves Payment.client_secret token used in BCR flow

        :param payment_id: Payment.id
        """
        payment = Payment.objects.get(id=payment_id)
        client_token = CommonPaymentServices.get_payment_client_token(payment=payment)

        entity = PaymentClientTokenEntity(token=client_token)
        return PortResponse(
            entity=entity,
            entity_type=ResponseEntityType.PAYMENT_CLIENT_TOKEN,
        )

    @staticmethod
    def initialize_transfer_fund(  # pylint: disable=too-many-arguments
        sender_account_holder_id: uuid.UUID,
        receiver_account_holder_id: uuid.UUID,
        amount: int,
        payment_provider_code: PaymentProviderCode,
    ):
        """
        Creates TransferFund object (money transfer from AccountHolder to AccountHolder)

        :param sender_account_holder_id: sender AccountHolder.id
        :param receiver_account_holder_id: receiver AccountHolder.id
        :param amount: TransferFund amount
        :param payment_provider_code: payment provider implementation to be used
        """
        sender_account_holder = AccountHolder.objects.get(id=sender_account_holder_id)
        receiver_account_holder = AccountHolder.objects.get(id=receiver_account_holder_id)
        transfer_fund = CommonTransferFundServices.initialize_transfer_fund(
            sender_account_holder=sender_account_holder,
            receiver_account_holder=receiver_account_holder,
            amount=amount,
            payment_provider_code=payment_provider_code,
        )
        return PortResponse(
            entity=transfer_fund.entity,
            entity_type=ResponseEntityType.TRANSFER_FUND_ENTITY,
        )

    @staticmethod
    def process_transfer_fund(
        transfer_fund_id: uuid.UUID,
        additional_data=None,
        refund_fee_metadata: dict[str, str] = None,
        metadata: dict[str, str] | None = None,
    ):
        """
        Process TransferFund object

        :param transfer_fund_id: TransferFund.id
        :param additional_data: additional data that have to be passed due to concrete psp
            implementation (i.e. psp radar data like customers IP address)
        :param metadata: values expected to be in psp Transfer metadata
        """
        transfer_fund = TransferFund.objects.get(id=transfer_fund_id)
        transfer_fund = CommonTransferFundServices.process_transfer_fund(
            transfer_fund=transfer_fund,
            additional_data=additional_data,
            refund_fee_metadata=refund_fee_metadata,
            metadata=metadata,
        )
        return PortResponse(
            entity=transfer_fund.entity,
            entity_type=ResponseEntityType.TRANSFER_FUND_ENTITY,
        )

    @staticmethod
    def get_transfer_fund(
        transfer_fund_id: uuid.UUID,
    ):
        """
        Retrieving a TransferFund object

        :param transfer_fund_id: transfer_fund id
        """
        transfer_fund = CommonTransferFundServices.get_transfer_fund(
            transfer_fund_id=transfer_fund_id,
        )
        if transfer_fund is None:
            return None

        transfer_fund_entity = transfer_fund.entity
        provider_external_id, created = (
            CommonTransferFundServices.get_provider_transfer_fund_details(
                transfer_fund=transfer_fund,
            )
        )
        transfer_fund_entity.provider_external_id = provider_external_id
        transfer_fund_entity.created = created

        return PortResponse(
            entity=transfer_fund_entity,
            entity_type=ResponseEntityType.TRANSFER_FUND_ENTITY,
        )

    @staticmethod
    def initialize_payout(
        account_holder_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
        amount: int,
        payout_type: PayoutType,
    ) -> PortResponse:
        """
        Initializes Payout

        :param account_holder_id: AccountHolder.id
        :param payment_provider_code: payment provider implementation to be used
        :param amount: amount to be paid out
        :param payout_type: how the payout should be processed
        """
        account_holder = AccountHolder.objects.get(id=account_holder_id)

        payout = CommonPayoutServices.initialize_payout(
            account_holder=account_holder,
            payment_provider_code=payment_provider_code,
            amount=amount,
            payout_type=payout_type,
        )

        return PortResponse(
            entity=payout,
            entity_type=ResponseEntityType.PAYOUT_ENTITY,
        )

    @staticmethod
    def get_available_payout_amount(
        account_holder_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
    ) -> PortResponse:
        """
        Retrieves AccountHolder available balances

        :param account_holder_id: AccountHolder.id
        :param payment_provider_code: payment provider implementation to be used
        """
        account_holder = AccountHolder.objects.get(id=account_holder_id)

        amounts = CommonPayoutServices.get_available_payout_amount(
            account_holder=account_holder,
            payment_provider_code=payment_provider_code,
        )
        return PortResponse(
            entity=amounts,
            entity_type=ResponseEntityType.AVAILABLE_PAYOUT_AMOUNTS,
        )

    @staticmethod
    def get_payout_details(
        payout_id: uuid.UUID,
    ) -> PortResponse:
        """
        Returns payout details entity

        :param payout_id: Payout.id
        """
        payout = Payout.objects.get(id=payout_id)
        payout_details = CommonPayoutServices.get_payout_details(payout=payout)

        return PortResponse(
            entity=payout_details,
            entity_type=ResponseEntityType.PAYOUT_DETAILS_ENTITY,
        )

    @staticmethod
    def send_for_refund(
        payment_id: str,
        amount: int,
    ) -> PortResponse:
        """
        Sends given amount for refund and creates PaymentOperation object

        :param payment_id: Payment.id
        :param amount: amount to be refunded (could not be greater than Payment.amount)
        """

        payment = Payment.objects.get(id=payment_id)
        payment_operation = CommonPaymentOperationServices.send_for_refund(
            payment=payment,
            amount=amount,
        )
        return PortResponse(
            entity=payment_operation.entity,
            entity_type=ResponseEntityType.PAYMENT_OPERATION_ENTITY,
        )

    @staticmethod
    def get_terminal_connection_token(
        payment_provider_code: PaymentProviderCode,
    ) -> PortResponse:
        """
        Return connection token for initialization terminal connection (BCR)

        :param payment_provider_code: payment provider implementation to be used
        """
        token = CommonTerminalServices.get_terminal_connection_token(
            payment_provider_code=payment_provider_code
        )
        return PortResponse(
            entity=ConnectionTokenEntity(token=token),
            entity_type=ResponseEntityType.CONNECTION_TOKEN_ENTITY,
        )

    @staticmethod
    def mark_payment_as_failed(
        payment_id: str,
        error_code: Optional[str] = None,
    ) -> PortResponse:
        """
        Mark Payment object as failed
        Only for client-side payment methods

        :param payment_id: Payment.id
        :param error_code: error_code
        """
        payment = Payment.objects.get(id=payment_id)
        CommonPaymentServices.mark_payment_as_failed(
            payment=payment,
            error_code=error_code,
        )

        return PortResponse(
            entity=payment.entity,
            entity_type=ResponseEntityType.PAYMENT_ENTITY,
        )
