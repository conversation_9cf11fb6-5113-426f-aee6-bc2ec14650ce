import json
import uuid
from collections import OrderedDict

import mock
import pytest
import stripe
from django.test import override_settings

# TODO uncomment after uncommenting tests, phase3
# from django.conf import settings
# from django.urls import reverse
from django.utils.crypto import get_random_string
from mock.mock import MagicMock
from model_bakery import baker
from parameterized import parameterized
from rest_framework.test import APITestCase
from stripe.error import CardError

from lib.payment_providers.entities import AuthorizePaymentMethodDataEntity
from lib.payment_providers.enums import (
    PaymentMethodType,
    PaymentOperationType,
    PaymentStatus,
    StripeAccountType,
)
from lib.payments.enums import PaymentProviderCode, BGCReportingCategoryEnum, Currency
from webapps.payment_providers.consts.common import NotificationStatus
from webapps.payment_providers.consts.stripe import PaymentIntentStripeStatus
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    Notification,
    Payment,
    PaymentOperation,
    ProviderRequestLog,
    StripeAccountHolder,
    <PERSON>eCustomer,
    StripePaymentIntent,
    <PERSON><PERSON>R<PERSON><PERSON>,
    StripeTokenizedPaymentMethod,
    TokenizedPaymentMethod,
)
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort
from webapps.payment_providers.services.notification import NotificationServices
from webapps.payment_providers.tests.stripe import sample_stripe_notifications

TEST_STRIPE_ACCOUNT_ID = 'test_12345'


def construct_event(payload, sig_header, secret):
    if hasattr(payload, "decode"):
        payload = payload.decode("utf-8")
    data = json.loads(payload, object_pairs_hook=OrderedDict)
    return stripe.Event.construct_from(data, stripe.api_key)


def patch_payment_providers_payment_updated_event():
    return mock.patch(
        'lib.payment_providers.events.payment_providers_payment_updated_event.send',
    )


@pytest.mark.django_db
class TestStripePayments(APITestCase):
    def setUp(self):
        super().setUp()
        self.customer = baker.make(
            Customer,
        )
        self.stripe_customer = baker.make(
            StripeCustomer,
            customer=self.customer,
        )

        self.account_holder = baker.make(
            AccountHolder,
            id='0fe38c59-6450-4036-b5ff-************',
        )
        self.stripe_account_holder = baker.make(
            StripeAccountHolder,
            account_holder=self.account_holder,
            external_id="acct_123456",
            account_type=StripeAccountType.CUSTOM,
        )
        self.amount = 1000
        self.fee_amount = 100
        self.payment_method = PaymentMethodType.TERMINAL
        self.payment_provider = PaymentProviderCode.STRIPE

    @mock.patch('stripe.PaymentIntent.create')
    def test_initialize_payment_port__custom(self, stripe_provider_mock):
        stripe_provider_mock.side_effect = lambda **_: stripe.PaymentIntent(
            id=get_random_string(12),
        )
        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=self.payment_provider,
            payment_method_type=self.payment_method,
            amount=self.amount,
            fee_amount=self.fee_amount,
            auto_capture=True,
            customer_id=self.customer.id,
        ).entity.id
        StripePaymentIntent.objects.get(payment__id=payment_id)
        stripe_provider_mock.assert_called_once_with(
            transfer_data={'destination': self.stripe_account_holder.external_id},
            amount=1000,
            currency='USD',
            payment_method_types=['card_present'],
            capture_method='manual',
            application_fee_amount=100,
            customer=self.stripe_customer.external_id,
            metadata={
                'provider_request_id': ProviderRequestLog.objects.last().id,
                'reporting_category': None,
            },
            on_behalf_of=None,  # None for custom!
        )

    @mock.patch('stripe.PaymentIntent.create')
    def test_initialize_payment_port__express(self, stripe_provider_mock):
        stripe_provider_mock.side_effect = lambda **_: stripe.PaymentIntent(
            id=get_random_string(12),
        )
        self.stripe_account_holder.account_type = StripeAccountType.EXPRESS
        self.stripe_account_holder.save()
        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=self.payment_provider,
            payment_method_type=self.payment_method,
            amount=self.amount,
            fee_amount=self.fee_amount,
            auto_capture=True,
            customer_id=self.customer.id,
        ).entity.id
        StripePaymentIntent.objects.get(payment__id=payment_id)
        stripe_provider_mock.assert_called_once_with(
            transfer_data={'destination': self.stripe_account_holder.external_id},
            amount=1000,
            currency='USD',
            payment_method_types=['card_present'],
            capture_method='manual',
            application_fee_amount=100,
            customer=self.stripe_customer.external_id,
            metadata={
                'provider_request_id': ProviderRequestLog.objects.last().id,
                'reporting_category': None,
            },
            on_behalf_of=self.stripe_account_holder.external_id,  # not None for express!
        )

    @override_settings(CURRENCY_CODE=Currency.PLN)
    @mock.patch('stripe.PaymentIntent.create')
    def test_initialize_payment_port_blik(self, stripe_provider_mock):
        stripe_provider_mock.side_effect = lambda **_: stripe.PaymentIntent(
            id=get_random_string(12),
        )
        self.payment_method = PaymentMethodType.BLIK
        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=self.payment_provider,
            payment_method_type=self.payment_method,
            amount=self.amount,
            fee_amount=self.fee_amount,
            auto_capture=True,
            customer_id=self.customer.id,
        ).entity.id

        self.assertTrue(StripePaymentIntent.objects.filter(payment__id=payment_id).exists())
        stripe_provider_mock.assert_called_once_with(
            transfer_data={'destination': self.stripe_account_holder.external_id},
            amount=1000,
            currency=Currency.PLN,
            payment_method_types=[PaymentMethodType.BLIK],
            capture_method='automatic_async',
            application_fee_amount=100,
            customer=self.stripe_customer.external_id,
            metadata={
                'provider_request_id': ProviderRequestLog.objects.last().id,
                'reporting_category': None,
            },
            on_behalf_of=None,
        )

    @override_settings(STRIPE_ACCOUNT_ID=TEST_STRIPE_ACCOUNT_ID)
    @mock.patch('stripe.PaymentIntent.create')
    def test_initialize_payment_port__booksy_payment(self, stripe_provider_mock):
        stripe_provider_mock.side_effect = lambda **_: stripe.PaymentIntent(
            id=get_random_string(12),
        )
        self.stripe_account_holder.account_type = StripeAccountType.EXPRESS
        self.stripe_account_holder.external_id = TEST_STRIPE_ACCOUNT_ID
        self.stripe_account_holder.save()
        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=self.payment_provider,
            payment_method_type=PaymentMethodType.CARD,
            amount=self.amount,
            fee_amount=0,
            auto_capture=True,
            customer_id=self.customer.id,
            metadata={
                'booksy_gift_card_id': 'example_id',
                'booksy_gift_card_external_id': 'another_example_id',
                'reporting_category': BGCReportingCategoryEnum.CHARGE_BGC,
            },
        ).entity.id
        StripePaymentIntent.objects.get(payment__id=payment_id)
        stripe_provider_mock.assert_called_once_with(
            amount=1000,
            currency='USD',
            payment_method_types=['card'],
            capture_method='automatic_async',
            application_fee_amount=None,
            customer=self.stripe_customer.external_id,
            metadata={
                'provider_request_id': ProviderRequestLog.objects.last().id,
                'reporting_category': BGCReportingCategoryEnum.CHARGE_BGC,
            },
        )

    @override_settings(CURRENCY_CODE=Currency.PLN)
    @mock.patch('stripe.PaymentIntent.create')
    def test_initialize_payment_port_with_keyed_in_payment(self, stripe_provider_mock):
        payment_token = 'pm_1Q0PsIJvEtkwdCNYMSaVuRz6'
        stripe_provider_mock.side_effect = lambda **_: stripe.PaymentIntent(
            id=get_random_string(12),
        )
        self.payment_method = PaymentMethodType.KEYED_IN_PAYMENT
        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=self.payment_provider,
            payment_method_type=self.payment_method,
            amount=self.amount,
            fee_amount=self.fee_amount,
            auto_capture=True,
            customer_id=self.customer.id,
            payment_token=payment_token,
        ).entity.id

        self.assertTrue(StripePaymentIntent.objects.filter(payment__id=payment_id).exists())
        stripe_provider_mock.assert_called_once_with(
            transfer_data={'destination': self.stripe_account_holder.external_id},
            amount=1000,
            currency=Currency.PLN,
            payment_method_types=['card'],
            capture_method='automatic_async',
            application_fee_amount=100,
            customer=self.stripe_customer.external_id,
            metadata={
                'provider_request_id': ProviderRequestLog.objects.last().id,
                'reporting_category': None,
            },
            on_behalf_of=None,
            payment_method=payment_token,
            payment_method_options={'card': {'moto': True}},
            confirm=True,
        )

    @override_settings(CURRENCY_CODE=Currency.PLN)
    @mock.patch('stripe.PaymentIntent.create')
    def test_initialize_payment_port_with_keyed_in_payment_fail(self, stripe_provider_mock):
        payment_token = 'pm_1Q0PsIJvEtkwdCNYMSaVuRz6'
        self.payment_method = PaymentMethodType.KEYED_IN_PAYMENT

        stripe_provider_mock.side_effect = CardError(
            message="Your card was declined.",
            param=None,
            code="card_declined",
            http_status=402,
            json_body=sample_stripe_notifications.failed_card_declined_error_msg,
        )

        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=self.payment_provider,
            payment_method_type=self.payment_method,
            amount=self.amount,
            fee_amount=self.fee_amount,
            auto_capture=True,
            customer_id=self.customer.id,
            payment_token=payment_token,
        ).entity.id

        self.assertTrue(StripePaymentIntent.objects.filter(payment__id=payment_id).exists())
        stripe_provider_mock.assert_called_once_with(
            transfer_data={'destination': self.stripe_account_holder.external_id},
            amount=1000,
            currency=Currency.PLN,
            payment_method_types=['card'],
            capture_method='automatic_async',
            application_fee_amount=100,
            customer=self.stripe_customer.external_id,
            metadata={
                'provider_request_id': ProviderRequestLog.objects.last().id,
                'reporting_category': None,
            },
            on_behalf_of=None,
            payment_method=payment_token,
            payment_method_options={'card': {'moto': True}},
            confirm=True,
        )

    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port(self, _, stripe_provider_mock, _payment_update_signal_mock):
        stripe_provider_mock.confirm_payment_intent.return_value = (
            stripe.SetupIntent().construct_from(
                sample_stripe_notifications.payment_intent__created,
                key=stripe.api_key,
            )['data']['object']
        )
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
            provider_code=self.payment_provider,
            payment_method=self.payment_method,
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            payment=payment,
        )
        tokenized_pm = baker.make(
            TokenizedPaymentMethod,
        )
        stripe_tokenized_pm = baker.make(
            StripeTokenizedPaymentMethod,
            tokenized_payment_method=tokenized_pm,
        )
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=tokenized_pm.id,
            ),
        )
        stripe_provider_mock.confirm_payment_intent.assert_called_once_with(
            payment_intent=payment_intent,
            payment_method=stripe_tokenized_pm,
            payment_token=None,
            off_session=None,
            method_type=self.payment_method,
        )

    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port_with_google_pay_and_card_brand(
        self, _, stripe_provider_mock, _payment_update_signal_mock
    ):
        stripe_provider_mock.confirm_payment_intent.return_value = (
            stripe.PaymentIntent.construct_from(
                sample_stripe_notifications.payment_intent__succeeded_google_pay,
                key=stripe.api_key,
            )
        )
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
            provider_code=self.payment_provider,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            StripePaymentIntent,
            payment=payment,
        )
        tokenized_pm = baker.make(
            TokenizedPaymentMethod,
        )
        baker.make(
            StripeTokenizedPaymentMethod,
            tokenized_payment_method=tokenized_pm,
        )
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=tokenized_pm.id,
            ),
        )
        payment.refresh_from_db()
        assert payment.additional_data == {"card_brand": "visa"}

    @mock.patch('webapps.payment_providers.services.common.payment_providers_payment_updated_event')
    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port_with_early_finish(
        self,
        _,
        _payment_update_signal_mock,
        mock_payment_providers_payment_updated_event,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
            provider_code=self.payment_provider,
            payment_method=self.payment_method,
        )
        baker.make(
            StripePaymentIntent,
            payment=payment,
        )
        tokenized_pm = baker.make(
            TokenizedPaymentMethod,
            internal_status=TokenizedPaymentMethod.INTERNAL_STATUS.INVALID,
        )
        baker.make(
            StripeTokenizedPaymentMethod,
            tokenized_payment_method=tokenized_pm,
        )
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=tokenized_pm.id,
            ),
        )
        assert mock_payment_providers_payment_updated_event.send.call_count == 1

    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port_for_off_session_payment(
        self, _, stripe_provider_mock, _payment_update_signal_mock
    ):
        stripe_provider_mock.confirm_payment_intent.return_value = (
            stripe.SetupIntent().construct_from(
                sample_stripe_notifications.payment_intent__created,
                key=stripe.api_key,
            )['data']['object']
        )
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
            provider_code=self.payment_provider,
            payment_method=self.payment_method,
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            payment=payment,
        )
        tokenized_pm = baker.make(
            TokenizedPaymentMethod,
        )
        stripe_tokenized_pm = baker.make(
            StripeTokenizedPaymentMethod,
            tokenized_payment_method=tokenized_pm,
        )
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=tokenized_pm.id,
            ),
            off_session=True,
        )
        stripe_provider_mock.confirm_payment_intent.assert_called_once_with(
            payment_intent=payment_intent,
            payment_method=stripe_tokenized_pm,
            payment_token=None,
            off_session=True,
            method_type=self.payment_method,
        )

    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    def test_mark_payment_as_failed_port(self, stripe_provider_mock, _payment_update_signal_mock):
        stripe_provider_mock.cancel_payment_intent.return_value = True
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
            provider_code=self.payment_provider,
            payment_method=self.payment_method,
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            payment=payment,
        )
        PaymentProvidersPaymentPort.mark_payment_as_failed(
            payment_id=payment.id, error_code="sth went wrong"
        )
        stripe_provider_mock.cancel_payment_intent.assert_called_once_with(
            payment_intent=payment_intent,
        )
        payment.refresh_from_db()
        payment_intent.refresh_from_db()
        self.assertEqual(payment.status, PaymentStatus.AUTHORIZATION_FAILED)
        self.assertEqual(payment_intent.stripe_status, PaymentIntentStripeStatus.CANCELED)
        self.assertEqual(payment_intent.error_code, "sth went wrong")

    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    def test_capture_payment_port(self, stripe_provider_mock, _payment_update_signal_mock):
        payment = baker.make(
            Payment,
            status=PaymentStatus.AUTHORIZED,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
            provider_code=self.payment_provider,
            payment_method=self.payment_method,
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            payment=payment,
        )
        PaymentProvidersPaymentPort.capture_payment(payment.id)
        stripe_provider_mock.capture_payment_intent.assert_called_once_with(
            payment_intent=payment_intent,
        )

    # TODO uncomment after phase3, when we put events on new structure
    # @mock.patch('stripe.Webhook.construct_event', side_effect=construct_event)
    # @mock.patch('webapps.payment_providers.services.stripe.StripeProvider')
    # def test_payment_flow_without_auto_capture(self, stripe_provider_mock, construct_event_mock):
    #     stripe_provider_mock.create_payment_intent.return_value = mock.MagicMock(
    #         id='payment_intent_external_id'
    #     )
    #     payment_id = PaymentProvidersPort.initialize_payment(
    #         customer_id=self.customer.id,
    #         account_holder_id=self.account_holder.id,
    #         payment_provider_code=self.payment_provider,
    #         payment_method_type=self.payment_method,
    #         amount=self.amount,
    #         fee_amount=self.fee_amount,
    #         auto_capture=False,
    #     ).entity.id
    #
    #     provider_request = baker.make(
    #         ProviderRequestLog,
    #     )
    #     body = sample_stripe_notifications.payment_intent__created
    #     body['data']['object']['metadata']['provider_request_id'] = str(provider_request.id)
    #
    #     settings.STRIPE_ACCOUNT_WEBHOOK_SECRET = 'tes_webhook_secret'
    #     resp = self.client.post(
    #         reverse('stripe_account_webhook'),
    #         data=json.dumps(body),
    #         content_type='application/json',
    #     )

    @parameterized.expand(
        [
            (uuid.UUID('0fe38c59-6450-4036-b5ff-************'), False),  # Booksy
            (uuid.uuid4(), True),  # Business
        ]
    )
    @mock.patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_booksy_wallet')
    @mock.patch('stripe.Refund.create')
    def test_send_for_refund_port(
        self,
        booksy_account_holder_id,
        expected_reverse_transfer,
        stripe_provider_mock,
        get_booksy_wallet_mock,
    ):
        get_booksy_wallet_mock_response = MagicMock()
        get_booksy_wallet_mock_response.account_holder_id = booksy_account_holder_id
        get_booksy_wallet_mock.return_value = get_booksy_wallet_mock_response
        stripe_provider_mock.side_effect = lambda **_: stripe.Refund(
            id=get_random_string(12),
        )
        _refund_with_empty_external_id = baker.make(
            # there was a bug, where such a record in the db caused send_for_refund fail
            # if there is a duplicate key exception, then there is a bug in the code
            StripeRefund,
            external_id="",
        )

        payment = baker.make(
            Payment,
            status=PaymentStatus.CAPTURED,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
            provider_code=self.payment_provider,
            payment_method=self.payment_method,
        )
        _payment_intent = baker.make(
            StripePaymentIntent,
            payment=payment,
            external_id="test123",
        )
        _refund = PaymentProvidersPaymentPort.send_for_refund(
            payment_id=payment.id,
            amount=payment.amount,
        ).entity

        stripe_provider_mock.assert_called_once_with(
            amount=self.amount,
            payment_intent="test123",
            refund_application_fee=False,
            reverse_transfer=expected_reverse_transfer,
        )
        self.assertEqual(StripeRefund.objects.count(), 2)

    @mock.patch('stripe.Webhook.construct_event', side_effect=construct_event)
    @mock.patch(
        'webapps.payment_providers.services.common.'
        'payment_providers_payment_operation_created_event.send'
    )
    def test_dispute_created_notification(
        self, payment_operation_created_event_mock, construct_event_mock
    ):
        provider_request = baker.make(ProviderRequestLog)
        body = sample_stripe_notifications.charge__dispute__created
        body['data']['object']['metadata']['provider_request_id'] = str(provider_request.id)

        payment: Payment = baker.make(Payment)
        baker.make(
            StripePaymentIntent,
            payment=payment,
            external_id="pi_3KyZRFIFJQxWAaXK1q1q024K",
        )
        notification: Notification = baker.make(
            Notification,
            event_code='charge.dispute.created',
            event_body=body,
            provider_code=PaymentProviderCode.STRIPE,
        )

        NotificationServices.handle_notification(notification)

        notification.refresh_from_db()
        self.assertEqual(notification.status, NotificationStatus.SUCCESS)
        self.assertEqual(PaymentOperation.objects.count(), 1)
        self.assertEqual(payment.operations.count(), 1)
        operation = payment.operations.first()
        self.assertEqual(operation.type, PaymentOperationType.CHARGEBACK)
        self.assertEqual(operation.amount, 5500)
        self.assertEqual(payment_operation_created_event_mock.call_count, 1)
