from django.db import models

from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON><PERSON>

from lib.models import UUIDArchiveModel
from lib.payment_providers.entities import AdyenAccountHolderEntity
from webapps.payment_providers.consts.adyen import FUND_TRANSFER_CHOICES


class AdyenTokenizedPaymentMethod(UUIDArchiveModel):
    external_id = models.CharField(  # auth reference, old PaymentMethod provider_ref
        max_length=64,
        unique=True,
    )
    tokenized_payment_method = models.OneToOneField(
        'TokenizedPaymentMethod',
        on_delete=models.CASCADE,
        related_name='adyen_tokenized_payment_method',
    )
    shopper_reference = models.CharField(
        # unique reference defined by you and stored on the Adyen
        # payments platform when creating a recurring contract
        max_length=80,
        blank=True,
        null=True,
        db_index=True,
    )
    cardholder_v1_id = models.IntegerField(
        blank=True,
        null=True,
        db_index=True,
    )
    recurr_detail_ref = models.CharField(
        max_length=16,
        blank=True,
        null=True,
        db_index=True,
    )
    name = models.CharField(max_length=128, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    ip = models.GenericIPAddressField(blank=True, null=True)


class AdyenAccountHolder(UUIDArchiveModel):
    account_holder = models.OneToOneField(
        'AccountHolder',
        related_name='adyen_account_holder',
        on_delete=models.CASCADE,
    )
    # old account_holder_code
    external_id = models.CharField(
        max_length=64,
        unique=True,
    )
    account_code = models.CharField(
        max_length=64,
        blank=True,
        null=True,
    )
    payouts_enabled = models.BooleanField(default=False)
    kyc_verified_at_least_once = models.BooleanField(default=False)

    @property
    def entity(self) -> AdyenAccountHolderEntity:
        return AdyenAccountHolderEntity(
            external_id=self.external_id,
            account_code=self.account_code,
            payouts_enabled=self.payouts_enabled,
            kyc_verified_at_least_once=self.kyc_verified_at_least_once,
        )

    def __str__(self):
        return f"AdyenAccountHolder {self.id}"


class AdyenCustomer(UUIDArchiveModel):
    customer = models.OneToOneField(
        'Customer',
        related_name='adyen_customer',
        on_delete=models.CASCADE,
    )
    user_v1_id = models.IntegerField(
        blank=True,
        null=True,
        db_index=True,
    )


class AdyenPayment(UUIDArchiveModel):
    payment = models.OneToOneField(
        'Payment',
        related_name='adyen_payment',
        on_delete=models.CASCADE,
    )

    @property
    def auth(self):
        return self.auths.last()


class AdyenOperationModel(UUIDArchiveModel):
    external_id = models.CharField(
        max_length=64,
        unique=True,
    )
    psp_reference = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        db_index=True,
    )

    # error code from 4xx/5xx response
    raw_error_code = models.CharField(
        max_length=16,
        blank=True,
        null=True,
    )
    oper_result = models.IntegerField(blank=True, null=True)

    class Meta:
        abstract = True


class AdyenAuth(AdyenOperationModel):
    amount = models.IntegerField()
    auth_code = models.CharField(
        max_length=64,
        blank=True,
        null=True,
    )
    three_d_secure_data = models.JSONField(
        verbose_name='3DSecure data',
        default=dict,
    )
    additional_data = models.JSONField(
        default=dict,
    )

    # could be card auth so it can be no payment
    adyen_payment = models.ForeignKey(
        AdyenPayment,
        related_name='auths',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    adyen_tokenized_payment_method = models.ForeignKey(
        AdyenTokenizedPaymentMethod,
        related_name='auths',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    # refusal reason from 200 response
    refusal_reason = models.CharField(max_length=64, blank=True, null=True)

    oper_result = models.IntegerField(blank=True, null=True)

    @property
    def payment(self):
        return self.adyen_payment.payment

    @property
    def capture(self):
        self.captures.last()


class AdyenCapture(AdyenOperationModel):
    auth = models.ForeignKey(
        AdyenAuth,
        related_name='captures',
        on_delete=models.CASCADE,
    )


class AdyenTransferFund(UUIDArchiveModel):
    transfer_fund = models.OneToOneField(
        'TransferFund',
        related_name='adyen_transfer_fund',
        on_delete=models.CASCADE,
    )

    external_id = models.CharField(
        max_length=64,
        unique=True,
    )
    transfer_code = models.CharField(
        max_length=80,
        choices=FUND_TRANSFER_CHOICES,
        null=True,  # TODO null=False https://booksy.atlassian.net/browse/POS-1015
    )


class AdyenRefund(AdyenOperationModel):
    payment_operation = models.OneToOneField(
        'PaymentOperation',
        related_name='adyen_refund',
        on_delete=models.CASCADE,
    )


class AdyenDispute(UUIDArchiveModel):
    payment_operation = models.OneToOneField(
        'PaymentOperation',
        related_name='adyen_dispute',
        on_delete=models.CASCADE,
    )
    external_id = models.CharField(
        max_length=64,
        unique=True,
    )


class AdyenPayout(UUIDArchiveModel):
    external_id = models.CharField(
        max_length=32,
        unique=True,
    )
    payout = models.OneToOneField(
        'Payout',
        related_name='adyen_payout',
        on_delete=models.CASCADE,
    )
    transfer_fund_external_ids = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    capture_external_ids = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    refund_external_ids = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    dispute_external_ids = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
