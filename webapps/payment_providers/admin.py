from django.contrib import admin
from django.utils.html import format_html

from lib.admin_helpers import (
    BaseModelAdmin,
    HistoryAdminMixin,
    ReadOnlyTabular,
    admin_link,
)
from lib.payments.admin import ReadOnlyMixin
from lib.payments.enums import PaymentProviderCode
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    Notification,
    Payment,
    PaymentHistory,
    PaymentOperation,
    PaymentOperationHistory,
    Payout,
    ProviderRequestLog,
    StripeAccountHolder,
    StripeCustomer,
    StripeDispute,
    StripePaymentIntent,
    StripePaymentIntentHistory,
    StripePayout,
    StripeRefund,
    StripeTransferFund,
    TransferFund,
    StripeAccountHolderSettings,
    PayoutMethodChangelog,
)


########### NOTIFICATIONS ###########


class NotificationAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = ('id', 'external_id', 'provider_code', 'event_code', 'status', 'created')
    search_fields = ['=id', '=external_id']
    list_filter = [
        'provider_code',
        'event_code',
        'status',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False


admin.site.register(Notification, NotificationAdmin)


class ProviderRequestLogAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = ('created', 'provider_code', 'request_url', 'request_type')

    def has_add_permission(self, request):
        return False


admin.site.register(ProviderRequestLog, ProviderRequestLogAdmin)


########### CUSTOMERS ###########


class StripeCustomerInline(ReadOnlyMixin, admin.StackedInline):
    model = StripeCustomer


class CustomerAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = ('id', 'email', 'phone', 'name')
    search_fields = [
        '=id',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False

    def get_inlines(self, request, obj: Payment):
        inlines = []
        if hasattr(obj, "_stripe_customer"):
            inlines.append(StripeCustomerInline)

        return inlines


admin.site.register(Customer, CustomerAdmin)


########### ACCOUNT HOLDERS ###########


class StripeAccountHolderSettingsInline(ReadOnlyMixin, admin.StackedInline):
    model = StripeAccountHolderSettings


class StripeAccountHolderInline(ReadOnlyMixin, admin.StackedInline):
    model = StripeAccountHolder


class AccountHolderAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = ('id', 'statement_name', 'updated', 'created')
    search_fields = ['=id', 'statement_name']
    ordering = ['-created']

    def has_add_permission(self, request):
        return False

    def get_inlines(self, request, obj: Payment):
        inlines = []
        if hasattr(obj, "stripe_account_holder"):
            inlines.append(StripeAccountHolderInline)
        if hasattr(obj, "stripe_account_holder_settings"):
            inlines.append(StripeAccountHolderSettingsInline)

        return inlines


admin.site.register(AccountHolder, AccountHolderAdmin)


########### PAYMENTS & PAYMENT HISTORY ###########


class PaymentHistoryAdmin(HistoryAdminMixin, ReadOnlyMixin, ReadOnlyTabular, BaseModelAdmin):
    ordering = ('-created',)

    fields = (
        'created',
        'payment_id',
        'formatted_traceback',
        'formatted_data',
        'pretty_diff',
        'raw_diff',
    )

    list_display = (
        'created',
        'id',
        'payment_id',
    )


admin.site.register(PaymentHistory, PaymentHistoryAdmin)


# pylint: disable=duplicate-code
class PaymentHistoryInline(HistoryAdminMixin, ReadOnlyTabular, admin.TabularInline):
    model = PaymentHistory

    ordering = ('-created',)

    fields = (
        'uuid',
        'created',
        'pretty_diff',
    )

    readonly_fields = (
        'uuid',
        'created',
        'pretty_diff',
    )

    @staticmethod
    def uuid(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)


class StripePaymentIntentInline(ReadOnlyMixin, admin.StackedInline):
    model = StripePaymentIntent


class PaymentAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = (
        'id',
        'status',
        'provider_code',
        'payment_method',
        'amount',
        'fee_amount',
        'auto_capture',
        'updated',
        'created',
    )
    search_fields = [
        '=id',
    ]
    list_filter = [
        'status',
        'provider_code',
        'payment_method',
        'auto_capture',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False

    def get_inlines(self, request, obj: Payment):
        inlines = [PaymentHistoryInline]
        if obj.provider_code == PaymentProviderCode.STRIPE:
            inlines.append(StripePaymentIntentInline)

        return inlines


admin.site.register(Payment, PaymentAdmin)


class StripePaymentIntentAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = (
        'id',
        'payment_id',
        'external_id',
        'updated',
        'created',
    )
    search_fields = [
        '=id',
        '=external_id',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False


admin.site.register(StripePaymentIntent, StripePaymentIntentAdmin)


class StripePaymentIntentHistoryAdmin(
    HistoryAdminMixin,
    ReadOnlyMixin,
    ReadOnlyTabular,
    BaseModelAdmin,
):
    ordering = ('-created',)

    fields = (
        'created',
        'payment_intent_id',
        'formatted_traceback',
        'formatted_data',
        'pretty_diff',
        'raw_diff',
    )

    list_display = (
        'created',
        'id',
        'payment_intent_id',
    )


admin.site.register(StripePaymentIntentHistory, StripePaymentIntentHistoryAdmin)


########### PAYMENT OPERATIONS & OPERATION HISTORY ###########


class PaymentOperationHistoryAdmin(
    HistoryAdminMixin,
    ReadOnlyMixin,
    ReadOnlyTabular,
    BaseModelAdmin,
):
    ordering = ('-created',)

    fields = (
        'created',
        'payment_operation_id',
        'formatted_traceback',
        'formatted_data',
        'pretty_diff',
        'raw_diff',
    )

    list_display = (
        'created',
        'id',
        'payment_operation_id',
    )


admin.site.register(PaymentOperationHistory, PaymentOperationHistoryAdmin)


class PaymentOperationHistoryInline(HistoryAdminMixin, ReadOnlyTabular, admin.TabularInline):
    model = PaymentOperationHistory

    ordering = ('-created',)

    fields = (
        'uuid',
        'created',
        'pretty_diff',
    )

    readonly_fields = (
        'uuid',
        'created',
        'pretty_diff',
    )

    @staticmethod
    def uuid(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)


class StripeRefundInline(ReadOnlyMixin, admin.StackedInline):
    model = StripeRefund


class StripeDisputeInline(ReadOnlyMixin, admin.StackedInline):
    model = StripeDispute


class PaymentOperationAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = ('id', 'type', 'amount', 'status', 'updated', 'created')
    search_fields = ['=id', '=payment__id']
    list_filter = [
        'type',
        'status',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False

    def get_inlines(self, request, obj: Payment):
        inlines = []
        if hasattr(obj, "stripe_refund"):
            inlines.append(StripeRefundInline)
        if hasattr(obj, "stripe_dispute"):
            inlines.append(StripeDisputeInline)

        return inlines


admin.site.register(PaymentOperation, PaymentOperationAdmin)


########### TRANSFER FUNDS ###########


class StripeTransferFundInline(ReadOnlyMixin, admin.StackedInline):
    model = StripeTransferFund


class TransferFundAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = ('id', 'provider_code', 'amount', 'updated', 'created')
    search_fields = ['=id']
    list_filter = [
        'provider_code',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False

    def get_inlines(self, request, obj: Payment):
        inlines = []
        if hasattr(obj, "stripe_transfer_fund"):
            inlines.append(StripeTransferFundInline)

        return inlines


admin.site.register(TransferFund, TransferFundAdmin)


########### PAYOUTS ###########


class StripePayoutInline(ReadOnlyMixin, admin.StackedInline):
    model = StripePayout


class PayoutAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = (
        'id',
        'status',
        'payment_provider_code',
        'amount',
    )
    search_fields = [
        '=id',
    ]
    list_filter = [
        'status',
        'payment_provider_code',
        'error_code',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False

    def get_inlines(self, request, obj: Payment):
        inlines = []
        if hasattr(obj, "stripe_payout"):
            inlines.append(StripePayoutInline)

        return inlines


admin.site.register(Payout, PayoutAdmin)


class PayoutMethodChangelogAdmin(ReadOnlyMixin, BaseModelAdmin):
    list_display = (
        'id',
        'account_holder',
        'provider_code',
        'token',
        'action',
        'user_id',
        'ip_address',
    )
    search_fields = [
        '=id',
        '=token',
        '=account_holder',
    ]
    list_filter = [
        'provider_code',
        'action',
    ]
    ordering = ['-created']

    def has_add_permission(self, request):
        return False


admin.site.register(PayoutMethodChangelog, PayoutMethodChangelogAdmin)
