def get_stripe_account_create_response(stripe_account_id: str):
    return {
        # based on data retrieved from payments4 test instance
        "business_profile": {
            "mcc": "7999",
            "name": None,
            "product_description": None,
            "support_address": None,
            "support_email": None,
            "support_phone": None,
            "support_url": None,
            "url": None,
        },
        "business_type": "individual",
        "capabilities": {},
        "charges_enabled": False,
        "company": {
            "address": {
                "city": "PASSED",
                "country": "US",
                "line1": "Woodlands",
                "line2": None,
                "postal_code": "90249",
                "state": "CA",
            },
            "directors_provided": True,
            "executives_provided": True,
            "name": None,
            "owners_provided": True,
            "phone": "+***********",
            "tax_id_provided": False,
            "verification": {
                "document": {
                    "back": None,
                    "details": None,
                    "details_code": None,
                    "front": None,
                },
            },
        },
        "country": "US",
        "created": **********,
        "default_currency": "usd",
        "details_submitted": False,
        "email": "<EMAIL>",
        "external_accounts": {
            "data": [
                {
                    "account": stripe_account_id,
                    "account_holder_name": None,
                    "account_holder_type": None,
                    "account_type": None,
                    "available_payout_methods": [
                        "standard",
                    ],
                    "bank_name": "WELLS FARGO BANK NA  (MINNESOTA)",
                    "country": "US",
                    "currency": "usd",
                    "default_for_currency": True,
                    "fingerprint": "u3v5bpZbXxqShugs",
                    "id": "ba_1MEbikIKKlixRLbl2lo93FMi",
                    "last4": "6789",
                    "metadata": {},
                    "object": "bank_account",
                    "routing_number": "*********",
                    "status": "new",
                }
            ],
            "has_more": False,
            "object": "list",
            "total_count": 1,
            "url": f"/v1/accounts/{stripe_account_id}/external_accounts",
        },
        "future_requirements": {
            "alternatives": [],
            "current_deadline": None,
            "currently_due": [],
            "disabled_reason": None,
            "errors": [],
            "eventually_due": [],
            "past_due": [],
            "pending_verification": [],
        },
        "id": stripe_account_id,
        "individual": {
            "account": stripe_account_id,
            "address": {
                "city": "PASSED",
                "country": "US",
                "line1": "Woodlands",
                "line2": None,
                "postal_code": "90249",
                "state": "CA",
            },
            "created": **********,
            "dob": {
                "day": 7,
                "month": 1,
                "year": 1992,
            },
            "email": "<EMAIL>",
            "first_name": "qwerty",
            "future_requirements": {
                "alternatives": [],
                "currently_due": [],
                "errors": [],
                "eventually_due": [],
                "past_due": [],
                "pending_verification": [],
            },
            "id": "person_1MEbikIKKlixRLblfCGlpx43",
            "id_number_provided": False,
            "last_name": "TestData",
            "metadata": {},
            "object": "person",
            "phone": "+***********",
            "political_exposure": "none",
            "relationship": {
                "director": False,
                "executive": False,
                "owner": False,
                "percent_ownership": None,
                "representative": True,
                "title": None,
            },
            "requirements": {
                "alternatives": [],
                "currently_due": [],
                "errors": [],
                "eventually_due": [],
                "past_due": [],
                "pending_verification": [],
            },
            "ssn_last_4_provided": False,
            "verification": {
                "additional_document": {
                    "back": None,
                    "details": None,
                    "details_code": None,
                    "front": None,
                },
                "details": None,
                "details_code": None,
                "document": {
                    "back": None,
                    "details": None,
                    "details_code": None,
                    "front": None,
                },
                "status": "unverified",
            },
        },
        "login_links": {
            "data": [],
            "has_more": False,
            "object": "list",
            "total_count": 0,
            "url": f"/v1/accounts/{stripe_account_id}/login_links",
        },
        "metadata": {},
        "object": "account",
        "payouts_enabled": False,
        "requirements": {
            "alternatives": [],
            "current_deadline": None,
            "currently_due": [
                "tos_acceptance.date",
                "tos_acceptance.ip",
            ],
            "disabled_reason": "requirements.past_due",
            "errors": [],
            "eventually_due": [
                "tos_acceptance.date",
                "tos_acceptance.ip",
            ],
            "past_due": [
                "tos_acceptance.date",
                "tos_acceptance.ip",
            ],
            "pending_verification": [],
        },
        "settings": {
            "bacs_debit_payments": {},
            "branding": {
                "icon": None,
                "logo": None,
                "primary_color": None,
                "secondary_color": None,
            },
            "card_issuing": {
                "tos_acceptance": {
                    "date": None,
                    "ip": None,
                },
            },
            "card_payments": {
                "decline_on": {
                    "avs_failure": False,
                    "cvc_failure": False,
                },
                "statement_descriptor_prefix": None,
                "statement_descriptor_prefix_kana": None,
                "statement_descriptor_prefix_kanji": None,
            },
            "dashboard": {
                "display_name": None,
                "timezone": "Etc/UTC",
            },
            "payments": {
                "statement_descriptor": None,
                "statement_descriptor_kana": None,
                "statement_descriptor_kanji": None,
            },
            "payouts": {
                "debit_negative_balances": True,
                "schedule": {
                    "delay_days": 2,
                    "interval": "daily",
                },
                "statement_descriptor": None,
            },
            "sepa_debit_payments": {},
        },
        "tos_acceptance": {
            "date": None,
            "ip": None,
            "user_agent": None,
        },
        "type": "custom",
    }


STRIPE_ACCOUNT_CREATE_RESPONSE = get_stripe_account_create_response(
    'acct_1MEbikIKKlixRLbl',
)
