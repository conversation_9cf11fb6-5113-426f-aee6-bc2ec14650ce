import datetime
import traceback
from typing import (
    Optional,
    Tuple,
)

import stripe
from django.conf import settings

from lib.payment_providers.entities import (
    PayoutDetailsEntity,
    StripeApplePayPaymentTokenExternalData,
    StripeAuthAdditionalDataEntity,
    StripeTokenizedPMExternalData,
)
from lib.payment_providers.enums import (
    PaymentMethodType,
    PaymentStatus,
)
from lib.payments.enums import (
    PaymentError,
    PayoutStatus,
    PayoutType,
)
from lib.serializers import safe_get
from webapps.business.ports.business_details import BusinessDetailsPort
from webapps.payment_providers.consts.stripe import (
    PAYOUT_STATUS_MAPPING,
    PAYOUT_TYPE_MAPPING_REVERSED,
    REFUND_STATUS_MAPPING,
    PaymentIntentStripeStatus,
    StripeBalanceTransactionType,
)
from webapps.payment_providers.consts.stripe_errors_mapping import STRIPE_PAYMENT_ERROR_MAPPING
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    Payment,
    PaymentOperation,
    Payout,
    SetupIntent,
    StripeCustomer,
    StripeDispute,
    StripePaymentIntent,
    StripePaymentIntentHistory,
    StripePayout,
    StripeRefund,
    StripeSetupIntent,
    StripeTokenizedPaymentMethod,
    StripeTransferFund,
    TokenizedPaymentMethod,
    TransferFund,
)
from webapps.payment_providers.providers.stripe import StripeProvider
from webapps.payment_providers.serializers.stripe import (
    PaymentIntentHistorySerializer,
)
from webapps.payment_providers.services.base import (
    BaseCustomerServices,
    BasePaymentMethodServices,
    BasePaymentOperationServices,
    BasePaymentServices,
    BasePayoutServices,
    BaseTerminalServices,
    BaseTransferFundServices,
)


class StripeCustomerServices(BaseCustomerServices):
    @staticmethod
    def update_customer(
        customer: Customer,
    ) -> StripeCustomer:
        """
        Modifies existing StripeCustomer object

        :param customer: Customer object
        """
        try:
            stripe_customer = customer._stripe_customer  # pylint: disable=protected-access
        except (
            Customer._stripe_customer.RelatedObjectDoesNotExist  # pylint: disable=protected-access
        ):
            return None
        StripeProvider.update_customer(
            stripe_customer,
            email=customer.email,
            name=customer.name,
            phone=customer.phone,
            metadata=customer.metadata,
        )
        return stripe_customer

    @staticmethod
    def get_or_create_customer(customer: Customer) -> StripeCustomer:
        """
        Creates instance of StripeCustomer object

        :param customer: Customer object
        """
        try:
            stripe_customer = customer._stripe_customer  # pylint: disable=protected-access
        except (
            Customer._stripe_customer.RelatedObjectDoesNotExist  # pylint: disable=protected-access
        ):
            stripe_customer = StripeCustomer(
                customer=customer,
            )
            stripe_response = StripeProvider.create_customer(stripe_customer)
            stripe_customer.external_id = stripe_response.id
            stripe_customer.save(update_fields=['external_id'])

        return stripe_customer


class StripePaymentMethodServices(BasePaymentMethodServices):
    @staticmethod
    def add_tokenized_pm(
        tokenized_pm: TokenizedPaymentMethod,
        external_data: StripeTokenizedPMExternalData,
    ):
        stripe_tokenized_pm = StripeTokenizedPaymentMethod.objects.create(
            tokenized_payment_method=tokenized_pm,
            external_id=external_data.external_id,
        )

        return stripe_tokenized_pm

    @staticmethod
    def remove_tokenized_pm(
        tokenized_pm: TokenizedPaymentMethod,
        make_psp_call: bool = False,
    ):
        stripe_tokenized_pm = tokenized_pm.stripe_tokenized_payment_method
        if make_psp_call:
            StripeProvider.detach_payment_method(
                tokenized_payment_method=stripe_tokenized_pm,
            )
        stripe_tokenized_pm.soft_delete()

    @staticmethod
    def initialize_setup_intent(
        setup_intent: SetupIntent,
        customer: Customer,
        method_type: PaymentMethodType = PaymentMethodType.CARD,
    ) -> str:
        ext_setup_intent = StripeProvider.create_setup_intent(
            customer=StripeCustomerServices.get_or_create_customer(customer),
            method_type=method_type,
        )
        StripeSetupIntent.objects.create(
            setup_intent=setup_intent,
            external_id=ext_setup_intent.id,
        )
        return ext_setup_intent.client_secret

    @staticmethod
    def create_payment_token(
        external_data: StripeApplePayPaymentTokenExternalData,
    ) -> str:
        return StripeProvider.create_payment_token(
            external_data=external_data,
        ).id


class StripePaymentServices(BasePaymentServices):
    @staticmethod
    def check_if_available() -> bool:
        return True

    @staticmethod
    def initialize_payment(payment: Payment, payment_token: str = None):
        """
        Creates StripePaymentIntent object. For transaction which requires create and confirm
        we pass payment token. If confirmation fails the error is handled and the transaction
        is updated with failed status via Stripe webhooks.

        :param payment: Payment object
        :param payment_token: Payment token
        """
        try:
            payment_intent_resp_obj = StripeProvider.create_payment_intent(
                account_holder=payment.account_holder.stripe_account_holder,
                customer=(
                    StripeCustomerServices.get_or_create_customer(
                        payment.customer,
                    )
                    if payment.customer
                    else None
                ),
                payment_method=payment.payment_method,
                application_fee=payment.fee_amount,
                amount=payment.amount,
                payment_token=payment_token,
                auto_capture=payment.auto_capture,
                metadata=payment.metadata,
            )
        except stripe.error.StripeError as exc:
            payment_intent_data = exc.error.payment_intent

            return StripePaymentIntent.objects.create(
                payment=payment,
                external_id=payment_intent_data.id,
            )

        return StripePaymentIntent.objects.create(
            payment=payment,
            external_id=payment_intent_resp_obj.id,
        )

    @staticmethod
    def _get_card_brand_if_applicable(payment_intent_data: dict):
        """
        Gets card brand (Visa/Mastercard...) for GooglePay/Apple Pay
        """

        data = safe_get(payment_intent_data, ['charges', 'data', 0])
        card_info = safe_get(data, ['payment_method_details', 'card'])
        if card_info:
            wallet_type = safe_get(card_info, ['wallet', 'type'])
            if wallet_type and wallet_type in ('google_pay', 'apple_pay'):
                return safe_get(card_info, ['brand'])
        return None

    @staticmethod
    def _prepare_payment_additional_data(payment_intent_data: dict):
        card_brand = StripePaymentServices._get_card_brand_if_applicable(payment_intent_data)
        return {'card_brand': card_brand} if card_brand else None

    @classmethod
    def authorize_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls,
        payment: Payment,
        tokenized_pm: TokenizedPaymentMethod = None,
        additional_data: StripeAuthAdditionalDataEntity | None = None,
        payment_token: str = None,
        gift_cards_ids: list[str] = None,
        off_session: bool | None = None,
    ) -> (Payment, PaymentError | None):
        """
        Tries to authorize StripePaymentIntent with given StripeTokenizedPaymentMethod

        :param payment: Payment object
        :param tokenized_pm: TokenizedPaymentMethod object
        :param additional_data:
        :param payment_token:
        :param gift_cards_ids:
        :param off_session:
        """
        stripe_error = None

        try:
            payment_intent_data = StripeProvider.confirm_payment_intent(
                payment_intent=payment.stripe_payment,
                payment_token=payment_token,
                off_session=off_session,
                method_type=payment.payment_method,
                payment_method=(
                    tokenized_pm.stripe_tokenized_payment_method if tokenized_pm else None
                ),
            )
        except stripe.error.StripeError as exc:
            stripe_error = exc
            payment_intent_data = exc.error.payment_intent

        if safe_get(payment_intent_data, ['status']) == PaymentIntentStripeStatus.REQUIRES_ACTION:
            client_secret = payment_intent_data.get('client_secret', None)
            payment.action_required_details = {
                'client_secret': client_secret,
            }
            payment.save(
                update_fields=[
                    'action_required_details',
                ]
            )
        # Hacky things for synchronous response >>>
        payment_intent = StripePaymentIntent.objects.filter(
            external_id=payment_intent_data['id']
        ).first()

        error_code = cls._get_stripe_error_code(
            stripe_error,
        )

        if error_code:
            payment_intent.error_code = error_code[:100]
            payment_intent.save(update_fields=['error_code'])

            error_code = StripePaymentServices.map_provider_error_to_common_payment_error(
                error=error_code,
                error_message=str(stripe_error),
            )
        # <<<
        card_data = StripePaymentServices._prepare_payment_additional_data(payment_intent_data)
        if card_data:
            additional_data = payment.additional_data or {}
            additional_data.update(**card_data)
            payment.additional_data = additional_data
            payment.save(update_fields=['additional_data'])
        return payment, error_code

    @staticmethod
    def _get_stripe_error_code(
        stripe_error: stripe.error.StripeError | None,
    ) -> str | None:
        if not stripe_error:
            return
        payment_intent_data = stripe_error.error.payment_intent
        error_code = safe_get(
            payment_intent_data, ['last_payment_error', 'decline_code']
        ) or safe_get(payment_intent_data, ['last_payment_error', 'code'])

        return error_code or stripe_error.code or str(stripe_error)

    @staticmethod
    def modify_payment(
        payment: Payment,
        fee_amount: int,
    ) -> None:
        StripeProvider.modify_payment_intent(
            payment_intent=payment.stripe_payment,
            application_fee_amount=fee_amount,
        )

    @staticmethod
    def capture_payment(payment: Payment) -> Payment:
        """
        Captures StripePaymentIntent

        :param payment: Payment object
        """
        try:
            StripeProvider.capture_payment_intent(
                payment_intent=payment.stripe_payment,
            )
        except stripe.error.CardError:
            # We will get webhook with information about fail
            pass
        except stripe.error.StripeError as e:
            from webapps.payment_providers.services.common import (  # pylint: disable=cyclic-import
                CommonPaymentServices,
            )

            # Catch rest of them - technical issues with request
            error_code = StripePaymentServices.map_provider_error_to_common_payment_error(
                error=e.error.code,
            )

            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.CAPTURE_FAILED,
                error_code=error_code,
            )

        return payment

    @staticmethod
    def cancel_payment(payment: Payment) -> bool:
        """
        Tries to cancel StripePaymentIntent

        :param payment: Payment object
        """
        is_canceled = StripeProvider.cancel_payment_intent(payment_intent=payment.stripe_payment)
        if is_canceled:
            payment.stripe_payment.stripe_status = PaymentIntentStripeStatus.CANCELED
            payment.stripe_payment.save(update_fields=['stripe_status'])
        return is_canceled

    @staticmethod
    def get_payment_client_token(payment: Payment) -> str:
        """
        Retrieves Payment.client_secret token used in BCR flow

        :param payment: Payment object
        """
        return StripeProvider.get_payment_client_token(payment_intent=payment.stripe_payment)

    @staticmethod
    def get_provider_payment_details(payment: Payment) -> str | None:
        if not (
            stripe_payment_intent := StripePaymentIntent.objects.filter(payment=payment).first()
        ):
            return None
        return stripe_payment_intent.external_id

    @staticmethod
    def update_payment_intent_status(
        payment_intent: StripePaymentIntent,
        stripe_status: str,
        internal_status: PaymentStatus,
        stripe_error_code: str | None = None,
        error_message: str | None = None,
    ):
        """
        Updates StripePaymentIntent.status
        and also updates Payment.status with corresponding status

        :param payment_intent: StripePaymentIntent object
        :param stripe_status: status received from stripe
        :param internal_status: corresponding common status
        :param stripe_error_code: error code received from stripe
        """
        from webapps.payment_providers.services.common import (  # pylint: disable=cyclic-import
            CommonPaymentServices,
        )

        payment_intent.stripe_status = stripe_status
        payment_intent.error_code = stripe_error_code
        payment_error = None
        if stripe_error_code:
            payment_error = StripePaymentServices.map_provider_error_to_common_payment_error(
                error=stripe_error_code,
                error_message=error_message,
            )
        payment_intent.save(update_fields=['stripe_status', 'error_code'])
        StripePaymentServices.save_payment_intent_to_history(payment_intent)

        payment = payment_intent.payment
        CommonPaymentServices.update_status(
            payment=payment,
            status=internal_status,
            error_code=payment_error,
        )

    @staticmethod
    def map_provider_error_to_common_payment_error(
        error: str,
        error_message: str | None = None,
    ) -> PaymentError:
        payment_error = STRIPE_PAYMENT_ERROR_MAPPING.get(error, PaymentError.GENERIC_ERROR)
        if payment_error == PaymentError.GENERIC_ERROR and error_message:
            if 'The code passed wasn’t a valid BLIK code.' in error_message:
                payment_error = PaymentError.INVALID_BLIK_CODE
            elif 'BLIK codes expire after 2 minutes' in error_message:
                payment_error = PaymentError.INVALID_BLIK_CODE

        return payment_error

    @staticmethod
    def save_payment_intent_to_history(payment_intent: StripePaymentIntent):
        serializer = PaymentIntentHistorySerializer(payment_intent)
        history = StripePaymentIntentHistory(
            data=serializer.data,
            payment_intent=payment_intent,
            traceback=traceback.format_stack(),
        )
        history.save()

    @staticmethod
    def mark_payment_as_failed(payment: Payment, error_code: Optional[str] = None):
        StripeProvider.cancel_payment_intent(payment_intent=payment.stripe_payment)
        StripePaymentServices.update_payment_intent_status(
            payment_intent=payment.stripe_payment,
            stripe_status=PaymentIntentStripeStatus.CANCELED,
            internal_status=PaymentStatus.AUTHORIZATION_FAILED,
            stripe_error_code=error_code,
        )


class StripeTransferFundServices(BaseTransferFundServices):
    @staticmethod
    def initialize_transfer_fund(
        transfer_fund: TransferFund,
    ):
        return transfer_fund

    @staticmethod
    def process_transfer_fund(  # pylint: disable=arguments-renamed
        transfer_fund: TransferFund,
        additional_data=None,
        refund_fee_metadata: dict[str, str] = None,
        metadata: dict[str, str] | None = None,
    ):
        """
        Creates StripeTransferFund object and make call to stripe psp

        :param transfer_fund: TransferFund object
        :param additional_data that have to be passed due to concrete psp
            implementation (i.e. psp radar data like customers IP address)
            currently not used
        :param metadata: values expected to be in Stripe Transfer metadata
        """

        transfer_response_obj = StripeProvider.create_transfer(
            sender=transfer_fund.sender.stripe_account_holder,
            receiver=transfer_fund.receiver.stripe_account_holder,
            amount=transfer_fund.amount,
            refund_fee_metadata=refund_fee_metadata,
            metadata=metadata,
        )
        return StripeTransferFund.objects.create(
            transfer_fund=transfer_fund,
            external_id=transfer_response_obj.id,
        )

    @staticmethod
    def get_provider_transfer_fund_details(
        transfer_fund: TransferFund,
    ):
        if not (
            stripe_transfer_fund := StripeTransferFund.objects.filter(
                transfer_fund_id=transfer_fund.id
            ).first()
        ):
            return None, None
        return stripe_transfer_fund.external_id, stripe_transfer_fund.created


class StripePaymentOperationServices(BasePaymentOperationServices):
    @staticmethod
    def send_for_refund(
        payment: Payment,
        payment_operation: PaymentOperation,
    ):
        """
        Sends given amount for refund and creates StripeRefund object

        :param payment: Payment object
        :param payment_operation: PaymentOperation object
        """
        from webapps.payment_gateway.ports import PaymentGatewayPort

        stripe_refund = StripeRefund(payment_operation=payment_operation)
        stripe_refund.save()

        refund_data = StripeProvider.create_refund(
            payment_intent=payment.stripe_payment,
            amount=payment_operation.amount,
            from_booksy=(
                PaymentGatewayPort.get_booksy_wallet().account_holder_id
                == payment.account_holder_id
            ),
        )

        stripe_refund.external_id = refund_data.id
        stripe_refund.save(update_fields=['external_id'])

    @staticmethod
    def process_dispute(
        payment_operation: PaymentOperation,
        external_id: str,
    ):
        StripeDispute.objects.create(
            payment_operation=payment_operation,
            external_id=external_id,
        )

    @staticmethod
    def update_refund_status(refund: StripeRefund, stripe_status: str):
        from webapps.payment_providers.services.common import CommonPaymentOperationServices

        refund.stripe_status = stripe_status
        refund.save(update_fields=['stripe_status'])

        # Update PaymentOperation status
        refund_status = REFUND_STATUS_MAPPING.get(stripe_status)
        CommonPaymentOperationServices.update_status(
            payment_operation=refund.payment_operation,
            status=refund_status,
        )


class StripePayoutServices(BasePayoutServices):
    @staticmethod
    def add_payout(
        payout: Payout,
        external_id: str,
        metadata: dict,
    ):  # pylint: disable=unused-argument
        return StripePayout.objects.create(
            payout=payout,
            external_id=external_id,
        )

    @staticmethod
    def get_available_payout_amount(account_holder: AccountHolder) -> dict:
        """
        Retrieves StripeAccountHolder available balances

        :param account_holder: AccountHolder object
        """
        result = {
            PayoutType.REGULAR: 0,
            PayoutType.FAST: 0,
        }

        balance_object = StripeProvider.get_balance(
            stripe_account_holder=account_holder.stripe_account_holder
        )

        if not balance_object:
            return result

        for balance in balance_object.available:
            if balance['currency'].lower() != settings.CURRENCY_CODE.lower():
                continue
            result[PayoutType.REGULAR] = balance.amount

        if settings.POS__FAST_PAYOUTS and balance_object.instant_available:
            for balance in balance_object.instant_available:
                if balance['currency'].lower() != settings.CURRENCY_CODE.lower():
                    continue
                result[PayoutType.FAST] = balance.amount

        return result

    @staticmethod
    def initialize_payout(
        account_holder: AccountHolder,
        amount: int,
        payout_type: PayoutType,
    ) -> Tuple[str, PayoutStatus, datetime.datetime]:
        """
        Initializes Payout

        :param account_holder: AccountHolder object
        :param amount: amount to be paid out
        :param payout_type: how the payout should be processed
        """
        # returns a tuple of external id, status and expected arrival date
        payout_method = PAYOUT_TYPE_MAPPING_REVERSED[payout_type]
        stripe_payout_obj = StripeProvider.create_payout(
            stripe_account_holder=account_holder.stripe_account_holder,
            amount=amount,
            payout_method=payout_method,
        )
        business = BusinessDetailsPort.get_business_details(account_holder.metadata['business_id'])

        return (
            stripe_payout_obj['id'],
            PAYOUT_STATUS_MAPPING[stripe_payout_obj['status']],
            datetime.datetime.fromtimestamp(
                stripe_payout_obj['arrival_date'], tz=business.timezone
            ),
        )

    @staticmethod
    def get_payout_details(payout: Payout) -> PayoutDetailsEntity:
        if payout.payout_type == PayoutType.FAST:
            return PayoutDetailsEntity(
                payments=[],
                payment_operations=[],
                transfer_funds=[],
            )

        # payments
        payment_balance_transactions = StripeProvider.get_payout_balance_transactions(
            payout=payout,
            transaction_type=StripeBalanceTransactionType.PAYMENT,
        )
        pi_ids = [
            pi['source']['source_transfer']['source_transaction']['payment_intent']
            for pi in payment_balance_transactions
        ]
        payment_intents = StripePaymentIntent.objects.filter(external_id__in=pi_ids)
        payments = [p.payment.entity for p in payment_intents]

        # payment operations
        refund_balance_transactions = StripeProvider.get_payout_balance_transactions(
            payout=payout,
            transaction_type=StripeBalanceTransactionType.PAYMENT_REFUND,
        )
        refund_ids = [
            r['source']['source_transfer_reversal']['source_refund']
            for r in refund_balance_transactions
        ]
        stripe_refunds = StripeRefund.objects.filter(external_id__in=refund_ids)
        payment_operations = [r.payment_operation.entity for r in stripe_refunds]

        # transfers
        transfer_balance_transactions = StripeProvider.get_payout_balance_transactions(
            payout=payout,
            transaction_type=StripeBalanceTransactionType.TRANSFER,
        )
        transfer_ids = [t['source'] for t in transfer_balance_transactions]
        stripe_transfers = StripeTransferFund.objects.filter(external_id__in=transfer_ids)
        transfer_funds = [t.transfer_fund.entity for t in stripe_transfers]

        return PayoutDetailsEntity(
            payments=payments,
            payment_operations=payment_operations,
            transfer_funds=transfer_funds,
        )


class StripeTerminalServices(BaseTerminalServices):
    @staticmethod
    def get_terminal_connection_token() -> str:
        return StripeProvider.create_connection_token().secret
