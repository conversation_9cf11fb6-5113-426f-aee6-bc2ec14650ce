# pylint: skip-file

import copy
from collections import defaultdict
from contextlib import contextmanager
from itertools import zip_longest

from django.db import transaction
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from lib.fields.phone_number import BooksyPhoneSerializerField
from lib.serializers import TimezoneNameField
from lib.tools import tznow
from webapps import consts
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import (
    AppointmentStatusChoices,
    WhoMakesChange,
)
from webapps.booking.exceptions import BookingConflict
from webapps.booking.models import BookingSources, BookingChange
from webapps.booking.models import Resource
from webapps.booking.models import Appointment, SubBooking
from webapps.booking.serializers.appointment import (
    CalculateStatusMixin,
    SubbookingResourceField,
    AppointmentTravelingMixin,
)
from webapps.booking.serializers.fields import BookingDateTimeField
from webapps.booking.serializers.validators import (
    validate_gap_hole,
    validate_booking_time,
    validate_subbookings_first_booked_from_required,
)
from webapps.business.enums import ComboType
from webapps.business.models import ServiceVariant
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.public_partners.exceptions import ConflictException
from webapps.public_partners.fields import ServiceVariantField, BusinessCustomerInfoField
from webapps.public_partners.populators import (
    populate_subbooking_combo_children,
    populate_subbooking_service,
)
from webapps.public_partners.serializers.appointment_metadata import (
    PASubbookingMetadataSerializerV04,
    PAAppointmentMetadataSerializerV04,
)
from webapps.public_partners.serializers.appointment_mixin import (
    PAAppointmentHistoryMixin,
    PAAppointmentStatusMixin,
    PABusinessAppointmentMixin,
    PACustomerAppointmentMixin,
)
from webapps.public_partners.serializers.metadata import MetadataMixin
from webapps.public_partners.validators import (
    validate_service_variant_or_name,
    validate_service_one_of_resources,
    validate_service_resources,
    validate_service_combo_children,
    AppointmentVersionValidator,
    UnsupportedComboAppointmentSubbookingValidator,
    UnsupportedMultibookingAppointmentValidator,
    UnsupportedRepeatingAppointmentValidator,
)


class PABaseAppointmentSerializer(PAAppointmentHistoryMixin, serializers.Serializer):
    WHO_MAKES_CHANGE = NotImplemented

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for field in {
            "customer_email",
            "customer_phone",
            "customer_name",
        }:
            if data[field] is None:
                data[field] = ""
        return data

    def get_appointment_data(self, validated_data):
        appointment_data = copy.deepcopy(validated_data)
        appointment_data.pop('subbookings', None)
        appointment_data.pop('traveling', None)
        appointment_data.pop('metadata', None)
        appointment_data.pop('_version', None)
        appointment_data.update(
            dict(
                business=self.context['business'],
                updated_by_id=self.context['user'].pk,
                source=(
                    self.instance.source
                    if self.instance
                    else BookingSources.get_cached(app_type=BookingSources.PUBLIC_API_APP)
                ),
                type=self.instance.type if self.instance else Appointment.TYPE.BUSINESS,
                created=self.instance.created if self.instance else tznow(),
            )
        )
        return appointment_data

    def handle_subbookings(self, validated_data):
        raise NotImplementedError()

    @transaction.atomic
    def save(self, **kwargs) -> AppointmentWrapper:
        assert hasattr(self, '_errors'), 'You must call `.is_valid()` before calling `.save()`.'

        assert not self.errors, 'You cannot call `.save()` on a serializer with invalid data.'

        # Guard against incorrect use of `serializer.save(commit=False)`
        assert 'commit' not in kwargs, (
            "'commit' is not a valid keyword argument to the 'save()' method. "
            "If you need to access data before committing to the database then "
            "inspect 'serializer.validated_data' instead. "
            "You can also pass additional keyword arguments to 'save()' if you "
            "need to set extra attributes on the saved model instance. "
            "For example: 'serializer.save(owner=request.user)'.'"
        )

        assert not hasattr(self, '_data'), (
            "You cannot call `.save()` after accessing `serializer.data`."
            "If you need to access data before committing to the database then "
            "inspect 'serializer.validated_data' instead. "
        )

        validated_data = {**self.validated_data, **kwargs}

        if self.instance is not None:
            with self.save_change_history(reason='update'):
                instance = self.update(self.instance, validated_data)
                self.instance = instance.appointment
        else:
            with self.save_change_history(reason='create'):
                instance = self.create(validated_data)
                self.instance = instance.appointment
        return instance

    def create(self, validated_data) -> AppointmentWrapper:
        return self._upsert(validated_data)

    def update(self, instance, validated_data) -> AppointmentWrapper:
        return self._upsert(validated_data)

    def _upsert(self, validated_data):
        subbookings, to_delete = self.handle_subbookings(validated_data)
        instance = AppointmentWrapper(subbookings=subbookings, is_public_api=True)
        try:
            instance.save(
                to_delete=to_delete,
                overbooking=self.validated_data.get('force', False),
                who_makes_change=self.WHO_MAKES_CHANGE,
                new_repeating=False,
                dry_run=False,
                update_future=False,
                recurring=False,
                prev_status=self.instance.status if self.instance else None,
            )
        except BookingConflict as e:
            raise ConflictException(detail=e.message) from e
        return instance


class PAAppointmentSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Appointment
        fields = (
            'id',
            'status',
        )


class PASubbookingSerializerV01(serializers.Serializer):
    class Meta:
        validators = [
            UnsupportedComboAppointmentSubbookingValidator(),
        ]

    id = serializers.IntegerField(read_only=True)
    booked_from = BookingDateTimeField(required=True)
    booked_till = BookingDateTimeField(required=False, allow_null=True)
    staffer_id = SubbookingResourceField(
        required=True,
        source='staffer',
        allow_null=False,
        resource_type=Resource.STAFF,
    )
    appliance_id = SubbookingResourceField(
        required=False,
        source='appliance',
        allow_null=True,
        resource_type=Resource.APPLIANCE,
    )
    service_variant_id = ServiceVariantField(
        read_only=False,
        required=False,
        allow_null=True,
        queryset=ServiceVariant.objects.all(),
    )
    service_name = serializers.CharField(
        required=False,
        allow_null=True,
        max_length=50,
    )

    def validate(self, attrs):
        data = super().validate(attrs)
        service_variant = data.get('service_variant_id')
        if not (service_variant or (data.get('booked_till') and data.get('service_name'))):
            raise serializers.ValidationError(
                'One of these fields is required: '
                'service_variant_id or booked_till with service_name.'
            )
        if service_variant:
            data['booked_till'] = data['booked_from'] + service_variant.duration

        return data


class PAAppointmentSerializerV01(
    CalculateStatusMixin,
    PAAppointmentStatusMixin,
    PABaseAppointmentSerializer,
):
    class Meta:
        validators = [
            UnsupportedMultibookingAppointmentValidator(),
            UnsupportedRepeatingAppointmentValidator(message=_("Can't modify repeating booking")),
        ]

    WHO_MAKES_CHANGE = WhoMakesChange.BUSINESS
    CHANGED_BY = BookingChange.BY_BUSINESS

    id = serializers.IntegerField(read_only=True)
    booked_from = BookingDateTimeField(read_only=True)
    booked_till = BookingDateTimeField(read_only=True)
    status = serializers.ChoiceField(
        choices=AppointmentStatusChoices.choices(),
        read_only=True,
    )
    customer_name = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        write_only=False,
        default='',
        max_length=(consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN + 1),
    )
    customer_phone = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        write_only=False,
        default='',
        max_length=50,
    )
    customer_email = serializers.EmailField(
        required=False,
        allow_null=True,
        allow_blank=True,
        write_only=False,
        default='',
        max_length=75,
    )
    subbookings = PASubbookingSerializerV01(required=True, many=True)
    booked_for_id = BusinessCustomerInfoField(
        required=False,
        source='booked_for',
        allow_null=True,
        queryset=BusinessCustomerInfo.objects.all(),
    )
    force = serializers.BooleanField(
        required=False,
        default=False,
        write_only=True,
    )

    @transaction.atomic
    def confirm(self):
        status = Appointment.STATUS.ACCEPTED
        if self.instance.status is not status:
            with self.save_change_history(reason='confirm'):
                self._update_status(dict(status=status))
        return self.instance

    @transaction.atomic
    def cancel(self):
        status = Appointment.STATUS.CANCELED
        if self.instance.status is not status:
            with self.save_change_history(reason='cancel'):
                self._update_status(dict(status=status))
        return self.instance

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if booked_for := attrs.get('booked_for', self.instance and self.instance.booked_for):
            customer_data = booked_for.as_customer_data()
            attrs['customer_name'] = customer_data.full_name
            attrs['customer_email'] = customer_data.email
            attrs['customer_phone'] = customer_data.cell_phone
        attrs['status'] = self.calculate_status(self.instance, attrs, False)
        return attrs

    @staticmethod
    def validate_subbookings(attrs):
        if len(attrs) != 1:
            raise serializers.ValidationError('Exactly one subbooking is required')
        return attrs

    def handle_subbookings(self, validated_data):
        appointment_data = self.get_appointment_data(validated_data)
        if self.instance:
            existing_bookings = list(self.instance.subbookings)
            appointment = self.instance
        else:
            existing_bookings = []
            appointment = Appointment()
        for field, value in appointment_data.items():
            setattr(appointment, field, value)

        subbooking_data = validated_data.pop('subbookings')
        subbookings = []
        now = tznow()
        for data, subbooking in zip_longest(subbooking_data, existing_bookings):
            if data.get('service_variant_id'):
                data['service_name'] = data['service_variant_id'].service.name
                data['service_variant_id'] = data['service_variant_id'].id

            if not subbooking:
                subbooking = SubBooking(
                    created=appointment_data.get('created', now),
                    appointment=appointment,
                    autoassign=False,
                )
            for key, value in data.items():
                setattr(subbooking, key, value)
            subbookings.append(subbooking)
        return subbookings, []


class PAAppointmentSerializerV02(PAAppointmentSerializerV01):
    business_id = serializers.PrimaryKeyRelatedField(
        read_only=True,
        required=False,
        source='business',
    )
    business_timezone = TimezoneNameField(
        source='business.get_timezone',
        read_only=True,
        required=False,
    )
    import_uid = serializers.CharField(required=False)
    customer_note = serializers.CharField(read_only=True, required=False)
    business_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
    )


class PASubbookingSerializerV03(serializers.Serializer):
    class Meta:
        validators = [
            validate_service_variant_or_name,
            validate_service_one_of_resources,
            validate_service_resources,
            validate_booking_time,
            validate_gap_hole,
            UnsupportedComboAppointmentSubbookingValidator(),
        ]

    autoassign = serializers.HiddenField(default=False)
    id = serializers.IntegerField(read_only=True)
    booked_from = BookingDateTimeField(required=True)
    booked_till = BookingDateTimeField(required=False, allow_null=True)
    service_variant_id = ServiceVariantField(
        required=False,
        source='service_variant',
        allow_null=True,
        queryset=ServiceVariant.objects.filter(active=True),
    )
    service_name = serializers.CharField(
        required=False,
        allow_null=True,
        max_length=50,
    )
    staffer_id = SubbookingResourceField(
        required=False,
        source='staffer',
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    appliance_id = SubbookingResourceField(
        required=False,
        source='appliance',
        allow_null=True,
        resource_type=Resource.APPLIANCE,
    )
    force = serializers.BooleanField(
        required=False,
        default=False,
        write_only=True,
    )

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        data = populate_subbooking_service(data)
        return data


class PABaseAppointmentSerializerV03(
    CalculateStatusMixin,
    PABaseAppointmentSerializer,
):
    class Meta:
        validators = [
            validate_subbookings_first_booked_from_required,
            UnsupportedRepeatingAppointmentValidator(),
        ]

    WHO_MAKES_CHANGE = WhoMakesChange.BUSINESS
    CHANGED_BY = BookingChange.BY_BUSINESS

    dry_run = serializers.HiddenField(default=False)
    id = serializers.IntegerField(read_only=True)
    business_id = serializers.PrimaryKeyRelatedField(
        read_only=True,
        required=False,
        source='business',
    )
    booked_for_id = BusinessCustomerInfoField(
        required=False,
        source='booked_for',
        allow_null=True,
        queryset=BusinessCustomerInfo.objects.all(),
    )
    status = serializers.ChoiceField(
        choices=AppointmentStatusChoices.choices(),
        read_only=True,
    )
    status_changed = serializers.DateTimeField(read_only=True)
    type = serializers.ChoiceField(
        choices=Appointment.TYPE_CHOICES,
        read_only=True,
    )
    booked_from = BookingDateTimeField(read_only=True)
    booked_till = BookingDateTimeField(read_only=True)
    subbookings = PASubbookingSerializerV03(required=True, many=True, allow_empty=False)
    customer_name = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        default='',
        max_length=(consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN + 1),
    )
    customer_phone = BooksyPhoneSerializerField(
        required=False,
        allow_null=True,
        allow_blank=True,
        default='',
        max_length=50,
    )
    customer_email = serializers.EmailField(
        required=False,
        allow_null=True,
        allow_blank=True,
        default='',
        max_length=75,
    )
    customer_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
    )
    business_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
    )
    business_timezone = TimezoneNameField(
        source='business.get_timezone',
        read_only=True,
        required=False,
    )
    import_uid = serializers.CharField(required=False)
    force = serializers.BooleanField(
        required=False,
        default=False,
        write_only=True,
    )

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        data = self._prepare_customer(data)
        data['status'] = self.calculate_status(self.instance, data, False)
        return data

    def handle_subbookings(self, validated_data):
        if self.instance:
            existing_bookings = list(self.instance.subbookings)
            appointment = self.instance
        else:
            existing_bookings = []
            appointment = Appointment()

        appointment_data = self.get_appointment_data(validated_data)
        for field, value in appointment_data.items():
            setattr(appointment, field, value)

        subbookings = []
        to_delete = []
        for subbooking_data, booking in self.match_subbokings(
            validated_data.pop('subbookings'),
            existing_bookings,
        ):
            if subbooking_data:
                # update or create
                booking = booking or SubBooking(
                    created=(tznow() if self.instance else appointment_data.get('created')),
                    appointment=appointment,
                )
                for attr, value in subbooking_data.items():
                    setattr(booking, attr, value)
                subbookings.append(booking)
            elif booking:
                # subbooking no longer needed - we will delete it
                to_delete.append(booking)
        return subbookings, to_delete

    @staticmethod
    def match_subbokings(subbooking_data, existing_bookings):
        order_dct = {id(d): i for i, d in enumerate(subbooking_data)}
        subbooking_data_dict = defaultdict(list)
        existing_data_dict = defaultdict(list)

        for booking in subbooking_data:
            variant = booking.get('service_variant')
            key = f'sv_{variant.id}' if variant else f'sn_{booking.get("service_name", "")}'
            subbooking_data_dict[key].append(booking)

        for booking in existing_bookings:
            variant_id = booking.service_variant_id
            key = f'sv_{variant_id}' if variant_id else f'sn_{booking.service_name or ""}'
            existing_data_dict[key].append(booking)

        data = []
        left_subbooking_data = []
        left_exisiting_subbookings = []

        for key in set(subbooking_data_dict) | set(existing_data_dict):
            subbooking_data = subbooking_data_dict[key]
            existing_data = existing_data_dict[key]

            if len(subbooking_data) > len(existing_data):
                left_subbooking_data += subbooking_data[len(existing_data) :]
                subbooking_data = subbooking_data[: len(existing_data)]

            elif len(existing_data) > len(subbooking_data):
                left_exisiting_subbookings += existing_data[len(subbooking_data) :]
                existing_data = existing_data[: len(subbooking_data)]

            data += list(zip(subbooking_data, existing_data))
        data += list(zip_longest(left_subbooking_data, left_exisiting_subbookings))
        return sorted(data, key=lambda a: order_dct.get(id(a[0]), float('inf')))

    def _prepare_customer(self, data):
        if booked_for := data.get('booked_for', self.instance and self.instance.booked_for):
            customer_data = booked_for.as_customer_data()
            data['customer_name'] = customer_data.full_name
            data['customer_email'] = customer_data.email
            data['customer_phone'] = customer_data.cell_phone
        return data


class PABusinessAppointmentSerializerV03(
    AppointmentTravelingMixin,
    PABusinessAppointmentMixin,
    PABaseAppointmentSerializerV03,
):
    business_secret_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_SECRET_NOTE__MAX_LENGTH,
    )


class PACustomerAppointmentSerializerV03(
    AppointmentTravelingMixin,
    PACustomerAppointmentMixin,
    PABaseAppointmentSerializerV03,
):
    pass


class PASubbookingComboChildSerializerV04(serializers.Serializer):
    class Meta:
        validators = [
            validate_service_one_of_resources,
            validate_service_resources,
            validate_booking_time,
        ]

    autoassign = serializers.HiddenField(default=False)
    id = serializers.IntegerField(read_only=True)
    booked_from = BookingDateTimeField(read_only=True)
    booked_till = BookingDateTimeField(read_only=True)
    service_variant_id = ServiceVariantField(
        required=True,
        source='service_variant',
        queryset=ServiceVariant.objects.filter(active=True),
    )
    service_id = serializers.IntegerField(
        source='service_variant.service_id',
        read_only=True,
    )
    service_name = serializers.CharField(
        required=False,
        allow_null=True,
        max_length=50,
    )
    staffer_id = SubbookingResourceField(
        required=False,
        source='staffer',
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    appliance_id = SubbookingResourceField(
        required=False,
        source='appliance',
        allow_null=True,
        resource_type=Resource.APPLIANCE,
    )
    force = serializers.BooleanField(
        required=False,
        default=False,
        write_only=True,
    )
    metadata = PASubbookingMetadataSerializerV04(required=False, allow_null=True)


class PASubbookingSerializerV04(PASubbookingSerializerV03):
    class Meta:
        validators = [
            validate_service_variant_or_name,
            validate_service_one_of_resources,
            validate_service_resources,
            validate_service_combo_children,
            validate_booking_time,
            validate_gap_hole,
        ]

    combo_type = serializers.ChoiceField(
        choices=ComboType.choices(),
        source='service_variant.service.combo_type',
        read_only=True,
        allow_null=True,
    )
    combo_children = PASubbookingComboChildSerializerV04(
        required=False,
        many=True,
        allow_empty=True,
    )
    service_id = serializers.IntegerField(source='service_variant.service_id', read_only=True)
    metadata = PASubbookingMetadataSerializerV04(required=False, allow_null=True)

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        data = populate_subbooking_combo_children(data)
        data = populate_subbooking_service(data)
        return data


class PABaseAppointmentSerializerV04(PABaseAppointmentSerializerV03):
    class Meta(PABaseAppointmentSerializerV03.Meta):
        validators = [
            validate_subbookings_first_booked_from_required,
            UnsupportedRepeatingAppointmentValidator(),
            AppointmentVersionValidator(),
        ]

    subbookings = PASubbookingSerializerV04(required=True, many=True, allow_empty=False)
    version = serializers.IntegerField(source='_version', required=False)
    created = serializers.DateTimeField(read_only=True)
    updated = serializers.DateTimeField(read_only=True)

    def handle_subbookings(self, validated_data):
        if self.instance:
            existing_bookings = list(self.instance.subbookings)
            appointment = self.instance
        else:
            existing_bookings = []
            appointment = Appointment()

        appointment_data = self.get_appointment_data(validated_data)
        for field, value in appointment_data.items():
            setattr(appointment, field, value)

        subbookings = []
        to_delete = []
        for subbooking_data, booking in self.match_subbokings(
            validated_data.pop('subbookings'),
            existing_bookings,
        ):
            if subbooking_data:
                # update or create
                booking = booking or SubBooking(
                    created=(tznow() if self.instance else appointment_data.get('created')),
                    appointment=appointment,
                )
                (
                    booking.combo_children,
                    combo_children_to_delete,
                ) = self.handle_subbooking_combo_children(
                    booking,
                    subbooking_data.pop('combo_children', []),
                )
                for attr, value in subbooking_data.items():
                    setattr(booking, attr, value)
                subbookings.append(booking)
                to_delete.extend(combo_children_to_delete)
            elif booking:
                # subbooking no longer needed - we will delete it
                to_delete.append(booking)
                to_delete.extend(booking.combo_children)
        return subbookings, to_delete

    def handle_subbooking_combo_children(self, subbooking, combo_children_data):
        combo_children = []
        to_delete = []

        for combo_child_data, combo_child in self.match_subbokings(
            combo_children_data,
            subbooking.combo_children,
        ):
            if combo_child_data:
                combo_child = combo_child or SubBooking(
                    created=(tznow() if self.instance else subbooking.created),
                    appointment=subbooking.appointment,
                    combo_parent=subbooking,
                )
                for attr, value in combo_child_data.items():
                    setattr(combo_child, attr, value)
                combo_children.append(combo_child)
            elif combo_child:
                to_delete.append(combo_child)

        return combo_children, to_delete


class PABusinessAppointmentSerializerV04(
    MetadataMixin,
    AppointmentTravelingMixin,
    PABusinessAppointmentMixin,
    PABaseAppointmentSerializerV04,
):
    metadata = PAAppointmentMetadataSerializerV04(required=False, allow_null=True)
    business_secret_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_SECRET_NOTE__MAX_LENGTH,
    )

    @contextmanager
    def save_with_metadata(self, validated_data):
        has_appointment_metadata = 'metadata' in validated_data
        has_subbookings_metadata = any('metadata' in s for s in validated_data['subbookings'])
        has_combo_children_metadata = any(
            'combo_children' in s and any('metadata' in c for c in s['combo_children'])
            for s in validated_data['subbookings']
        )
        appointment_metadata = validated_data.pop('metadata', None)
        subbookings_metadata = [s.pop('metadata', None) for s in validated_data['subbookings']]
        subbookings_combo_children_metadata = [
            [c.pop('metadata', None) for c in s.get('combo_children', [])]
            for s in validated_data['subbookings']
        ]
        yield
        if has_appointment_metadata:
            self._save_metadata(self.instance, appointment_metadata)
        if has_subbookings_metadata:
            for i, metadata in enumerate(subbookings_metadata):
                self._save_metadata(self.instance.subbookings[i], metadata)
        if has_combo_children_metadata:
            for i, combo_children_metadata in enumerate(subbookings_combo_children_metadata):
                for j, metadata in enumerate(combo_children_metadata):
                    self._save_metadata(self.instance.subbookings[i].combo_children[j], metadata)


class PACustomerAppointmentSerializerV04(
    AppointmentTravelingMixin,
    PACustomerAppointmentMixin,
    PABaseAppointmentSerializerV04,
):
    pass
