# pylint: skip-file

from django.db import transaction
from rest_framework import serializers

from webapps import consts
from webapps.booking.enums import WhoMakesChange
from webapps.booking.models import BookingChange
from webapps.booking.models import Appointment
from webapps.public_partners.serializers.appointment_mixin import (
    PAAppointmentHistoryMixin,
    PAAppointmentStatusMixin,
)
from webapps.public_partners.validators import (
    AppointmentStatusTransitionValidator,
    AppointmentVersionValidator,
)


class PABaseAppointmentStatusV03Serializer(
    PAAppointmentHistoryMixin,
    PAAppointmentStatusMixin,
    serializers.Serializer,
):
    class Meta:
        validators = [
            AppointmentStatusTransitionValidator(),
        ]

    WHO_MAKES_CHANGE = NotImplemented

    status = serializers.ChoiceField(
        required=True,
        choices=Appointment.STATUS.choices(),
    )
    customer_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
    )
    business_note = serializers.Char<PERSON>ield(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
    )


class PABusinessAppointmentStatusV03Serializer(PABaseAppointmentStatusV03Serializer):
    WHO_MAKES_CHANGE = WhoMakesChange.BUSINESS
    CHANGED_BY = BookingChange.BY_BUSINESS
    UPDATE_STATUS_FIELDS = ['status', 'business_note']

    customer_note = serializers.CharField(required=False, read_only=True)

    @transaction.atomic
    def confirm(self):
        with self.save_change_history(reason='confirm'):
            self._update_status()
            return self.instance

    @transaction.atomic
    def cancel(self):
        with self.save_change_history(reason='cancel'):
            self._update_status()
            return self.instance

    @transaction.atomic
    def decline(self):
        with self.save_change_history(reason='decline'):
            self._update_status()
            return self.instance

    @transaction.atomic
    def finish(self):
        with self.save_change_history(reason='finish'):
            self._update_status()
            return self.instance

    @transaction.atomic
    def no_show(self):
        with self.save_change_history(reason='no_show'):
            self._update_status()
            return self.instance


class PABusinessAppointmentStatusV04Serializer(PABusinessAppointmentStatusV03Serializer):
    class Meta(PABusinessAppointmentStatusV03Serializer.Meta):
        validators = [
            AppointmentVersionValidator(),
            AppointmentStatusTransitionValidator(),
        ]

    version = serializers.IntegerField(source='_version', required=True)


class PACustomerAppointmentStatusV03Serializer(PABaseAppointmentStatusV03Serializer):
    WHO_MAKES_CHANGE = WhoMakesChange.CUSTOMER
    CHANGED_BY = BookingChange.BY_CUSTOMER
    UPDATE_STATUS_FIELDS = ['status', 'customer_note']

    business_note = serializers.CharField(required=False, read_only=True)

    @transaction.atomic
    def accept(self):
        with self.save_change_history(reason='accept'):
            self._update_status()
            return self.instance

    @transaction.atomic
    def cancel(self):
        with self.save_change_history(reason='cancel'):
            self._update_status()
            return self.instance


class PACustomerAppointmentStatusV04Serializer(PACustomerAppointmentStatusV03Serializer):
    class Meta(PACustomerAppointmentStatusV03Serializer.Meta):
        validators = [
            AppointmentVersionValidator(),
            AppointmentStatusTransitionValidator(),
        ]

    version = serializers.IntegerField(source='_version', required=True)
