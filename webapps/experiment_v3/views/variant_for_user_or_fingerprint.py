from bo_obs.datadog.enums import BooksyTeams

from rest_framework.response import Response
from webapps.experiment_v3.exp.cx_onboarding_welcome_modal import CxOnboardingWelcomeModalExperiment
from webapps.experiment_v3.exp.full_calendar_on_widget import FullCalendarOnWidgetExperiment
from webapps.experiment_v3.exp.hide_past_dates_on_customer_calendar import (
    HidePastDatesOnCustomerCalendarExperiment,
)
from webapps.experiment_v3.exp.incentivize_with_gift_card import (
    IncentivizeWithGiftCardExperiment,
)
from webapps.experiment_v3.views.variant_for_user import (
    ExperimentVariantForUserView,
    ExperimentVariantForUserViewSchema,
)


class ExperimentVariantForUserOrFingerprintViewSchema(ExperimentVariantForUserViewSchema):

    def get_path_parameters(self, path, method):
        valid_experiments = ExperimentVariantForUserOrFingerprintView.valid_experiments
        return [
            {
                'name': 'experiment_name',
                'in': 'path',
                'required': True,
                'description': '',
                'schema': {
                    'type': 'string',
                    'enum': [experiment.name for experiment in valid_experiments],
                },
            },
        ]


class ExperimentVariantForUserOrFingerprintView(ExperimentVariantForUserView):
    booksy_teams = (BooksyTeams.TEST_TEAM,)

    schema = ExperimentVariantForUserOrFingerprintViewSchema()

    valid_experiments = [
        FullCalendarOnWidgetExperiment,
        CxOnboardingWelcomeModalExperiment,
        HidePastDatesOnCustomerCalendarExperiment,
    ]

    # experiments that should keep the same value for user that is logged in and logged out
    experiments_fingerprint_only = [
        FullCalendarOnWidgetExperiment.name,
        CxOnboardingWelcomeModalExperiment.name,
        HidePastDatesOnCustomerCalendarExperiment.name,
    ]

    # pylint: disable=useless-parent-delegation
    # used for swagger method description
    def get(self, request, experiment_name):
        """
        Get experiment variant for the current user, use fingerprint if user is not logged in.
        For experiments defined in experiment_fingerprint_only use only fingerprint as relation_id
        """
        if experiment_name == IncentivizeWithGiftCardExperiment.name:
            variant = IncentivizeWithGiftCardExperiment.get_variant_from_eppo_with_counting(
                self.fingerprint
            )
            serializer = self.get_serializer(instance={'variant': variant})
            return Response(serializer.data)
        return super().get(request, experiment_name)

    def get_variant(self, experiment_class):

        if experiment_class.name in self.experiments_fingerprint_only:
            relation_id = self.fingerprint
        else:
            relation_id = self.user_id or self.fingerprint
        return (
            experiment_class(relation_id=relation_id).get_variant(return_usable_name=False)
            if relation_id
            else None
        )


class DeprecatedExperimentVariantForUserOrFingerprintView(
    ExperimentVariantForUserOrFingerprintView
):
    swagger_mark_deprecated = True
