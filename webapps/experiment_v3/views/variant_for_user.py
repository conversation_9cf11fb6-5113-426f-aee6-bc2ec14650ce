import typing as t

from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.permissions.authentication import OptionalLogin
from lib_drf.docs.schema import BooksyAutoSchema
from webapps.experiment_v3.exp import AlternativeSwitchToAnyoneExperiment
from webapps.experiment_v3.exp.base import BaseExperimentV3
from webapps.experiment_v3.exp.easier_access_to_book_again_on_web_experiment import (
    EasierAccessToBookAgainOnWebExperiment,
)
from webapps.experiment_v3.exp.wait_list import WaitListEmphasizedTimeExperiment
from webapps.experiment_v3.serializers import ExperimentVariantForUserResponseSerializer


class ExperimentVariantForUserViewSchema(BooksyAutoSchema):
    def get_path_parameters(self, path, method):
        return [
            {
                'name': 'experiment_name',
                'in': 'path',
                'required': True,
                'description': '',
                'schema': {
                    'type': 'string',
                    'enum': [
                        experiment.name
                        for experiment in ExperimentVariantForUserView.valid_experiments
                    ],
                },
            },
        ]

    def get_responses(self, path, method):
        serializer = self.get_response_serializer(path, method)
        return {
            status.HTTP_200_OK: {
                'content': {
                    'application/json': {
                        'schema': self._get_reference(serializer),
                    },
                },
                'description': 'Experiment variant for the current user',
            },
            status.HTTP_400_BAD_REQUEST: {
                'content': {
                    'application/json': {},
                },
                'description': 'Invalid experiment name',
            },
        }


class ExperimentVariantForUserView(  # nosemgrep: no-is-authenticated-permission-for-drf
    BaseBooksySessionGenericAPIView
):
    permission_classes = (OptionalLogin,)
    schema = ExperimentVariantForUserViewSchema()
    serializer_class = ExperimentVariantForUserResponseSerializer
    booksy_teams = (BooksyTeams.DEFAULT_MAINTENANCE,)

    valid_experiments = [
        AlternativeSwitchToAnyoneExperiment,
        EasierAccessToBookAgainOnWebExperiment,
        WaitListEmphasizedTimeExperiment,
    ]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._valid_experiments = {
            experiment.name: experiment for experiment in self.valid_experiments
        }

    def get(self, request, experiment_name):
        """
        Get experiment variant for the current user.
        Draw variant if none was selected before.
        """
        experiment_class = self.get_experiment_class(experiment_name)
        variant = self.get_variant(experiment_class)
        serializer = self.get_serializer(instance={'variant': variant})
        return Response(serializer.data)

    def get_variant(self, experiment_class):
        return (
            experiment_class(relation_id=self.user_id).get_variant(return_usable_name=False)
            if self.user_id
            else None
        )

    def get_experiment_class(self, name) -> t.Type[BaseExperimentV3]:
        try:
            return self._valid_experiments[name]
        except KeyError as e:
            raise ValidationError('Invalid experiment name') from e

    @property
    def user_id(self):
        return self.user.id if self.user else None


class DeprecatedExperimentVariantForUserView(ExperimentVariantForUserView):
    booksy_teams = (BooksyTeams.DEFAULT_MAINTENANCE,)
    swagger_mark_deprecated = True
