from webapps.experiment_v3.exp.alternative_switch_to_anyone_experiment import (
    AlternativeSwitchToAnyoneExperiment,
)
from webapps.experiment_v3.exp.booking_confirmation_experiment import (
    BookingConfirmationExperiment,
    BookingConfirmationExperimentVer2,
)
from webapps.experiment_v3.exp.boost_appointment_cancellation import BoostAppointmentCancellation
from webapps.experiment_v3.exp.bounding_box import BoundingBoxExperiment
from webapps.experiment_v3.exp.churn_reason import ChurnReasonExperiment
from webapps.experiment_v3.exp.df_communication import DfCommunication
from webapps.experiment_v3.exp.easier_access_to_book_again_on_web_experiment import (
    EasierAccessToBookAgainOnWebExperiment,
)
from webapps.experiment_v3.exp.easier_access_to_popular_calendar_filters import (
    EasierAccessToPopularCalendarFiltersExperiment,
)
from webapps.experiment_v3.exp.email_kyc_verified import EmailKYCVerifiedExperiment
from webapps.experiment_v3.exp.experiment_payment_processor_update import (
    ConsentAdyenStripeCalendarTextExperiment,
)
from webapps.experiment_v3.exp.full_calendar_on_widget import FullCalendarOnWidgetExperiment
from webapps.experiment_v3.exp.hide_past_dates_on_customer_calendar import (
    HidePastDatesOnCustomerCalendarExperiment,
)
from webapps.experiment_v3.exp.hint_and_walkthrough import HintAndWalkthroughExperiment
from webapps.experiment_v3.exp.new_on_booksy_gallery import NewOnBooksyGalleryExperiment
from webapps.experiment_v3.exp.onboarding_address_and_location import (
    OnboardingAddressAndLocationExperiment,
)
from webapps.experiment_v3.exp.profile_delay_days import ProfileDelayDays
from webapps.experiment_v3.exp.r_and_d import UTT2InSearch
from webapps.experiment_v3.exp.trial_subscription_message import TrialSubscriptionMessageExperiment
from webapps.experiment_v3.exp.wait_list import WaitListEmphasizedTimeExperiment
from webapps.experiment_v3.exp.cx_onboarding_welcome_modal import CxOnboardingWelcomeModalExperiment

experiments = [
    DfCommunication,
    UTT2InSearch,
    BoundingBoxExperiment,
    WaitListEmphasizedTimeExperiment,
    EasierAccessToBookAgainOnWebExperiment,
    AlternativeSwitchToAnyoneExperiment,
    CxOnboardingWelcomeModalExperiment,
    HidePastDatesOnCustomerCalendarExperiment,
    # Growth
    OnboardingAddressAndLocationExperiment,
    NewOnBooksyGalleryExperiment,
    HintAndWalkthroughExperiment,
    ChurnReasonExperiment,
    # Growth Revenue
    ConsentAdyenStripeCalendarTextExperiment,
    EmailKYCVerifiedExperiment,
    # Boost / Px Marketing
    BoostAppointmentCancellation,
    # Calendar
    EasierAccessToPopularCalendarFiltersExperiment,
    TrialSubscriptionMessageExperiment,
    ProfileDelayDays,
    # Customer Booking
    BookingConfirmationExperiment,
    BookingConfirmationExperimentVer2,
    FullCalendarOnWidgetExperiment,
]

experiments_dict = {e.name: e for e in experiments}

__all__ = [e.__name__ for e in experiments]
