from django.utils.translation import gettext as _, ngettext
from rest_framework import serializers

from lib.serializers import Translated<PERSON><PERSON><PERSON>ield
from lib.tools import tznow
from webapps.booking.serializers.appointment import BusinessInCustomerAppointmentSerializer
from webapps.business.business_categories.cache import (
    CategoryCache,
    SubcategoryCache,
)
from webapps.consents.serializers import CustomerConsentSerializer
from webapps.pop_up_notification.models import (
    B2BReferralNotification,
    BusinessYouMayLikeNotification,
    CategoryYouMayLikeNotification,
    ConsentsNotification,
    DigitalFlyerNotification,
    FamilyAndFriendsBusinessSetupNotification,
    FamilyAndFriendsInvitationNotification,
    FamilyAndFriendsInvitationResponseNotification,
    FamilyAndFriendsUnlinkNotification,
    GenericPopUpNotificationModel,
    LateCancellationNotification,
    MaxLeadTimeNotification,
    MobilePaymentsIntroduction,
    MobilePaymentsMigration,
    MobilePaymentsTurnOnPrepayments,
    RewardC2BStatusChangeNotification,
    ShortReviewNotification,
)


class GenericPopUpActionSerializer(serializers.ModelSerializer):
    class Meta:
        model = GenericPopUpNotificationModel
        fields = ("id", "action", "user_id")

    USED_NOTIFICATION = "used"
    REJECTED_NOTIFICATION = "rejected"
    ACTIONS = (
        USED_NOTIFICATION,
        REJECTED_NOTIFICATION,
    )
    action = serializers.ChoiceField(choices=ACTIONS)
    user_id = serializers.IntegerField()

    def validate_user_id(self, value):
        if self.instance.user.id != value:
            raise serializers.ValidationError(
                _("Non existing push-up notification for current user.")
            )
        return value

    def validate(self, attrs):
        action = attrs.get('action')
        now = tznow()
        if self.instance.used and action == self.REJECTED_NOTIFICATION:
            raise serializers.ValidationError(_("Used notification can't be rejected"))
        if self.instance.deleted is not None and action == self.USED_NOTIFICATION:
            raise serializers.ValidationError(_("Rejected notification can't  be used"))
        if self.instance.valid_till is not None and self.instance.valid_till < now:
            raise serializers.ValidationError(_("Past notification can't be modified"))

        return attrs

    def update(self, instance, validated_data):
        action = validated_data.get("action")

        if (
            action == self.USED_NOTIFICATION
            or instance.notification_type == 'booksy_gift_card_visa_cashback'
        ):
            # mark notification as used
            instance.used = True
            instance.save()
        elif action == self.REJECTED_NOTIFICATION:
            # reject this notification
            instance.soft_delete()

        return instance


class BusinessYouMayLikeSerializer(serializers.Serializer):

    title = _('We think, you may like')

    class Meta:
        model = BusinessYouMayLikeNotification
        fields = ('id', 'business', 'see_other')

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['id'] = instance.id
        ret['title'] = self.title
        ret['business'] = BusinessInCustomerAppointmentSerializer(instance=instance.business).data
        ret['see_other'] = CategoryCache.get_by_id(
            instance.see_other.id
        ) or SubcategoryCache.get_by_id(instance.see_other.id)
        return ret


class CategoryYouMayLikeSerializer(serializers.Serializer):

    title = _('We think, you may like')
    title2 = _('Maybe, you are looking for')

    class Meta:
        model = CategoryYouMayLikeNotification
        fields = ('id', 'region', 'business_categories')

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        ret['id'] = instance.id
        ret['title'] = self.title
        ret['title2'] = self.title2

        ret['business_categories'] = CategoryCache.get_by_id(
            instance.business_categories.id
        ) or SubcategoryCache.get_by_id(instance.business_categories.id)
        return ret


class ShortReviewSerializer(serializers.Serializer):

    class Meta:
        model = ShortReviewNotification
        fields = ()

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        if instance.subbooking.staffer:
            staffer_name = instance.subbooking.staffer.name
            staffer_id = instance.subbooking.staffer_id
        else:
            staffer_name = None
            staffer_id = None

        business = instance.subbooking.appointment.business

        ret['id'] = instance.id
        ret['booking_id'] = instance.subbooking_id
        ret['business'] = BusinessInCustomerAppointmentSerializer(
            instance=instance.subbooking.appointment.business
        ).data
        ret['staffer_name'] = staffer_name
        ret['staffer_id'] = staffer_id
        # TODO delete title1 and title2 after all apps will use of title
        #  approximate date 14.03.2019
        ret['title1'] = _('How was your appointment with')
        if staffer_name is None:
            ret['title2'] = _('<b>{}</b>?').format(business.name)
        else:
            ret['title2'] = _('<b>{}</b> at <b>{}</b>?').format(
                staffer_name,
                business.name,
            )

        ret['title'] = f"{ret['title1']} {ret['title2']}"
        return ret


class LateCancellationSerializer(serializers.ModelSerializer):
    title1 = _('Another Late Cancellation?')

    class Meta:
        model = LateCancellationNotification
        fields = ('id', 'booked_from')


class RewardC2BStatusChangeSerializer(serializers.ModelSerializer):
    business_id = serializers.PrimaryKeyRelatedField(
        read_only=True, required=False, source='business'
    )

    class Meta:
        model = RewardC2BStatusChangeNotification
        fields = (
            'id',
            'status',
            'business_name',
            'required_paying_months',
            'reward_text',
            'reward_text_short',
            'business_id',
        )


class MaxLeadTimeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaxLeadTimeNotification
        fields = ('id', 'notification_type')


class DigitalFlyerSerializer(serializers.ModelSerializer):
    description = TranslatedCharField()
    title = TranslatedCharField()
    button_text = TranslatedCharField()

    class Meta:
        model = DigitalFlyerNotification
        fields = (
            'id',
            'notification_type',
            'event_type',
            'display_type',
            'title',
            'description',
            'button_text',
            'df_category_id',
            'df_background_id',
            'df_text_id',
        )


class MobilePaymentsIntroductionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MobilePaymentsIntroduction
        fields = ('id',)


class MobilePaymentsMigrationSerializer(serializers.ModelSerializer):
    class Meta:
        model = MobilePaymentsMigration
        fields = ('id',)


class MobilePaymentsTurnOnPrepaymentsSerializer(serializers.ModelSerializer):
    class Meta:
        model = MobilePaymentsTurnOnPrepayments
        fields = ('id',)


class ConsentsNotificationPopUpSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConsentsNotification
        fields = ('id', 'notification_type', 'used', 'valid_till', 'consents')

    consents = CustomerConsentSerializer(many=True, source='unsigned_consents')

    def to_representation(self, instance):
        data = super().to_representation(instance)

        consents_data = data['consents']

        if instance.business:
            business_name = instance.business.name
        else:
            try:
                business_name = consents_data[0]['business_name']
            except IndexError:
                business_name = _('Business')

        data['title'] = ngettext(
            '{} asks that you complete a custom form',
            '{} asks that you complete custom forms',
            len(consents_data),
        ).format(business_name)

        data['title2'] = ngettext(
            'Save time by signing it now', 'Save time by signing them now', len(consents_data)
        )

        return data


class B2BReferralNotificationSerializer(serializers.ModelSerializer):

    class Meta:
        model = B2BReferralNotification
        fields = (
            'id',
            'image_type',
            'text_1',
            'text_2',
            'text_3',
            'text_button',
            'event_type',
        )


class FamilyAndFriendsInvitationNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = FamilyAndFriendsInvitationNotification
        fields = ('id', 'parent_id', 'parent_full_name', 'key')

    parent_id = serializers.PrimaryKeyRelatedField(read_only=True, required=False, source='parent')

    key = serializers.CharField(max_length=40)

    parent_full_name = serializers.CharField(source='parent.full_name', read_only=True)


class FamilyAndFriendsInvitationResponseNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = FamilyAndFriendsInvitationResponseNotification
        fields = ('id', 'member_id', 'member_full_name', 'invitation_status')

    member_id = serializers.PrimaryKeyRelatedField(read_only=True, required=False, source='member')
    member_full_name = serializers.CharField(source='member.full_name', read_only=True)


class FamilyAndFriendsUnlinkNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = FamilyAndFriendsUnlinkNotification
        fields = ('id', 'trigger_profile_id', 'trigger_profile_full_name')

    trigger_profile_id = serializers.PrimaryKeyRelatedField(
        read_only=True, required=False, source='trigger_profile'
    )
    trigger_profile_full_name = serializers.CharField(
        source='trigger_profile.full_name', read_only=True
    )


class FamilyAndFriendsBusinessSetupNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = FamilyAndFriendsBusinessSetupNotification
        fields = ('id', 'business_name')


class WhatIsNewFamilyAndFriendsSerializer(serializers.ModelSerializer):
    class Meta:
        model = GenericPopUpNotificationModel
        fields = ('id', 'notification_type')
