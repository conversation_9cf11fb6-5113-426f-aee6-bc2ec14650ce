import enum

from dateutil.relativedelta import relativedelta
from django.utils.translation import gettext_lazy as _

from lib.enums import DomainEvent, StrChoicesEnum, StrEnum


class AppointmentStatus(StrEnum):
    ACCEPTED = 'A'  # Accepted by business
    CANCELED = 'C'
    DECLINED = 'D'
    FINISHED = 'F'
    MODIFIED = 'M'  # Modified by customer
    NOSHOW = 'N'
    PROPOSED = 'P'  # Proposed by staff
    UNCONFIRMED = 'W'
    REJECTED = 'R'  # DEPRECATED STATUS
    UNVERIFIED = 'V'  # DEPRECATED STATUS
    PENDING_PAYMENT = 'O'


class AppointmentStatusChoices(StrChoicesEnum):
    ACCEPTED = AppointmentStatus.ACCEPTED.value, _('Confirmed')
    CANCELED = AppointmentStatus.CANCELED.value, _('Cancelled')
    DECLINED = AppointmentStatus.DECLINED.value, _('Declined')
    FINISHED = AppointmentStatus.FINISHED.value, _('Finished')
    MODIFIED = AppointmentStatus.MODIFIED.value, _('Modified')
    PROPOSED = AppointmentStatus.PROPOSED.value, _('Proposed')
    NOSHOW = AppointmentStatus.NOSHOW.value, _('No-show')
    UNCONFIRMED = AppointmentStatus.UNCONFIRMED.value, _('Waiting for confirmation (by business)')
    PENDING_PAYMENT = AppointmentStatus.PENDING_PAYMENT.value, _('Waiting for deposit')
    # DEPRECATED STATUSES
    REJECTED = AppointmentStatus.REJECTED.value, _('Rejected')
    UNVERIFIED = AppointmentStatus.UNVERIFIED.value, _('Awaiting email (account) verification')


class AppointmentTypeSM(StrEnum):
    SINGLE = 'single'
    MULTI = 'multi'


class AppointmentTypeSMChoices(StrChoicesEnum):
    MULTI = AppointmentTypeSM.MULTI.value, _('Multi booking appointment')
    SINGLE = AppointmentTypeSM.SINGLE.value, _('Single booking appointment')


class BookingAction(StrEnum):
    CANCEL = 'cancel'
    # customer only actions
    ACCEPT = 'accept'
    PAY = 'pay'
    # business only actions
    CONFIRM = 'confirm'
    DECLINE = 'decline'
    NO_SHOW = 'no_show'
    CANCEL_NO_SHOW = 'cancel_no_show'
    # not actions: it means that booking is editable
    CHANGE = 'change'
    CHANGE_TIME_OR_NOTE = 'change_time_or_note'


class BookingType(StrEnum):
    CUSTOMER_BOOKING = 'CB'
    WALK_IN = 'walk-in'
    RESERVATION_BOOKING = 'reserved_time'
    BUSINESS_BOOKING = 'BB'


class SubbookingServiceVariantMode(StrEnum):
    VARIANT = 'variant'
    NO_VARIANT = 'no-variant'


class AppointmentCustomerMode(StrEnum):
    WALK_IN = 'walk-in'
    CUSTOMER_CARD = 'customer-card'
    MANUAL = 'manual'


class WhoMakesChange(StrEnum):
    BUSINESS = 'b'
    CUSTOMER = 'c'
    STAFF = 's'


class BookingMode(StrEnum):
    AUTO = 'A'
    MANUAL = 'M'
    SEMIAUTO = 'S'


class UpdateFutureBooking(enum.Enum):
    YES = True
    NO = False
    SKIP = None

    def __bool__(self):
        return bool(self.value)


class AppointmentType(StrEnum):
    BUSINESS = 'B'
    CUSTOMER = 'C'
    RESERVATION = 'R'
    TIMEOFF__DEPRECATED = 'T'


class AppointmentTypeChoices(StrChoicesEnum):
    BUSINESS = AppointmentType.BUSINESS.value, _('Created by business')
    CUSTOMER = AppointmentType.CUSTOMER.value, _('Created by customer')
    RESERVATION = AppointmentType.RESERVATION.value, _('Resource time reservation (like booking)')


_ApptStatus = AppointmentStatus
_who = WhoMakesChange

# BA Deposit
_BA_DEPOSIT_STATUSES = [
    (_ApptStatus.PENDING_PAYMENT, _ApptStatus.ACCEPTED, _who.BUSINESS),
    (_ApptStatus.PENDING_PAYMENT, _ApptStatus.CANCELED, _who.BUSINESS),
]

ALLOWED_TRANSITIONS = {
    # biz booking mode
    BookingMode.AUTO: [
        # (From, To, Who can do the transition [(c)ustomer, (b)usiness,
        # (s)ystem])
        ('', _ApptStatus.ACCEPTED, _who.CUSTOMER),
        ('', _ApptStatus.UNVERIFIED, _who.CUSTOMER),
        (_ApptStatus.UNVERIFIED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.ACCEPTED, _ApptStatus.CANCELED, _who.BUSINESS),
        (_ApptStatus.ACCEPTED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.ACCEPTED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.ACCEPTED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.ACCEPTED, _who.CUSTOMER),
        (_ApptStatus.PROPOSED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.FINISHED, _ApptStatus.NOSHOW, _who.BUSINESS),
        (_ApptStatus.NOSHOW, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.NOSHOW, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.FINISHED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.FINISHED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        # fallback for bookings made while biz was in different mode
        (_ApptStatus.MODIFIED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.MODIFIED, _ApptStatus.ACCEPTED, _who.CUSTOMER),
        (_ApptStatus.MODIFIED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.DECLINED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.ACCEPTED, _who.CUSTOMER),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.MODIFIED, _who.CUSTOMER),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.CANCELED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.DECLINED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.FINISHED, _who.BUSINESS),
        *_BA_DEPOSIT_STATUSES,  # BA Deposit
    ],
    BookingMode.MANUAL: [
        ('', _ApptStatus.UNCONFIRMED, _who.CUSTOMER),
        ('', _ApptStatus.UNVERIFIED, _who.CUSTOMER),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.DECLINED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.UNCONFIRMED, _who.CUSTOMER),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.UNVERIFIED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.ACCEPTED, _ApptStatus.CANCELED, _who.BUSINESS),
        (_ApptStatus.ACCEPTED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.ACCEPTED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.ACCEPTED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.ACCEPTED, _ApptStatus.MODIFIED, _who.CUSTOMER),
        (_ApptStatus.PROPOSED, _ApptStatus.ACCEPTED, _who.CUSTOMER),
        (_ApptStatus.PROPOSED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.PROPOSED, _ApptStatus.MODIFIED, _who.CUSTOMER),
        (_ApptStatus.MODIFIED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.DECLINED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.MODIFIED, _ApptStatus.MODIFIED, _who.CUSTOMER),
        (_ApptStatus.FINISHED, _ApptStatus.NOSHOW, _who.BUSINESS),
        (_ApptStatus.NOSHOW, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.NOSHOW, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.FINISHED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.FINISHED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        *_BA_DEPOSIT_STATUSES,  # BA Deposit
    ],
    BookingMode.SEMIAUTO: [
        ('', _ApptStatus.UNCONFIRMED, _who.CUSTOMER),
        ('', _ApptStatus.UNVERIFIED, _who.CUSTOMER),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.DECLINED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.UNCONFIRMED, _who.CUSTOMER),
        (_ApptStatus.UNCONFIRMED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.UNVERIFIED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.ACCEPTED, _ApptStatus.CANCELED, _who.BUSINESS),
        (_ApptStatus.ACCEPTED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.ACCEPTED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.ACCEPTED, _ApptStatus.MODIFIED, _who.CUSTOMER),
        (_ApptStatus.ACCEPTED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.ACCEPTED, _who.CUSTOMER),
        (_ApptStatus.PROPOSED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.PROPOSED, _ApptStatus.MODIFIED, _who.CUSTOMER),
        (_ApptStatus.MODIFIED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.DECLINED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.MODIFIED, _ApptStatus.CANCELED, _who.CUSTOMER),
        (_ApptStatus.MODIFIED, _ApptStatus.MODIFIED, _who.CUSTOMER),
        (_ApptStatus.FINISHED, _ApptStatus.NOSHOW, _who.BUSINESS),
        (_ApptStatus.NOSHOW, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.NOSHOW, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.FINISHED, _who.BUSINESS),
        (_ApptStatus.FINISHED, _ApptStatus.PROPOSED, _who.BUSINESS),
        (_ApptStatus.FINISHED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        (_ApptStatus.PROPOSED, _ApptStatus.ACCEPTED, _who.BUSINESS),
        *_BA_DEPOSIT_STATUSES,  # BA Deposit
    ],
}


class RepeatType(StrChoicesEnum):
    EVERY_DAY = 'D', _('Every day')
    EVERY_WEEK = 'W', _('Every week')
    EVERY_TWO_WEEKS = 'F', _('Every two weeks')
    EVERY_THREE_WEEKS = 'T', _('Every three weeks')
    EVERY_FOUR_WEEKS = 'H', _('Every four weeks')
    EVERY_SIX_WEEKS = 'S', _('Every six weeks')
    EVERY_MONTH = 'M', _('Every month')
    CUSTOM = 'C', _('Custom')
    GROUP = 'G', _('Group')


REPEATED_PHRASES_SHORT = {
    RepeatType.EVERY_DAY: _('Repeated daily'),
    RepeatType.EVERY_WEEK: _('Repeated every week'),
    RepeatType.EVERY_TWO_WEEKS: _('Repeated every two weeks'),
    RepeatType.EVERY_THREE_WEEKS: _('Repeated every three weeks'),
    RepeatType.EVERY_FOUR_WEEKS: _('Repeated every four weeks'),
    RepeatType.EVERY_SIX_WEEKS: _('Repeated every six weeks'),
    RepeatType.EVERY_MONTH: _('Repeated every month'),
    RepeatType.CUSTOM: _('Repeated on selected days'),
}

REPEATED_PHRASES = {
    RepeatType.EVERY_DAY: _('Repeated daily for %(num_repetitions)s days, ends on %(end_date)s'),
    RepeatType.EVERY_WEEK: _(
        'Repeated every week for %(num_repetitions)s weeks, ends on %(end_date)s'
    ),
    RepeatType.EVERY_TWO_WEEKS: _(
        'Repeated every fortnight for %(num_repetitions)s fortnights, ends on %(end_date)s'
    ),
    RepeatType.EVERY_THREE_WEEKS: _(
        'Repeated every three weeks for %(num_repetitions)s times, ends on %(end_date)s'
    ),
    RepeatType.EVERY_FOUR_WEEKS: _(
        'Repeated every four weeks for %(num_repetitions)s times, ends on %(end_date)s'
    ),
    RepeatType.EVERY_SIX_WEEKS: _(
        'Repeated every six weeks for %(num_repetitions)s times, ends on %(end_date)s'
    ),
    RepeatType.EVERY_MONTH: _(
        'Repeated every month for %(num_repetitions)s months, ends on %(end_date)s'
    ),
}


class RepeatEndType(StrChoicesEnum):
    NEVER = 'N', _('Never')
    ON_DATE = 'D', _('On date')
    AFTER_N_BOOKINGS = 'A', _('After selected number of bookings')


REPEAT_DELTAS = {
    RepeatType.EVERY_DAY: relativedelta(days=1),
    RepeatType.EVERY_WEEK: relativedelta(weeks=1),
    RepeatType.EVERY_TWO_WEEKS: relativedelta(weeks=2),
    RepeatType.EVERY_THREE_WEEKS: relativedelta(weeks=3),
    RepeatType.EVERY_FOUR_WEEKS: relativedelta(weeks=4),
    RepeatType.EVERY_SIX_WEEKS: relativedelta(weeks=6),
    RepeatType.EVERY_MONTH: relativedelta(months=1),
    RepeatType.CUSTOM: None,
    RepeatType.GROUP: relativedelta(),
}


class BookingRangesErrors(StrEnum):
    BOOKING_SERVICE_INACTIVE_ERROR = 'service_inactive'
    BOOKING_WORKING_HOURS_ERROR = 'working_hours'
    BOOKING_OVERBOOKING_ERROR = 'overbooking'
    BOOKING_INTERNAL_OVERBOOKING_ERROR = 'internal_overbooking'
    BOOKING_TIMEOFF_ERROR = 'timeoff'
    BOOKING_LEADTIME_ERROR = 'lead_time'
    BOOKING_SLOT_ERROR = 'slot'


class AppointmentEvent(DomainEvent):
    CREATED = 'created'
    FINISHED = 'finished'
    CANCELED = 'canceled'


class BookAgainStatusesEnum(StrChoicesEnum):
    UNKNOWN = 'unknown', _('Unknown')
    OK = 'ok', _('OK')
    ALL_SERVICES_DELETED = 'all_services_deleted', _('All services deleted')
    SOME_SERVICES_DELETED = 'some_services_deleted', _('Some services deleted')
    STAFFER_NOT_AVAILABLE = 'staffer_not_available', _('Staffer not available')
    SERVICE_VARIANTS_MODIFIED = 'service_variants_modified', _('Service variants were modified')
    SERVICE_MODIFIED = 'settings_modified', _('Some settings of the service were modified')
    FAMILY_AND_FRIENDS_MEMBER_DELETED = 'member_not_available', _(
        'Family & Friends member is no longer available'
    )

    def is_booking_possible(self):
        return self.value in [
            BookAgainStatusesEnum.OK,
            BookAgainStatusesEnum.SERVICE_MODIFIED,
            BookAgainStatusesEnum.SOME_SERVICES_DELETED,
            BookAgainStatusesEnum.SERVICE_VARIANTS_MODIFIED,
            BookAgainStatusesEnum.STAFFER_NOT_AVAILABLE,
            BookAgainStatusesEnum.FAMILY_AND_FRIENDS_MEMBER_DELETED,
        ]


class MerchantChanged(enum.IntEnum):
    CHANGED = 1
    NOT_CHANGED = 2
