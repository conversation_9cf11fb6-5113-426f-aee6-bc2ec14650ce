#!/usr/bin/env python
import contextlib
import datetime

import json

from django.contrib import admin, messages
from django.db.models import Prefetch, F
from django.shortcuts import get_object_or_404
from django.urls.base import reverse as url_reverse
from django.utils.html import escape, format_html
from django.template import loader
from lib.feature_flag.bug import FixSlowAppointmentAdminQuery
from lib.tools import SimplePaginator
from lib.admin_helpers import (
    admin_link,
    NoRowsInListViewMixin,
    BaseModelAdmin,
    NoAddDelMixin,
    ReadOnlyFieldsMixin,
    FormatBusinessMixin,
)
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import (
    AppointmentStatusChoices,
    UpdateFutureBooking,
    WhoMakesChange,
)
from webapps.booking.models import (
    Appointment,
    SubBooking,
    BookingChange,
    BookingResource,
    RepeatingBooking,
    NoShowLostMoney,
)
from webapps.business._admin.views import ServiceAddOnUseInline
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.public_partners.admin import PartnerAppDataInline
from webapps.public_partners.models import (
    AppointmentMetadata,
    SubbookingMetadata,
)
from webapps.user.models import User
from webapps.zoom.admin import ZoomMeetingInline


class BookingChangeInline(NoAddDelMixin, admin.TabularInline):
    model = BookingChange
    extra = 0
    readonly_fields = [
        'id',
        'moment',
        'changed_by',
        'changed_user_id',
        'changed_user_email',
        'booked_from',
        'booked_till',
        'booked_from_business_tz',
        'booked_till_business_tz',
        'status',
        'service_variant_id',
        'autoassign',
        'staffers_ids',
        'appliances_ids',
        'metadata',
    ]
    fields = readonly_fields
    can_delete = False

    def has_change_permission(self, request, obj=None):
        # HACK: remember obj for use in get_queryset :)
        self._obj = obj
        return super().has_change_permission(request, obj=obj)

    def get_queryset(self, request):
        if getattr(self, '_obj', None):
            # Use the HACK from has_change_permission
            # start QuerySet from Booking so it will not be fetched
            # in loop for every BookingChange (as well as biz and biz regions)
            qs = self._obj.bookingchange_set.all()
        else:
            qs = super().get_queryset(request)
        return qs

    @staticmethod
    def moment(obj):
        return obj.created.strftime('%F_%T.%f')[:-3]

    @staticmethod
    def booked_from_business_tz(obj):
        tz = obj.subbooking.appointment.business.get_timezone()
        booked_from = obj.booked_from.astimezone(tz)
        return booked_from.strftime('%Y-%m-%d %H:%M:%S')

    booked_from_business_tz.short_description = 'Booked from (business TZ)'

    @staticmethod
    def booked_till_business_tz(obj):
        tz = obj.subbooking.appointment.business.get_timezone()
        booked_till = obj.booked_till.astimezone(tz)
        return booked_till.strftime('%Y-%m-%d %H:%M:%S')

    booked_till_business_tz.short_description = 'Booked till (business TZ)'


def move_bookings(_modeladmin, request, queryset):
    # We have to be sure CS doesn't pick bookings from 2 different businesses
    if not request.GET.get('business__id'):
        messages.warning(request, 'You have to filter by business id first')
        return

    # Only for imported bookings
    queryset = (
        queryset.filter(appointment__import_uid__isnull=False)
        .exclude(appointment__import_uid='')
        .prefetch_related('resources')
    )

    hours = int(request.POST.get('hours', 0))

    if not hours or hours < -23 or hours > 23:
        messages.warning(
            request, 'Provide valid number of hours. You can move bookings by +- 23 hours.'
        )
        return

    success_count = 0
    for booking in queryset:
        appointment = AppointmentWrapper(
            subbookings=[booking],
        )

        booking.booked_from += datetime.timedelta(hours=hours)
        booking.booked_till += datetime.timedelta(hours=hours)
        appointment.save(
            to_delete=[],
            overbooking=True,
            who_makes_change=WhoMakesChange.STAFF,
            update_future=UpdateFutureBooking.NO,
        )
        BookingChange.add(
            appointment,
            changed_by=BookingChange.BY_SYSTEM,
            changed_user=request.user,
            metadata={
                'reason': 'wrong_import',
                'notify_about_reschedule': False,
            },
        )

        success_count += 1

    messages.success(request, f'{success_count} bookings have been moved by {hours} hours.')


move_bookings.short_description = 'Change hours (imported bookings only)'


class SubBookingAdmin(NoAddDelMixin, NoRowsInListViewMixin, BaseModelAdmin):
    paginator = SimplePaginator
    model = SubBooking
    inlines = [
        BookingChangeInline,
        ServiceAddOnUseInline,
        type(
            'SubbookingPartnerAppDataInline',
            (PartnerAppDataInline,),
            {"model": SubbookingMetadata},
        ),
    ]
    search_fields = (
        '=id',
        '=appointment__id',
        '=appointment__business__id',
        '=appointment__customer_email',
    )
    show_full_result_count = False
    # date_hierarchy = 'booked_from'  # too damn slow
    list_display = [
        'id',
        'appointment_link',
        'booked_from',
        'booked_till',
    ]
    raw_id_fields = [
        'appointment',
        'resolved_promotion',
        'service_variant',
        'waitlist',
    ]

    exclude = [
        'service_data_internal',
    ]
    readonly_fields = [
        'id',
        'appointment_link',
        'autoassign',
        'booked_from',
        'booked_from_business_tz',
        'booked_till',
        'booked_till_business_tz',
        'booking_change_diff',
        'created',
        'deleted',
        'padded_booked_range',
        'resolved_promotion',
        'service_name',
        'service_variant_link',
        'updated',
        'service_data_json',
        'waitlist',
    ]

    fieldsets = (
        (None, {'fields': ('created', 'updated', 'deleted')}),
        (
            'Sub booking',
            {
                'fields': (
                    'id',
                    'appointment_link',
                    'autoassign',
                    'chargeable',
                    'is_highlighted',
                    'payable',
                    'resolved_discount',
                    'resolved_price',
                    'resolved_promotion_type',
                    'booked_from',
                    'booked_from_business_tz',
                    'booked_till',
                    'booked_till_business_tz',
                    'booking_change_diff',
                    'padded_booked_range',
                    'resolved_promotion',
                    'service_name',
                    'service_variant_link',
                    'service_data_json',
                    'waitlist',
                )
            },
        ),
    )

    actions_on_bottom = True
    actions_on_top = False
    actions = [
        move_bookings,
    ]
    change_list_template = 'admin/change_lists/change_list__booking.html'

    @staticmethod
    def appointment_link(obj):
        appt = obj.appointment
        return format_html('<a href="{}">{}</a>', admin_link(appt), appt)

    appointment_link.short_description = 'Appointment'

    @admin.display(description='Service variant')
    def service_variant_link(self, obj):
        if obj.service_variant:
            return format_html(
                '<a href="{}?version={}">{}</a>',
                admin_link(obj.service_variant),
                obj.service_data.service_variant_version,
                obj.service_variant,
            )

    @staticmethod
    def booked_from_business_tz(obj):
        tz = obj.appointment.business.get_timezone()
        booked_from = obj.booked_from.astimezone(tz)
        return booked_from.strftime('%Y-%m-%d %H:%M:%S')

    booked_from_business_tz.short_description = 'Booked from (business TZ)'

    @staticmethod
    def booked_till_business_tz(obj):
        tz = obj.appointment.business.get_timezone()
        booked_till = obj.booked_till.astimezone(tz)
        return booked_till.strftime('%Y-%m-%d %H:%M:%S')

    booked_till_business_tz.short_description = 'Booked till (business TZ)'

    def get_queryset(self, request):  # pylint: disable=arguments-differ
        qs = (
            super()
            .get_queryset(request)
            .prefetch_related(
                Prefetch(
                    "appointment",
                    queryset=Appointment.objects.only("id", "created", "updated"),
                )
            )
        )
        return qs

    def save_model(self, request, obj, form, change):
        """Save is disabled. All fields are read-only."""

    @staticmethod
    def booking_change_diff(obj):
        """
        :param obj: Booking
        :return:
        """

        def calculate_change_diff(values, names):
            result = []
            for old, new in zip(values, values[1:]):
                result.append(
                    (
                        f'{new["id"]}: {new["created"].strftime("%F_%T.%f")[:-3]}',
                        [[name, new.get(name)] for name in names if old.get(name) != new.get(name)],
                    )
                )
            return result

        template = loader.get_template(
            'admin/fields/field__booking_change_diff.html',
        )
        change_diff = calculate_change_diff(
            [{}] + [i.__dict__ for i in obj.bookingchange_set.order_by('created').all()],
            [i.name for i in BookingChange._meta.get_fields() if i.name not in ['id', 'created']],
        )
        for _, diff_list in change_diff:
            for diff in diff_list:
                if diff[0] == 'metadata':
                    with contextlib.suppress(json.JSONDecodeError):
                        diff[1] = json.dumps(json.loads(diff[1]), indent=4)

        return template.render({'change_diff': change_diff})

    @staticmethod
    def service_data_json(obj):
        if not obj.service_data:
            return '-'

        return json.dumps(obj.service_data.serialize(), indent=4)

    service_data_json.short_description = 'Frozen data'


class AppointmentMixin(FormatBusinessMixin, NoAddDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin):
    readonly_fields = ['sub_bookings']

    @staticmethod
    def get_booked_for(obj):
        if obj.booked_for is not None:
            bci = obj.booked_for
            full_name = bci.user.full_name if bci.user else bci.full_name
            return format_html(
                '<a href="{}">{}: {}</a>',
                admin_link(bci),
                bci.id,
                escape(full_name),
            )

    @staticmethod
    def sub_bookings(obj):
        objs = obj if isinstance(obj, list) else [obj]
        subbookings = list(
            SubBooking.objects.filter(deleted__isnull=True, appointment_id__in=(o.id for o in objs))
            .order_by('booked_from', 'id')
            .only(
                'id',
                'booked_from',
                'booked_till',
            )
        )
        if not subbookings:
            return

        tz = objs[0].business.get_timezone()
        return format_html(
            ', '.join(
                '<a href="{}">({}) {} {} - {}</a>'.format(  # pylint: disable=consider-using-f-string
                    admin_link(booking),
                    booking.id,
                    booking.booked_from.astimezone(tz).date(),
                    booking.booked_from.astimezone(tz).strftime('%H:%M'),
                    booking.booked_till.astimezone(tz).strftime('%H:%M'),
                )
                for booking in subbookings
            )
        )

    get_booked_for.short_description = 'Booked for'


class AppointmentAdmin(NoRowsInListViewMixin, AppointmentMixin):
    paginator = SimplePaginator
    model = Appointment
    inlines = [
        ZoomMeetingInline,
        type(
            'AppointmentPartnerAppDataInline',
            (PartnerAppDataInline,),
            {"model": AppointmentMetadata},
        ),
    ]
    list_display = [
        'id',
        'type',
        'status',
        'get_booked_for',
        'get_business',
        'booked_from',
        'booked_till',
        'sub_bookings',
    ]
    exclude = [
        'service_questions',
    ]
    readonly_fields = [
        'sub_bookings',
        'service_questions_json',
        'is_booksy_gift_card_appointment',
        'booksy_pay_available',
    ]
    show_full_result_count = False
    search_vector_fields = [
        'booked_for__first_name',
        'booked_for__last_name',
    ]
    query_fields_placeholders = {
        'text_search': 'customer_name',
        'booked_for__email': 'customer_email',
        'booked_for__cell_phone': 'customer_phone',
    }

    def get_queryset(self, request):
        qset = (
            super()
            .get_queryset(request)
            .select_related(
                'business',
                'business__owner',
                'booked_for__user',
            )
            .prefetch_related('bookings')
        )
        if FixSlowAppointmentAdminQuery():
            qset = qset.only(
                'id',
                'type',
                'status',
                'booked_from',
                'booked_till',
                'business__id',
                'business__name',
                'business__time_zone_name',
                'business__owner__id',
                'business__owner__first_name',
                'business__owner__last_name',
                'business__owner__email',
                'booked_for__id',
                'booked_for__user__id',
                'booked_for__user__first_name',
                'booked_for__user__last_name',
                'booked_for__user__email',
                'booked_for__user__deleted',
            ).annotate(
                owner_first_name=F('business__owner__first_name'),
                owner_last_name=F('business__owner__last_name'),
                owner_email=F('business__owner__email'),
                booked_for_first_name=F('booked_for__user__first_name'),
                booked_for_last_name=F('booked_for__user__last_name'),
                booked_for_email=F('booked_for__user__email'),
            )
        return qset

    def get_search_fields(self, request):
        return [
            '=id',
            '=business__id',
            '=booked_for__id',
            'customer_name',
            'customer_email',
            'customer_phone',
        ]

    @staticmethod
    def sub_bookings(obj):
        subbookings = obj.bookings.all()
        if not subbookings:
            return

        tz = obj.business.get_timezone()
        return format_html(
            ', '.join(
                '<a href="{}">({}) {} {} - {}</a>'.format(  # pylint: disable=consider-using-f-string
                    admin_link(booking),
                    booking.id,
                    booking.booked_from.astimezone(tz).date(),
                    booking.booked_from.astimezone(tz).strftime('%H:%M'),
                    booking.booked_till.astimezone(tz).strftime('%H:%M'),
                )
                for booking in subbookings
            )
        )

    @staticmethod
    def service_questions_json(obj):
        if not obj.service_questions:
            return '-'

        return json.dumps(obj.service_questions.to_dict(), indent=4)

    service_questions_json.short_description = 'Service questions'

    @staticmethod
    def booksy_pay_available(obj):
        return format_html(
            '<a href="{}">{}</a>',
            url_reverse('admin:booksy_pay_appointment', args=(obj.id,)),
            obj.is_booksy_pay_available,
        )


class RepeatingBookingAdmin(AppointmentMixin):
    model = RepeatingBooking
    search_fields = [
        '=id',
        '=appointments__booked_for__id',
        '=appointments__business__id',
        '=appointments__id',
    ]
    list_display = [
        'id',
        'appointment_type',
        'appointment_status',
        'booked_for',
        'business',
        'appointments',
        'sub_bookings',
    ]
    readonly_fields = ['sub_bookings']

    def get_queryset(self, request):
        qset = (
            super()
            .get_queryset(request)
            .prefetch_related(
                Prefetch(
                    'appointments',
                    Appointment.all_objects.select_related(
                        'business',
                        'business__owner',
                        'booked_for',
                    ),
                ),
            )
        )
        return qset

    @staticmethod
    def _get_appointment(obj):
        appointments = list(obj.appointments.all())  # because is prefetched
        return appointments[0] if appointments else None

    def appointment_type(self, obj):
        appointment = self._get_appointment(obj)
        return appointment and appointment.get_type_display()

    @staticmethod
    def appointments(obj):
        links = ', '.join(f'<a href="{admin_link(a)}">{a.id}</a>' for a in obj.appointments.all())
        return format_html(links)

    def appointment_status(self, obj):
        appointment = self._get_appointment(obj)
        return appointment and appointment.get_status_display()

    def booked_for(self, obj):
        appointment = self._get_appointment(obj)
        return appointment and super().get_booked_for(appointment)

    def business(self, obj):
        appointment = self._get_appointment(obj)
        return appointment and super().get_business(appointment)

    def sub_bookings(self, obj):  # pylint: disable=arguments-differ
        appointments = list(obj.appointments.all())
        return super().sub_bookings(appointments)


class BookingChangeAdmin(NoAddDelMixin, ReadOnlyFieldsMixin, NoRowsInListViewMixin, BaseModelAdmin):
    paginator = SimplePaginator
    show_full_result_count = False
    model = BookingChange
    search_fields = [
        '=id',
        '=subbooking_id',
        '=appointment_id',
    ]
    list_display = [
        'id',
        'created',
        'status',
        'booked_from',
        'booked_till',
        'subbooking_link',
        'appointment_link',
        'service_name',
        'customer_email',
        'customer_name',
    ]
    raw_id_fields = ['subbooking', 'appointment', 'repeating']

    def get_object(self, request, object_id, from_field=None):
        queryset = super().get_queryset(request)
        return get_object_or_404(queryset.filter(pk=object_id))

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.only(
            'id',
            'created',
            'status',
            'booked_from',
            'booked_till',
            'service_name',
            'customer_email',
            'customer_name',
            'subbooking_id',
            'appointment_id',
        )

    def change_view(self, request, object_id, form_url='', extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_save'] = False
        extra_context['show_save_and_continue'] = False

        return super().change_view(
            request, object_id, form_url=form_url, extra_context=extra_context
        )

    @staticmethod
    def subbooking_link(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj.subbooking), obj.subbooking_id)

    @staticmethod
    def appointment_link(obj):
        if obj.appointment_id:
            appt = obj.appointment
            return format_html('<a href="{}">{}</a>', admin_link(appt), appt.id)


class BookingStatusListFilter(admin.SimpleListFilter):
    # Human-readable title which will be displayed in the
    # right admin sidebar just above the filter options.
    title = 'appointment status'

    # Parameter for the filter that will be used in the URL query.
    parameter_name = 'status'

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return AppointmentStatusChoices.choices()

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if not self.value():
            return queryset
        return queryset.filter(subbooking__appointment__status=self.value())


class BookingResourceAdmin(NoAddDelMixin, NoRowsInListViewMixin, BaseModelAdmin):
    paginator = SimplePaginator
    show_full_result_count = False
    list_display = [
        'id',
        'subbooking_id',
        'appointment_id',
        'appointment_business',
        'appointment_booked_for',
        'resource_id',
        'resource_name',
        'appointment_status',
    ]
    search_fields = [
        '=id',
        '=subbooking__appointment__business__id',
        '=subbooking__id',
        '=subbooking__appointment__id',
        '=resource__id',
        'resource__name',
        'resource__staff_email',
    ]
    list_filter = [
        BookingStatusListFilter,
    ]
    raw_id_fields = ['subbooking', 'resource']
    readonly_fields = ('created',)

    query_fields_placeholders = {
        'subbooking__appointment__business__id': 'appointment__business__id',
        'subbooking__appointment__id': 'appointment__id',
    }

    # pylint: disable=arguments-differ, duplicate-code
    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .filter(
                subbooking__appointment__deleted__isnull=True,
            )
            .prefetch_related(
                Prefetch(
                    "subbooking",
                    queryset=SubBooking.objects.only("id", "appointment_id", "booked_from"),
                ),
                Prefetch(
                    "subbooking__appointment",
                    queryset=Appointment.objects.only(
                        "id", "business_id", "booked_for_id", 'status'
                    ),
                ),
                Prefetch(
                    "subbooking__appointment__business",
                    queryset=Business.objects.only("id", "name", "owner_id"),
                ),
                Prefetch(
                    "subbooking__appointment__business__owner",
                    queryset=User.objects.only("id", "email"),
                ),
                Prefetch(
                    "subbooking__appointment__booked_for",
                    queryset=BusinessCustomerInfo.objects.only('id', 'email'),
                ),
                Prefetch(
                    "resource",
                    queryset=Business.objects.only("id", "name"),
                ),
            )
            .only(
                "id",
                "subbooking_id",
                "resource_id",
            )
        )

    @staticmethod
    def appointment_status(obj):
        return obj.subbooking.appointment.get_status_display()

    @staticmethod
    def appointment_id(obj):
        return obj.subbooking.appointment_id

    @staticmethod
    def appointment_business(obj):
        return obj.subbooking.appointment.business

    @staticmethod
    def appointment_booked_for(obj):
        bci = obj.subbooking.appointment.booked_for
        return bci.email if bci else None

    @staticmethod
    def resource_id(obj):
        return obj.resource.id

    @staticmethod
    def resource_name(obj):
        return obj.resource.name


class NoShowLostMoneyAdmin(NoAddDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin):
    model = NoShowLostMoney

    search_fields = [
        '=id',
        '=business_id',
    ]

    list_display = [
        'id',
        'total_value',
        'get_business',
        'calculation_period',
        'no_show_visits_count',
    ]

    @admin.display(description='Business', ordering='obj.no_show_lost_money.business')
    def get_business(self, obj):
        biz = obj.business

        return format_html(
            '<a href="{}">{}: {} ({})</a>',
            admin_link(biz),
            biz.id,
            escape(biz.name),
            escape(biz.owner.email),
        )


admin.site.register(SubBooking, SubBookingAdmin)
admin.site.register(Appointment, AppointmentAdmin)
admin.site.register(RepeatingBooking, RepeatingBookingAdmin)
admin.site.register(BookingChange, BookingChangeAdmin)
admin.site.register(BookingResource, BookingResourceAdmin)
admin.site.register(NoShowLostMoney, NoShowLostMoneyAdmin)
