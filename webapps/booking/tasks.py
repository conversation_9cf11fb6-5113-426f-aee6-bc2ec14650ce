import datetime
import logging
import typing as t
from collections import Counter, defaultdict
from datetime import timed<PERSON><PERSON>
from itertools import cycle, groupby
from operator import itemgetter

from bo_obs.datadog import set_apm_tag_in_root_span
from celery import chord
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.cache import cache
from django.db.models import Case, CharField, F, Prefetch, Q, Value, When

from lib.celery_tools import (
    BooksyCeleryTask,
    celery_task,
    post_transaction_task,
    retry_post_transaction_task,
)
from lib.db import (
    READ_ONLY_DB,
    REPORTS_READ_ONLY_DB,
    chunked_queryset_iterator,
    using_db_for_reads,
)
from lib.feature_flag.feature.booking import RemoveBookingCntInBulkFinishAppointments
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.rivers import River, bump_document
from lib.tools import grouper, tznow
from webapps.booking.enums import (
    BookingAction,
    RepeatType,
    UpdateFutureBooking,
    WhoMakesChange,
)
from webapps.booking.events import appointment_finished_event
from webapps.booking.factory.service_questions import QuestionsAndAnswersList
from webapps.booking.models import (
    Appointment,
    BookingChange,
    RepeatingBooking,
    SubBooking,
)
from webapps.booking.tools.repeating import (
    NoSingleServiceVariantException,
    get_repeating_booking_conflicts,
)
from webapps.business.resources import AnyResource
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_booking import BookingFinishedScenario
from webapps.pos.enums import receipt_status
from webapps.pos.models import PaymentRowChange
from webapps.pos.provider.proxy import ProxyProvider
from webapps.search_engine_tuning.models import BusinessCustomerTuning
from webapps.user.models import User
from webapps.user.tools import get_system_user

log = logging.getLogger('booksy.booking_tasks')


def bulk_finish_appointments(appointment_ids, status, accepted_old_statuses=None):
    """Finish multiple bookings in one go.

    :param appointment_ids: a QuerySet of Appointments
    :param status: a final status that will be used for the bookings
    :param accepted_old_statuses: if set, change only appointment that are in these statuses
    """
    filters = Q(id__in=appointment_ids, booked_till__lt=tznow())

    if accepted_old_statuses:
        filters &= Q(status__in=accepted_old_statuses)

    appointments = (
        Appointment.objects.filter(filters)
        .exclude(
            status=status,
        )
        .select_related(
            'business',
        )
        .prefetch_related(
            Prefetch(
                'bookings',
                queryset=SubBooking.objects.only(
                    'id',
                    'appointment',
                    'autoassign',
                    'booked_from',
                    'booked_till',
                    'deleted',
                    'updated',
                    'service_variant',
                    'service_name',
                ),
            ),
        )
    )

    system = get_system_user()
    finished = []
    for appointment in chunked_queryset_iterator(appointments, 2000):
        if not RemoveBookingCntInBulkFinishAppointments():
            finished.extend(s.id for s in appointment.bookings.all() if not s.deleted)

        # do the update, booking changes and scenarios
        appointment.update_appointment(
            updated_by=system,
            status=status,
            who_makes_change=WhoMakesChange.STAFF,
            update_future=UpdateFutureBooking.SKIP,
        )

        if status == Appointment.STATUS.FINISHED:
            appointment_finished_event.send(appointment)

        if status == Appointment.STATUS.FINISHED and (
            appointment.type in Appointment.TYPES_BOOKABLE
        ):
            start_scenario(BookingFinishedScenario, appointment=appointment)

        # handle BookingChange
        BookingChange.add(
            appointment,
            changed_by=BookingChange.BY_SYSTEM,
            metadata={
                'reason': 'task:bulk_finish_appointments',
            },
        )
        bump_document(River.WELCOME_NEW_CLIENT_MESSAGE_BLAST, appointment.id)

    if not RemoveBookingCntInBulkFinishAppointments():
        log.info('bulk_finish_appointments: %r status=%s', finished, status)
    else:
        log.info('bulk_finish_appointments: %d', len(appointment_ids))

    if not RemoveBookingCntInBulkFinishAppointments():
        return len(finished), len(appointment_ids)


BULK_FINISH_ACCEPTED_STATUSES = [
    Appointment.STATUS.ACCEPTED,
    Appointment.STATUS.MODIFIED,
    Appointment.STATUS.PROPOSED,
    Appointment.STATUS.UNCONFIRMED,
]


@celery_task(ignore_result=False)
def bulk_update_appointments(data_to_update: t.Mapping[str, t.Sequence[int]]) -> bool | None:
    number_of_appointments = sum(len(appts) for appts in data_to_update.values())
    set_apm_tag_in_root_span(
        'bulk_update_appointments number of all appointments',
        number_of_appointments,
    )
    set_apm_tag_in_root_span(
        'bulk_update_appointments statuses',
        data_to_update.keys(),
    )

    appointments_processed_counter = 0
    for status, appointment_ids in data_to_update.items():

        bulk_finish_appointments(
            appointment_ids, status, accepted_old_statuses=BULK_FINISH_ACCEPTED_STATUSES
        )

        appointments_processed_counter += len(appointment_ids)
        set_apm_tag_in_root_span(
            'bulk_update_appointments already processed appointments',
            appointments_processed_counter,
        )
    return True


@celery_task
def schedule_bulk_update_appointments_task(
    workers: int = 10,
    daily_check: bool = False,
    mod_result: int = 0,
):
    """To schedule update of appointments, with specific business existing only
    in one celery worker, during one dispatch cycle.

    :param workers: Define on how many celery task executions,
                    existing work should be split.
    :param daily_check: If false it means it's a standard procedure run every couple of minutes.
                        else it means that it's an additional daily check for tasks that did not
                        finish during their interval
    """
    from django.conf import settings

    now = tznow()
    cache_key = f"schedule_bulk_update_appointments_task_last_sync_{mod_result}"
    modulo_divider = settings.SCHEDULE_BULK_UPDATE_APPOINTMENTS_MODULO
    last_sync = cache.get(cache_key, None)

    qset = get_appointments_to_close_query(
        end_till=now,
        daily_check=daily_check,
        modified_after=last_sync,
        modulo_divider=modulo_divider,
        mod_result=mod_result,
    )

    buckets = tuple(defaultdict(list) for i in range(workers))
    cycle_buckets = cycle(buckets)
    for _, b_group in groupby(qset, itemgetter('business_id')):
        current_bucket = next(cycle_buckets)
        for row in b_group:
            current_bucket[row['status_to_set']].extend(row['appointment_ids'])

    if daily_check:
        cache_key = None
    tasks = [bulk_update_appointments.s(bucket) for bucket in filter(len, buckets)]
    chord(tasks, set_cache_after_bulk_update_appointments.s(cache_key, now)).apply_async()


def get_appointments_to_close_query(
    end_till: datetime.datetime,
    daily_check: bool = False,
    modified_after: datetime.datetime | None = None,
    modulo_divider: int | None = None,
    mod_result: int | None = None,
) -> Q:

    status_for_finish = Q(
        status__in=[
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.PROPOSED,
        ]
    )
    types_for_finish = Q(
        type__in=[
            Appointment.TYPE.CUSTOMER,
            Appointment.TYPE.BUSINESS,
            Appointment.TYPE.RESERVATION,
        ]
    )
    appointments_to_finish = status_for_finish & types_for_finish

    status_for_decline = Q(
        status__in=[
            Appointment.STATUS.UNCONFIRMED,
            Appointment.STATUS.MODIFIED,
        ]
    )
    types_for_decline = Q(type__in=Appointment.TYPES_BOOKABLE)
    appointments_to_decline = status_for_decline & types_for_decline
    qset = Appointment.objects.exclude(
        deleted__isnull=False,
    ).filter(
        appointments_to_finish | appointments_to_decline,
        booked_till__lte=end_till,
    )
    if daily_check:
        qset = qset.filter(
            Q(
                booked_till__gte=end_till - timedelta(hours=25),
                booked_till__lte=end_till - timedelta(hours=1),
            )
            | Q(
                updated__gte=end_till - timedelta(hours=25),
                updated__lte=end_till - timedelta(hours=1),
            )
        )
    else:
        if modified_after:
            qset = qset.filter(Q(booked_till__gte=modified_after) | Q(updated__gte=modified_after))
        if modulo_divider:
            qset = qset.annotate(business_id_mod=F('business_id') % modulo_divider).filter(
                business_id_mod=mod_result
            )
    qset = (
        qset.annotate(
            status_to_set=Case(
                When(
                    appointments_to_finish,
                    then=Value(Appointment.STATUS.FINISHED),
                ),
                When(
                    appointments_to_decline,
                    then=Value(Appointment.STATUS.DECLINED),
                ),
                output_field=CharField(),
            ),
        )
        .values('business_id', 'status_to_set')
        .annotate(
            appointment_ids=ArrayAgg('id', distinct=True),
        )
        .order_by('business_id')
    )

    return qset


@celery_task
def set_cache_after_bulk_update_appointments(results, cache_key, now):
    if cache_key and all(results):
        cache.set(cache_key, now, timeout=24 * 60 * 60)


@post_transaction_task
def update_repeating_by_parent_booking(  # pylint: disable=too-many-return-statements, too-many-statements, too-many-branches
    child_id,
    who_makes_change,
    updated_by_id,
    updated_bookings=None,
    resource_ids=None,
):
    """
    Update all booking of RepeatingBooking instance
    with id=child_id. Create new bookings based on booking with id=booking_id
    All bookings created with overbooking True.
    After that check booking conflicts and send report to user(updated_by)

    :param child_id: int. valid RepeatingBooking id

    :param who_makes_change: enum 'c' or 'b'
    :param updated_by_id: User object
    :param updated_bookings: optional argument for recursive-call.
           list of updated booking ids
    :param resources: staffer, appliance pair (can be AnyResource)
    :return: None
    """
    from webapps.business.models import Resource

    def base_recursion_case(list_bookings, error_message=None):
        if error_message:
            log.error(error_message)
        check_booking_conflicts_and_send_report_task.delay(
            list_bookings,
            updated_by_id,
        )

    # entry point update_repeating_by_parent_booking
    updated_by = User.objects.get(id=updated_by_id)
    child = RepeatingBooking.objects.get(id=child_id)
    parent = child.parent

    if parent is None:
        message = f'Got repeating child without parent {child_id}. User id {updated_by.id}'
        base_recursion_case(
            updated_bookings,
            message,
        )
        return

    # get latest booking of parent as template booking
    template_appointment = (
        parent.extra_appointments_unlimited.select_related(
            'business',
        )
        .prefetch_related(
            'bookings',
        )
        .order_by('-booked_from')
        .first()
    )

    if template_appointment is None:
        message = f'Got parent without bookings in child {child_id}, parent {parent.id}'
        base_recursion_case(
            updated_bookings,
            message,
        )
        return

    # check if we get valid data
    if child.deleted:
        message = f'Got deleted repeating booking in updateRepeating {child.id}'
        base_recursion_case(
            updated_bookings,
            message,
        )
        return
    # do not modify canceled
    if template_appointment.deleted:
        message = f'Got deleted template appointment in updateRepeating {template_appointment.id}'
        base_recursion_case(
            updated_bookings,
            message,
        )
        return
    # prevent modify custom/group bookings
    if child.repeat in [RepeatType.CUSTOM, RepeatType.GROUP]:
        message = f'Got custom repeating booking in update {child.id}'
        base_recursion_case(
            updated_bookings,
            message,
        )
        return

    # prevent endless recursion
    if child.parent_id == child.id:
        message = f'Endless recursion on repeating booking: {child.id}'
        base_recursion_case(
            updated_bookings,
            message,
        )
        return

    child_appointments = list(
        child.extra_appointments_unlimited.select_related(
            'business',
        )
        .prefetch_related(
            'bookings',
        )
        .order_by('booked_from')
    )

    if not child_appointments:
        # nothing to update
        base_recursion_case(
            updated_bookings,
        )
        return
    # create new plan for  repeating booking
    replanned = child.make_plan([template_appointment] + child_appointments)

    num_bookings = len(child_appointments)
    # slice parent template booking
    # also slice all future planned bookings
    # that way num_bookings == len(updated_child_bookings)
    updated_child_appointments = replanned[1 : (num_bookings + 1)]

    # get force cancel status
    force_cancel = template_appointment.status == Appointment.STATUS.CANCELED

    template_booking = template_appointment.first_booking
    template_booking.staffer, template_booking.appliance = [
        (
            AnyResource
            if resource_id == AnyResource.id
            else Resource.objects.get(pk=resource_id) if resource_id else resource_id
        )
        for resource_id in resource_ids
    ]
    bookings_list = [
        # repeated clones
        child.clone_appointment(
            src_appointment=template_appointment,
            src_subbooking=template_booking,
            draft=draft,
            subbooking_id=appt.first_booking_id,
            id=appt.id,
            created=appt.created,
            status=(
                SubBooking.compute_new_status(
                    booked_till=appt.booked_till,
                    base_status=appt.status,
                )
                if not force_cancel
                else Appointment.STATUS.CANCELED
            ),
            repeating_id=child.id,
        )[1]
        for appt, draft in zip(child_appointments, updated_child_appointments)
    ]
    updated_child_bookings = []
    if bookings_list:
        # overbooking all child bookings
        # after that check_booking_conflicts_and_send_report_task
        # will compute conflicts

        child = child.make_repeating_booking(
            data={},  # correct data already in the instance
            bookings_list=bookings_list,
            deleted_appointments=child_appointments,
            updated_by=updated_by,
            _who_makes_change=who_makes_change,
            overbooking=True,
            business=template_appointment.business,
            _use_lock=False,
        )
        # get all updated ids
        updated_child_bookings = list(
            SubBooking.objects.filter(
                appointment__repeating=child,
                deleted__isnull=True,
            )
            .order_by('-booked_from')
            .values_list(
                'id',
                flat=True,
            )
        )
    else:
        # we get from child.make_plan
        # only one booking (template_booking)
        # planned booking not available, delete bookings of child
        SubBooking.delete_appointments(child_appointments, updated_by)

    # get child of child
    child_of_child = child.children.order_by('-created').first()
    # if action was not cancel
    # we do not need to check conflicts and send report
    if updated_bookings is None and not force_cancel:
        updated_bookings = updated_child_bookings
    elif not force_cancel:
        updated_bookings = updated_bookings + updated_child_bookings

    if child_of_child is None:
        # nothing to update end of recursion
        # check for conflicts and send report
        base_recursion_case(
            updated_bookings,
        )
    else:
        # update all child booking
        update_repeating_by_parent_booking.delay(
            child_id=child_of_child.id,
            who_makes_change=who_makes_change,
            updated_by_id=updated_by_id,
            updated_bookings=updated_bookings,
            resource_ids=resource_ids,
        )


@celery_task
def check_booking_conflicts_and_send_report_task(list_booking_ids, user_id):
    """
    Check conflicts for all bookings in list_booking_ids
    and send report if there are any conflict to user
    :param list_booking_ids: list of ints. Valid SubBooking id
    :param user_id: int. valid id of User
    :return: None
    """
    from webapps.booking.reports import UpdateRepeatingReporter

    if not list_booking_ids:
        return

    try:
        business, conflict_ranges_per_res = get_repeating_booking_conflicts(list_booking_ids)
    except NoSingleServiceVariantException:
        log.error('Number of service variants not equal to one in : %s', list_booking_ids)
        return

    try:
        reporter = UpdateRepeatingReporter(
            conflict_ranges_per_res, user_id, len(list_booking_ids), business=business
        )
        reporter.prepare_and_send_report()
    except User.DoesNotExist:
        log.error(
            "Can't send updateRepeatingReport. "
            "Non existing User_id: %s. "
            "Business_id: %s. "
            "First repeating id: %s",
            user_id,
            business.id,
            list_booking_ids[0],
        )


@post_transaction_task
def split_new_repeating_task(repeating_id, cycle_=0):
    """After new_repeating created, cascade split and replan tasks to fill booking_max_lead_time"""
    repeating = RepeatingBooking.objects.get(pk=repeating_id)
    splited = repeating.split_too_long_repeating()
    if splited:
        child = repeating.children.order_by('-created').first()
        replan_new_repeating_task.delay(repeating_id=child.id, cycle_=cycle_)
    return splited


split_new_repeating_task.max_allowed_cycles = 10


@post_transaction_task
def replan_new_repeating_task(repeating_id, cycle_):
    repeating = RepeatingBooking.objects.get(pk=repeating_id)
    action, count = repeating.replan_repeating()
    if action == 'expanded':
        cycle_ += 1
        # limit cycle_ to prevent a bug in spliting/replan to result in an infinite loop
        if cycle_ < split_new_repeating_task.max_allowed_cycles:
            split_new_repeating_task.delay(repeating_id, cycle_)
    return action, count


class RepeatingBookingsCeleryTaskBase(BooksyCeleryTask):
    abstract = True

    @staticmethod
    def get_repeatings_by_ids(repeating_ids):
        return (
            RepeatingBooking.objects.filter(
                id__in=repeating_ids,
                deleted__isnull=True,  # not canceled
            )
            .exclude(
                repeat__in=[
                    RepeatType.CUSTOM,
                    RepeatType.GROUP,
                ]
            )
            .prefetch_related(
                Prefetch(
                    'appointments',
                    # .extra_bookings but without MAX_EXTRA_BOOKINGS limit
                    queryset=Appointment.objects.filter(
                        deleted__isnull=True,
                    )
                    .select_related(
                        'business',
                    )
                    .prefetch_related(
                        'bookings',
                    )
                    .order_by('booked_from'),
                    to_attr='_prefetched_extra_appointments_unlimited',
                ),
            )
        )

    @staticmethod
    def get_active_repeatings():
        return (
            Appointment.objects.filter(
                business__active=True,
                status__in=Appointment.STATUSES_OCCUPYING_TIME_SLOTS,
                deleted__isnull=True,
                repeating__deleted__isnull=True,
            )
            .exclude(
                repeating__repeat__in=[
                    RepeatType.CUSTOM,
                    RepeatType.GROUP,
                ]
            )
            .values_list('repeating__id', flat=True)
            .distinct()
        )


@celery_task(bind=True, base=RepeatingBookingsCeleryTaskBase)
@using_db_for_reads(READ_ONLY_DB)
def replan_repeating_bookings(self, repeating_ids):
    """Run .replan_repeating() on selected RepeatingBookings efficiently."""
    repeatings = self.get_repeatings_by_ids(repeating_ids)
    counts = Counter({'all': len(repeating_ids)})
    for repeating in repeatings:
        result, _ = repeating.replan_repeating()
        counts[result] += 1
        counts['processed'] += 1
    return counts


@celery_task(bind=True, base=RepeatingBookingsCeleryTaskBase)
@using_db_for_reads(REPORTS_READ_ONLY_DB)
def replan_all_repeating_bookings(self):
    """Run replan_repeating_bookings task on all businesses
    RepeatingBookings."""
    repeating_ids = list(self.get_active_repeatings())
    for package in grouper(repeating_ids, 10):
        replan_repeating_bookings.delay(package)
    return len(repeating_ids)


@celery_task(bind=True, base=RepeatingBookingsCeleryTaskBase)
@using_db_for_reads(READ_ONLY_DB)
def split_repeating_bookings(self, repeating_ids):
    """Run .split_too_long_repeating() on selected RepeatingBookings."""
    repeatings = self.get_repeatings_by_ids(repeating_ids)
    counts = Counter({'all': len(repeating_ids)})
    for repeating in repeatings:
        result = repeating.split_too_long_repeating()
        counts['split'] += 1 if result else 0
        counts['processed'] += 1
    return counts


@celery_task(bind=True, base=RepeatingBookingsCeleryTaskBase)
@using_db_for_reads(REPORTS_READ_ONLY_DB)
def split_all_repeating_bookings(self):
    """Run split_repeating_bookings task on all businesses RepeatingBookings."""
    repeating_ids = list(self.get_active_repeatings())
    for package in grouper(repeating_ids, 10):
        split_repeating_bookings.delay(package)
    return len(repeating_ids)


@celery_task
def update_bci_service_questions_task(
    appointment_id,
    business_id=None,
    customer_user_id=None,
):
    """
    Save answers to service_questions from appointment to bci for future
    suggest.
    """
    from webapps.business.models.bci import BusinessCustomerInfo

    qset = Appointment.objects.filter(
        id=appointment_id,
        service_questions__isnull=False,
    ).values(
        'service_questions',
        'booked_for__id',
        'booked_for__service_questions',
    )
    if business_id:
        qset = qset.filter(business_id=business_id)
    else:
        qset = qset.filter(booked_for_id=customer_user_id)
    data = qset.first()
    if data is None:
        return

    appointment_answers = data['service_questions']
    if not appointment_answers:
        return

    bci_answers = data['booked_for__service_questions'] or QuestionsAndAnswersList()

    changes = appointment_answers.difference(bci_answers).filter(
        lambda question, answer: bool(answer)
    )
    if changes:
        bci_answers.update(changes)

        BusinessCustomerInfo.objects.filter(
            id=data['booked_for__id'],
        ).update(
            service_questions=bci_answers,
            updated=tznow(),
        )


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def short_review_pop_up_notification_delete_for_noshow_booking(appointment_id):
    from webapps.pop_up_notification.models import ShortReviewNotification

    ShortReviewNotification.delete_for_noshow_booking(appointment_id)


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def create_late_cancellation_notifications_task(
    appointment,
):  # pylint: disable=too-many-return-statements
    from django.conf import settings

    from webapps.business.models import ServiceVariantPayment
    from webapps.pop_up_notification.models import LateCancellationNotification
    from webapps.pos.models import POS

    if not settings.POS__PAY_BY_APP or not settings.POS__PREPAYMENTS:
        return

    business = appointment.business

    # Never send push for pending businesses
    if business.pos and business.pos.pay_by_app_status == POS.PAY_BY_APP_PENDING:
        return

    # Don't send push for business with PBA disabled when settings is false
    if (
        business.pos
        and business.pos.pay_by_app_status == POS.PAY_BY_APP_DISABLED
        and not settings.UNVERIFIED_PUSH_PAYMENTS
    ):
        return

    has_cf_or_pp = (
        ServiceVariantPayment.objects.filter(
            service_variant__service__business_id=business.id,
            deleted__isnull=True,
            service_variant__active=True,
            service_variant__service__active=True,
        )
        .filter(
            Q(payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE)
            | Q(payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE)
        )
        .exists()
    )

    if has_cf_or_pp:
        return
    business.events.late_cancellation_count += 1
    business.events.save()

    late_cancellation_count = business.events.late_cancellation_count

    # if has waiting notifications
    if LateCancellationNotification.objects.filter(
        valid_till__gt=tznow(), business_id=business.id, user_id=business.owner.id, used=False
    ).exists():
        return

    if late_cancellation_count in [1, 5, 50, 100]:
        lc_notification = LateCancellationNotification(
            user=business.owner,
            business=business,
            booked_from=appointment.booked_from,
            valid_till=tznow() + timedelta(hours=24),
        )
        lc_notification.save()


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def update_any_mobile_customer_appointments_task(
    customer_id,
    newest_appointment_id=None,
    skip_if_true=True,
):
    newest_appointment = None
    if newest_appointment_id:
        newest_appointment = Appointment.objects.filter(
            id=newest_appointment_id,
        ).first()
    BusinessCustomerTuning.update_any_mobile_customer_appointments(
        customer_id=customer_id,
        newest_appontment=newest_appointment,
        skip_if_true=skip_if_true,
    )


@celery_task
def post_business_update_appointment_task(
    notify: bool,
    appointment_id: int,
    before: dict,  # with appointment details before any changes
    user_id: int,
    metadata: dict,
):
    from service.booking.tools import (
        get_vital_summary_appointment,
        should_run_reschedule_scenario,
    )
    from webapps.booking.events import appointment_changed_by_business_event
    from webapps.notification.scenarios.scenarios_booking import (
        BookingChangedScenario as BCS,
    )

    appointment = Appointment.objects.filter(id=appointment_id).first()
    user = User.objects.filter(id=user_id).first()
    if not appointment or not user:
        return
    if not metadata:
        metadata = {}
    after = get_vital_summary_appointment(appointment)
    reschedule_scenario = should_run_reschedule_scenario(before, after, notify)

    # inform separately about notify and reschedule
    rescheduled = should_run_reschedule_scenario(before, after, True)

    BookingChange.add(
        appointment,
        changed_by=BookingChange.BY_BUSINESS,
        changed_user=user,
        # handler=self,
        metadata={
            'reason': 'business_update_appointment',
            'notify_about_reschedule': notify,
            **metadata,
        },
    )

    # this updates reminder for customer 24h before
    start_scenario(
        BCS,
        appointment=appointment,
        action=BCS.BUSINESS_CHANGE,
    )

    # handle reschedule scenario
    if reschedule_scenario:
        start_scenario(
            BCS,
            appointment=appointment,
            action=BCS.BUSINESS_BOOKING_RESCHEDULE_REQUEST,
            notify_about_reschedule=notify,
        )

    # handle customer or staff change scenario
    for action, change_keys in [
        (BCS.BUSINESS_CHANGED_CUSTOMER, ['booked_for_id', 'customer_email']),
        # ('business_changed_staffer', ['staffer']),  # on hold
    ]:
        if any(before[key] != after[key] for key in change_keys):
            start_scenario(
                BCS,
                appointment=appointment,
                action=action,
            )

    appointment_changed_by_business_event.send(
        appointment,
        rescheduled=rescheduled,
        notify=notify,
        operator_id=user_id,
    )

    update_bci_service_questions_task.delay(
        appointment.id,
        business_id=appointment.business_id,
    )
    if appointment.type == Appointment.TYPE.CUSTOMER and appointment.booked_for:
        update_any_mobile_customer_appointments_task.delay(
            customer_id=appointment.booked_for.id,
        )


@celery_task
def add_coordinates_to_traveling_appointments_task(_traveling_appointments_ids):
    """
    Some appointments with traveling have no coordinates (lat and lon is (0.0, 0.0)).
    This task is run from a script to fix these appointments.
    """
    from lib.geocoding.here_maps import (
        ORDER_AUTOCOMPLETE_ADDREESS,
        AutoComplete,
        ResolveGeocoder,
    )
    from webapps.booking.models import AppointmentTraveling

    class AddCoordinatesTask:
        def __init__(self, traveling_appointments_ids):
            self.travelings_to_update = []
            self.faulty_ids = []
            self.traveling_appointments_ids = traveling_appointments_ids

        def run(self):
            traveling_appointments = AppointmentTraveling.objects.filter(
                id__in=self.traveling_appointments_ids,
                latitude=0,
                longitude=0,
            )

            for traveling in traveling_appointments:
                self.update_lat_lon(traveling)
            AppointmentTraveling.objects.bulk_update(
                self.travelings_to_update,
                ['latitude', 'longitude'],
                batch_size=100,
            )
            if self.faulty_ids:
                faulty_ids_str = ', '.join(str(id_) for id_ in self.faulty_ids)
                log.warning(
                    "Couldn\'t find location for AppointmentTraveling with ids: %s", faulty_ids_str
                )

        def update_lat_lon(self, traveling_appointment: AppointmentTraveling):
            try:
                data = self._geolocate(traveling_appointment)
                traveling_appointment.latitude = data['match']['latitude']
                traveling_appointment.longitude = data['match']['longitude']
                self.travelings_to_update.append(traveling_appointment)
            except LookupError:
                self.faulty_ids.append(traveling_appointment.id)

        def _geolocate(self, traveling_appointment: AppointmentTraveling):
            location_query = self.get_location_query(traveling_appointment)
            # Search address by query string
            auto_complete = AutoComplete(
                location_query,
                advanced=True,
                here_order_match_types=ORDER_AUTOCOMPLETE_ADDREESS,
                only_street=True,
            )
            hints = auto_complete.get_data()

            location_id = hints[0]['location_id']
            # Get full address by hint
            geocoder_result = ResolveGeocoder().get_data(
                location_query,
                location_id=location_id,
            )
            return geocoder_result

        @staticmethod
        def get_location_query(traveling_appointment: AppointmentTraveling):
            city = traveling_appointment.city
            address_1 = traveling_appointment.address_line_1
            zipcode = traveling_appointment.zipcode
            return f"{city} {address_1}, {zipcode}"

    AddCoordinatesTask(_traveling_appointments_ids).run()


@celery_task
def add_reminder_notification_task(appointments_ids):
    from webapps.notification.scenarios.scenarios_booking import BookingChangedScenario

    for appointment_id in appointments_ids:
        start_scenario(
            BookingChangedScenario,
            appointment_id=appointment_id,
            action=None,  # only do the reminders
            repeating_use_all=False,  # process single appointment
        )


@celery_task
def regenerate_deeplinks_task(businesses_ids):
    from django.conf import settings

    from webapps.business.models import Business
    from webapps.subdomain_grpc.client import SubdomainGRPC, SubdomainGRPCError
    from webapps.subdomain_grpc.enums import SubdomainsSearchLimits

    def update_all_subdomains(_business_subdomains, _business, _deeplinks):
        success = True
        for subdomain in _business_subdomains:
            update_data = {
                'uuid': subdomain['uuid'],
                'business_id': _business.id,
                'country_code': settings.API_COUNTRY,
                'deeplinks': _deeplinks,
            }
            try:
                SubdomainGRPC.edit(data=update_data)
            except SubdomainGRPCError:
                log.warning(
                    'SubdomainGRPCError while updating business: %s deeplinks',
                    business.id,
                )
                success = False
        return success

    for business_id in businesses_ids:
        business = Business.objects.get(pk=business_id)
        business_subdomains = SubdomainGRPC.search(
            data={
                'business_id': business.id,
                'country_code': settings.API_COUNTRY,
                'limit': SubdomainsSearchLimits.SEARCH_LIMIT_MAX,
            }
        )
        if not business_subdomains:
            continue
        deeplinks = business.generate_new_deeplinks()
        all_subdomains_updated = update_all_subdomains(business_subdomains, business, deeplinks)
        if all_subdomains_updated:
            business.integrations['deeplinks_regenerated'] = tznow().isoformat()
            business.save(update_fields=['integrations'])


@retry_post_transaction_task
def appointment_analytics_task(analytics: dict):
    from webapps.booking.serializers.booking import AppointmentAnalyticsSerializer

    serializer = AppointmentAnalyticsSerializer(data=analytics)
    if serializer.is_valid():
        serializer.save()
    else:
        log.error(serializer.errors)


@celery_task(time_limit=31 * 60, soft_time_limit=30 * 60)
def update_subbooking_internal_service_data_task(appointment_ids: list):
    from webapps.booking.service_data import SubBookingServiceData

    subbookings = SubBooking.objects.filter(appointment__id__in=appointment_ids)
    for subbooking in subbookings.iterator():
        subbooking.service_data_internal = SubBookingServiceData.build(subbooking)
    SubBooking.objects.bulk_update(subbookings, ['service_data_internal'])


logger = logging.getLogger('booksy_experiments.time_slots_experiment')


# region: experiment logging tasks


@celery_task
def compare_with_time_slots_v2_task(  # pylint: disable=unused-argument
    country_code: str,
    request_offset: str,  # isoformat datetime
    time_slots_query_data: dict,
    staff_time_slots: dict,
    v1_elapsed_time: float,
) -> None:
    # Drain already scheduled tasks
    ...


# endregion: experiment logging tasks
