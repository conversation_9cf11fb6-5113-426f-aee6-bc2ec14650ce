import copy
import datetime
import typing as t
import weakref
from collections import OrderedDict, defaultdict
from dataclasses import dataclass
from functools import partial
from itertools import zip_longest
from typing import List, Optional, Union
from urllib.parse import urljoin

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.db import transaction as db_transaction
from django.db.transaction import atomic
from django.utils.translation import gettext as _, gettext_lazy
from rest_framework import serializers
from rest_framework.fields import (
    empty,
    get_attribute,
)

import webapps.family_and_friends.factory as family_and_friends_factory
from domain_services.booking.src.domains.calendar.aggregate.types import AppointmentType
from lib.elasticsearch.consts import ESDocType
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature.booksy_med import (
    RequireBooksyMedConsentsFlag,
    ShowBooksyMedConsentsFlag,
)
from lib.feature_flag.feature.monetisation import EnablePeakHoursFlag
from lib.feature_flag.feature.payment import (
    FeatureDepositOnBusinessAppointment,
    PrepaymentsForBusinessAppointmentEnabled,
)
from lib.french_certification.utils import french_certification_enabled
from lib.gdpr_descriptions import BUSINESS_CUSTOMER_AGREEMENTS
from lib.serializers import (
    DurationField,
    MultiModeSerializer,
    RelativedeltaField,
    safe_get,
    set_attribute,
)
from lib.tools import firstof, format_currency, tznow, sget_v2
from lib.x_version_compatibility import BooksyPayCompatibility
from service.booking.repeating_matching import RepeatingMatcher
from webapps import consts
from webapps.adyen.typing import DeviceDataDict
from webapps.booking.appointment_matcher import AppointmentMatcher
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.no_show_protection.business_appointment.business_appointment_can_have_deposit_validator_factory import (  # pylint: disable=line-too-long
    BusinessAppointmentCanHaveDepositValidatorFactory,
    BusinessAppointmentCanHaveDepositValidatorFactoryParams,
)
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    AppointmentStatusChoices,
    BookingAction,
    RepeatEndType,
    RepeatType,
    SubbookingServiceVariantMode as SVMode,
    UpdateFutureBooking,
    WhoMakesChange,
    MerchantChanged,
)
from webapps.booking.factory.addons import (
    AddonIdError,
    addon_use_make,
    validate_business_addon_quantity,
    validate_customer_addon_quantity,
    validate_customers_addons_with_services,
    validate_uniq_addons_in_subbooking,
)
from webapps.booking.factory.service_questions import (
    QuestionsAndAnswersList,
    merge_questions,
)
from webapps.booking.factory.subbookings import (
    business_subbookings_make,
    clear_wrong_any_resource,
    customer_subbookings_make,
    inject_autoassign,
)
from webapps.booking.models import (
    Appointment,
    AppointmentTraveling,
    BookingSources,
    SubBooking,
)
from webapps.booking.repeating_info import RepeatingInfo
from webapps.booking.serializers.booking import (
    BookingDateTimeField,
    BookingServiceSerializer,
    BusinessBookingActionsMixin,
    GTMAppointmentAnalyticsMixin,
    ExtendedBookingServiceSerializer,
)
from webapps.booking.serializers.family_and_friends_appointment import (
    FamilyAndFriendsAppointmentSerializerMixin,
    MemberAppointmentSerializer,
)
from webapps.booking.serializers.fields import (
    ModelLoaderRelatedField,
    ServiceQuestionsWithAnswersField,
)
from webapps.booking.serializers.utils import (
    augment_subbookings_with_wait_time,
    auto_notify_about_reschedule,
    has_traveling_services,
    inject_requested_staffer,
    list_agreements_business_customer,
)
from webapps.booking.serializers.validators import (
    required_for_commit,
    validate_appointment_version,
    validate_booking_time,
    validate_combo_children_with_parent,
    validate_dry_run_custom_service,
    validate_existing_customers_only,
    validate_gap_hole,
    validate_lead_time,
    validate_is_highlighted,
    validate_staffer_with_service_variant,
    validate_subbookings_first_booked_from_required,
    validate_requested_staffer,
    validate_staffer_selects_staffer,
)
from webapps.booking.strategy import (
    PartnerAppointmentClientDataStrategy,
    PartnerAppointmentCustomerDataStrategy,
)
from webapps.booking.timeslots.v1.consts import ERROR
from webapps.booking.timeslots.v1.data_sources import BookingServiceVariants
from webapps.booking.timeslots.v1.match_dispatchers import (
    AppointmentMatchDispatcher,
    GroupBookingMatchDispatcher,
)
from webapps.booking.tools.tools import iter_leaf_services
from webapps.business import serializers as business_serializers
from webapps.business.elasticsearch.business import (
    BusinessLocationSerializer,
    TravelingToClientsESSerializer,
)
from webapps.business.enums import PriceType, NoShowProtectionType
from webapps.business.models import (
    Business,
    Resource,
    ServiceAddOn,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.notifications.marketing_consent import send_sms_marketing_consent_notification
from webapps.business.resources import AnyResource
from webapps.business.searchables.business import BusinessWithSingleImagesSearchable
from webapps.business.searchables.serializers.business import BusinessWithSingleImagesHitSerializer
from webapps.business.serializers import SerializerBusinessMixin
from webapps.business.serializers.fields import ServicePriceField
from webapps.business.serializers.serializers import ExtendedResourceSerializer
from webapps.business.service_price import ServicePrice
from webapps.business.service_promotions import (
    AppointmentServicePromotionMixin,
    SubbookingServicePromotionMixin,
)
from webapps.business_customer_info.serializers.serializers import ShortCustomerInfoSerializer
from webapps.calculations import two_place_rounded_decimal
from webapps.consents.serializers import AppointmentConsentFormsInfoSerializer
from webapps.family_and_friends.helpers.review import family_and_friends_can_add_review
from webapps.family_and_friends.serializers.appointment import (
    FamilyAndFriendsBookForSerializer,
    get_family_and_friends_for_dry_run_appointment_serializer,
    validate_not_supported_family_and_friends_attrs,
)
from webapps.french_certification.utils import (
    is_subbooking_editable,
)
from webapps.french_certification.validators import raise_on_paid_service_change
from webapps.images.enums import IMAGES_CATEGORIES, ImageTypeEnum
from webapps.images.models import Image
from webapps.images.tools import BaseThumbnailsMixin
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_booking import (
    BookingFinishedScenario,
)
from webapps.pos.deposit import (
    charge_prepayment_on_confirm,
    create_prepayment_transaction,
)
from webapps.pos.enums import receipt_status
from webapps.pos.models import Transaction
from webapps.pos.serializer_fields import (
    BusinessBookingPaymentInfoField,
    CustomerBookingPaymentInfoField,
    FCBusinessBookingPaymentInfoField,
)
from webapps.public_partners.ports import PartnerAppDataMixin
from webapps.public_partners.utils import get_partner_names_from_business
from webapps.zoom.models import ZoomMeeting

SubbookingsList = List[SubBooking]
ProposedSubbookingsList = List[dict]


#########################################################
# ###################### COMMON ####################### #
#########################################################


class ServiceVariantIdLabelSerializer(serializers.Serializer):
    id = ModelLoaderRelatedField(
        required=True,
        source='service_variant',
        loader=BookingServiceVariants,
    )
    version = serializers.SerializerMethodField()

    @staticmethod
    def get_version(instance):
        if isinstance(instance, dict):
            return get_attribute(instance, ['service_variant', 'version'])

        if instance.service_data and instance.service_data.service_variant_id:
            return instance.service_data.service_variant_version

        if instance.service_variant:
            return instance.service_variant.version

        return None


class ServiceNameSerializer(serializers.Serializer):
    service_name = serializers.CharField(required=True, max_length=50)


class SubbookingServiceVariantSerializer(MultiModeSerializer):
    MODES = {
        SVMode.VARIANT: ServiceVariantIdLabelSerializer,
        SVMode.NO_VARIANT: ServiceNameSerializer,
        None: None,  # allowed in dry run (set allow_null to True)
    }

    def infer_mode(self, instance):
        if get_attribute(instance, ['service_variant']) is not None:
            # service variant is present
            return SVMode.VARIANT
        if get_attribute(instance, ['service_name']) is AnyResource:
            # dry run
            return None
        # service name was entered manually
        return SVMode.NO_VARIANT

    def fill_other_mode_data(self, mode, attrs):
        """Fill service_name or service_variant depending on the mode."""
        if mode == SVMode.VARIANT:
            # fill service name from service variant
            attrs['service_name'] = get_attribute(attrs, ['service_variant', 'service', 'name'])
        elif mode == SVMode.NO_VARIANT:
            # mark service variant as not chosen
            attrs['service_variant'] = None
        elif mode is None:
            attrs.update(
                {
                    'service_variant': None,
                    'service_name': AnyResource,
                }
            )
        return attrs


class SubbookingAddOnUsesSerializer(serializers.Serializer):
    id = serializers.PrimaryKeyRelatedField(
        source='service_addon',
        queryset=ServiceAddOn.all_objects.all(),
    )
    quantity = serializers.IntegerField()

    # read only fields
    name = serializers.CharField(
        read_only=True,
    )
    price = serializers.DecimalField(
        read_only=True,
        max_digits=10,
        decimal_places=2,
    )
    price_type = serializers.CharField(read_only=True)
    price_description = serializers.CharField(read_only=True)
    photo = serializers.SerializerMethodField()
    max_allowed_quantity = serializers.IntegerField(read_only=True)
    duration = DurationField(read_only=True)
    service_price = ServicePriceField()
    formatted_total_wo_discount = ServicePriceField(source='total_price')

    @staticmethod
    def get_photo(obj):
        photo = obj.service_addon.photo
        if photo:
            return {
                'id': photo.id,
                'url': photo.image_url,
            }

    def to_internal_value(self, data):
        value = super().to_internal_value(data)

        try:
            return addon_use_make(
                addon=value['service_addon'],
                quantity=value['quantity'],
                appointment=self.root.instance,
            )
        except AddonIdError:
            raise serializers.ValidationError(  # pylint: disable=raise-missing-from
                _('Wrong id of Add-on or Add-on was removed')
            )


class CustomerSubbookingAddOnUsesListSerializer(serializers.ListSerializer):
    child = SubbookingAddOnUsesSerializer(validators=[validate_customer_addon_quantity])
    validators = [
        validate_uniq_addons_in_subbooking,
    ]


class SubbookingAddOnUsesListSerializer(serializers.ListSerializer):
    child = SubbookingAddOnUsesSerializer(validators=[validate_business_addon_quantity])
    validators = [
        validate_uniq_addons_in_subbooking,
    ]


class ExistingCustomerSerializer(serializers.Serializer):
    id = serializers.PrimaryKeyRelatedField(
        source='booked_for',
        required=True,
        queryset=BusinessCustomerInfo.objects.all(),
    )

    def validate_id(self, value):
        if self.context.get('business') and value.business_id != self.context['business'].id:
            raise serializers.ValidationError(_('Invalid customer'))
        # we still have some old customer cards in the database that have cell_phone in wrong
        # format and duplicated card with proper cell_phone number. We want to detect this situation
        # earlier and return [400] with validation error instead of [500] with IntegrityError
        value.validate_unique()

        return value


class ManualCustomerSerializer(serializers.Serializer):
    name = serializers.CharField(
        source='customer_name',
        required=True,
        allow_null=True,
        allow_blank=True,
        max_length=(consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN + 1),
    )
    phone = serializers.CharField(
        source='customer_phone',
        required=True,
        allow_null=True,
        allow_blank=True,
        max_length=50,
    )
    email = serializers.EmailField(
        source='customer_email',
        required=True,
        allow_null=True,
        allow_blank=True,
        max_length=75,
    )
    invite = serializers.BooleanField(
        required=False,
        default=False,
        write_only=True,
        allow_null=True,
    )
    detailed_walkin = serializers.BooleanField(
        required=False,
        default=False,
        write_only=True,
        allow_null=True,
    )


class AppointmentCustomerSerializer(MultiModeSerializer):
    MODES = {
        ACMode.WALK_IN: None,  # empty serializer
        ACMode.CUSTOMER_CARD: ExistingCustomerSerializer,
        ACMode.MANUAL: ManualCustomerSerializer,
        None: None,  # allowed in dry run (set allow_null to True)
    }

    def infer_mode(self, instance):
        customer_card = get_attribute(instance, ['booked_for'])
        if customer_card is not None:
            if not customer_card.visible_in_business:
                # delete customer
                return ACMode.WALK_IN
            # customer card is present
            return ACMode.CUSTOMER_CARD
        if any(
            get_attribute(instance, [attr])
            for attr in [
                'customer_name',
                'customer_phone',
                'customer_email',
            ]
        ):
            # no customer card, but some data is present
            return ACMode.MANUAL
        # no customer card nor customer data - it's a walk-in!
        return ACMode.WALK_IN

    def fill_other_mode_data(self, mode, attrs):
        """Fill customer related data depending on the mode."""
        if mode == ACMode.CUSTOMER_CARD:
            # fill data with customer data
            customer_data = attrs['booked_for'].as_customer_data_with_relation()
            attrs.update(
                {
                    'customer_name': customer_data.full_name,
                    'customer_phone': customer_data.cell_phone,
                    'customer_email': customer_data.email,
                }
            )
        elif mode == ACMode.MANUAL:
            # None for customer card field
            attrs['booked_for'] = None
        elif mode == ACMode.WALK_IN:
            # None for all fields
            attrs.update(
                {
                    'booked_for': None,
                    'customer_name': None,
                    'customer_phone': None,
                    'customer_email': None,
                }
            )
        return attrs


class BookingDateSerializer(serializers.Serializer):
    booked_from = BookingDateTimeField(required=True)
    booked_till = BookingDateTimeField(required=True)

    def validate(self, attrs):
        validate_booking_time(attrs)
        return attrs


class AppointmentNewRepeatingInfoSerializer(SerializerBusinessMixin, serializers.Serializer):
    repeat = serializers.ChoiceField(
        choices=RepeatType.choices(),
        required=True,
    )
    end_type = serializers.ChoiceField(
        choices=RepeatEndType.choices(),
        default=RepeatEndType.AFTER_N_BOOKINGS,
        required=False,
    )
    repeat_till = BookingDateTimeField(
        allow_null=True,
        required=False,
    )
    repeat_number = serializers.IntegerField(
        allow_null=True,
        required=False,
    )
    booking_dates = BookingDateSerializer(
        required=False,
        many=True,
    )

    starting_on = BookingDateTimeField(read_only=True)
    ending_on = BookingDateTimeField(read_only=True)
    actual_repeats = serializers.IntegerField(read_only=True)
    has_child = serializers.BooleanField(read_only=True)
    series_count = serializers.IntegerField(read_only=True)
    total = serializers.SerializerMethodField()

    def get_total(self, instance):
        if instance.repeat == RepeatType.GROUP:
            value = self.root.instance.total.value
            if value:
                value = value * instance.repeat_number
            price_type = self.root.instance.total.price_type
            discount = self.root.instance.total.discount * instance.repeat_number

            return str(
                ServicePrice(
                    value=value,
                    price_type=price_type,
                    discount=discount,
                )
            )

        return None

    def to_representation(self, instance):
        info = (
            instance
            if isinstance(instance, RepeatingInfo)
            else RepeatingInfo.from_repeating_data(tzinfo=self.timezone, **instance)
        )
        return super().to_representation(info)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if (
            self.instance
            and '_version' in attrs
            and self.instance.appointment._version  # pylint: disable=protected-access
            != attrs['_version']
        ):
            raise serializers.ValidationError(
                {
                    '_version': _(
                        'This appointment has been changed in the meantime. '
                        'Please refresh its details.'
                    )
                }
            )

        # if repeat was not validated return
        if 'repeat' not in attrs:
            return attrs

        # default for end_type
        if 'end_type' not in attrs and attrs['repeat'] in [
            RepeatType.EVERY_DAY,
            RepeatType.EVERY_WEEK,
            RepeatType.EVERY_TWO_WEEKS,
            RepeatType.EVERY_THREE_WEEKS,
            RepeatType.EVERY_FOUR_WEEKS,
            RepeatType.EVERY_SIX_WEEKS,
            RepeatType.EVERY_MONTH,
        ]:
            attrs['end_type'] = RepeatEndType.AFTER_N_BOOKINGS

        # default for repeat_number
        if (
            attrs['repeat'] != RepeatType.CUSTOM
            and attrs.get('end_type') == RepeatEndType.AFTER_N_BOOKINGS
        ):
            attrs['repeat_number'] = attrs.get('repeat_number') or 2

        return attrs


class AppointmentRepeatingInfoSerializer(AppointmentNewRepeatingInfoSerializer):
    id = serializers.IntegerField(read_only=True)
    _version = serializers.IntegerField(source='appointment._version', read_only=True)
    total = serializers.CharField(read_only=True)


class AppointmentTravelingSerializer(serializers.Serializer):
    price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        allow_null=True,
        required=False,
    )
    address_line_1 = serializers.CharField(
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    address_line_2 = serializers.CharField(
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
        required=False,
        allow_blank=True,
    )
    apartment_number = serializers.CharField(
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
        required=False,
        allow_blank=True,
    )
    city = serializers.CharField(
        max_length=100,
    )
    zipcode = serializers.CharField(
        max_length=20,
    )
    latitude = serializers.FloatField(
        required=False,
        allow_null=True,
    )
    longitude = serializers.FloatField(
        required=False,
        allow_null=True,
    )

    formatted_price = serializers.SerializerMethodField()
    policy = serializers.SerializerMethodField()

    def get_fields(self):
        # price field for business only
        fields = super().get_fields()

        # customer cannot edit price
        if self.parent.WHO_MAKES_CHANGE == WhoMakesChange.CUSTOMER:
            del fields['price']

        # in dry_run allow empty values
        if self.context.get('dry_run'):
            for field in fields.values():
                field.required = False
                field.allow_blank = True
                field.allow_null = True
        return fields

    def get_formatted_price(self, instance):
        # If price edited in booking, ie edited by business, it is taken
        # to be fixed of fixed type.
        #
        # Price from business traveling settings taken with it's price_type
        price = safe_get(instance, ['price'])
        price_type = PriceType.FIXED
        if price == 0:
            price_type = PriceType.FREE
        if price is None and self.context['business'].is_traveling:
            price = self.context['business'].traveling.price
            price_type = self.context['business'].traveling.price_type
        return PriceType.format_service_price(price, price_type)

    def get_policy(self, _instance):
        business = self.context['business']
        if business.is_traveling:
            return business.traveling.policy
        return ''


class AppointmentTravelingMixin(serializers.Serializer):
    traveling = AppointmentTravelingSerializer(
        required=False,
        allow_null=True,
    )

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        if self.WHO_MAKES_CHANGE == WhoMakesChange.BUSINESS:
            return self._by_business_validate(validated_data)
        return self._by_customer_validate(validated_data)

    def _by_customer_validate(self, attrs):
        """Validate changes by customer"""
        has_traveling, mixed = has_traveling_services(attrs)
        if mixed:
            raise serializers.ValidationError(
                _('Mixing traveling services with local services is not allowed'),
                code='traveling_mixed',
            )

        if not has_traveling:
            return attrs

        if attrs.get('traveling') is None:
            if attrs['dry_run']:
                address = self._get_customer_address(attrs)
                attrs['traveling'] = AppointmentTraveling(**address)
            else:
                raise serializers.ValidationError(
                    {
                        'traveling': _('Mobile service info is required'),
                    },
                    code='required',
                )
        return attrs

    def _by_business_validate(self, attrs):
        """Validate changes by business"""
        if not self.context['business'].is_traveling:
            attrs['traveling'] = None
            return attrs

        traveling = attrs.get('traveling')
        dry_run = attrs['dry_run']

        # auto-fill data when empty traveling selected
        if dry_run and traveling is not None:
            if not traveling.get('address_line_1'):
                # missing address data? preserve data if any
                address = self._get_customer_address(attrs)
                address.update(traveling)
                attrs['traveling'] = traveling = address
            if traveling.get('price') is None:
                traveling['price'] = self.context['business'].traveling.price

        # traveling is required for save
        has_traveling, __mixed = has_traveling_services(attrs)
        if not dry_run and traveling is None and has_traveling:
            raise serializers.ValidationError(
                {
                    'traveling': _('Mobile service info is required'),
                },
                code='required',
            )

        return attrs

    @staticmethod
    def _get_customer_address(validated_data):
        customer = validated_data['booked_for']
        if customer:
            doc = customer.get_document()
            return {
                'address_line_1': doc.merged_data.address_line_1,
                'address_line_2': doc.merged_data.address_line_2,
                'apartment_number': doc.merged_data.apartment_number,
                'city': doc.merged_data.city,
                'latitude': doc.merged_data.latitude,
                'longitude': doc.merged_data.longitude,
                'zipcode': doc.merged_data.zipcode,
            }
        return {}

    @atomic
    def save(self, **kwargs) -> AppointmentWrapper:
        is_empty = 'traveling' not in self.validated_data
        traveling_data = self.validated_data.pop('traveling', None)
        instance: AppointmentWrapper = super().save(**kwargs)
        if is_empty:
            try:
                # clear checkout cached_property
                del instance.checkout
            except AttributeError:
                pass
            return instance

        if self.validated_data['dry_run']:
            if traveling_data and isinstance(traveling_data, dict):
                traveling_data = AppointmentTraveling(**traveling_data)
            instance.traveling = traveling_data if traveling_data else None
            return instance

        if traveling_data is not None:
            traveling = AppointmentTraveling.objects.filter(
                appointments__in=[instance.appointment.id],
            ).first()
            if traveling is None:
                if isinstance(traveling_data, AppointmentTraveling):
                    traveling = traveling_data
                else:
                    traveling = AppointmentTraveling(**traveling_data)
            else:
                for name, value in traveling_data.items():
                    setattr(traveling, name, value)
            traveling.save()
            instance.appointment.traveling = traveling
        else:
            instance.appointment.traveling = None
            AppointmentTraveling.objects.filter(
                appointments__in=[instance.appointment.id],
            ).delete()

        del instance.checkout
        instance.appointment.total = instance.checkout.total

        instance.appointment.save(
            update_fields=['traveling', 'total_value', 'total_type', 'total_discount']
        )

        return instance


class AppointmentConsentFormsMixin(serializers.Serializer):
    consent_forms = serializers.SerializerMethodField()

    @staticmethod
    def get_consent_forms(instance: AppointmentWrapper):
        """List consent form ids and info about (un)signed consents
        for the current appointment.
        """
        booked_for = instance.booked_for
        if booked_for and booked_for.id is AnyResource:
            booked_for = None

        consent_forms = instance.consent_manager.get_consent_forms(_booked_for=booked_for)

        return AppointmentConsentFormsInfoSerializer(
            instance=consent_forms,
            many=True,
        ).data

    def save(self, **kwargs):
        instance = super().save(**kwargs)
        if not self.validated_data['dry_run']:
            instance.consent_manager_cache_clear()
            instance.consent_manager.update_consents()

        return instance


class SubbookingResourceField(serializers.PrimaryKeyRelatedField):
    """PrimaryKeyRelatedField for Resource in Subbookings.

    Restrict resources to business in context.
    AnyResource means that resource will be autoassigned.

    """

    def __init__(self, **kwargs):
        self.resource_type = kwargs.pop('resource_type')
        kwargs['queryset'] = Resource.objects.filter(active=True)
        error_messages = kwargs.pop('error_messages', {})
        if self.resource_type == Resource.STAFF:
            error_messages['does_not_exist'] = error_messages.get(
                'does_not_exist',
                _(
                    'This service is not provided by chosen staff member. '
                    'Please select another one.'
                ),
            )
        elif self.resource_type == Resource.APPLIANCE:
            error_messages['does_not_exist'] = error_messages.get(
                'does_not_exist',
                _('This service is not provided by chosen resource. Please select another one.'),
            )
        kwargs['error_messages'] = error_messages
        super().__init__(**kwargs)

    def to_internal_value(self, data):
        if data in ('-1', -1):
            return AnyResource  # this means autoassign the resource!
        return super().to_internal_value(data)

    def to_representation(self, value):
        if value is AnyResource:
            return -1
        return super().to_representation(value)

    def get_queryset(self):
        return self.queryset.filter(
            business_id=self.context['business'].id,
            type=self.resource_type,
        )

    def use_pk_only_optimization(self):
        return False  # it's broken outside of Models and related fields


class SubbookingAutoassignIdField(SubbookingResourceField):
    def to_representation(self, value):
        if isinstance(value, int):
            return value
        if value is AnyResource:
            return -1
        return super().to_representation(value)


class UpdateFutureBookingsField(serializers.BooleanField):
    def __init__(self, **kwargs):
        kwargs.setdefault('required', False)
        kwargs.setdefault('default', UpdateFutureBooking.NO)
        super().__init__(**kwargs)

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        return UpdateFutureBooking(data)


class BciAgreementsCreateAppointmentSerializer(serializers.Serializer):
    def get_fields(self):
        fields = super().get_fields()
        for agreement in BUSINESS_CUSTOMER_AGREEMENTS:
            fields[agreement] = serializers.BooleanField(required=False)
        return fields


class BaseAppointmentSerializer(
    AppointmentServicePromotionMixin,
    AppointmentTravelingMixin,
    AppointmentConsentFormsMixin,
    business_serializers.SerializerBusinessMixin,
    serializers.Serializer,
):
    WHO_MAKES_CHANGE = NotImplemented  # 'b'usiness or 'c'ustomer or 's'taff
    # Allow overwrite WHO_MAKES_CHANGE for validation only
    VALIDATE_AS = None
    CONTROL_FIELDS = NotImplemented

    appointment_uid = serializers.IntegerField(read_only=True)
    _update_future_bookings = UpdateFutureBookingsField(read_only=False)

    def __init__(self, instance=None, data=empty, **kwargs):
        """Inject data['dry_run'] into context."""
        kwargs['context'] = kwargs.get('context') or {}
        kwargs['context']['dry_run'] = data is not empty and data['dry_run']
        super().__init__(instance=instance, data=data, **kwargs)

    # REPRESENTATION

    def to_representation(self, instance):
        """Add wait_time fields"""
        subbookings = get_attribute(instance, ['subbookings'])
        augment_subbookings_with_wait_time(subbookings)

        if self.initial is None and EnablePeakHoursFlag():
            # When this serializer is used in context of creating or modifying
            # appointment (so in CustomerAppointmentHandler with dry run), it
            # should take the current config of peak hours. Otherwise it should
            # take the config from the time of the last update of the
            # appointment, because it's used in endpoint returning details of
            # the appointment.
            # pylint: disable-next=protected-access
            instance._use_current_peak_hours_config = self.context.get("dry_run", False)
            # AppointmentCheckout may apply client discount. Ensure it's initialized before
            # serializing subbookings.
            instance.checkout  # pylint: disable=pointless-statement

        return super().to_representation(instance)

    # VALIDATION

    def validate_subbookings(self, value):
        if self.instance and self.instance.repeating and len(value) > 1:
            raise serializers.ValidationError(_('You can not add services to a repeating booking.'))

        if value[0]['booked_from'] is None:
            raise serializers.ValidationError(
                {
                    'subbookings.0.booked_from': [_('This field is required.')],
                }
            )

        return value

    @staticmethod
    def _validate_subbooking_availability(
        availability: dict, staffer_id: Optional[int], appliance_id: Optional[int]
    ) -> bool:
        if not availability:
            return False

        if staffer_id is None:
            staffer_id = AnyResource.id

        if appliance_id is None:
            appliance_id = AnyResource.id

        if staffer_id in availability['staffers'] and not availability['staffers'][staffer_id].get(
            'ok'
        ):
            return False

        if appliance_id in availability['appliances'] and not availability['appliances'][
            appliance_id
        ].get('ok'):
            return False

        return True

    def _clear_invalid_booking_times(self, subbookings):
        for subbooking in subbookings:
            if combo_children := subbooking.get('combo_children'):
                is_valid = all(
                    self._validate_subbooking_availability(
                        availability=child.get('_availability'),
                        staffer_id=child.get('staffer_id'),
                        appliance_id=child.get('appliance_id'),
                    )
                    for child in combo_children
                )
                self._clear_invalid_booking_times(combo_children)
            else:
                is_valid = self._validate_subbooking_availability(
                    availability=subbooking.get('_availability'),
                    staffer_id=subbooking.get('staffer_id'),
                    appliance_id=subbooking.get('appliance_id'),
                )

            if not is_valid:
                subbooking['booked_from'] = None
                subbooking['booked_till'] = None

    def validate(self, attrs):  # pylint: disable=too-many-branches
        # we force to return incomplete booking
        # so do not validate booking_time
        if not (force_incomplete := attrs.get('force_incomplete', False)):
            self._validate_booking_time(attrs)

        # dry_run logic
        if attrs['dry_run']:
            group_booking_count = 0
            if new_repeating := attrs.get('new_repeating'):
                if new_repeating['repeat'] == RepeatType.GROUP:
                    group_booking_count = new_repeating['repeat_number']

            if group_booking_count:
                dispatcher = GroupBookingMatchDispatcher(
                    business=self.business,
                    subbookings=attrs['subbookings'],
                    who_makes_change=self.WHO_MAKES_CHANGE,
                    existing_instance=self.instance,
                    group_booking_count=group_booking_count,
                )
            else:
                dispatcher = AppointmentMatchDispatcher(
                    business=self.business,
                    subbookings=attrs['subbookings'],
                    who_makes_change=self.WHO_MAKES_CHANGE,
                    existing_instance=self.instance,
                )
            attrs['subbookings'] = dispatcher.appointment_subbookings_match(
                subbookings=attrs['subbookings'],
                raise_on_conflict=self.WHO_MAKES_CHANGE != WhoMakesChange.BUSINESS
                and not force_incomplete,
            )

            if force_incomplete:
                self._clear_invalid_booking_times(attrs['subbookings'])

        attrs['status'] = self._calculate_status(attrs)

        # if needed, run repeating-booking matcher
        if attrs.get('new_repeating'):
            self.repeating_matcher(attrs)

        self._validate_service_questions(attrs)
        # region resolve promotion for appointment
        # if you wonder why it is here i just move it from
        # AppointmentServicePromotionMixin because it shadows
        # real validate method on BaseAppointmentSerializer
        if (
            self.instance
            and self.instance.appointment
            and self.instance.appointment.is_family_and_friends
            and self.instance.appointment.booked_by
        ):
            attrs['bci_for_promotion'] = self.instance.appointment.booked_by
        attrs = self._inject_service_data(attrs)
        attrs = self._inject_promotion(attrs)

        if EnablePeakHoursFlag():
            attrs = self._inject_surcharge(attrs)
        # endregion

        return super().validate(attrs)

    @staticmethod
    def _validate_booking_time(attrs):
        # basic validation
        # omit not 'dated' bookings during dry_run
        subbookings = [s for s in attrs['subbookings'] if s['booked_from'] is not None]
        subbookings.sort(key=lambda sb: sb['booked_from'])
        booked_from = min(sb['booked_from'] for sb in subbookings)
        booked_till = None
        for subbooking in [x for x in subbookings if x.get('booked_till')]:
            if booked_till is None:
                booked_till = subbooking['booked_till']
            else:
                booked_till = max(booked_till, subbooking['booked_till'])

        if booked_from and booked_till:
            if booked_from >= booked_till:
                raise serializers.ValidationError(
                    {
                        'booked_till': [
                            _('End time cannot be earlier than or equal to start date.')
                        ],
                    }
                )

            if booked_from.date() != booked_till.date():
                raise serializers.ValidationError(
                    {
                        'booked_till': [_('End date has to be the same day as start date.')],
                    }
                )
        return attrs

    @staticmethod
    def _get_service_questions(service_variant):
        return (
            merge_questions(
                *[
                    combo_membership.child.service.questions
                    for combo_membership in service_variant.combo_children_through.select_related(
                        'child'
                    )
                ]
            )
            if service_variant.service.combo_type
            else service_variant.service.questions
        )

    def _validate_service_questions(self, attrs):
        service_variants = filter(
            None, (booking.get('service_variant') for booking in attrs['subbookings'])
        )
        customer = attrs.get('booked_for')
        appointment = self.instance
        service_questions = attrs.get('service_questions')

        validated_service_questions = QuestionsAndAnswersList.with_questions(
            merge_questions(
                *[
                    self._get_service_questions(service_variant)
                    for service_variant in service_variants
                ]
            )
        )

        for answers in [
            customer and customer.service_questions,
            appointment and appointment.service_questions,
        ]:
            if answers is None:
                continue
            # fill-in with previously given answers
            validated_service_questions.update_answers(answers.filter_answered())

        validated_service_questions.update(service_questions)

        attrs['service_questions'] = validated_service_questions

        return attrs

    def _calculate_status(self, attrs):
        raise NotImplementedError()

    # SAVE / UPDATE

    def get_appointment_data(self, validated_data):
        fields = {f.name for f in Appointment._meta.fields} | {'service_questions'}
        common_data = {
            k: v for k, v in validated_data.items() if k in fields and k not in self.CONTROL_FIELDS
        }
        common_data.update(
            {
                'business_id': self.context['business'].id,
                'updated_by_id': safe_get(self.context, ['user', 'id']),
                'source_id': (
                    self.instance.source_id if self.instance else self.context['source'].id
                ),
                'type': (self.instance.type if self.instance else self.context['type']),
            }
        )
        if self.instance is None:
            common_data['created'] = tznow()
        return common_data

    @staticmethod
    def force_repeating_params(validated_data):
        """
        Overwrite params without throwing an error (#42852).
        Do it only for brand-new repeating bookings.
        :param validated_data:
        :return:
        """
        new_repeating = validated_data.get('new_repeating')
        if new_repeating:
            if new_repeating.get('repeating') == RepeatType.CUSTOM:
                new_repeating['end_type'] = None
                new_repeating['repeat_till'] = None
                new_repeating['repeat_number'] = None
            elif new_repeating.get('end_type') == RepeatEndType.ON_DATE:
                new_repeating['repeat_number'] = None
            elif new_repeating.get('end_type') == RepeatEndType.AFTER_N_BOOKINGS:
                new_repeating['repeat_till'] = None
            elif new_repeating.get('end_type') == RepeatEndType.NEVER:
                new_repeating['repeat_till'] = None
                new_repeating['repeat_number'] = None

    def create(self, validated_data: dict) -> AppointmentWrapper:
        # pylint: disable=too-many-branches
        # 42852
        self.force_repeating_params(validated_data)
        # list of agreement dicts
        bci_agreements = validated_data.pop('bci_agreements', {}) or {}
        if bci_agreements:
            bci_agreements_serializer = BciAgreementsCreateAppointmentSerializer(
                data=bci_agreements,
            )
            bci_agreements_serializer.is_valid()
            bci_agreements = bci_agreements_serializer.validated_data

        # handle new / updated / deleted subbookings
        subbookings, to_delete = self.handle_subbookings(validated_data)
        appointment_wrapper = AppointmentWrapper(
            subbookings=subbookings,
        )
        appointment_wrapper.save(
            is_booksy_gift_card_appointment=self.context.get('is_booksy_gift_card_appointment'),
            to_delete=to_delete,
            overbooking=validated_data.get('overbooking', False),
            who_makes_change=self.WHO_MAKES_CHANGE,
            new_repeating=validated_data.get('new_repeating'),
            dry_run=validated_data['dry_run'],
            update_future=validated_data.get('_update_future_bookings', UpdateFutureBooking.NO),
            recurring=validated_data.get('recurring'),
            validate_as=self.VALIDATE_AS,
            prev_status=None,
            deposit=validated_data.get('deposit', None),
        )
        dry_run = validated_data['dry_run']
        if not dry_run:
            for booking in appointment_wrapper.subbookings:
                ZoomMeeting.create_meeting_for_booking_if_necessary(booking)
            appointment_wrapper.appointment.refresh_zoom_meeting()

        if bci := appointment_wrapper.booked_for:
            # sometimes frontend sends None for bci?
            for name, value in bci_agreements.items():
                agreement = BUSINESS_CUSTOMER_AGREEMENTS.get(name)
                if agreement is None or agreement.information is True:
                    continue

                if (
                    name == settings.WB_AGREEMENT
                    and self.WHO_MAKES_CHANGE == WhoMakesChange.CUSTOMER
                ):
                    if not getattr(bci, name) and value:
                        db_transaction.on_commit(
                            partial(send_sms_marketing_consent_notification, bci)
                        )
                set_attribute(appointment_wrapper.booked_for, [name], value)
                if not dry_run:
                    set_attribute(appointment_wrapper.booked_for, ['ask_for_consent'], False)
            appointment_wrapper.booked_for.save()
        if (
            not dry_run
            and sget_v2(appointment_wrapper, ['appointment', 'status'])
            == Appointment.STATUS.PENDING_PAYMENT
        ):
            if validated_data.get('deposit', None) is not None:
                prepayment_total = two_place_rounded_decimal(
                    validated_data['deposit']['deposit_amount']
                )
            else:
                prepayment_total = appointment_wrapper.checkout.prepayment.total

            create_prepayment_transaction(
                subbookings_ids=[b.id for b in appointment_wrapper.subbookings],
                prepaid_total=prepayment_total,
                prepayment=prepayment_total,
                pos=self.business.pos,
                tip=None,
                user=bci.user if bci else None,
            )
        return appointment_wrapper

    def update(self, instance: AppointmentWrapper, validated_data: dict) -> AppointmentWrapper:
        # fields for bci
        marketing_agreement = validated_data.pop('marketing_agreement', None)
        # handle subbookings is changing the status
        prev_status = instance.status
        # handle new / updated / deleted subbookings
        subbookings, to_delete = self.handle_subbookings(validated_data)
        # do the save
        instance.subbookings = subbookings
        is_booksy_gift_card_appointment = (
            instance.appointment.is_booksy_gift_card_appointment
            or self.context.get('is_booksy_gift_card_appointment')
        )

        instance.save(
            is_booksy_gift_card_appointment=is_booksy_gift_card_appointment,
            to_delete=to_delete,
            overbooking=validated_data.get('overbooking', False),
            who_makes_change=self.WHO_MAKES_CHANGE,
            new_repeating=validated_data.get('new_repeating'),
            dry_run=validated_data['dry_run'],
            update_future=validated_data.get('_update_future_bookings', UpdateFutureBooking.NO),
            recurring=validated_data.get('recurring'),
            validate_as=self.VALIDATE_AS,
            prev_status=prev_status,
        )
        if marketing_agreement is not None:
            bci = instance.booked_for
            bci.marketing_agreement = marketing_agreement
            bci.save(update_fields=['marketing_agreement'])

        return instance

    @staticmethod
    def match_subbokings(subbooking_data, existing_bookings):
        order_dct = {id(d): i for i, d in enumerate(subbooking_data)}
        subbooking_data_dict = defaultdict(list)
        existing_data_dict = defaultdict(list)

        for booking in subbooking_data:
            if variant := booking.get('service_variant'):
                key = f'sv_{variant.id}'
            else:
                key = f'sn_{booking.get("service_name", "")}'

            subbooking_data_dict[key].append(booking)

        for booking in existing_bookings:
            if variant_id := booking.service_variant_id:
                key = f'sv_{variant_id}'
            else:
                key = f'sn_{booking.service_name or ""}'

            existing_data_dict[key].append(booking)

        data = []
        left_subbooking_data = []
        left_exisiting_subbookings = []

        for key in set(subbooking_data_dict) | set(existing_data_dict):
            subbooking_data = subbooking_data_dict[key]
            existing_data = existing_data_dict[key]

            if len(subbooking_data) > len(existing_data):
                left_subbooking_data += subbooking_data[len(existing_data) :]
                subbooking_data = subbooking_data[: len(existing_data)]

            elif len(existing_data) > len(subbooking_data):
                left_exisiting_subbookings += existing_data[len(subbooking_data) :]
                existing_data = existing_data[: len(subbooking_data)]

            data += list(zip(subbooking_data, existing_data))
        data += list(zip_longest(left_subbooking_data, left_exisiting_subbookings))

        return sorted(data, key=lambda a: order_dct.get(id(a[0]), float('inf')))

    def handle_subbookings(self, validated_data):
        """Prepare list of subbookings to create, update or delete."""

        if self.instance:
            existing_bookings = self.instance.subbookings
            appointment = self.instance.appointment
        else:
            existing_bookings = []
            appointment = Appointment()

        appointment_data = self.get_appointment_data(validated_data)
        for field, value in appointment_data.items():
            setattr(appointment, field, value)

        subbookings = []
        to_delete = []
        for subbooking_data, booking in self.match_subbokings(
            validated_data.pop('subbookings'), existing_bookings
        ):
            if subbooking_data:
                # update or create
                booking = booking or SubBooking(
                    created=appointment_data.get('created'),
                    appointment=appointment,
                )
                to_delete.extend(
                    self.handle_subbooking_combo_children(
                        booking, subbooking_data.pop('combo_children', None)
                    )
                )

                for attr, value in subbooking_data.items():
                    setattr(booking, attr, value)
                subbookings.append(booking)
            elif booking:
                # subbooking no longer needed - we will delete it
                to_delete.append(booking)
                to_delete.extend(booking.combo_children)

        return subbookings, to_delete

    @staticmethod
    def handle_subbooking_combo_children(subbooking, combo_children_data):
        if not combo_children_data:
            # make sure children from old service are not left over
            subbooking.combo_children = []
            return []

        existing = defaultdict(list)
        for child in subbooking.combo_children:
            existing[child.service_variant_id].append(child)

        combo_children = []
        for data in combo_children_data:
            service_variant_id = data['service_variant'].id
            try:
                child = existing[service_variant_id].pop(0)
            except IndexError:
                child = SubBooking()
            for attr, value in data.items():
                setattr(child, attr, value)
            combo_children.append(child)

        for child in combo_children:
            child.appointment = subbooking.appointment
            if child.combo_parent is None:
                child._combo_parent_proxy = weakref.proxy(  # pylint: disable=protected-access
                    subbooking
                )

        subbooking.combo_children = combo_children

        to_delete = [v[0] for v in existing.values() if v]
        return to_delete

    @atomic
    def save(self, **kwargs) -> AppointmentWrapper:
        # DRY RUN
        if self.validated_data.get('dry_run'):
            return super().save(**kwargs)

        # CREATE
        if self.instance is None:
            return super().save(**kwargs)

        # EDIT
        prev_booked_from = self.instance.booked_from
        prev_booked_till = self.instance.booked_till
        status_before = self.instance.status

        appointment_wrapper = super().save(**kwargs)
        if isinstance(self, AppointmentSerializer):
            from webapps.pos.serializers import TransactionSerializer

            TransactionSerializer.update_transaction_with_booking(
                appointment_wrapper.appointment,
            )
            appointment_wrapper.clear_cache_payment_and_deposit()

        # if appointment was moved from future to past
        if prev_booked_till > tznow() > appointment_wrapper.booked_till and (
            appointment_wrapper.status == Appointment.STATUS.FINISHED
        ):
            start_scenario(BookingFinishedScenario, appointment=self.instance.appointment)

        # If appointment was moved it may be necessary to update zoom meeting
        if (
            prev_booked_from != appointment_wrapper.booked_from
            and prev_booked_till != appointment_wrapper.booked_till
        ):
            zoom_meeting = appointment_wrapper.appointment.get_zoom_meeting()
            if zoom_meeting:
                zoom_meeting.delete()
                appointment_wrapper.appointment.refresh_zoom_meeting()
            for booking in appointment_wrapper.subbookings:
                ZoomMeeting.create_meeting_for_booking_if_necessary(booking)

        # update payment info
        if status_before in [
            Appointment.STATUS.UNCONFIRMED,
            Appointment.STATUS.PROPOSED,
        ] and appointment_wrapper.status in [
            Appointment.STATUS.FINISHED,
            Appointment.STATUS.ACCEPTED,
        ]:
            charge_prepayment_on_confirm(
                appointment_id=appointment_wrapper.appointment.id,
                device_data=DeviceDataDict(
                    fingerprint=self.context.get('device_fingerprint', ''),
                    phone_number=self.context.get('cell_phone', ''),
                    user_agent=self.context.get('user_agent', ''),
                ),
            )

        return self.instance

    # MATCHERS

    def repeating_matcher(self, attrs):
        # adapt attrs for matcher
        prototype = attrs['subbookings'][0]
        data = copy.deepcopy(attrs['new_repeating'])
        data['extra_bookings'] = []
        if data.get('booking_dates', []):
            dates = sorted(data['booking_dates'], key=lambda x: x['booked_from'])
            # append prototype
            data['extra_bookings'].append(copy.deepcopy(prototype))
            # append rest of the draft
            for date in dates[1:]:
                booking = copy.deepcopy(prototype)
                booking['booked_from'] = date['booked_from']
                booking['booked_till'] = date['booked_till']
                data['extra_bookings'].append(booking)
        else:
            data['extra_bookings'].append(copy.deepcopy(prototype))

        # run matcher
        draft = RepeatingMatcher(data, business=self.context['business']).call()

        # update attrs with data from draft
        attrs['new_repeating']['booking_dates'] = [
            {
                'booked_from': draft_booking['booked_from'],
                'booked_till': draft_booking['booked_till'],
            }
            for draft_booking in draft['extra_bookings']
        ]

        return attrs

    def matcher(self, attrs):
        subbookings = attrs['subbookings']
        if attrs.get('_preserve_order'):
            for i, subbooking in enumerate(subbookings, 1):
                subbooking['order'] = i

        # run matcher
        draft = AppointmentMatcher(
            {'subbookings': subbookings},
            business=self.context['business'],
            who_makes_change=self.WHO_MAKES_CHANGE,
            omit_booking_ids=(
                [sb.id for sb in self.instance.subbookings] if self.instance else None
            ),
            full_availability=True,
            force_incomplete=attrs.get('force_incomplete', False),
        ).call()

        attrs['subbookings'] = draft['subbookings']

        return attrs

    @staticmethod
    def _inject_service_data(attrs):
        for subbooking_data in attrs['subbookings']:
            if not subbooking_data['service_variant']:
                continue

            combo_children_data = subbooking_data.get('combo_children', [])

            subbooking = SubBooking(
                service_variant=subbooking_data['service_variant'],
                combo_parent=None,
            )
            subbooking.combo_children = [
                SubBooking(
                    service_variant=child_data['service_variant'],
                    combo_parent=subbooking,
                )
                for child_data in combo_children_data
            ]

            subbooking_data['service_data'] = subbooking.service_data
            for child, child_data in zip(subbooking.combo_children, combo_children_data):
                child_data['service_data'] = child.service_data

            del subbooking.combo_children

        return attrs


#########################################################
# ################### BUSINESS API #################### #
#########################################################


class ComboChildSerializer(PartnerAppDataMixin, serializers.Serializer):
    class Meta:
        validators = [
            required_for_commit(['booked_from', 'booked_till']),
            validate_staffer_selects_staffer,
            validate_requested_staffer,
        ]

    booked_from = BookingDateTimeField(required=True, allow_null=True)
    booked_till = BookingDateTimeField(required=False, allow_null=True)
    duration = DurationField(
        # helper field when booked_* are null in multibooking dry_runs (drag)
        required=False,
        min_value=relativedelta(minutes=5),
        write_only=True,
        allow_null=True,
    )
    staffer_id = SubbookingResourceField(
        required=True,
        source='staffer',
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    autoassigned_staffer_id = SubbookingAutoassignIdField(
        required=False,
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    appliance_id = SubbookingResourceField(
        required=True,
        source='appliance',
        allow_null=True,
        resource_type=Resource.APPLIANCE,
    )
    service_variant = SubbookingServiceVariantSerializer(
        required=True,
        disable_modes=[None, SVMode.NO_VARIANT],  # 'variant' mode required
    )
    is_staffer_requested_by_client = serializers.BooleanField(required=False, allow_null=True)
    # read_only fields
    id = serializers.IntegerField(read_only=True)
    service = ExtendedBookingServiceSerializer(read_only=True, source='*')
    service_price = ServicePriceField()
    staffer = ExtendedResourceSerializer(read_only=True)
    appliance = ExtendedResourceSerializer(read_only=True)
    autoassign = serializers.BooleanField(read_only=True)
    gap_hole_start = BookingDateTimeField(read_only=True, required=False)
    gap_hole_end = BookingDateTimeField(read_only=True, required=False)
    is_highlighted = serializers.BooleanField(read_only=True)
    # all 3 injected by BaseAppointmentSerializer.to_representation()
    wait_time = RelativedeltaField(required=False, read_only=True, default=relativedelta())
    long_wait_time = serializers.BooleanField(required=False, read_only=True)
    wait_type = serializers.CharField(required=False, read_only=True)
    gap_time = RelativedeltaField(required=False, read_only=True)
    _availability = serializers.DictField(read_only=True)
    addons = SubbookingAddOnUsesListSerializer(
        required=False,
        allow_null=True,
        many=False,
    )


class CustomerComboChildSerializer(serializers.Serializer):
    class Meta:
        validators = [
            validate_staffer_with_service_variant,
        ]

    booked_from = BookingDateTimeField(required=True, allow_null=True)
    duration = DurationField(
        # helper field when booked_* are null in multibooking dry_runs (drag)
        required=False,
        min_value=relativedelta(minutes=5),
        write_only=True,
        allow_null=True,
    )
    staffer_id = SubbookingResourceField(
        required=True,
        source='staffer',
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    service_variant = SubbookingServiceVariantSerializer(
        required=True,
        disable_modes=[None, SVMode.NO_VARIANT],  # 'variant' mode required
    )
    addons = CustomerSubbookingAddOnUsesListSerializer(
        required=False,
        allow_null=True,
        many=False,
    )
    # read_only fields
    id = serializers.IntegerField(read_only=True)
    service = ExtendedBookingServiceSerializer(read_only=True, source='*')
    staffer = ExtendedResourceSerializer(read_only=True)
    appliance = ExtendedResourceSerializer(read_only=True)
    autoassign = serializers.BooleanField(read_only=True)
    gap_hole_start = BookingDateTimeField(read_only=True, required=False)
    gap_hole_end = BookingDateTimeField(read_only=True, required=False)
    # all 3 injected by BaseAppointmentSerializer.to_representation()
    wait_time = RelativedeltaField(required=False, read_only=True)
    long_wait_time = serializers.BooleanField(required=False, read_only=True)
    wait_type = serializers.CharField(required=False, read_only=True)
    gap_time = RelativedeltaField(required=False, read_only=True)
    _availability = serializers.DictField(read_only=True)


class SubbookingSerializer(
    SubbookingServicePromotionMixin,
    PartnerAppDataMixin,
    business_serializers.SerializerBusinessMixin,
    serializers.Serializer,
):
    class Meta:
        validators = [
            required_for_commit(['booked_from', 'booked_till']),
            validate_booking_time,
            validate_staffer_selects_staffer,
            validate_requested_staffer,
            # sic, business also must preserve combo structure
            validate_combo_children_with_parent,
            validate_dry_run_custom_service,
            # business and only business can change duration incoherent with gap_hole
            validate_gap_hole,
            # business can do anything, no validate resources:
            # validate_resources_with_service_variant
            validate_is_highlighted,
        ]

    booked_from = BookingDateTimeField(required=True, allow_null=True)
    booked_till = BookingDateTimeField(required=True, allow_null=True)
    duration = DurationField(
        # helper field when booked_* are null in multibooking dry_runs (drag)
        required=False,
        min_value=relativedelta(minutes=5),
        write_only=True,
        allow_null=True,
    )
    staffer_id = SubbookingResourceField(
        required=True,
        source='staffer',
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    autoassigned_staffer_id = SubbookingAutoassignIdField(
        required=False,
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    appliance_id = SubbookingResourceField(
        required=True,
        source='appliance',
        allow_null=True,
        resource_type=Resource.APPLIANCE,
    )
    service_variant = SubbookingServiceVariantSerializer(required=True, allow_null=True)
    addons = SubbookingAddOnUsesListSerializer(
        required=False,
        allow_null=True,
        many=False,
    )
    combo_children = ComboChildSerializer(many=True, required=False)
    is_staffer_requested_by_client = serializers.BooleanField(required=False, allow_null=True)

    # read_only fields
    id = serializers.IntegerField(read_only=True)
    service = ExtendedBookingServiceSerializer(read_only=True, source='*')
    service_price = ServicePriceField()
    staffer = ExtendedResourceSerializer(read_only=True)
    appliance = ExtendedResourceSerializer(read_only=True)
    autoassign = serializers.BooleanField(read_only=True)
    gap_hole_start = BookingDateTimeField(read_only=True, required=False)
    gap_hole_end = BookingDateTimeField(read_only=True, required=False)
    # all 3 injected by BaseAppointmentSerializer.to_representation()
    wait_time = RelativedeltaField(required=False, read_only=True)
    long_wait_time = serializers.BooleanField(required=False, read_only=True)
    wait_type = serializers.CharField(required=False, read_only=True)
    gap_time = RelativedeltaField(required=False, read_only=True)
    is_highlighted = serializers.BooleanField(required=False, read_only=True)
    service_promotion = serializers.SerializerMethodField()
    surcharge = serializers.SerializerMethodField()
    editable = serializers.SerializerMethodField()  # for FRCert usage only
    _version = serializers.IntegerField(
        read_only=True,
        required=False,
        source='appointment._version',
    )
    # injected by matcher:
    _availability = serializers.DictField(read_only=True)

    def get_fields(self):
        """Modify some fields based on circumstances.

        * Make service_variant field nullable in dry_runs.

        """
        fields = super().get_fields()
        if self.context.get('dry_run'):
            fields['service_variant'].allow_null = True

        self.allow_booking_highlighting_for_business(fields)

        if not EnablePeakHoursFlag():
            fields.pop('surcharge')

        return fields

    def allow_booking_highlighting_for_business(self, fields: dict) -> None:
        if self.parent.parent.WHO_MAKES_CHANGE == WhoMakesChange.BUSINESS:
            fields['is_highlighted'].read_only = False

    def get_editable(self, instance):
        if french_certification_enabled(self.business.id) and instance.id:
            return is_subbooking_editable(instance.id)
        return True


class CalculateStatusMixin:
    @classmethod
    def calculate_status(  # pylint: disable=too-many-return-statements too-many-branches
        cls,
        instance: t.Optional[Appointment],
        attrs: dict,
        notify_about_reschedule: bool,
    ) -> Appointment.STATUS:
        """Calculate booking status after save."""

        if not attrs.get('subbookings'):
            return Appointment.STATUS.ACCEPTED

        if instance:  # PUT
            status = cls.get_post_edit_status(
                instance=instance,
                attrs=attrs,
                notify_about_reschedule=notify_about_reschedule,
            )
        else:  # POST
            status = cls.get_post_create_status(attrs)
        return status

    @staticmethod
    def get_booked_from(attrs: dict) -> datetime.datetime:
        return min(bk['booked_from'] for bk in attrs['subbookings'])

    @staticmethod
    def get_post_create_status(attrs: dict) -> Appointment.STATUS:
        now = tznow()
        booked_till = attrs['subbookings'][-1]['booked_till']
        if booked_till > now:  # future
            status = Appointment.STATUS.ACCEPTED
        else:
            status = Appointment.STATUS.FINISHED  # past
        return status

    @classmethod
    def get_post_edit_status(
        cls,
        instance: Appointment,
        attrs: dict,
        notify_about_reschedule: bool,
    ) -> Appointment.STATUS:
        original_status = status = get_attribute(instance, ['status'])

        if attrs.get('dry_run'):
            return status

        now = tznow()
        booking_type = get_attribute(instance, ['type'])
        booked_from = cls.get_booked_from(attrs)
        booked_till = max(bk['booked_till'] for bk in attrs['subbookings'])

        if (
            status in Appointment.STATUSES_NONEDITABLE
            or status == Appointment.STATUS.PENDING_PAYMENT
        ):
            return status

        # edit ongoing appointment - don't change status
        is_ongoing_appointment = (
            booked_till > now > booked_from and instance.booked_till > now > instance.booked_from
        )
        if is_ongoing_appointment:
            return status

        if booked_till > now:
            status = Appointment.STATUS.ACCEPTED  # future
        elif status != Appointment.STATUS.NOSHOW:
            status = Appointment.STATUS.FINISHED  # past

        if (
            not (
                original_status == Appointment.STATUS.FINISHED
                and status == Appointment.STATUS.ACCEPTED
            )
            and (original_status == Appointment.STATUS.PROPOSED or notify_about_reschedule)
            and status not in Appointment.STATUSES_TIME_PASSED
            and booking_type == Appointment.TYPE.CUSTOMER
        ):
            # we have modified customer booking
            # and want the customer to confirm it
            status = Appointment.STATUS.PROPOSED
        return status


@dataclass
class ExternalPaymentMethod:
    partner: str
    token: str


class DepositSerializer(serializers.Serializer):
    deposit_amount = serializers.FloatField(required=True, write_only=True)


class ExternalPaymentMethodSerializer(serializers.Serializer):
    partner = serializers.CharField(required=True, write_only=True)
    token = serializers.CharField(required=True, write_only=True)

    def create(self, validated_data):
        return ExternalPaymentMethod(**validated_data)


class BusinessAppointmentDepositSerializer(serializers.Serializer):
    is_deposit_available = serializers.SerializerMethodField()
    external_payment_method = ExternalPaymentMethodSerializer(
        required=False, allow_null=True, write_only=True
    )
    deposit = DepositSerializer(required=False, allow_null=True, write_only=True)

    def _is_business_appointment(self) -> bool:
        return self.context.get('type', AppointmentType.CUSTOMER) == AppointmentType.BUSINESS

    @staticmethod
    def _has_business_appointment_deposit(attrs) -> bool:
        return attrs.get("deposit", None) is not None

    @staticmethod
    def _is_deposit_available(appointment: Appointment, business_id: int) -> bool:
        params = BusinessAppointmentCanHaveDepositValidatorFactoryParams(
            business_id=business_id, appointment=appointment
        )
        return BusinessAppointmentCanHaveDepositValidatorFactory.make(params).validate()

    def get_is_deposit_available(self, obj: AppointmentWrapper):

        if not self._is_business_appointment():
            return False
        business = self.context.get('business')
        if not business:
            return False

        return self._is_deposit_available(obj.appointment, self.context.get('business').id)

    def validate(self, attrs):
        super().validate(attrs)
        if not self._is_business_appointment():
            return attrs
        if self._has_business_appointment_deposit(attrs):
            if not self.is_appointment_eligible_for_deposit(attrs):
                raise serializers.ValidationError(
                    {
                        'deposit': _(
                            'This appointment is not eligible for a deposit. '
                            'Check details and try again.'
                        ),
                    }
                )
        return attrs

    def is_appointment_eligible_for_deposit(
        self,
        attrs: dict,  # pylint: disable=unused-argument
    ) -> bool:
        business = self.context.get('business')
        return bool(FeatureDepositOnBusinessAppointment(UserData(subject_key=business.id)))

    @classmethod
    def get_post_create_status(cls, attrs: dict) -> Appointment.STATUS:
        status = super().get_post_create_status(attrs)
        if attrs.get('deposit', None) is not None and status == Appointment.STATUS.ACCEPTED:
            status = Appointment.STATUS.PENDING_PAYMENT
        return status


class AppointmentSerializer(
    BusinessBookingActionsMixin,
    BusinessAppointmentDepositSerializer,
    CalculateStatusMixin,
    FamilyAndFriendsAppointmentSerializerMixin,
    PartnerAppDataMixin,
    BaseAppointmentSerializer,
):
    class Meta:
        validators = (validate_subbookings_first_booked_from_required,)

    WHO_MAKES_CHANGE = WhoMakesChange.BUSINESS

    _version = serializers.IntegerField(
        required=False, allow_null=True, validators=(validate_appointment_version,)
    )

    customer = AppointmentCustomerSerializer(allow_null=True)

    business_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
    )
    business_secret_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.BUSINESS_SECRET_NOTE__MAX_LENGTH,
    )

    subbookings = SubbookingSerializer(required=True, many=True, allow_empty=False)
    # read-only fields
    appointment_id = serializers.IntegerField(read_only=True)
    appointment_type = serializers.CharField(read_only=True)
    booked_from = BookingDateTimeField(read_only=True)  # based on subbookings
    booked_till = BookingDateTimeField(read_only=True)  # based on subbookings
    status = serializers.ChoiceField(
        choices=AppointmentStatusChoices,
        read_only=True,
    )
    type = serializers.ChoiceField(
        choices=Appointment.TYPE_CHOICES,
        read_only=True,
    )
    customer_note = serializers.CharField(read_only=True)
    repeating = AppointmentRepeatingInfoSerializer(
        read_only=True,
        source='repeating.repeating_info',
    )
    repeating_series = AppointmentRepeatingInfoSerializer(
        read_only=True,
        source='repeating.repeating_series_info',
    )
    actions = serializers.SerializerMethodField()
    payment_info = BusinessBookingPaymentInfoField()
    total = serializers.CharField(source='total.price_only', read_only=True)
    total_value = serializers.FloatField(source='total.value', read_only=True)
    total_discount_amount = serializers.FloatField(source='total.discount', read_only=True)
    total_tax_excluded = serializers.FloatField(
        source='checkout.total_tax_excluded', read_only=True
    )
    external_source = serializers.SerializerMethodField()
    service_questions = ServiceQuestionsWithAnswersField(required=False)
    join_meeting_url = serializers.CharField(read_only=True)
    meeting_id = serializers.IntegerField(read_only=True)
    from_promo = serializers.BooleanField(read_only=True)
    is_booksy_gift_card_appointment = serializers.BooleanField(read_only=True)

    # write-only fields (but returned back in dry_run)
    new_repeating = AppointmentNewRepeatingInfoSerializer(
        required=False, allow_null=True, write_only=True
    )

    # write-only fields
    dry_run = serializers.BooleanField(required=True, write_only=True)
    overbooking = serializers.BooleanField(required=True, write_only=True)
    _notify_about_reschedule = serializers.BooleanField(
        required=True,  # not required in POST
        write_only=True,
    )
    _preserve_order = serializers.BooleanField(
        required=False,
        default=False,
        write_only=True,
    )
    _update_future_bookings = UpdateFutureBookingsField(write_only=True)
    _notification_enabled = serializers.BooleanField(
        required=False,
        default=True,
        write_only=True,
    )
    _resource_selection_required = serializers.SerializerMethodField()

    CONTROL_FIELDS = (
        '_version',
        'overbooking',
        '_notify_about_reschedule',
        'dry_run',
        '_preserve_order',
        '_update_future_bookings',
    )

    def get_fields(self):
        """Modify some fields based on circumstances.

        * Make _version not required in POST scenario.
        * Make customer field nullable in dry_runs.

        """
        fields = super().get_fields()
        if self.instance is None:
            fields['_notify_about_reschedule'].required = False
        if self.context['dry_run']:
            fields['customer'].allow_null = True
            fields['new_repeating'].write_only = False

        return fields

    @staticmethod
    def get_external_source(appointment):
        return appointment.external_source or ''

    # VALIDATION
    def _calculate_status(self, attrs):
        return self.calculate_status(
            self.instance,
            attrs,
            attrs.get('_notify_about_reschedule'),
        )

    def validate(self, attrs):
        if self.instance:
            transaction = Transaction.objects.by_appointment_id(self.instance.appointment_id).last()

            if transaction and transaction.latest_receipt.status_code in [
                receipt_status.CALL_FOR_PAYMENT,
                receipt_status.PENDING,
                receipt_status.PAYMENT_FAILED,
            ]:
                raise serializers.ValidationError(
                    _('To edit this appointment please finish connected transaction.'),
                    code='callback_waiting',
                )

        force_set_any_resources = False
        if (repeating := attrs.get('new_repeating')) and repeating['repeat'] == RepeatType.GROUP:
            force_set_any_resources = True
        attrs['subbookings'] = business_subbookings_make(
            attrs['subbookings'],
            dry_run=self.context['dry_run'],
            preferred_staffer=None,
            business=self.context['business'],
            force_set_any_resources=force_set_any_resources,
        )

        # TODO: fill service_questions?

        self._validate_service_variants(attrs)

        auto_notify_about_reschedule(attrs, attrs['subbookings'], self.instance)
        attrs = super().validate(attrs)

        return attrs

    @staticmethod
    def _validate_service_variants(attrs):
        # backwards compatibility if the service variant is not performed by the staffer
        subbookings = attrs.get('subbookings')

        variants = set(
            sv.id for sv in filter(None, (s.get('service_variant') for s in subbookings))
        )

        variant_resources = dict(
            ServiceVariant.objects.filter(id__in=variants)
            .prefetch_related('resources')
            .values_list('id', 'resources')
        )

        for subbooking in subbookings:
            variant = subbooking.get('service_variant')
            staffer = subbooking.get('staffer')
            if (  # pylint: disable=too-many-boolean-expressions
                variant
                and staffer
                and staffer is not AnyResource
                and variant_resources[variant.id] is not None
                and staffer.id not in variant.staffer_ids
                and not variant.service.is_combo
            ):
                subbooking['service_name'] = variant.service.name
                subbooking['service_variant'] = None

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if hasattr(self, 'initial_data'):
            dry_run = self.context.get('dry_run')
            customer = self.initial_data.get('customer') or {}
            if dry_run is True and customer.get('mode') is None:
                data['customer'] = None
            data['with_prepayment'] = self.initial_data.get('with_prepayment', False)
        for subbooking in data['subbookings']:
            subbooking['actions'] = data['actions']
        return data

    def get__resource_selection_required(
        self, instance
    ):  # pylint: disable=too-many-return-statements
        """
        Indicates if should prevent appointment.save because of DrawForOverbooking

        We don't support draw AnyResource for overbooking:
        - AnyResource selected
        - no resource available without conflicts
        - more than one resource on the selection list, ie. active resources in business
        """
        if not self.context['dry_run']:
            return False

        if safe_get(instance, ['new_repeating', 'repeat']) == RepeatType.GROUP:
            return False

        many_staffers = len(self.business.staffer_ids) > 1
        many_appliances = len(self.business.appliance_ids) > 1

        if not many_staffers and not many_appliances:
            return False

        for sbk in iter_leaf_services(instance.subbookings):
            if many_staffers and sbk.staffer is AnyResource:
                if safe_get(sbk, ['_availability', 'staffers', AnyResource.id, 'type']) == ERROR:
                    return True

            if many_appliances and sbk.appliance is AnyResource:
                if safe_get(sbk, ['_availability', 'appliances', AnyResource.id, 'type']) == ERROR:
                    return True

        return False


class FCAppointmentSerializer(AppointmentSerializer):
    payment_info = FCBusinessBookingPaymentInfoField()

    def validate(self, attrs):
        validated_data = super().validate(attrs)  # pylint: disable=too-many-function-args
        raise_on_paid_service_change(self.instance.appointment_id, attrs['subbookings'])
        return validated_data


def get_appointment_serializer(*args, **kwargs):
    business_id = kwargs['context']['business'].id
    if french_certification_enabled(business_id):
        return FCAppointmentSerializer(*args, **kwargs)
    return AppointmentSerializer(*args, **kwargs)


# pylint: disable=too-many-ancestors
class AnalyticsAppointmentSerializer(GTMAppointmentAnalyticsMixin, AppointmentSerializer):
    pass


#########################################################
# ################### CUSTOMER API #################### #
#########################################################


class CustomerSubbookingSerializer(
    SubbookingServicePromotionMixin,
    business_serializers.SerializerBusinessMixin,
    serializers.Serializer,
):
    class Meta:
        validators = [
            required_for_commit(['booked_from']),
            validate_booking_time,
            validate_customers_addons_with_services,
            validate_combo_children_with_parent,
            validate_staffer_with_service_variant,
            validate_lead_time,
        ]

    booked_from = BookingDateTimeField(required=True, allow_null=True)
    duration = DurationField(
        # helper field when booked_* are null in multibooking dry_runs (drag)
        required=False,
        min_value=5,
        write_only=True,
        allow_null=True,
    )
    staffer_id = SubbookingResourceField(
        required=True,
        source='staffer',
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    service_variant = SubbookingServiceVariantSerializer(
        required=True,
        disable_modes=[
            None,
            SVMode.NO_VARIANT,
        ],  # only 'variant' mode is available
    )
    addons = CustomerSubbookingAddOnUsesListSerializer(
        required=False,
        allow_null=True,
        many=False,
    )
    combo_children = CustomerComboChildSerializer(many=True, required=False)

    # read_only fields
    id = serializers.IntegerField(read_only=True)
    booked_till = BookingDateTimeField(read_only=True)
    service = BookingServiceSerializer(read_only=True, source='*')
    service_price = ServicePriceField()
    staffer = business_serializers.ResourceSerializer(read_only=True)
    appliance = business_serializers.ResourceSerializer(read_only=True)
    autoassign = serializers.BooleanField(read_only=True)
    # all 3 injected by BaseAppointmentSerializer.to_representation()
    wait_time = RelativedeltaField(required=False, read_only=True)
    long_wait_time = serializers.BooleanField(required=False, read_only=True)
    wait_type = serializers.CharField(required=False, read_only=True)
    gap_time = RelativedeltaField(required=False, read_only=True)
    service_promotion = serializers.SerializerMethodField()
    surcharge = serializers.SerializerMethodField()
    _version = serializers.IntegerField(
        read_only=True,
        required=False,
        source='appointment._version',
    )
    # injected by matcher:
    _availability = serializers.DictField(read_only=True)

    def get_fields(self):
        fields = super().get_fields()
        if not EnablePeakHoursFlag():
            fields.pop('surcharge')

        return fields

    def to_representation(self, instance):
        data = super().to_representation(instance)

        if not data['service_promotion']:
            return data

        service_variant = get_attribute(instance, ['service_variant'])
        omnibus_price = self.root.instance.omnibus_prices.get(service_variant.id)

        omnibus_price = omnibus_price or service_variant.price
        data['omnibus_price'] = format_currency(omnibus_price) if omnibus_price else None

        return data


class AttrDictField(serializers.DictField):
    def to_representation(self, value):
        return value.to_dict()


class TravelingToClientInCustomerAppointmentSerializer(
    TravelingToClientsESSerializer,
):
    def get_fields(self):
        fields = super().get_fields()
        fields.pop('location', None)
        fields.pop('area', None)
        return fields


class ImageFromContextSerializer(
    BaseThumbnailsMixin,
    serializers.ModelSerializer,
):
    image_id = serializers.IntegerField(source='id')
    image = serializers.CharField(source='full_url')
    inspiration_categories = serializers.PrimaryKeyRelatedField(
        many=True,
        read_only=True,
    )

    class Meta:
        model = Image
        fields = [
            'image_id',
            'business_id',
            'tags',
            'staffers',
            'description',
            'image',
            'height',
            'width',
            'category',
            'is_cover_photo',
            'inspiration_categories',
            'order',
            'visible',
            'active',
            'created',
        ]
        extra_kwargs = {
            'created': {'format': settings.ES_DATETIME_FORMAT},
        }

    def get_thumbnails(self, data):
        image_url = data.get('image')
        category = data.get('category')
        if not (image_url and category):
            return {}
        thumbnails = IMAGES_CATEGORIES[category]['thumbnails']
        return self.get_urls(thumbnails, image_url)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if not self.context.get('no_thumbs', False):
            data['thumbnails'] = self.get_thumbnails(data)
        return data


class SingleImageFromContextSerializer(serializers.Serializer):
    cover = serializers.SerializerMethodField()
    cover_orig = serializers.SerializerMethodField()
    logo = serializers.SerializerMethodField()

    def _get_images_representation(self, constraint, obj):
        images = ImageFromContextSerializer(
            filter(constraint, obj.images.all()),
            many=True,
            context={'no_thumbs': self.context.get('no_thumbs', False)},
        ).data
        return images

    def get_cover(self, obj):
        images = self._get_images_representation(
            constraint=lambda i: i.is_cover_photo,
            obj=obj,
        )
        return images

    def get_cover_orig(self, obj):
        images = self._get_images_representation(
            constraint=lambda i: (i.category == ImageTypeEnum.COVER_ORIGINAL_DEPRECATED),
            obj=obj,
        )
        return images

    def get_logo(self, obj):
        images = self._get_images_representation(
            constraint=lambda i: i.category == ImageTypeEnum.LOGO,
            obj=obj,
        )
        return images

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['biz_photo_count'] = (
            None  # this was many type image, not exist in inner_types query, always None
        )
        ret['biz_photo_per_page'] = 20
        return ret


class BusinessInCustomerAppointmentContextSerializer(
    serializers.ModelSerializer,
):
    thumbnail_photo = serializers.SerializerMethodField()
    images = SingleImageFromContextSerializer(source='*')
    location = BusinessLocationSerializer(source='*')
    reviews_rank = serializers.FloatField(source='reviews_rank_avg')
    url = serializers.CharField(source='get_marketplace_sitemap_url')
    traveling = TravelingToClientInCustomerAppointmentSerializer()
    partners = serializers.SerializerMethodField()

    class Meta:
        model = Business
        fields = [
            'id',
            'name',
            'slug',
            'phone',
            'thumbnail_photo',
            'images',
            'location',
            'reviews_count',
            'reviews_stars',
            'reviews_rank',
            'is_b_listing',
            'url',
            'active',
            'visible',
            'primary_category',
            'traveling',
            'partners',
        ]

    @staticmethod
    def get_thumbnail_photo(instance):
        url = instance.get_photo(ImageTypeEnum.LOGO) or ''
        if not url:
            return None
        return urljoin(settings.MEDIA_URL, url.lstrip('/'))

    @staticmethod
    def get_partners(instance):
        return get_partner_names_from_business(instance)

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['location'] = self._convert_location(ret['location'])
        # In BusinessWithSingleImagesSearchable is not declared
        # _script_fields -> distance always None
        ret['distance'] = None
        ret['service_categories'] = []
        ret['staff'] = []
        ret['reviews'] = []
        return ret

    @staticmethod
    def _convert_location(value):
        """convert lat/lon -> latitude/longitude"""
        if not value:
            return value

        coordinate = value.get('coordinate')
        if not coordinate:
            return value

        value['coordinate'] = {
            'latitude': coordinate['lat'],
            'longitude': coordinate['lon'],
        }
        return value


class BusinessInCustomerAppointmentSerializer(serializers.Serializer):
    def to_representation(self, instance):
        resp = (
            BusinessWithSingleImagesSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessWithSingleImagesHitSerializer,
            )
            .params(
                no_thumbs=self.context.get('no_thumbs', False),
            )
            .execute(
                {
                    'id': instance.id,
                }
            )
        )
        if resp.hits.total.value:
            return resp[0].to_dict()
        # fallback for not indexed business
        return business_serializers.BusinessInCustomerBookingSerializer(
            instance=instance,
        ).data


class BusinessCustomerAgreementsField(serializers.JSONField):
    def __init__(self, **kwargs):
        kwargs['required'] = False
        super().__init__(**kwargs)

    def get_attribute(self, instance):
        """Return list of agreement dicts business customer:
        Present agreements:
            - 'web_communication_agreement',
        :param instance: Appointment
        :return: list
        """
        bci = instance.booked_for
        return list_agreements_business_customer(bci, instance.business)


class CustomerAppointmentSerializer(BaseAppointmentSerializer):
    """An unified serializer for Appointments in Customer API."""

    WHO_MAKES_CHANGE = WhoMakesChange.CUSTOMER

    _version = serializers.IntegerField(
        allow_null=True,
        required=False,
        validators=(validate_appointment_version,),
    )
    customer_note = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        max_length=consts.CUSTOMER_NOTE__MAX_LENGTH,
        required=False,
    )
    subbookings = CustomerSubbookingSerializer(
        allow_empty=False,
        many=True,
        required=True,
    )

    # read-only fields
    actions = serializers.SerializerMethodField()
    appointment_id = serializers.IntegerField(read_only=True)
    appointment_type = serializers.CharField(read_only=True)
    ask_for_consent = serializers.BooleanField(
        allow_null=True,
        required=False,
        source='booked_for.ask_for_consent',
    )
    bci_agreements = BusinessCustomerAgreementsField()
    booked_from = BookingDateTimeField(read_only=True)  # based on subbookings
    booked_till = BookingDateTimeField(read_only=True)  # based on subbookings
    business = BusinessInCustomerAppointmentContextSerializer(read_only=True)
    business_note = serializers.CharField(read_only=True)
    can_add_review = serializers.SerializerMethodField()
    customer_info = ShortCustomerInfoSerializer(
        read_only=True,
        source='booked_for',
    )
    external_source = serializers.CharField(
        read_only=True,
        required=False,
    )
    join_meeting_url = serializers.CharField(read_only=True)
    payment_info = CustomerBookingPaymentInfoField()
    repeating = AppointmentRepeatingInfoSerializer(
        read_only=True,
        source='repeating.repeating_info',
    )
    repeating_series = AppointmentRepeatingInfoSerializer(
        read_only=True,
        source='repeating.repeating_series_info',
    )
    rwg_token = serializers.CharField(required=False)
    merchant_changed = serializers.ChoiceField(
        required=False,
        choices=(MerchantChanged.CHANGED, MerchantChanged.NOT_CHANGED),
    )
    service_questions = ServiceQuestionsWithAnswersField(required=False)
    status = serializers.ChoiceField(
        choices=AppointmentStatusChoices,
        read_only=True,
    )
    total = serializers.CharField(
        read_only=True,
        source='total.price_only',
    )
    total_discount_amount = serializers.FloatField(
        read_only=True,
        source='total.discount',
    )
    total_tax_excluded = serializers.FloatField(
        read_only=True,
        source='checkout.total_tax_excluded',
    )
    total_value = serializers.FloatField(
        read_only=True,
        source='total.value',
    )
    type = serializers.ChoiceField(
        choices=Appointment.TYPE_CHOICES,
        read_only=True,
    )

    # write-only fields
    _from_subdomain = serializers.BooleanField(
        default=False,
        required=False,
        write_only=True,
    )
    _preserve_order = serializers.BooleanField(
        default=False,
        required=False,
        write_only=True,
    )
    book_for_family_member = FamilyAndFriendsBookForSerializer(
        allow_null=True,
        required=False,
        write_only=True,
    )
    currency = serializers.SerializerMethodField()
    dry_run = serializers.BooleanField(
        required=True,
        write_only=True,
    )
    family_and_friends = serializers.SerializerMethodField(read_only=True)
    # force_incomplete has meaning only with dry_run=True
    # Setting this field to true will result in including
    # invalid subbookings in the response.
    force_incomplete = serializers.BooleanField(
        required=False,
        write_only=True,
        default=False,
    )
    recurring = serializers.BooleanField(
        write_only=True,
        required=False,
        allow_null=True,
    )  # can be changed to a required BooleanField - based on dry_run & bci

    CONTROL_FIELDS = (
        '_version',
        'recurring',
        'dry_run',
        '_preserve_order',
        '_from_subdomain',
        '_update_future_bookings',
    )

    default_error_messages = {
        'prepaid_edit_error': gettext_lazy(
            'Only time and note can be edited for prepaid appointments',
        ),
    }

    @staticmethod
    def get_currency(*_) -> str:
        return settings.CURRENCY_CODE

    def get_fields(self):
        """Modify some fields based on circumstances.

        * Make recurring required if bci.client_type==CLIENT_TYPE__UNKNOWN
          and not dry_run
        """
        fields = super().get_fields()
        fields.pop('_update_future_bookings', None)

        if not self.context['dry_run']:
            user = self.context['user']
            # this serializer is used also in handlers with optional_login e.g. BookAgainHandler
            bci = (
                user
                and user.business_customer_infos.filter(
                    business__id=self.context['business'].id,
                ).first()
            )
            if not bci or bci.client_type == BusinessCustomerInfo.CLIENT_TYPE__UNKNOWN:
                fields['recurring'] = serializers.BooleanField(
                    write_only=True,
                    required=True,
                )
            fields['book_for_family_member'].context.update(self.context)
        else:
            fields['bci_agreements'] = BusinessCustomerAgreementsField(
                allow_null=True,
            )
        return fields

    # REPRESENTATION

    def get_actions(self, appointment):
        """Get allowed actions on booking by customer."""
        if self.context['dry_run']:
            return {
                'accept': False,
                'cancel': False,
                'change': True,
            }

        appt = appointment.appointment
        possible_states = appt.get_possible_statuses(who_makes_change=WhoMakesChange.CUSTOMER) - {
            appt.status
        }
        actions = {
            'accept': (
                Appointment.STATUS.ACCEPTED in possible_states
                and appt.status != Appointment.STATUS.PENDING_PAYMENT
            ),
            'cancel': all(
                appt.is_active() and not subbooking.has_ended()
                for subbooking in appointment.subbookings
            ),
            'change': appt.can_customer_change(self.is_customer),
            'pay': (
                appt.status == Appointment.STATUS.PENDING_PAYMENT
                or (
                    BooksyPayCompatibility(self.context.get('request'))
                    and appt.is_booksy_pay_available is True
                    and appt.is_booksy_pay_payment_window_open is True
                )
            ),
        }

        if actions[BookingAction.CHANGE]:
            if self.is_customer and appt.is_prepaid and not appt.has_resolved_service_promotion:
                actions[BookingAction.CHANGE_TIME_OR_NOTE] = True

        return actions

    def get_can_add_review(self, appointment: Union[AppointmentWrapper, OrderedDict]) -> bool:
        """Marks if the provided appointment can be reviewed.

        Only booking that is not older than 30 days and has been performed as
        last, and has no review, can be reviewed.
        """
        from webapps.reviews.models import Review

        # we use safe_get because instance is not always AppointmentWrapper
        # see rest_framework/serializers.py:512
        appointment_id = safe_get(appointment, ['appointment_uid']) or safe_get(
            appointment, ['appointment_id']
        )
        latest_id = None
        try:
            latest_subbooking = (
                SubBooking.objects.filter(appointment_id=appointment_id)
                .values_list('id', 'booked_till', named=True)
                .latest('booked_from', 'id')
            )
            if latest_subbooking.booked_till <= (tznow() - settings.CAN_ADD_REVIEW_LIMIT_PERIOD):
                return False
            review_exists = Review.objects.filter(
                # for multibooking review should be assigned to last booking
                subbooking_id=latest_subbooking.id
            ).exists()
        except SubBooking.DoesNotExist:
            review_exists = False
        status = safe_get(appointment, ['status'])
        business = safe_get(appointment, ['business'])
        precondition_to_review = (
            not review_exists
            and status == Appointment.STATUS.FINISHED
            and business.active
            and business.visible
        )
        if not family_and_friends_can_add_review(appointment, self.context, latest_id):
            return False
        if precondition_to_review:
            # No following booking exists
            following_booking = SubBooking.objects.filter(
                booked_till__gt=safe_get(appointment, ['booked_till']),
                appointment__booked_for_id=safe_get(appointment, ['booked_for_id']),
                appointment__business_id=business.id,
                appointment__status=Appointment.STATUS.FINISHED,
            )
            if latest_id is not None:
                following_booking = following_booking.exclude(id=latest_id)
            return not following_booking.exists()
        return False  # review_can_be_added == False

    def get_family_and_friends(self, appointment_wrapper: AppointmentWrapper) -> Optional[dict]:
        appointment = appointment_wrapper.appointment

        member = None
        context = {}
        if hasattr(self, '_validated_data'):
            member = self._get_member(self.validated_data)
            context['member'] = member

        if not appointment.is_family_and_friends and not member:
            return
        if not member and self.context.get('dry_run'):
            return

        if appointment.is_family_and_friends and not self.context.get('dry_run'):
            member_appointment = appointment.member_appointment
        else:
            member_appointment = get_family_and_friends_for_dry_run_appointment_serializer(
                member, self.context
            )
            context['parent'] = safe_get(
                self.context, ['user', 'customer_profile', 'member_profile']
            )

        return MemberAppointmentSerializer(member_appointment, context=context).data

    @property
    def is_customer(self) -> bool:
        if booking_source := self.context.get('booking_source'):
            return booking_source.app_type == BookingSources.CUSTOMER_APP
        return False

    def _validate_changes_for_existing_prepayment(self, data: dict):
        if not self.is_prepaid_appointment:
            return

        existing_subbookings = list(iter_leaf_services(self.instance.subbookings))
        proposed_subbookings = list(iter_leaf_services(data['subbookings']))

        disallowed_changes = [
            self._subbookings_count_changed(
                existing_subbookings,
                proposed_subbookings,
            ),
            self._variants_have_changed(
                existing_subbookings,
                proposed_subbookings,
            ),
            self._staffers_have_changed(
                existing_subbookings,
                proposed_subbookings,
            ),
        ]
        if any(disallowed_changes):
            self.fail('prepaid_edit_error')

    @property
    def is_prepaid_appointment(self) -> Optional[bool]:
        return self.instance and self.instance.appointment.is_prepaid

    # VALIDATION

    def validate(self, attrs):
        attrs = self._validate_force_incomplete(attrs)
        attrs = self._validate_bci(attrs)
        self._validate_changes_for_existing_prepayment(attrs)

        attrs['subbookings'] = customer_subbookings_make(
            attrs['subbookings'],
            dry_run=self.context['dry_run'],
            business=self.context['business'],
        )
        validate_not_supported_family_and_friends_attrs(attrs, self.context)
        attrs = super().validate(attrs)

        return attrs

    @staticmethod
    def _validate_force_incomplete(attrs):
        if not attrs.get('dry_run', False) and attrs.get('force_incomplete', False):
            # do not raise any error just ignore force incomplete
            attrs['force_incomplete'] = False
        return attrs

    def _validate_bci(self, attrs):
        user = self.context.get('user')
        # get_or_prepare_unsaved_instance :)
        bci = user and (
            # try to get existing client
            BusinessCustomerInfo.objects.filter(
                user_id=user.id,
                business_id=self.context['business'].id,
            ).first()
            or
            # prepare unsaved instance as a placeholder for later
            BusinessCustomerInfo(
                business=self.context['business'],
                user=user,
            )
        )
        attrs['booked_for'] = bci if bci and bci.id else None
        if bci:
            customer_data = bci.as_customer_data()
            attrs['customer_name'] = customer_data.full_name
            attrs['customer_email'] = customer_data.email
            attrs['customer_phone'] = customer_data.cell_phone
        else:
            attrs['customer_name'] = None
            attrs['customer_email'] = None
            attrs['customer_phone'] = None

        if bci and bci.blacklisted:
            raise serializers.ValidationError(_("Requested service is no longer available."))

        validate_existing_customers_only(
            self.context['business'], self.context.get('user'), bci, self._get_member(attrs)
        )

        return attrs

    def _calculate_status(self, attrs):
        service = firstof(
            (
                booking['service_variant'].service
                for booking in attrs['subbookings']
                if booking['service_variant'] is not None
            )
        )
        return SubBooking.get_status_for_customer_booking(
            self.instance,
            self.context['business'],
            service=service,
        )

    @staticmethod
    def _get_member(attrs):
        if attrs.get('book_for_family_member'):
            return attrs.get('book_for_family_member').get('member')
        return None

    @staticmethod
    def _subbookings_count_changed(
        existing_subbookings: SubbookingsList,
        proposed_subbookings: ProposedSubbookingsList,
    ) -> bool:
        return len(existing_subbookings) != len(proposed_subbookings)

    @staticmethod
    def _variants_have_changed(
        existing_subbookings: SubbookingsList,
        proposed_subbookings: ProposedSubbookingsList,
    ) -> bool:
        existing_variant_ids = {
            safe_get(sbk, ['service_variant', 'id']) for sbk in existing_subbookings
        }

        proposed_variant_ids = {
            safe_get(sbk, ['service_variant', 'id']) for sbk in proposed_subbookings
        }

        return existing_variant_ids != proposed_variant_ids

    @staticmethod
    def _staffers_have_changed(
        existing_subbookings: SubbookingsList,
        proposed_subbookings: ProposedSubbookingsList,
    ) -> bool:
        existing_staffer_ids = {safe_get(sbk, ['staffer', 'id']) for sbk in existing_subbookings}
        proposed_staffer_ids = {safe_get(sbk, ['staffer', 'id']) for sbk in proposed_subbookings}

        return existing_staffer_ids != proposed_staffer_ids

    def create(self, validated_data: dict) -> AppointmentWrapper:
        family_and_friends_factory.make_bci(validated_data, self.context)
        appointment_wrapper = super().create(validated_data)
        family_and_friends_factory.set_proper_customer_for_appointment(
            validated_data, self.context, appointment_wrapper
        )
        return appointment_wrapper

    def update(self, instance, validated_data: dict) -> AppointmentWrapper:
        family_and_friends_factory.make_bci(validated_data, self.context)
        appointment_wrapper = super().update(instance, validated_data)
        family_and_friends_factory.set_proper_customer_for_appointment(
            validated_data, self.context, appointment_wrapper
        )
        return appointment_wrapper


class AnalyticsCustomerAppointmentSerializer(
    GTMAppointmentAnalyticsMixin, CustomerAppointmentSerializer
):
    pass


class CreateAnalyticsCustomerAppointmentSerializer(AnalyticsCustomerAppointmentSerializer):
    show_medical_consent = serializers.SerializerMethodField(read_only=True)

    def get_show_medical_consent(self, _):
        if self.context.get('is_medical_consent_required', False) and ShowBooksyMedConsentsFlag(
            UserData(subject_key=self.context.get('business').id)
        ):
            return True
        return False

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if (
            not self.context['dry_run']
            and self.context.get('is_medical_consent_required', False)
            and RequireBooksyMedConsentsFlag()
        ):
            raise serializers.ValidationError(
                _("Please update your application and accept the medical consents.")
            )
        return attrs


class SubbookingSignalSerializer(serializers.Serializer):
    id = serializers.IntegerField()


class AppointmentSignalSerializer(serializers.Serializer):
    booked_for_id = serializers.IntegerField()
    business_id = serializers.IntegerField(source='business.id')
    status = serializers.CharField()
    subbookings = SubbookingSignalSerializer(many=True)


class ShortAppointmentSerializer(serializers.Serializer):
    booked_from = BookingDateTimeField(read_only=True)


#########################################################
# ################### PARTNER API ##################### #
#########################################################


class PartnerSubbookingSerializer(SubbookingSerializer):
    class Meta:
        validators = CustomerSubbookingSerializer.Meta.validators

    booked_from = BookingDateTimeField(required=True, allow_null=False)
    booked_till = BookingDateTimeField(required=True, allow_null=False)
    service_variant = SubbookingServiceVariantSerializer(
        required=True,
        disable_modes=[
            None,
            SVMode.NO_VARIANT,
        ],  # only 'variant' mode is available
    )


class PartnerCustomerAppointmentSerializer(BaseAppointmentSerializer):
    WHO_MAKES_CHANGE = WhoMakesChange.CUSTOMER

    subbookings = PartnerSubbookingSerializer(required=True, many=True)
    customer_name = serializers.CharField(required=False, allow_blank=True)
    customer_email = serializers.CharField(required=False, allow_blank=True)
    customer_phone = serializers.CharField(required=False, allow_blank=True)
    customer_note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=consts.CUSTOMER_NOTE__MAX_LENGTH,
    )

    # read-only
    _version = serializers.IntegerField(
        required=False, allow_null=True, validators=(validate_appointment_version,)
    )
    appointment_id = serializers.IntegerField(read_only=True)
    appointment_type = serializers.CharField(read_only=True)
    booked_from = BookingDateTimeField(read_only=True)  # based on subbookings
    booked_till = BookingDateTimeField(read_only=True)  # based on subbookings
    status = serializers.ChoiceField(
        choices=AppointmentStatusChoices,
        read_only=True,
    )
    type = serializers.ChoiceField(
        choices=Appointment.TYPE_CHOICES,
        read_only=True,
    )
    # write-only
    dry_run = serializers.BooleanField(required=True, write_only=True)

    CONTROL_FIELDS = (
        '_version',
        'dry_run',
    )

    def get_fields(self):
        fields = super().get_fields()
        fields.pop('_update_future_bookings', None)

        return fields

    def validate(self, attrs):
        inject_autoassign(attrs['subbookings'])
        clear_wrong_any_resource(attrs['subbookings'])
        inject_requested_staffer(attrs['subbookings'])
        return super().validate(attrs)

    def create(self, validated_data: dict) -> AppointmentWrapper:
        # handle new / updated / deleted subbookings
        subbookings, to_delete = self.handle_subbookings(validated_data)
        appointment_wrapper = AppointmentWrapper(subbookings=subbookings)
        appointment_wrapper.add_strategy('customer_data', PartnerAppointmentCustomerDataStrategy())
        appointment_wrapper.add_strategy(
            'client_data',
            PartnerAppointmentClientDataStrategy(client_type=self.context['client_type']),
        )
        appointment_wrapper.save(
            to_delete=to_delete,
            overbooking=validated_data.get('overbooking', False),
            who_makes_change=self.WHO_MAKES_CHANGE,
            dry_run=validated_data['dry_run'],
            update_future=validated_data.get('_update_future_bookings', False),
            validate_as=self.VALIDATE_AS,
            prev_status=None,
        )
        return appointment_wrapper

    def _calculate_status(self, attrs):
        service = firstof(
            (
                booking['service_variant'].service
                for booking in attrs['subbookings']
                if booking['service_variant'] is not None
            )
        )
        return SubBooking.get_status_for_customer_booking(
            self.instance,
            self.context['business'],
            service=service,
        )
