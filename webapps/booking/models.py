# pylint: disable=too-many-lines
# pylint: disable=cyclic-import
import logging
import operator
import typing as t
import uuid
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from functools import reduce
from dateutil.relativedelta import relativedelta
from dateutil.tz import gettz
from dirtyfields import DirtyFieldsMixin
from dirtyfields.dirtyfields import reset_state
from django.conf import settings
from django.contrib.postgres.fields import Array<PERSON>ield
from django.contrib.postgres.fields.ranges import DateTimeRangeField
from django.core.exceptions import ValidationError
from django.db import DEFAULT_DB_ALIAS, IntegrityError, models, transaction
from django.db.models import (
    SET_NULL,
    Case,
    Count,
    DecimalField,
    Exists,
    ExpressionWrapper,
    F,
    IntegerField,
    Max,
    Min,
    OuterRef,
    Prefetch,
    Q,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.constraints import UniqueConstraint
from django.db.models.expressions import RawSQL
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import Cast
from django.utils.functional import cached_property
from django.utils.text import format_lazy
from django.utils.translation import get_language
from django.utils.translation import gettext_lazy as _

import versions
from lib import safe_json, tools
from lib.booksy_sms.utils import parse_phone_number
from lib.cache import lru_booksy_cache
from lib.db import PRIMARY_DB, using_db_for_reads, READ_ONLY_DB
from lib.enums import AppleService
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature import (
    LoyaltyProgramFlag,
)
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayAvailabilityOnBookingFlag,
)
from lib.feature_flag.feature.monetisation import EnablePeakHoursFlag
from lib.fields.dataclass_field import DataClassField
from lib.fields.phone_number import BooksyPhoneNumberField
from lib.interval.fields import IntervalField
from lib.models import (
    ArchiveManager,
    ArchiveModel,
    ImportanceAggregate,
    SoftDeleteManager,
    SoftDeleteQuerySet,
    TimestampedModel,
    UndeletableModel,
    VersionedModel,
)
from lib.queryset import CustomPrefetchQuerySet
from lib.time_24_hour import format_datetime
from lib.tools import (
    duration_formatter,
    firstof,
    format_currency,
    get_meta_data_from_handler,
    sget_v2,
    tznow,
)
from lib.validators import validate_latitude, validate_longitude
from webapps import consts
from webapps.booking.boost_querysets import AppointmentsFromBoostPerspectiveQuerySet
from webapps.booking.enums import (
    ALLOWED_TRANSITIONS,
    REPEAT_DELTAS,
    REPEATED_PHRASES_SHORT,
    AppointmentStatus,
    AppointmentStatusChoices,
    AppointmentType,
    AppointmentTypeChoices,
    BookingMode,
    BookingRangesErrors,
    RepeatEndType,
    RepeatType,
    UpdateFutureBooking,
)
from webapps.booking.enums import WhoMakesChange as Who
from webapps.booking.events import (
    appointment_changed_event,
    repeated_appointment_changed_event,
    repeated_appointment_expanded_signal,
)
from webapps.booking.exceptions import BookingConflict
from webapps.booking.factory.addons import addon_use_make
from webapps.booking.factory.service_questions import QuestionsAndAnswersList
from webapps.booking.messages.appointment import AppointmentChangedMessage
from webapps.booking.repeating_info import RepeatingInfo
from webapps.booking.service_data import SubBookingServiceData, merge_service_data
from webapps.booking.tools.tools import (
    calculate_repeating_start_end,
    iter_leaf_services,
)
from webapps.boost.enums import BoostClientCardStatus
from webapps.business.context import business_context
from webapps.business.enums import PriceType
from webapps.business.models import (
    SERVICE_PROMOTION_TYPES,
    SERVICE_VARIANT_CLIENT_DISCOUNT,
    Business,
    BusinessVersion,
    Resource,
    Service,
    ServiceAddOnUse,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.resources import AnyResource
from webapps.business.service_price import ServicePrice
from webapps.consts import (
    FRONTDESK_SOURCES,
    MIGRATED_PARTNER,
    PERFORMANCE_TEST,
    SALON_APPS,
)
from webapps.feeds.enums import EventType
from webapps.feeds.utils import update_to_external_partners
from webapps.notification.scenarios import BookingChangedScenario, start_scenario
from webapps.notification.scenarios.scenarios_booking_mixin import BookingMixin
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.enums.receipt_status import (
    PREPAYMENT_AUTHORISATION_SUCCESS,
    PREPAYMENT_SUCCESS,
)
from webapps.premium_services.public import SubBookingSurchargeData, SubBookingSurchargeRepository
from webapps.public_partners.models import (
    AppointmentMetadata,
    BusinessCustomerInfoMetadata,
    PartnerPermissionBusiness,
    ResourceMetadata,
    ServiceMetadata,
    SubbookingMetadata,
)
from webapps.public_partners.query_sets import PartnerAppDataQuerySet
from webapps.statistics.tasks import maybe_aggregate_statistics
from webapps.user.tools import get_system_user

_bk_change_log = logging.getLogger('booksy.booking_change')

RESOLVED_BOOKING_PROMOTION_TYPES = SERVICE_PROMOTION_TYPES + (
    (SERVICE_VARIANT_CLIENT_DISCOUNT, 'Client Discount'),
)


@lru_booksy_cache(timeout=3600, skip_in_pytest=True)
@using_db_for_reads(PRIMARY_DB)
def _get_booking_source(api_key, app_type, name):
    kwargs = {
        key: value
        for key, value in {
            'api_key': api_key,
            'app_type': app_type,
            'name': name,
        }.items()
        if value is not None
    }

    try:
        return BookingSources.objects.get(**kwargs)
    except (BookingSources.DoesNotExist, BookingSources.MultipleObjectsReturned):
        return None


class BookingSources(ArchiveModel):
    BUSINESS_APP = 'B'
    CUSTOMER_APP = 'C'
    UNIVERSAL_APP = 'U'
    INTERNAL_APP = 'I'
    PUBLIC_API_APP = 'P'
    PARTNER_MERGER_APP = 'M'
    ADMIN_APP = 'A'
    APP_TYPES = (
        (BUSINESS_APP, _('Business')),
        (CUSTOMER_APP, _('Customer')),
        (UNIVERSAL_APP, _('Universal')),
        (INTERNAL_APP, _('Internal')),
        (PUBLIC_API_APP, _('Public API')),
        (PARTNER_MERGER_APP, _('Partner Merger')),
        (ADMIN_APP, _('Admin API')),
    )

    id = models.AutoField(primary_key=True, db_column='booking_source_id')
    name = models.CharField(max_length=30, null=False, blank=False, choices=consts.SOURCE_NAMES)
    api_key = models.TextField(null=False, blank=False, unique=True)
    app_type = models.CharField(max_length=1, choices=APP_TYPES, null=False, blank=False)

    chargeable = models.BooleanField(default=False)

    objects = ArchiveManager()

    class Meta:
        unique_together = [('name', 'app_type')]

    def __str__(self):
        return f'{self.name} {self.get_app_type_display()}'

    @staticmethod
    def get_cached(api_key=None, app_type=None, name=None):
        return _get_booking_source(api_key, app_type, name)

    @classmethod
    def get_fallback(cls):
        """Get Web Business source as a fallback in old emails."""
        return cls.get_cached(app_type=cls.BUSINESS_APP, name='Web')

    @staticmethod
    def guess_from_path(path):
        """Guess BookingSource from given path.

        DEPRECATED: this is madness, do not use!

        """
        if path.startswith(f'/api/{settings.API_COUNTRY}/'):
            # remove country code and api version
            path = path.split('/', 4)[-1]
        if path.startswith('/'):
            path = path[1:]
        if path.startswith('country-'):
            # remove country code
            path = path.split('/', 1)[1]

        if path.startswith('customer_api') or path.startswith('business_api'):
            return None

        bsparams = path.split('/')[0]
        app_type = bsparams[0]  # ignore deprecated theme
        return BookingSources.objects.filter(app_type__iexact=app_type).first()

    def generate_path(self, path):
        return f'/api/{settings.API_COUNTRY}/{versions.VERSION}/{self.app_type.lower()}{path}'

    def get_codename(self):
        return self.name + {'B': 'BIZ', 'C': 'CUS', 'I': 'INT'}[self.app_type]

    @property
    def is_performance_test(self):
        return self.name == PERFORMANCE_TEST and not settings.LIVE_DEPLOYMENT

    def is_salon_app(self):
        return self.name in FRONTDESK_SOURCES or self.name in SALON_APPS

    def get_apple_service_id(self) -> t.Optional[str]:
        if self.app_type == BookingSources.BUSINESS_APP:
            if self.name == consts.FRONTDESK:
                return AppleService.BIZ_SALON_WEB

            return AppleService.BIZ_SALON if self.is_salon_app() else AppleService.BIZ

        if self.app_type == BookingSources.CUSTOMER_APP:
            if self.name == consts.WEB:
                return AppleService.BIZ_SALON_WEB

            return AppleService.CUST


class SubBookingServiceDataField(models.JSONField):
    def __init__(self, **kwargs):
        kwargs['blank'] = True

        super().__init__(**kwargs)

    def from_db_value(self, value, expression, connection):  # pylint: disable=unused-argument
        if value is None:
            return value

        value = super().from_db_value(value, expression, connection)

        return SubBookingServiceData.deserialize(value)

    def to_python(self, value):
        if isinstance(value, SubBookingServiceData):
            return value

        data = super().to_python(value)
        try:
            return SubBookingServiceData.deserialize(data or {})
        except (ValueError, TypeError) as error:
            raise ValidationError from error

    def get_prep_value(self, value):
        if isinstance(value, SubBookingServiceData):
            value = value.serialize()
        return super().get_prep_value(value)


class SubBookingQuerySet(
    SoftDeleteQuerySet,
    CustomPrefetchQuerySet,
    PartnerAppDataQuerySet,
):
    """A QuerySet for Bookings that can prefetch custom additional data.

    Enabled features:
     - payments and deposits (BookingPaymentInfoField in POS)
     - customer booking counts (CustomerReadOnlyField)
     - annotate staffer

    """

    bookings_order = ('booked_from', 'id')

    @staticmethod
    def ended_bookings():
        return (
            SubBooking.objects.filter(
                appointment__status__in=[
                    Appointment.STATUS.FINISHED,
                    Appointment.STATUS.NOSHOW,
                ],
            )
            .order_by('-booked_till')
            .only(
                'id',
                'booked_from',
                'booked_till',
            )
        )

    def active(self):
        return (
            self.filter(
                deleted__isnull=True,
            )
            .annotate_has_addons()
            .order_by(*self.bookings_order)
        )

    def annotate_staffer_id(self):
        """Annotate a 'staffer_id' field to simplify statistics."""
        return self.annotate(
            staffer_id=RawSQL(  # nosemgrep: avoid-raw-sql
                """(
                    SELECT bbr.resource_id
                    FROM booking_bookingresource bbr
                    JOIN business_resource br USING (resource_id)
                    WHERE
                        br.type=%s AND
                        bbr.subbooking_id = booking_subbooking.subbooking_id
                    LIMIT 1
                )""",
                (Resource.STAFF,),
            )
        )

    def annotate_is_chargeable_for_analytics(self):
        # TODO When method is_first_booking() is moved to BCI interface this should be removed
        chargeable_statuses = {
            Appointment.STATUS.FINISHED,
            *Appointment.STATUSES_OCCUPYING_TIME_SLOTS,
            Appointment.STATUS.NOSHOW,
        }
        return self.annotate(
            is_chargeable_for_analytics=Case(
                When(
                    appointment__booked_for_id__isnull=False,
                    appointment__type=Appointment.TYPE.CUSTOMER,
                    appointment__status__in=chargeable_statuses,
                    appointment__source__chargeable__isnull=False,
                    then=Exists(
                        Appointment.objects.filter(
                            booked_for__user_id=OuterRef('appointment__booked_for__user_id'),
                            booked_for__client_type=(
                                BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
                            ),
                            bci_as_first_appointment__id=OuterRef('appointment_id'),
                            bci_as_first_appointment__client_type=(
                                BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
                            ),
                            type=Appointment.TYPE.CUSTOMER,
                            id=OuterRef('appointment_id'),
                        )
                    ),
                ),
                default=None,
                output_field=models.BooleanField(),
            )
        )

    def annotate_service_name_replication(self):
        """Change to `annotate_service_name` after Subbooking.service_name field deprecation."""
        return self.annotate(
            service_name_replication=KeyTextTransform(
                'service_name',
                'service_data_internal',
                output_field=models.TextField(),
            ),
        )

    def annotate_service_variant_duration(self):
        return self.annotate(
            service_variant_duration=ExpressionWrapper(
                Cast(
                    KeyTextTransform('service_variant_duration', 'service_data_internal'),
                    IntegerField(),
                )
                * RawSQL('\'1 minute\'::interval', []),  # nosemgrep
                output_field=IntervalField(),
            ),
        )

    def annotate_service_variant_label(self):
        return self.annotate(
            service_variant_label=KeyTextTransform(
                'service_variant_label',
                'service_data_internal',
                output_field=models.TextField(),
            ),
        )

    def annotate_has_addons(self):
        return self.annotate(
            has_addons=Exists(ServiceAddOnUse.objects.filter(subbooking=OuterRef('pk'))),
        )

    def annotate_service_variant_price(self):
        return self.annotate(
            service_variant_price=Cast(
                KeyTextTransform('service_variant_price', 'service_data_internal'),
                DecimalField(max_digits=10, decimal_places=2),
            ),
        )

    def annotate_duration(self):
        return self.annotate(
            duration=F('booked_till') - F('booked_from'),
        )

    def prefetch_all(self, for_listing=False):
        """Prefetch all important stuff."""

        from webapps.pos.models import POS
        from webapps.user.models import UserProfile

        extra_select_related = (
            []
            if for_listing
            else (
                'service_variant__payment',
                'service_variant__service__treatment',
                'appointment__repeating',
                'resolved_promotion',
            )
        )
        extra_prefetch_related = (
            []
            if for_listing
            else (
                Prefetch(
                    'appointment__business',
                    queryset=Business.objects.prefetch_related(
                        Prefetch(
                            'partnerpermissionbusiness_set',
                            queryset=(
                                PartnerPermissionBusiness.objects.select_related('partner').only(
                                    'name'
                                )
                            ),
                        ),
                    ),
                ),
                Prefetch(
                    'service_variant__service__business',
                    queryset=Business.objects.prefetch_related(
                        Prefetch(
                            'pos_set',
                            queryset=POS.objects.filter(active=True),
                        ),
                    ),
                ),
                Prefetch(
                    'appointment__booked_for',
                    queryset=BusinessCustomerInfo.objects.select_related(
                        'user',
                    )
                    .prefetch_related(
                        'bookmarked_resources',
                        Prefetch(
                            'bookings',
                            queryset=SubBooking.objects.ended_bookings(),
                            to_attr='ended_bookings',
                        ),
                    )
                    .with_partner_app_data(BusinessCustomerInfoMetadata),
                ),
            )
        )

        result_qs = (
            self.select_related(  # pylint: disable=not-callable
                'service_variant__service',
                'service_variant__service__service_category',
                'appointment__booked_for',
                'appointment',
                'appointment__business',
                *extra_select_related,
            )
            .prefetch_related(
                *extra_prefetch_related,
                Prefetch(
                    'resources',
                    queryset=Resource.objects.select_related('photo')
                    .only(
                        'id',
                        'type',
                        'name',
                        'active',
                        'visible',
                        'description',
                        'staff_user_id',
                        'photo',
                    )
                    .with_partner_app_data(ResourceMetadata),
                ),
                Prefetch(
                    'appointment__booked_for__user__profiles',
                    queryset=UserProfile.objects.filter(
                        profile_type=UserProfile.Type.CUSTOMER,
                    ).select_related('photo'),
                ),
                Prefetch(
                    'appointment__business__pos_set',
                    queryset=POS.objects.filter(active=True).prefetch_related('tax_rates'),
                ),
                Prefetch(
                    'addons_set',
                    queryset=ServiceAddOnUse.objects.filter(deleted__isnull=True),
                ),
                Prefetch(
                    'addons_set__service_addon__photo',
                ),
                Prefetch(
                    'surcharges',
                ),
                Prefetch(
                    'service_variant__service',
                    queryset=Service.objects.with_partner_app_data(ServiceMetadata),
                ),
                Prefetch(
                    'appointment',
                    queryset=Appointment.objects.with_partner_app_data(AppointmentMetadata),
                ),
            )
            .prefetch_customer_booking_counts()
            .prefetch_payment_and_deposit()
            .prefetch_booked_by()
            .with_partner_app_data(SubbookingMetadata)
        )
        result_qs = result_qs.prefetch_combo_children_set()

        return result_qs

    def prefetch_payment_and_deposit(self):
        """Remember that _prefetch_payment_and_deposit should be executed."""
        return self._custom_prefetch('payment_and_deposit')

    def prefetch_customer_booking_counts(self):
        """
        Remember that _prefetch_customer_booking_counts should be executed.
        """
        return self._custom_prefetch('customer_booking_counts')

    def prefetch_booked_by(self):
        return self._custom_prefetch('booked_by')

    def prefetch_combo_children_set(self):
        """
        Fill combo_children_set cache without additional query

        If queryset is filtered by appointments and does not filter anything
        based on combo_parent, then all combo children records are in place:
        they need to be grouped only in cache
        """
        qs = self._custom_prefetch('combo_children_set')
        return qs

    def _prefetch_payment_and_deposit(self):
        """
        Prefetch payment data for each (multi-)booking:

        'payment_and_deposit' - latest payment and deposit;
        'paid' - is appointment paid;
        'is_paid_by_booksy_pay' - is appointment paid by booksy pay.
        """
        # pylint: disable=protected-access,cyclic-import
        from webapps.pos.models import Transaction

        # extract booking_ids and appointment_ids from result cache
        appointment_ids = {booking.appointment_id for booking in self._result_cache}

        transactions = (
            Transaction.objects.filter(
                appointment__id__in=appointment_ids,
                latest_receipt__isnull=False,
            )
            .order_by(
                '-latest_receipt__created',
            )
            .get_transactions_with_receipt_and_appointment_details()
        )

        # prepare transaction_cache
        # {(<obj_id>, <transaction_type>): <transaction>}
        transaction_cache = {}
        paid = set()
        paid_by_booksy_pay = set()
        for instance in transactions:
            key = (instance.appointment_id, instance.transaction_type)
            if key not in transaction_cache:
                transaction_cache[key] = instance
            if (
                instance.transaction_type == Transaction.TRANSACTION_TYPE__PAYMENT
                and instance.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
            ):
                paid.add(instance.appointment_id)
            if (
                instance.transaction_type == Transaction.TRANSACTION_TYPE__PAYMENT
                and instance.latest_receipt.status_code
                in (
                    receipt_status.BOOKSY_PAY_SUCCESS,
                    receipt_status.PAYMENT_SUCCESS,
                    receipt_status.CALL_FOR_BOOKSY_PAY_3DS,
                )
                and sget_v2(instance.latest_receipt, ['payment_type', 'code'])
                == PaymentTypeEnum.BOOKSY_PAY
            ):
                paid_by_booksy_pay.add(instance.appointment_id)

        # inject transactions into each booking's _prefetched_objects_cache
        for booking in self._result_cache:
            if not hasattr(booking, '_prefetched_objects_cache'):
                booking._prefetched_objects_cache = {}
            if not hasattr(booking.appointment, '_prefetched_objects_cache'):
                booking.appointment._prefetched_objects_cache = {}
            obj_id = booking.appointment_id
            booking._prefetched_objects_cache['paid'] = obj_id in paid
            booking._prefetched_objects_cache['payment_and_deposit'] = (
                # prefetched payment
                transaction_cache.get(
                    (obj_id, Transaction.TRANSACTION_TYPE__PAYMENT),
                    None,
                ),
                # prefetched deposit
                transaction_cache.get(
                    (
                        obj_id,
                        Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
                    ),
                    None,
                ),
            )
            booking.appointment._prefetched_objects_cache['payment_and_deposit'] = (
                booking._prefetched_objects_cache['payment_and_deposit']
            )
            booking.appointment._prefetched_objects_cache['paid'] = (
                booking._prefetched_objects_cache['paid']
            )
            booking.appointment._prefetched_objects_cache['is_paid_by_booksy_pay'] = (
                obj_id in paid_by_booksy_pay
            )

    def _prefetch_customer_booking_counts(self):
        # pylint: disable=protected-access

        """Prefetch customer bookings counts for each customer."""
        # extract BusinessCustomerInfo IDs present in QuerySet's result
        customer_ids = {
            booking.appointment.booked_for_id
            for booking in self._result_cache
            if booking.appointment.booked_for_id is not None
        }

        # prepare aggregation of counts per status per customer id
        booking_statuses = (
            Appointment.STATUS.FINISHED,
            Appointment.STATUS.NOSHOW,
            Appointment.STATUS.CANCELED,
            *Appointment.STATUSES_OCCUPYING_TIME_SLOTS,
        )
        counts = (
            Appointment.objects.filter(
                booked_for__in=customer_ids,
                status__in=AppointmentStatus.values(),
                deleted__isnull=True,
            )
            .values(
                'booked_for',
                'status',  # GROUP BY booked_for, status
            )
            .annotate(
                count=Count('status'),
            )
            .values_list('booked_for', 'status', 'count')
        )
        counts_cache = defaultdict(dict)
        for booked_for, status, count in counts:
            counts_cache[booked_for][status] = count

        # inject counts to instance's _prefetched_objects_cache
        for booking in self._result_cache:
            bci_id = booking.appointment.booked_for_id
            if bci_id is None:
                continue
            if not hasattr(booking, '_prefetched_objects_cache'):
                booking._prefetched_objects_cache = {}
            booking._prefetched_objects_cache['customer_booking_counts'] = {
                status: counts_cache.get(bci_id, {}).get(status, 0) for status in booking_statuses
            }

    def _prefetch_booked_by(self):
        from webapps.family_and_friends.models import MemberAppointment

        appointment_ids = [booking.appointment_id for booking in self._result_cache]

        family_and_friends_appointments = MemberAppointment.objects.filter(
            appointment__id__in=appointment_ids
        ).prefetch_related('booked_by')
        appointment_booked_by = {
            faf.appointment.id: faf.appointment.booked_by for faf in family_and_friends_appointments
        }

        for booking in self._result_cache:
            if not hasattr(booking, '_prefetched_objects_cache'):
                # pylint: disable=protected-access
                booking._prefetched_objects_cache = {}
            # pylint: disable=protected-access
            booking._prefetched_objects_cache['booked_by'] = appointment_booked_by.get(
                booking.appointment_id
            )

    def _prefetch_combo_children_set(self):
        # pylint: disable=protected-access

        ## skip if it does not have all related subbookings:
        # skip if it does not have appointment filter
        if not any(where.lhs.target.name == 'appointment' for where in self._query.where.children):
            return

        # skip if it has combo_parent filter
        if any(where.lhs.target.name == 'combo_parent' for where in self._query.where.children):
            return

        # prepare cache; exit if not good object
        for booking in self._result_cache:
            if not hasattr(booking, '_prefetched_objects_cache'):
                try:
                    booking._prefetched_objects_cache = {}
                except (AttributeError, TypeError):
                    return

        # collect combo parents with children
        combo_parents = defaultdict(list)
        for booking in self._result_cache:
            if parent_id := booking.combo_parent_id:
                combo_parents[parent_id].append(booking)

        # add to cache in each instance as queryset
        children_queryset = SubBooking.objects.filter(
            deleted__isnull=True,
        ).order_by('booked_from', 'id')

        for booking in self._result_cache:
            children = list(
                sorted(combo_parents.get(booking.id, []), key=lambda x: (x.booked_from, x.id))
            )
            manager = booking.combo_children_set
            children_qs = manager._apply_rel_filters(children_queryset)
            children_qs._result_cache = children
            children_qs._prefetch_done = True
            booking._prefetched_objects_cache['combo_children_set'] = children_qs
        # pylint: enable=protected-access

    def localtime_filter(self, **kwargs):
        tznames = (
            Business.objects.filter(
                time_zone_name__isnull=False,
            )
            .values_list(
                'time_zone_name',
                flat=True,
            )
            .distinct()
        )
        if not tznames:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'businesses without time_zone_names'
            )

        filters = []
        for tzname in tznames:
            tz = gettz(tzname)
            parts = [Q(appointment__business__time_zone_name=tzname)]
            for lookup, value in kwargs.items():
                if isinstance(value, datetime):
                    if value.tzinfo is not None:
                        raise RuntimeError(  # pylint: disable=broad-exception-raised
                            'values should be without timezone'
                        )
                    value = value.replace(tzinfo=tz)
                parts.append(Q(**{lookup: value}))
            filters.append(reduce(operator.iand, parts))
        return self.filter(reduce(operator.ior, filters))


class SubBookingManager(SoftDeleteManager.from_queryset(SubBookingQuerySet)):
    pass


# pylint: disable=protected-access, too-many-public-methods
class SubBooking(DirtyFieldsMixin, UndeletableModel, ArchiveModel):
    # most important on top
    # descending order keep django сonvention
    SORT_ORDER_IMPORTANCE_DESC = '-importance'

    class Meta:
        indexes = [models.Index(fields=['deleted'], name='booking_subbooking_deleted_idx')]

    id = models.AutoField(primary_key=True, db_column='subbooking_id')
    appointment = models.ForeignKey(
        'booking.Appointment',
        related_name='bookings',
        on_delete=models.PROTECT,
    )
    service_variant = models.ForeignKey(
        'business.ServiceVariant',
        related_name='bookings',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    combo_parent = models.ForeignKey(
        'self', related_name='combo_children_set', null=True, blank=True, on_delete=models.CASCADE
    )
    resources = models.ManyToManyField(
        'business.Resource',
        through='BookingResource',
        related_name='subbookings',
    )
    booked_from = models.DateTimeField(
        db_index=True,
        verbose_name='Booked from (UTC)',
    )
    booked_till = models.DateTimeField(
        db_index=True,
        verbose_name='Booked till (UTC)',
    )
    autoassign = models.BooleanField(help_text='first available staffer was selected by the system')

    service_name = models.CharField(max_length=50, null=True, blank=True)

    padded_booked_range = DateTimeRangeField(null=True, blank=True, auto_created=True)
    # 47160
    # Sometimes there's only 1 time block in StyleSeat appointment,
    # so no multibooking is created in Booksy
    chargeable = models.BooleanField(
        default=False,
    )  # DEPRECATED - use appointment.chargeable (BOOS-309)
    payable = models.BooleanField(default=False)  # DEPRECATED - use appointment.payable (BOOS-309)
    is_highlighted = models.BooleanField(
        null=False,
        blank=False,
        default=False,
    )
    is_staffer_requested_by_client = models.BooleanField(null=True, default=False)
    resolved_promotion = models.ForeignKey(
        'business.ServicePromotion',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    resolved_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )
    resolved_promotion_type = models.CharField(
        choices=RESOLVED_BOOKING_PROMOTION_TYPES,
        max_length=2,
        blank=True,
        null=True,
        default=None,
    )
    resolved_discount = models.IntegerField(
        blank=True,
        null=True,
        default=None,
    )
    waitlist = models.ForeignKey(
        'wait_list.WaitList',
        blank=True,
        null=True,
        default=None,
        related_name='bookings',
        on_delete=SET_NULL,
        db_index=False,
    )
    service_data_internal = SubBookingServiceDataField(null=True)
    # custom manager
    objects = SubBookingManager()
    all_objects = SoftDeleteManager()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_booked_from = self.booked_from

    def __repr__(self):
        return (
            f'<SubBooking id={self.id} '
            f'appointment_id={self.appointment_id} '
            f'{self.booked_from} {self.booked_till}>'
        )

    def __str__(self):
        return self.__repr__()

    @cached_property
    def staffer(self):
        return self._get_resource(Resource.STAFF)

    @cached_property
    def staffer_id(self):
        value, is_cached = self._get_id_from_cached_property('staffer')
        if is_cached:
            return value

        return self._get_resource(Resource.STAFF, only_id=True)

    @cached_property
    def appliance(self):
        return self._get_resource(Resource.APPLIANCE)

    @cached_property
    def appliance_id(self):
        value, is_cached = self._get_id_from_cached_property('appliance')
        if is_cached:
            return value

        return self._get_resource(Resource.APPLIANCE, only_id=True)

    @cached_property
    def addons(self):
        prefetched_objects_cache = getattr(self, '_prefetched_objects_cache', {})
        if 'addons_set' in prefetched_objects_cache:
            return prefetched_objects_cache['addons_set']

        return list(self.addons_set.filter(deleted__isnull=True)) if self.id else []

    @cached_property
    def has_addons(self):
        prefetched_objects_cache = getattr(self, '_prefetched_objects_cache', {})
        if 'addons_set' in prefetched_objects_cache:
            return bool(prefetched_objects_cache['addons_set'])

        return self.addons_set.filter(deleted__isnull=True).exists()

    def addons_cache_clear(self):
        try:  # nosemgrep
            del self.addons  # noqa
        except AttributeError:
            pass

        prefetched_objects_cache = getattr(self, '_prefetched_objects_cache', {})
        if 'addons_set' in prefetched_objects_cache:
            del prefetched_objects_cache['addons_set']

    def _get_id_from_cached_property(self, cached_property_name):
        # if there is cached_property with resource
        # we can use id from this object instead of make query to db
        if cached_property_name in self.__dict__:
            resource = self.__dict__[cached_property_name]
            return resource and resource.id, True

        return None, False

    def _get_resource(self, type_, only_id=False):
        prefetched_data = getattr(self, '_prefetched_objects_cache', {})
        resources = prefetched_data.get('resources')
        # firstly use prefetched resources
        if resources is not None:
            resource = firstof(r for r in resources if r.type == type_)
            return resource.id if (only_id and resource) else resource

        if self.id:  # 'id' is required to use many-to-many relationship
            qset = self.resources.filter(type=type_)
            if only_id:
                qset = qset.values_list('id', flat=True)

            return qset.first()

    def clear_resource_caches(self):
        for key in ('staffer', 'staffer_id', 'appliance', 'appliance_id'):
            if key in self.__dict__:
                del self.__dict__[key]

        if 'resources' in getattr(self, '_prefetched_objects_cache', {}):
            del self._prefetched_objects_cache['resources']

    @property
    def gap_hole_start(self):
        return self._gap_hole_data[0]

    @property
    def gap_hole_end(self):
        return self._gap_hole_data[1]

    @cached_property
    def _gap_hole_data(self):
        if not self.service_variant or not self.service_variant.gap_hole or not self.booked_from:
            return None, None

        gap_hole_start = self.booked_from + self.service_variant.gap_hole_start_after
        gap_hole_end = gap_hole_start + self.service_variant.gap_hole_duration
        if self.booked_till < gap_hole_end:
            # invalid duration - abort!
            return None, None
        return gap_hole_start, gap_hole_end

    @property
    def formatted_booked_from(self):
        return self._get_tz_formatted_datetime('booked_from')

    @property
    def formatted_booked_till(self):
        return self._get_tz_formatted_datetime('booked_till')

    @property
    def formatted_updated(self):
        return self._get_tz_formatted_datetime('updated')

    def _get_tz_formatted_datetime(self, field):
        if getattr(self, field) is None:
            return None
        tz = self.business.get_timezone()
        fmt = settings.DATETIME_FORMAT
        return getattr(self, field).astimezone(tz).strftime(fmt)

    @property
    def paid_cached(self):
        cache = getattr(self, '_prefetched_objects_cache', {})
        if 'paid' in cache:
            return cache['paid']

    @cached_property
    def paid(self):
        """Check if appointment is already paid for."""
        if self.paid_cached is not None:
            return self.paid_cached

        from webapps.booking.serializers.booking import BusinessBookingActionsMixin

        return BusinessBookingActionsMixin._is_paid(self)

    @property
    def service_promotion(self):
        return getattr(self, '_service_promotion', None)

    @cached_property
    def combo_children(self):
        prefetched_objects_cache = getattr(self, '_prefetched_objects_cache', {})
        if 'combo_children_set' in prefetched_objects_cache:
            return list(prefetched_objects_cache['combo_children_set'])

        return (
            list(self.combo_children_set.filter(deleted__isnull=True).order_by('booked_from', 'id'))
            if self.id
            else []
        )

    @property
    def service_price(self) -> ServicePrice:
        if self.resolved_discount:
            return ServicePrice(
                value=self.resolved_price,
                price_type=self.service_data.service_variant_type,
                discount=Decimal(self.resolved_discount),
            )

        if self.resolved_price and EnablePeakHoursFlag() and self.surcharge:
            return ServicePrice(
                value=self.resolved_price,
                price_type=self.service_data.service_variant_type,
            )

        if self.combo_children:
            return reduce(
                operator.iadd,
                [child.service_price for child in self.combo_children],
            )

        return ServicePrice(
            value=self.service_data.service_variant_price,
            price_type=self.service_data.service_variant_type,
        )

    @property
    def service_data(self) -> SubBookingServiceData:
        self.ensure_service_data()

        return self.service_data_internal

    @service_data.setter
    def service_data(self, value: t.Optional[SubBookingServiceData]) -> None:
        self.service_data_internal = value

    @staticmethod
    def get_status_for_customer_booking(booking, business, service=None):
        """Determine Subbooking.appointment.status in customer booking scenario.

        :param service: Service of the booking beeing created

        New status depends on:
            - Business.booking_mode
            - Booking being new
            - previous status of a Booking
            - service of the booking
        """

        # in automatic mode all active bookings are ACCEPTED
        # except for traveling services
        if service is None and booking is not None and booking.service_variant:
            service = booking.service_variant.service
        if business.booking_mode == Business.BookingMode.AUTO and not (
            service and service.is_traveling_service
        ):
            return Appointment.STATUS.ACCEPTED

        # SEMI AUTOMATIC MODE
        if (
            booking
            and booking.id
            and booking.appointment.status
            in [
                Appointment.STATUS.ACCEPTED,
                Appointment.STATUS.MODIFIED,
                Appointment.STATUS.PROPOSED,
            ]
        ):
            # existing active booking gets modified
            return Appointment.STATUS.MODIFIED
        # new booking and non-active get unconfirmed (waiting)
        return Appointment.STATUS.UNCONFIRMED

    def get_customer_profile(self):
        """Exception free shortcut to customer_profile"""
        if self.appointment.booked_for is None:
            return
        return self.appointment.booked_for.customer_profile

    @staticmethod
    def get_badge(customer_data, booking_counts):
        if customer_data.blacklisted:
            return 'blacklisted'
        if customer_data.from_promo:
            return 'from_promo'

        visit_frequency = booking_counts.get(Appointment.STATUS.FINISHED, 0)
        no_shows = booking_counts.get(Appointment.STATUS.NOSHOW, 0)

        first_visit = (
            sum(booking_counts.get(x, 0) for x in Appointment.STATUSES_OCCUPYING_TIME_SLOTS) == 1
            and visit_frequency + no_shows == 0
        )

        if first_visit:
            return 'first_visit'
        if customer_data.user_id:
            return 'is_user'

    def get_customer_data_dict(self, get_counts=True):
        # TODO: move to appointment
        appointment = self.appointment
        if appointment.booked_for is not None:
            customer_data = appointment.booked_for.as_customer_data()
            if get_counts:
                booking_counts = self.get_customer_booking_counts()
            else:
                booking_counts = {}
            visit_frequency = booking_counts.get(Appointment.STATUS.FINISHED, 0)
            no_shows = booking_counts.get(Appointment.STATUS.NOSHOW, 0)
            first_visit = (
                sum(booking_counts.get(x, 0) for x in Appointment.STATUSES_OCCUPYING_TIME_SLOTS)
                == 1
                and visit_frequency + no_shows == 0
            )
            photo = customer_data.photo and customer_data.photo.full_url
            if not photo:  # android crash
                photo = None

            ret = {
                'id': self.appointment.booked_for_id,
                'name': customer_data.full_name or appointment.customer_name,
                'phone': customer_data.cell_phone or appointment.customer_phone,
                'email': customer_data.email or appointment.customer_email,
                'photo': photo,
                'business_secret_note': customer_data.business_secret_note or None,
                # customer_booking_counts
                'visit_frequency': visit_frequency,
                'no_shows': no_shows,
                'visit_no': visit_frequency + 1,
                'badge': self.get_badge(customer_data, booking_counts),
                'is_user': bool(customer_data.user_id),
                # DEPRICATED - use badge instead
                'blacklisted': customer_data.blacklisted,
                'first_visit': first_visit,
            }
        else:
            ret = {
                'name': appointment.customer_name,
                'phone': appointment.customer_phone,
                'email': appointment.customer_email,
                'badges': None,
                'is_user': False,
                # DEPRICATED - use badge instead
                'blacklisted': False,
            }
        return ret

    @staticmethod
    def get_allowed_transitions(old_status, booking_mode, who_makes_change=Who.CUSTOMER):
        """
        Return all possible transitions from old status
        based on who makes change and the ALLOWED_TRANSITIONS dict.

        This function DOES NOT take into account business' booking lead time
        information.

        :param who_makes_change: 'c' or 'b' or 's'
        :return:
        """
        ret = {old_status} | {
            ns
            for os, ns, who in ALLOWED_TRANSITIONS.get(booking_mode, [])
            if os == old_status and who == who_makes_change
        }

        # business and system can cancel anytime
        if (
            who_makes_change == Who.BUSINESS
            and old_status not in Appointment.STATUSES_TIME_PASSED
            and old_status not in Appointment.STATUSES_NONEDITABLE
        ):
            ret.add(Appointment.STATUS.CANCELED)

        # HACK: we need it only for transition from existing booking
        #       to multibooking
        elif who_makes_change == Who.CUSTOMER:
            ret.add(Appointment.STATUS.UNCONFIRMED)

        # system is allowed to change the state any way it wants
        elif who_makes_change == Who.STAFF:
            ret.update(Appointment.STATUS)

        return ret

    def has_ended(self):
        return (
            (self.booked_till and self.booked_till <= tools.tznow())
            or
            # sometimes bookings are marked as no-show even
            # if they have not ended yet
            self.appointment.status in Appointment.STATUSES_TIME_PASSED
        )

    def is_multibooking(self):
        if self.appointment_id:
            if not hasattr(self, '_is_multi'):
                self._is_multi = (
                    SubBooking.objects.filter(
                        appointment_id=self.appointment_id,
                    ).count()
                    > 1
                )
            return self._is_multi

        return False

    def save(self, *args, **kwargs):
        if kwargs.pop('override', False):
            self._update_service_data()
            super().save(*args, **kwargs)
            self.appointment.subbookings_cache_clear()
            maybe_aggregate_statistics(
                self.appointment, previous_datetime=self._original_booked_from
            )
        else:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'Booking does not support ORM save. Use Appointment methods.'
            )

    @staticmethod
    def delete_appointments(appointments, deleted_by):
        ids = [a.id for a in appointments]
        now = tools.tznow()
        SubBooking.objects.filter(appointment_id__in=ids).update(
            updated=now,
            deleted=now,
        )
        Appointment.objects.filter(id__in=ids).update(
            updated_by=deleted_by,
            status=Appointment.STATUS.CANCELED,
            updated=now,
            deleted=now,
        )

    def refresh(self):
        """Reload an object from the database"""
        new = self.__class__._default_manager.get(pk=self.pk)
        self.__dict__.update(new.__dict__)
        reset_state(None, self)  # reset dirtyfields state

    @staticmethod
    def get_booking_sort_order(order):
        """
        Create function for sort order of bookings.
        :param order: str. Acceptable order values:
                    - any attribute value of SubBooking object
                    -  -importance . Special field to sort in client app.
        :return: django.db.models function
        """
        striped_importance = SubBooking.SORT_ORDER_IMPORTANCE_DESC.strip('-')
        if order == striped_importance:
            now = tznow()
            sort_shift = now + timedelta(days=365 * 10)
            sort = ImportanceAggregate(
                output_field=models.DateTimeField(),
                sort_shift=sort_shift,
            )
        else:
            sort = (Max if order in ['booked_till', 'updated'] else Min)(order)
        return sort

    @staticmethod
    def get_appointment_sort_order(order):
        """
        Create function for sort order of appointments.
        :param order: str. Acceptable order values:
                    - any attribute value of SubBooking object
                    - -importance . Special field to sort in client app.
        :return: django.db.models function
        """
        striped_importance = SubBooking.SORT_ORDER_IMPORTANCE_DESC.strip('-')
        if order == striped_importance:
            now = tznow()
            sort_shift = now + timedelta(days=365 * 10)
            sort = Case(
                When(booked_till__gt=now, then=Value('NOW()') - F('booked_from')),
                default=F('booked_from') - sort_shift,
                output_field=models.DateTimeField(),
            )
            return sort

    @staticmethod
    def get_dashboard_items(
        filters: t.List[Q],
        offset: int,
        limit: int,
        order: str,
        for_listing: t.Optional[bool] = False,
    ):
        """Get booking list for dashboard-like booking listings.

        Dashboard listing is a listing of Bookings, but each MultiBooking
        is represented by only the first Booking - rest of the bookings
        are injected into first_booking._extra_bookings_dashboard_cache
        attribute, which can be later serialized into extra_bookings field.

        All this is done to ensure proper paging and no duplication.

        :param filters: a list of QuerySet filters to apply to listing query
        :param offset: query offset
        :param limit: query limit
        :param order: query order
        :param for_listing: don't prefetch all
        :return: (bookings, count)

        """
        order_u = order.strip('-')
        appointments_qset = Appointment.objects.filter(*filters).filter(
            deleted__isnull=True,
        )
        count = appointments_qset.count()
        if offset == 0 and limit == 0:
            return [], count

        sort = SubBooking.get_appointment_sort_order(order_u)
        if sort:
            sign = '-' if order.startswith('-') else ''
            appointments_qset = appointments_qset.annotate(sort=sort).order_by(f'{sign}sort')
        else:
            appointments_qset = appointments_qset.order_by(order)

        sorted_ids = list(
            appointments_qset.values_list(
                'id',
                flat=True,
            )[offset:limit]
        )

        subbookings_qset = (
            SubBooking.objects.filter(
                deleted__isnull=True,
                appointment__id__in=sorted_ids,  # it isn't keeping order
            )
            .prefetch_all(for_listing=for_listing)
            .prefetch_related(
                Prefetch(
                    'appointment__bookings',
                    to_attr='active_bookings_cached',
                    queryset=SubBooking.objects.active(),
                ),
            )
        )

        appt_subbooking = {}
        for s in subbookings_qset:
            if s.combo_parent_id:
                continue
            appt_subbooking.setdefault(s.appointment_id, []).append(s)

        appointments = []
        for appointment_id in sorted_ids:
            if appointment_id not in appt_subbooking:
                # for case when all subbookings are marked as deleted,
                # but appointment for some reason is not deleted
                continue

            subbookings = appt_subbooking[appointment_id]
            subbookings.sort(key=lambda bk: (bk.booked_from, bk.id))
            first, *extra = subbookings
            first._extra_bookings_dashboard_cache = extra
            appointments.append(first)

        return appointments, count

    def clear_cache_payment_and_deposit(self):
        getattr(self, '_prefetched_objects_cache', {}).pop('payment_and_deposit', None)

    def get_payment_and_deposit(self):
        """Get latest payment and deposit for this (multi-)booking.

        If available it uses stuff prefetched by
        SubBookingQuerySet.prefetch_payment_and_deposit()

        :returns: (payment, deposit)

        """
        if 'payment_and_deposit' in getattr(self, '_prefetched_objects_cache', {}):
            return self._prefetched_objects_cache['payment_and_deposit']

        from webapps.pos.models import Transaction

        transaction_qs = (
            Transaction.objects.by_appointment_id(self.appointment_id)
            .order_by(
                'created',
            )
            .get_transactions_with_receipt_and_appointment_details()
        )
        payment = transaction_qs.filter(
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        ).last()
        deposit = transaction_qs.filter(
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        ).last()
        return payment, deposit

    @cached_property
    def deposit_info(self) -> t.Optional[dict]:
        payment, deposit = self.get_payment_and_deposit()
        info = {}

        if payment and payment.latest_receipt.status_code in [
            PREPAYMENT_SUCCESS,
            PREPAYMENT_AUTHORISATION_SUCCESS,
        ]:
            amount = payment.latest_receipt.payment_rows.filter(
                payment_type__code=PaymentTypeEnum.PREPAYMENT
            ).aggregate(Sum('amount'))['amount__sum']
            info.update(
                {
                    'info': _(
                        'The deposit amount of {amount} was charged to your '
                        'card and deducted from your total due after the '
                        'appointment. This amount is not refundable.'
                    ).format(amount=format_currency(amount)),
                    'cancel': '',  # no way to cancel prepayment
                }
            )
        elif deposit:
            cancel_deadline = self.booked_from - deposit.pos.deposit_cancel_time
            tz = self.appointment.business.get_timezone()
            cancel_deadline_tz = cancel_deadline.astimezone(tz)
            info_msg = _(
                'Cancellation fee of {amount} will be charged '
                'if you violate cancellation policy.'
            ).format(
                amount=format_currency(deposit.total),
            )
            cancel_msg = _(
                'Free cancellation at least {relative} '
                'in advance (before {absolute}, {absolute_time})'
            ).format(
                relative=duration_formatter(
                    deposit.pos.deposit_cancel_time,
                    short=False,
                ),
                absolute=format_datetime(
                    cancel_deadline_tz,
                    'date_ymd',
                    language=get_language(),
                ),
                absolute_time=format_datetime(
                    cancel_deadline_tz,
                    'time_hm',
                    language=get_language(),
                ),
            )
            info.update(
                {
                    'info': info_msg,
                    'cancel': cancel_msg,
                }
            )
        return info or None  # return none instead of of empty dict

    @property
    def surcharge(self) -> SubBookingSurchargeData | None:
        if not hasattr(self, '_surcharge'):
            if not self.id:
                surcharge = None
            else:
                surcharge_repository = SubBookingSurchargeRepository()
                prefetched_objects_cache = getattr(self, '_prefetched_objects_cache', {})
                if 'surcharges' in prefetched_objects_cache:
                    try:
                        prefetched_model = list(prefetched_objects_cache['surcharges'])[0]
                    except IndexError:
                        prefetched_model = None
                    surcharge = surcharge_repository.get_for_subbooking(
                        self.id,
                        prefetched_model=prefetched_model,
                    )
                else:
                    surcharge = surcharge_repository.get_for_subbooking(self.id)

            self._surcharge = surcharge

        return self._surcharge

    @surcharge.setter
    def surcharge(self, value: SubBookingSurchargeData | None) -> None:
        self._surcharge = value

    def get_customer_booking_counts(self):
        """Return customer's booking counts.

        If available, use data prefetched by
        SubBookingQuerySet.prefetch_customer_booking_counts().

        """
        # TODO: move to appointment
        if self.appointment.booked_for_id is None:
            return {}
        if 'customer_booking_counts' in getattr(self, '_prefetched_objects_cache', {}):
            return self._prefetched_objects_cache['customer_booking_counts']
        filters = {
            'booked_for_id': self.appointment.booked_for_id,
            'deleted__isnull': True,
            'status__in': (
                Appointment.STATUS.FINISHED,
                Appointment.STATUS.NOSHOW,
                Appointment.STATUS.CANCELED,
                *Appointment.STATUSES_OCCUPYING_TIME_SLOTS,
            ),
        }
        qset = (
            Appointment.objects.filter(**filters)
            .values(
                'status',  # GROUP BY status
            )
            .annotate(count=Count('status'))
            .values_list('status', 'count')
        )

        return dict(qset)

    @staticmethod
    def compute_new_status(booked_till, base_status):
        """Adjust base status to FINISHED or ACCEPTED based on booked_till."""
        past = booked_till < tznow()
        if past:
            if base_status in Appointment.STATUSES_TIME_PASSED:
                return base_status
            return Appointment.STATUS.FINISHED

        if base_status in Appointment.STATUSES_TIME_PASSED:
            return Appointment.STATUS.ACCEPTED
        return base_status

    def check_access(self, staffer):
        """Check whether booking is editable by given staffer."""
        if staffer is None:
            return True  # backward compatibility hack

        assert isinstance(staffer, Resource) and staffer.type == Resource.STAFF

        if staffer.staff_access_level not in Resource.STAFF_ACCESS_LEVELS_LIMIT_CALENDAR:
            return True

        return self.resources.filter(id=staffer.id).exists()

    @classmethod
    def split_params_to_appointment(cls, params):
        subbooking_fields = {f.name for f in cls._meta.fields}
        b_kwargs = {}
        a_kwargs = {}
        for field, value in params.items():
            f = field.replace('_id', '') if field.endswith('_id') else field
            kws = b_kwargs if f in subbooking_fields else a_kwargs
            kws[field] = value
        if 'updated' in params:
            a_kwargs['updated'] = params['updated']

        return b_kwargs, a_kwargs

    def _update_service_data(self):
        # Don't use in this method self.service_data, otherwise it could result in infinite loop
        old_service_variant_id = (
            self.service_data_internal and self.service_data_internal.service_variant_id
        )
        new_service_variant_id = self.service_variant_id

        old_service_variant_combo_parent_id = (
            self.service_data_internal
            and self.service_data_internal.service_variant_combo_parent_id
        )
        new_service_variant_combo_parent_id = (
            self.combo_parent and self.combo_parent.service_variant_id
        )

        if (
            old_service_variant_id
            and old_service_variant_id == new_service_variant_id
            and old_service_variant_combo_parent_id == new_service_variant_combo_parent_id
        ):
            return

        self.service_data_internal = SubBookingServiceData.build(self)

    def ensure_service_data(self):
        if not self.service_data_internal:
            self._update_service_data()

    def get_bci_to_discount(self):
        if 'booked_by' in getattr(self, '_prefetched_objects_cache', {}):
            booked_by = self._prefetched_objects_cache['booked_by']
        else:
            booked_by = self.appointment.booked_by
        return booked_by or self.appointment.booked_for


class AppointmentSoftDeleteQuerySet(SoftDeleteQuerySet):
    def soft_delete(self):
        return self.update(deleted=tznow(), archived=True)


class AppointmentSoftDeleteManager(SoftDeleteManager.from_queryset(AppointmentSoftDeleteQuerySet)):
    use_in_migrations = True


class AppointmentQuerySet(
    AppointmentSoftDeleteQuerySet,
    AppointmentsFromBoostPerspectiveQuerySet,
    PartnerAppDataQuerySet,
):
    def localtime_filter(self, **kwargs):
        tznames = (
            Business.objects.filter(
                time_zone_name__isnull=False,
            )
            .values_list(
                'time_zone_name',
                flat=True,
            )
            .distinct()
        )
        if not tznames:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'businesses without time_zone_names'
            )

        filters = []
        for tzname in tznames:
            tz = gettz(tzname)
            parts = [Q(business__time_zone_name=tzname)]
            for lookup, value in kwargs.items():
                if isinstance(value, datetime):
                    if value.tzinfo is not None:
                        raise RuntimeError(  # pylint: disable=broad-exception-raised
                            'values should be without timezone'
                        )
                    value = value.replace(tzinfo=tz)
                parts.append(Q(**{lookup: value}))
            filters.append(reduce(operator.iand, parts))
        return self.filter(reduce(operator.ior, filters))

    def annotate_payment_status(self):
        from webapps.pos.models import Transaction

        return self.annotate(
            payment_status=Subquery(
                Transaction.objects.filter(
                    appointment_id=OuterRef('pk'),
                    transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
                    latest_receipt__isnull=False,
                )
                .order_by('-latest_receipt__created')
                .values('latest_receipt__status_code')[:1]
            ),
        )

    def annotate_is_repeating(self):
        return self.annotate(
            is_repeating=Case(
                When(
                    repeating__isnull=False,
                    repeating__deleted__isnull=True,
                    then=True,
                ),
                default=False,
                output_field=models.BooleanField(),
            ),
        )

    def prefetch_subbookings(self, queryset=None):
        if queryset is None:
            queryset = SubBooking.objects.select_related(
                'service_variant__service'
            ).prefetch_related('resources')

        queryset = queryset.active()

        return self.prefetch_related(
            Prefetch(
                'bookings',
                to_attr='active_bookings_cached',
                queryset=queryset,
            )
        )

    def ended_appointments(self):
        return (
            self.filter(
                status__in=[
                    Appointment.STATUS.FINISHED,
                    Appointment.STATUS.NOSHOW,
                ],
            )
            .order_by('-booked_till')
            .only(
                'id',
                'booked_from',
                'booked_till',
            )
        )

    def annotate_family_and_friends_role(self):
        from webapps.family_and_friends.helpers.appointment import (
            family_and_friends_role_case_expr,
        )

        return self.annotate(
            family_and_friends_role=family_and_friends_role_case_expr(),
        )


class AppointmentManager(SoftDeleteManager.from_queryset(AppointmentQuerySet)):
    pass


class Empty:
    """
    Placeholder class to indicate that the attribute was not set
    when None may be a proper value.
    """


class Appointment(  # pylint: disable=too-many-instance-attributes
    UndeletableModel,
    ArchiveModel,
    VersionedModel,
    DirtyFieldsMixin,
):
    # pylint: disable=too-many-instance-attributes

    STATUS = AppointmentStatus
    TYPE = AppointmentType
    TYPE_CHOICES = AppointmentTypeChoices
    TYPES_BOOKABLE = [TYPE.BUSINESS, TYPE.CUSTOMER]

    STATUSES_ALREADY_ACCEPTED_OR_DONE = (
        STATUS.ACCEPTED,
        STATUS.FINISHED,
        STATUS.NOSHOW,
        STATUS.PENDING_PAYMENT,
        STATUS.PROPOSED,
    )
    # New statuses that require booking to be active
    STATUS_CHANGE_NEEDS_ACTIVE_BY_BUSINESS = {
        STATUS.ACCEPTED,
        STATUS.CANCELED,
        STATUS.DECLINED,
    }
    STATUS_CHANGE_NEEDS_ACTIVE_BY_CUSTOMER = {
        STATUS.ACCEPTED,
    }

    STATUSES_INACTIVE = (
        STATUS.CANCELED,
        STATUS.DECLINED,
        STATUS.REJECTED,
        STATUS.NOSHOW,  # inactive but still editable
    )
    STATUSES_NONEDITABLE = (
        STATUS.CANCELED,
        STATUS.DECLINED,
        STATUS.REJECTED,
    )
    STATUSES_OCCUPYING_TIME_SLOTS = (
        STATUS.ACCEPTED,
        STATUS.UNCONFIRMED,
        STATUS.MODIFIED,
        STATUS.PROPOSED,
    )
    STATUSES_TIME_PASSED = (
        STATUS.FINISHED,
        STATUS.NOSHOW,
    )

    class Meta:
        indexes = [
            models.Index(
                fields=['business', 'type', 'archived', 'last_edit'],
                name='biz_id_type_arch_last_edit_idx',
            ),
            models.Index(
                fields=['business', 'booked_till'],
                name='biz_id_booked_till_idx',
            ),
        ]

    objects = AppointmentManager()
    all_objects = AppointmentSoftDeleteManager()

    id = models.AutoField(primary_key=True, db_column='appointment_id')
    archived = models.BooleanField(null=False, blank=False, default=False)
    booked_from = models.DateTimeField(
        db_index=True,
        verbose_name='Booked from (UTC)',
    )
    booked_till = models.DateTimeField(
        db_index=True,
        verbose_name='Booked till (UTC)',
    )
    customer_phone = BooksyPhoneNumberField(null=True)
    customer_email = models.EmailField(
        max_length=75,
        null=True,
        blank=True,
    )
    customer_name = models.CharField(
        null=True,
        blank=True,
        max_length=(consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN + 1),
    )
    source = models.ForeignKey(
        BookingSources,
        related_name='appointments',
        on_delete=models.PROTECT,
    )
    business = models.ForeignKey(
        'business.Business',
        related_name='appointments',
        on_delete=models.PROTECT,
    )
    booked_for = models.ForeignKey(
        'business.BusinessCustomerInfo',
        related_name='appointments',
        null=True,
        blank=True,
        default=None,
        on_delete=models.PROTECT,
    )
    type = models.CharField(
        max_length=1,
        choices=TYPE_CHOICES.choices(),
    )
    customer_note = models.CharField(
        max_length=consts.CUSTOMER_NOTE__MAX_LENGTH,
        null=True,
        blank=True,
    )
    business_note = models.CharField(
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
        null=True,
        blank=True,
    )
    business_secret_note = models.CharField(
        max_length=consts.BUSINESS_SECRET_NOTE__MAX_LENGTH,
        null=True,
        blank=True,
    )
    repeating = models.ForeignKey(
        'booking.RepeatingBooking',
        null=True,
        blank=True,
        related_name='appointments',
        on_delete=models.SET_NULL,
    )
    import_uid = models.CharField(
        max_length=64,
        null=True,
        blank=True,
    )
    multibooking_import_uid = models.CharField(
        max_length=64,
        null=True,
        blank=True,
    )
    service_questions = DataClassField(
        QuestionsAndAnswersList,
        null=True,
    )
    status = models.CharField(
        max_length=1,
        choices=AppointmentStatusChoices.choices(),
        db_index=True,
    )
    status_changed = models.DateTimeField(
        auto_now_add=True,
        null=True,
        blank=True,
        verbose_name='Status changed (UTC)',
    )
    updated_by = models.ForeignKey(
        'user.User',
        related_name='updated_appointments',
        on_delete=models.PROTECT,
    )
    traveling = models.ForeignKey(
        'booking.AppointmentTraveling',
        blank=True,
        null=True,
        default=None,
        related_name='appointments',
        on_delete=SET_NULL,
    )
    secret = models.UUIDField(
        default=uuid.uuid4,
        null=True,
        blank=True,
        editable=False,
    )
    chargeable = models.BooleanField(default=False)
    payable = models.BooleanField(default=False)
    is_booksy_gift_card_appointment = models.BooleanField(
        default=False,
        blank=True,
        null=True,
    )
    total_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        editable=False,
    )
    total_type = models.CharField(
        max_length=1,
        null=True,
        choices=PriceType.choices(),
        editable=False,
    )
    total_discount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        editable=False,
    )

    @cached_property
    def subbookings(self):
        """All non-deleted subbookings of a appointment."""
        if hasattr(self, 'active_bookings_cached'):  # prefetched
            active_bookings = self.active_bookings_cached
        else:
            active_bookings = self.bookings.active() if self.id else []

        subbookings = tuple(self.group_combo_children_gen(active_bookings))
        for subbooking in subbookings:
            subbooking.appointment = self

        return subbookings

    def subbookings_cache_clear(self):
        try:  # nosemgrep
            del self.subbookings  # noqa
            if hasattr(self, 'active_bookings_cached'):
                del self.active_bookings_cached  # noqa
        except AttributeError:
            pass

    @cached_property
    def prefetch_all_subbookings(self):
        return list(self.group_combo_children_gen(self.bookings.active().prefetch_all()))

    @cached_property
    def member_appointment(self) -> t.Optional['MemberAppointment']:
        return self.family_and_friends.first() if self.id else None

    @cached_property
    def booked_by(self) -> t.Optional[BusinessCustomerInfo]:
        return self.member_appointment.booked_by if self.member_appointment else None

    @cached_property
    def is_family_and_friends(self) -> bool:
        return bool(self.booked_by) if self.id else False

    @staticmethod
    def group_combo_children_gen(subbookings):
        subbookings = list(subbookings)
        combo_children = [booking for booking in subbookings if booking.combo_parent]
        combo_parents = set(booking.combo_parent for booking in combo_children)

        for subbooking in [booking for booking in subbookings if not booking.combo_parent]:
            if subbooking in combo_parents:
                subbooking.combo_children = [
                    booking for booking in combo_children if booking.combo_parent == subbooking
                ]
            yield subbooking

    @property
    def repeating_prefetched_first_booking(self):
        if (
            self.repeating
            and hasattr(self, '_prefetched_objects_cache')
            and 'bookings' in self._prefetched_objects_cache
        ):
            return self._prefetched_objects_cache['bookings'][0]
        return SubBooking.objects.none()

    @property
    def first_booking(self):
        """Gets first subbooking of a multibooking."""
        repeating_booking = self.repeating_prefetched_first_booking
        if repeating_booking:
            return repeating_booking
        return self.subbookings[0]

    @property
    def first_booking_id(self):
        first_booking = self.first_booking
        return first_booking.id if first_booking else None

    @property
    def version(self):
        return self._version

    @property
    def total(self) -> ServicePrice:
        return ServicePrice(
            value=self.total_value,
            price_type=self.total_type,
            discount=self.total_discount,
        )

    @total.setter
    def total(self, obj: ServicePrice):
        self.total_value = obj.value
        self.total_type = obj.price_type
        self.total_discount = obj.discount

    @transaction.atomic
    def make_appointment(  # pylint: disable=too-many-arguments,too-many-positional-arguments,too-many-branches, too-many-statements
        self,
        subbookings,
        subbooking_ids_to_delete=None,
        who_makes_change=Who.CUSTOMER,
        overbooking=None,
        updated_by=None,
        prev_status=Empty,
        resource_required=True,
        is_booksy_gift_card_appointment=None,
    ):
        """
        Create or reschedule an appointment. It influences resources availability and requires
        global business lock.

        When updating attributes not related to availability use `update_appointment` method.
        """

        if who_makes_change == Who.CUSTOMER:
            if any(booking.service_variant_id is None for booking in subbookings):
                raise BookingConflict(str(_("Unable to create booking!")))
            if overbooking is None:
                overbooking = self.business.booking_mode == Business.BookingMode.MANUAL

        booking_lock = BusinessVersion.create_booking_lock(self.business_id)
        user_data = UserData(key=f'business_{self.business_id}')
        if prev_status is Empty or not LoyaltyProgramFlag(user_data):
            # Checking FeatureFlag should be dropped, but it is set to keep the old
            # incorrect behaviour.  Quick fix may cause unpredictable errors on production.
            # Fix issue: AAR-395
            prev_status = self.status

        # remove bookings that won't be used anymore
        self._delete_subbookings(subbooking_ids_to_delete)

        created = not self.id

        if is_booksy_gift_card_appointment is not None:
            self.is_booksy_gift_card_appointment = is_booksy_gift_card_appointment

        if updated_by:
            self.updated_by = updated_by

        from webapps.booking.timeslots.v1.draw_dispatchers import (
            AppointmentDrawDispatcher,
        )

        dispatcher = AppointmentDrawDispatcher(
            appointment=self,
            subbookings=subbookings,
            # resource_required=False allows to skip DrawForOverbookingException
            # in GroupBooking
            resource_required=resource_required,
        )

        try:
            with business_context(self.business):
                validated_bookings = dispatcher.validate_draw_resources(
                    subbookings=subbookings,
                    validate=who_makes_change == Who.CUSTOMER,
                    force_overbooking=overbooking,
                )
        except BookingConflict as exc:
            BusinessVersion.release_booking_lock(self.business_id, booking_lock)
            raise exc

        self._set_booked_and_deleted_date(validated_bookings, save=created)
        self.last_edit = tznow()
        for booking in validated_bookings:
            booking.appointment = self
            # update _version of each booking
            booking.last_edit = self.last_edit
            booking.save(override=True)
            if not booking.combo_children:
                booking.combo_children_set.update(deleted=self.last_edit)
            for child in booking.combo_children:
                child.appointment = self
                child.last_edit = self.last_edit
                child.combo_parent = booking
                child.save(override=True)

        self._set_booked_and_deleted_date()
        self._update_resources(validated_bookings)
        self.subbookings_cache_clear()

        # update txn.charge_date
        if self.booked_till:
            self.transactions.update(charge_date=self.booked_till)

        BusinessVersion.release_booking_lock(self.business_id, booking_lock)
        # local django event
        appointment_changed_event.send(
            self.id,
            created=created,
            previous_status=prev_status,
            modified_subbookings_id=[s.id for s in validated_bookings],
        )
        # pubsub event
        self.send_appointment_changed_message_if_needed(prev_status)

    def _update_group_bookings(self, updated_by, who_makes_change, overbooking):
        """coupled repeating method; remove after RepeatingBookingDecoupling"""
        # only booked_from/till/status are allowed to be updated spreading to all appointments
        # no staffers, repeat_number can be updated
        appointments = list(
            self.repeating.extra_appointments_unlimited.prefetch_related(
                'bookings',
            ).exclude(status=Appointment.STATUS.CANCELED)
        )

        force_cancel = self.status == Appointment.STATUS.CANCELED
        prototype_booking = self.first_booking
        booked_from = prototype_booking.booked_from
        status = (
            SubBooking.compute_new_status(
                booked_till=prototype_booking.booked_till,
                base_status=self.status,
            )
            if not force_cancel
            else Appointment.STATUS.CANCELED
        )

        bookings_list = []
        for appointment in appointments:
            booking = appointment.first_booking
            booking_duration = booking.booked_till - booking.booked_from
            booked_till = booked_from + booking_duration
            if updated_by:
                appointment.updated_by = updated_by
            appointment.booked_from = booked_from
            appointment.booked_till = booked_till
            appointment.status = status
            booking.booked_from = booked_from
            booking.booked_till = booked_till
            bookings_list.append(booking)

        self.repeating.make_repeating_booking(
            data={},  # correct data already in the instance
            bookings_list=bookings_list,
            deleted_appointments=[],
            updated_by=updated_by,
            _who_makes_change=who_makes_change,
            overbooking=overbooking,
            business=prototype_booking.appointment.business,
            _use_lock=False,
        )

    @staticmethod
    def _update_resources(validated_subbookings):
        leaf_subbookings = list(iter_leaf_services(validated_subbookings))
        combo_parents = [booking for booking in validated_subbookings if booking.combo_children]
        all_subbookings = leaf_subbookings + combo_parents

        if any(s.staffer is AnyResource or s.appliance is AnyResource for s in leaf_subbookings):
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'AnyResource assigned to save'
            )

        BookingResource.objects.filter(subbooking__in=all_subbookings).delete()
        BookingResource.objects.bulk_create(
            BookingResource(subbooking_id=s.id, resource_id=r.id)
            for s in leaf_subbookings
            for r in [s.staffer, s.appliance]
            if r
        )
        for subbooking in all_subbookings:
            for cached_property_name in ('staffer', 'staffer_id', 'appliance', 'appliance_id'):
                if cached_property_name in subbooking.__dict__:
                    del subbooking.__dict__[cached_property_name]

    def _validate_booking_ranges(self, subbookings, overbooking, who_makes_change):
        from webapps.booking.time_slot_tools import (
            BookingRanges,  # pylint: disable=cyclic-import
        )

        omit_errors = (
            {
                BookingRangesErrors.BOOKING_LEADTIME_ERROR,
                BookingRangesErrors.BOOKING_WORKING_HOURS_ERROR,
                BookingRangesErrors.BOOKING_TIMEOFF_ERROR,
                BookingRangesErrors.BOOKING_SERVICE_INACTIVE_ERROR,
                BookingRangesErrors.BOOKING_SLOT_ERROR,
            }
            if who_makes_change in (Who.BUSINESS, Who.STAFF)
            else set()
        )
        if overbooking:
            omit_errors.add(BookingRangesErrors.BOOKING_OVERBOOKING_ERROR)
        validated_bookings, errors = BookingRanges(
            bookings=subbookings,
            business=self.business,
            ignore_invisible_staff=who_makes_change == Who.CUSTOMER,
        ).validate_make_booking(who_makes_change, omit_errors)
        return errors, validated_bookings

    @classmethod
    def update_subbookings_addons(cls, subbookings):
        time_for_update = tznow()
        used_addons_to_update = []
        addons_to_create = []
        for subbooking in iter_leaf_services(subbookings):
            old_used_addons = {
                used_addon.service_addon_id: used_addon
                for used_addon in subbooking.addons_set.all()
            }
            if subbooking.addons:
                for obj in subbooking.addons:
                    if obj.service_addon_id in old_used_addons.keys():
                        used_addon = old_used_addons.pop(obj.service_addon_id)
                        used_addon.quantity = obj.quantity
                        if used_addon.quantity == 0:
                            used_addon.deleted = time_for_update
                            used_addon.updated = time_for_update
                        used_addons_to_update.append(used_addon)
                    else:
                        obj.subbooking_id = subbooking.id
                        addons_to_create.append(obj)

                for addon in old_used_addons.values():
                    addon.deleted = time_for_update
                    addon.updated = time_for_update
                    used_addons_to_update.append(addon)
                subbooking.addons_cache_clear()
            elif old_used_addons and subbooking.addons == []:
                # soft delete
                for addon in old_used_addons.values():
                    addon.deleted = time_for_update
                    addon.updated = time_for_update
                    used_addons_to_update.append(addon)
                subbooking.addons_cache_clear()

        ServiceAddOnUse.objects.bulk_create(addons_to_create)
        ServiceAddOnUse.objects.bulk_update(
            used_addons_to_update, ['quantity', 'deleted', 'updated']
        )

    @classmethod
    def update_subbookings_surcharges(cls, subbookings):
        subbookings_with_children = set(subbookings) | set(iter_leaf_services(subbookings))
        SubBookingSurchargeRepository().update_for_subbookings(
            {subbooking.id: subbooking.surcharge for subbooking in subbookings_with_children}
        )

    def send_appointment_changed_message_if_needed(
        self,
        prev_status: AppointmentStatus,
    ):
        user_data = UserData(key=f'business_{self.business_id}')

        if prev_status != self.status and LoyaltyProgramFlag(user_data):
            AppointmentChangedMessage(self, context={'prev_status': prev_status}).publish()

    @transaction.atomic
    def update_appointment(  # pylint: disable=too-many-branches
        self,
        subbooking=None,
        force_status_transition=False,
        who_makes_change=Who.CUSTOMER,
        **params,
    ):
        """
        A decoupled method replacing `update_appointment_fields`.

        This method should be called on update of any booking attributes, except:
        booked_from, booked_till, resources.
        """
        s_kwargs, a_kwargs = SubBooking.split_params_to_appointment(params)
        if subbooking is None and s_kwargs.keys() - {'deleted'}:
            raise BookingConflict(str(_('Specify subbooking')))

        a_kwargs['updated'] = tools.tznow(tz=self.booked_from.tzinfo)
        booked_till_before = self.booked_till
        booking_lock = BusinessVersion.create_booking_lock(self.business_id)
        prev_status = self.status
        if subbooking:
            bookings_list = [subbooking]
        else:
            # update subbookings (active or deleted)
            bookings_list = self.subbookings or self.bookings.all().order_by(
                *SubBookingQuerySet.bookings_order
            )

        if s_kwargs:
            SubBooking.objects.filter(id__in=(b.id for b in bookings_list)).update(
                updated=a_kwargs['updated'], **SubBooking.split_params_to_appointment(params)[0]
            )
            for booking in bookings_list:
                booking.refresh_from_db()
                booking.appointment = self

        if 'status' in a_kwargs:
            if self.status != a_kwargs['status']:
                a_kwargs['status_changed'] = a_kwargs['updated']

            if not force_status_transition:
                self.status = a_kwargs['status']
                self.verify_appointment_status_transition(
                    who_makes_change=who_makes_change,
                )

        if 'deleted' in a_kwargs:
            self.deleted = a_kwargs['deleted']

        for field, value in a_kwargs.items():
            setattr(self, field, value)
        self.subbookings_cache_clear()
        self._set_booked_and_deleted_date()

        if self.booked_till and self.booked_till != booked_till_before:
            self.transactions.update(charge_date=self.booked_till)

        BusinessVersion.release_booking_lock(self.business_id, booking_lock)
        appointment_changed_event.send(self.id, previous_status=prev_status)
        self.send_appointment_changed_message_if_needed(prev_status)

    @transaction.atomic
    def update_appointment_fields(  # pylint: disable=too-many-branches
        self,
        subbooking=None,
        force_status_transition=False,
        update_future=UpdateFutureBooking.NO,
        who_makes_change=Who.CUSTOMER,
        **params,
    ):
        """
        This method should be called on update of any booking attributes, except:
        booked_from, booked_till, resources.
        """
        s_kwargs, a_kwargs = SubBooking.split_params_to_appointment(params)
        if subbooking is None and s_kwargs.keys() - {'deleted'}:
            raise BookingConflict(str(_('Specify subbooking')))

        a_kwargs['updated'] = tools.tznow(tz=self.booked_from.tzinfo)
        booked_till_before = self.booked_till
        booking_lock = BusinessVersion.create_booking_lock(self.business_id)
        prev_status = self.status
        if subbooking:
            bookings_list = [subbooking]
        else:
            # update subbookings (active or deleted)
            bookings_list = self.subbookings or self.bookings.all().order_by(
                *SubBookingQuerySet.bookings_order
            )

        if s_kwargs:
            SubBooking.objects.filter(id__in=(b.id for b in bookings_list)).update(
                updated=a_kwargs['updated'], **SubBooking.split_params_to_appointment(params)[0]
            )
            for booking in bookings_list:
                booking.refresh_from_db()
                booking.appointment = self

        if 'status' in a_kwargs:
            if self.status != a_kwargs['status']:
                a_kwargs['status_changed'] = a_kwargs['updated']

            if not force_status_transition:
                self.status = a_kwargs['status']
                self.verify_appointment_status_transition(
                    who_makes_change=who_makes_change,
                )

        if 'deleted' in a_kwargs:
            self.deleted = a_kwargs['deleted']

        for field, value in a_kwargs.items():
            setattr(self, field, value)
        self.subbookings_cache_clear()
        self._set_booked_and_deleted_date()

        if update_future != UpdateFutureBooking.SKIP and self.repeating_id:
            # action cancel and type is repeat
            self._update_repeating_bookings(update_future, who_makes_change, bookings_list)

        if self.booked_till and self.booked_till != booked_till_before:
            self.transactions.update(charge_date=self.booked_till)

        BusinessVersion.release_booking_lock(self.business_id, booking_lock)
        appointment_changed_event.send(self.id, previous_status=prev_status)
        self.send_appointment_changed_message_if_needed(prev_status)

    def _set_booked_and_deleted_date(self, subbookings=None, save=True):
        """DEPRECATED. DONT USE OUTSIDE OF Appointment methods"""
        if subbookings is None:
            if subbookings is None:
                subbookings = self.subbookings or self.bookings.all()
        deleted = [b.deleted for b in subbookings]
        # check that list is not empty
        self.deleted = deleted[0] if deleted and all(deleted) else None
        self.booked_from = self.calculate_booked_from(subbookings)
        self.booked_till = self.calculate_booked_till(subbookings)
        if save:
            self.last_edit = tznow()
            self.save()

    def _update_repeating_bookings(self, update_future, who_makes_change, bookings_list):
        if self.repeating.repeat == RepeatType.CUSTOM:
            # TODO: delete all future repeating bookings
            self.repeating.cancel_after_booking(
                booking=self,
                updated_by=self.updated_by,
            )
        elif self.repeating.repeat == RepeatType.GROUP:
            self._update_group_bookings(self.updated_by, who_makes_change, True)
        else:
            self.repeating.split_repeating_by_booking(
                appointment=self,
                update_future=update_future,
                overbooking=True,
                # update_booking shouldn't be rescheduling
                who_makes_change=who_makes_change,
                updated_by=self.updated_by,
                addons_to_update=bookings_list[0].addons,
                resources=[bookings_list[0].staffer, bookings_list[0].appliance],
            )

    def calculate_booked_from(self, bookings=None):
        if bookings is None:
            bookings = self.subbookings
        booked_from = (
            bk.booked_from for bk in bookings if bk.booked_from and (not bk.deleted or self.deleted)
        )
        return min(booked_from, default=self.booked_from)

    def calculate_booked_till(self, bookings=None):
        if bookings is None:
            bookings = self.subbookings
        booked_till = (
            bk.booked_till for bk in bookings if bk.booked_till and (not bk.deleted or self.deleted)
        )
        return max(booked_till, default=self.booked_till)

    def is_multibooking(self):
        return (
            self.id
            and SubBooking.objects.filter(
                appointment_id=self.id,
            ).count()
            > 1
        )

    def __str__(self):
        created = self.created and self.created.strftime('%B %d, %Y %H:%M')
        updated = self.updated and self.updated.strftime('%B %d, %Y %H:%M')
        return f'Appointment id={self.pk}; Created {created}; Update {updated}'

    def get_zoom_meeting(self):
        return getattr(self, 'zoom_meeting', None)

    def refresh_zoom_meeting(self):
        self.refresh_from_db(fields=['zoom_meeting'])

    @property
    def join_meeting_url(self) -> t.Optional[str]:
        zoom_meeting = self.get_zoom_meeting()
        return zoom_meeting and zoom_meeting.join_url

    @property
    def meeting_id(self) -> t.Optional[int]:
        zoom_meeting = self.get_zoom_meeting()
        return zoom_meeting and zoom_meeting.id

    @property
    def external_source(self):
        sources = (
            (consts.GOOGLE, 'google'),
            (consts.YELP, 'yelp'),  # DEPRECATED
            (consts.PARTNERS_GROUPON, 'groupon'),
            (consts.INSTAGRAM, 'instagram'),
            (consts.INSTAGRAM_STAFFER, 'instagram_staffer'),
            (consts.FACEBOOK, 'facebook'),
        )
        for name, source in sources:
            b_s = BookingSources.get_cached(name=name)
            if b_s and b_s.id == self.source_id:
                return source

    @property
    def from_promo(self):
        if not self.booked_for:
            return False
        return (
            self.payable
            and self.booked_for.first_appointment.payable
            and self.booked_for.client_type == BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        )

    def verify_appointment_status_transition(
        self,
        who_makes_change=Who.CUSTOMER,
    ):
        old_status = self.get_dirty_fields().get('status', self.status)
        if (
            self.type != Appointment.TYPE.CUSTOMER
            or self.status == old_status
            or self.status in self.get_possible_statuses(who_makes_change)
        ):
            return True

        raise BookingConflict(
            str(_("This status transition is not allowed.")),
            notices=[
                {
                    'field': 'status',
                    'booking_mode': self.business.booking_mode,
                    'old_status': old_status,
                    'new_status': self.status,
                    'who_makes_change': who_makes_change,
                }
            ],
        )

    def get_possible_statuses(self, who_makes_change=Who.CUSTOMER):
        old_status = self.get_dirty_fields().get('status', self.status)
        impossible_statuses = self.get_impossible_statuses(who_makes_change)
        result = self.get_possible_statuses_for_data(
            create=(self.id is None),
            old_status=old_status,
            booked_from=self.booked_from,
            booked_till=self.booked_till,
            business=self.business,
            who_makes_change=who_makes_change,
            impossible_statuses=impossible_statuses,
        )
        # traveling exception: must be confirmed by business
        if who_makes_change == Who.CUSTOMER and self.traveling_id:
            if old_status in (self.STATUS.UNCONFIRMED, self.STATUS.MODIFIED):
                result.discard(self.STATUS.ACCEPTED)
            elif old_status == self.STATUS.ACCEPTED:
                result.add(self.STATUS.MODIFIED)

        return result

    def is_active(self):
        old_status = self.get_dirty_fields().get('status', self.status)
        return self.is_active_status(old_status)

    @property
    def is_having_deposit(self) -> bool:
        if 'payment_and_deposit' in getattr(self, '_prefetched_objects_cache', {}):
            _, deposit = self._prefetched_objects_cache['payment_and_deposit']
        else:
            _, deposit = self.first_booking.get_payment_and_deposit()
        return bool(deposit)

    @cached_property
    def is_prepaid(self) -> bool:
        expected_codes = (
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
        )

        if not self.id:
            return False
        return self.transactions.filter(receipts__status_code__in=expected_codes).exists()

    # region booksy pay
    # TODO: Remove those properties from Appointment model
    @cached_property
    def is_booksy_pay_eligible(self) -> bool:
        """
        The command checks for Booksy Pay eligibility - for both existing and
        pre-initialized (i.e. during booking - `id` empty) appointment objects.

        IMPORTANT:
        This part of the code is to be refactored soon. The BP eligibility
        logic is to be extracted to a dedicated module (see PN-5).
        """
        from webapps.booksy_pay.utils import is_booksy_pay_eligible

        if 'payment_and_deposit' in getattr(self, '_prefetched_objects_cache', {}):
            payment, _ = self._prefetched_objects_cache['payment_and_deposit']
        else:
            payment, _ = self.first_booking.get_payment_and_deposit()

        if 'paid' in getattr(self, '_prefetched_objects_cache', {}):
            paid = self._prefetched_objects_cache['paid']
        else:
            paid = self.first_booking.paid if self.id else False

        # Trusted clients are not required to issue PP or CF, and so Booksy Pay should be available
        # for them. Hence, the NSP check shall be bypassed. Note that in case of Family & Friends,
        # NSP does not apply ONLY if both the parent and member are trusted, and therefore, the same
        # logic needs to be applicable to BP (see `prepayment_applies` method).
        if self.is_family_and_friends:
            is_trusted_client = (self.booked_by.trusted if self.booked_by else False) and (
                self.booked_for.trusted if self.booked_for else False
            )
        else:
            is_trusted_client = self.booked_for.trusted if self.booked_for else False

        # When an appointment is not created yet (i.e. a Cx is in the booking process),
        # the aggregated `total_value` and `total_type` properties are not populated
        # on the pre-initialized appointment object. Therefore, in order to assess
        # if the appointment will be BP-eligible, it's needed to rely on its subbookings.
        if self.id is None and BooksyPayAvailabilityOnBookingFlag():
            appointment_total_value = sum(
                subbooking.service_price.value
                for subbooking in self.subbookings
                if subbooking.service_price.value
            )
            appointment_total_type = ServicePrice.get_merged_price_type(
                subbooking.service_price.price_type for subbooking in self.subbookings
            )
        else:
            appointment_total_value = self.total_value
            appointment_total_type = self.total_type

        return is_booksy_pay_eligible(
            business_id=self.business_id,
            appointment_status=self.status,
            appointment_service_variants_ids=[
                subbooking.service_variant_id for subbooking in self.subbookings
            ],
            is_appointment_paid=paid,
            is_paid_by_booksy_pay_=self.is_paid_by_booksy_pay,
            booksy_pay_eligible=self.business.booksy_pay_eligible,
            appointment_total_value=appointment_total_value,
            appointment_total_type=appointment_total_type,
            payment=payment,
            is_appointment_prepaid=self.is_prepaid,
            appointment_id=self.id,
            is_booksy_gift_card_appointment=self.is_booksy_gift_card_appointment,
            is_trusted_client=is_trusted_client,
        )

    @cached_property
    def is_booksy_pay_available(self) -> bool:
        return self.business.booksy_pay_available and self.is_booksy_pay_eligible

    @cached_property
    def is_booksy_pay_payment_window_open(self) -> bool:
        from webapps.booksy_pay.utils import is_booksy_pay_payment_window_open

        return self.is_booksy_pay_available and is_booksy_pay_payment_window_open(
            booked_from=self.booked_from,
            booked_till=self.booked_till,
            now=tznow(),
        )

    @cached_property
    def is_paid_by_booksy_pay(self) -> bool:
        from webapps.pos.utils import is_paid_by_booksy_pay

        if 'is_paid_by_booksy_pay' in getattr(self, '_prefetched_objects_cache', {}):
            return self._prefetched_objects_cache['is_paid_by_booksy_pay']

        if not self.id:
            return False

        return is_paid_by_booksy_pay(appointment_id=self.id)

    # endregion booksy pay

    @property
    def has_resolved_service_promotion(self) -> bool:
        allowed_promotion_types = (
            None,
            SERVICE_VARIANT_CLIENT_DISCOUNT,
        )
        return any(
            sbk.resolved_promotion_type not in allowed_promotion_types
            for sbk in iter_leaf_services(self.subbookings)
        )

    def in_deposit_cancel_time(self):
        if self.booked_from is None:
            return None

        if pos := self.business.pos:
            return self.booked_from - pos.deposit_cancel_time > tznow()
        return None

    # pylint: disable=too-many-return-statements
    def can_customer_change(self, is_customer_app: bool = True) -> bool:
        if self.status == AppointmentStatus.PENDING_PAYMENT:
            return False
        if self.repeating and self.repeating.repeat == RepeatType.GROUP:
            return False
        if 'payment_and_deposit' in getattr(self, '_prefetched_objects_cache', {}):
            payment, deposit = self._prefetched_objects_cache['payment_and_deposit']
        else:
            payment, deposit = self.first_booking.get_payment_and_deposit()

        if deposit and not self.in_deposit_cancel_time():
            return False

        if not self.is_active() or not (self.business.active and self.business.visible):
            can_change = False
        else:
            possible_statuses = self.get_possible_statuses(
                who_makes_change=Who.CUSTOMER,
            )

            subbookings_change_allowed = all(
                not s.has_ended() and s.service_variant_id is not None for s in self.subbookings
            )
            appt_change_allowed_based_on_status = (
                Appointment.STATUS.MODIFIED in possible_statuses
                or Appointment.STATUS.UNCONFIRMED in possible_statuses
                or (
                    Appointment.STATUS.ACCEPTED == self.status
                    and self.business.booking_mode == BookingMode.AUTO
                )
            )
            can_change = (
                subbookings_change_allowed
                and appt_change_allowed_based_on_status
                and self.business.check_booking_update_time(self.booked_from)
            )

        if can_change:
            if is_customer_app and self.is_prepaid and not self.has_resolved_service_promotion:
                return True

        if payment:
            return False
        return can_change

    @staticmethod
    def is_active_status(old_status):
        """In this context "active" means that it is changeable."""
        return old_status not in (None, '', *Appointment.STATUSES_NONEDITABLE)

    def _delete_subbookings(self, subbooking_ids):
        """Bookings are ArchiveModel, so they are only marked as deleted."""
        if subbooking_ids:
            now = tools.tznow()
            SubBooking.objects.filter(id__in=subbooking_ids, appointment_id=self.id).update(
                updated=now,
                deleted=now,
            )

    def _previous_user_appointments(self, for_new_instance):
        """Previously created appointments for the customer user.

        :param for_new_instance: is this method called
            while creating a new appointment instance
        """

        if not self.booked_for_id or not self.booked_for.user_id:
            return None
        user_appointments = Appointment.objects.filter(
            booked_for__user_id=self.booked_for.user_id,
        )
        if for_new_instance or not self.created:
            return user_appointments.exclude(
                id=self.id,
            )
        return user_appointments.exclude(
            created__gte=self.created,
        )

    def is_first(self, for_new_instance=False):
        """Is this the first appointment for the customer user.

        :param for_new_instance: is this method called
            while creating a new appointment instance
        """

        previous_user_appointments = self._previous_user_appointments(
            for_new_instance=for_new_instance,
        )
        if previous_user_appointments is None:
            return None
        return not previous_user_appointments.exists()

    def is_crossing(self, for_new_instance=True):
        """Is this a cross booking.

        Cross booking is the FIRST booking at subsequent (not first) merchant.

        :param for_new_instance: is this method called
            while creating a new appointment instance
        """

        previous_user_appointments = self._previous_user_appointments(
            for_new_instance=for_new_instance,
        )
        if previous_user_appointments is None:
            return None

        is_first_for_business = not previous_user_appointments.filter(
            business_id=self.business_id,
        ).exists()

        # first for business and not first for user
        return is_first_for_business and previous_user_appointments.exists()

    def is_crossing_and_cross_category(self, for_new_instance=True):
        """Is this booking crossing businesses and categories.

        :param for_new_instance: is this method called
            while creating a new appointment instance
        """
        is_crossing = self.is_crossing(for_new_instance=for_new_instance)
        if not is_crossing:
            return is_crossing  # handling both False and None

        # we already know it's not None
        previous_user_appointments = self._previous_user_appointments(
            for_new_instance=for_new_instance,
        )
        is_first_for_category = not previous_user_appointments.filter(
            business__primary_category__id=self.business.primary_category_id,
        ).exists()
        # we already know it's not first for user
        return is_first_for_category

    def is_first_crossing(self, for_new_instance=True):
        """Is this the first cross booking for the customer user.

        :param for_new_instance: is this method called
            while creating a new appointment instance
        """

        previous_user_appointments = self._previous_user_appointments(
            for_new_instance=for_new_instance,
        )
        if previous_user_appointments is None:
            return None

        is_first_for_business = not previous_user_appointments.filter(
            business_id=self.business_id,
        ).exists()

        # Using a getter to utilize lazy evaluation in the next statement.
        def get_is_second_for_user():
            return previous_user_appointments.values('business_id').distinct().count() == 1

        return is_first_for_business and get_is_second_for_user()

    def is_first_for_business(self):  # TODO remove - deprecated - PXMAR-1164
        """Is this first booking for customer user for this business."""
        # TODO This method should be in BCI interface (with appointment_id as a parameter) MAR-1013
        new_customer = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        return (
            Appointment.objects.filter(
                id=self.id,
                bci_as_first_appointment__id=self.booked_for_id,
                bci_as_first_appointment__client_type=new_customer,
            ).exists()
            if self.booked_for_id
            else None
        )

    def is_only_cb_finished_for_business(self):
        """Is this the only finished customer booking for business"""
        if (
            self.type != Appointment.TYPE.CUSTOMER
            or self.status != AppointmentStatusChoices.FINISHED
        ):
            return False

        qs = Appointment.objects.filter(
            type=Appointment.TYPE.CUSTOMER,
            business_id=self.business_id,
            status=self.status,
        ).exclude(
            id=self.id,
        )

        return not qs.exists()

    def is_only_cb_finished_for_customer(self):
        """Is this the only finished customer booking for customer"""
        if (
            self.type != Appointment.TYPE.CUSTOMER
            or self.status != AppointmentStatusChoices.FINISHED
            or not self.booked_for
            or not self.booked_for.user
        ):
            return False

        qs = Appointment.objects.filter(
            type=Appointment.TYPE.CUSTOMER,
            booked_for__user_id=self.booked_for.user.id,
            status=self.status,
        ).exclude(
            id=self.id,
        )

        return not qs.exists()

    @using_db_for_reads(READ_ONLY_DB)
    def is_first_cb_for_business(self):
        """Is this first customer booking for business"""

        if not self.type == Appointment.TYPE.CUSTOMER:
            return False

        qs = Appointment.objects.filter(
            type=Appointment.TYPE.CUSTOMER,
            business_id=self.business_id,
        )
        if self.id:
            qs = qs.exclude(id__gte=self.id)
        return not qs.exists()

    @using_db_for_reads(READ_ONLY_DB)
    def is_first_cb_for_customer(self):
        """Is this first booking for customer user."""

        if not self.booked_for_id or not self.booked_for.user_id:
            return None

        qs = Appointment.objects.filter(
            booked_for__user_id=self.booked_for.user_id,
            type=Appointment.TYPE.CUSTOMER,
        )
        if self.id:
            qs = qs.exclude(id__gte=self.id)
        return not qs.exists()

    def set_chargeable_payable(self):
        prev_appointment_payable = self.payable

        self.chargeable = self.get_chargeable()
        self.payable = self.get_payable()

        # save to db without triggering signals
        Appointment.objects.filter(id=self.id).update(
            chargeable=self.chargeable,
            payable=self.payable,
        )

        return prev_appointment_payable

    def set_chargeable(self):
        prev_appointment_chargeable = self.chargeable
        self.chargeable = self.get_chargeable()
        if prev_appointment_chargeable == self.chargeable:
            return

        # save to db without triggering signals
        Appointment.objects.filter(id=self.id).update(chargeable=self.chargeable)

    def set_subbookings_chargeable_payable(self):  # to be removed after BOOS-309
        from webapps.marketplace.models import MarketplaceCommission

        is_appointment_paid_by_pos = MarketplaceCommission.is_appointment_paid_by_pos(self)

        for booking in self.bookings.filter(combo_parent__isnull=True):
            # ^ combo children are not entitled to be payable
            prev_chargeable, prev_payable = booking.chargeable, booking.payable

            if self.chargeable:
                booking.chargeable = (
                    (
                        is_appointment_paid_by_pos
                        and (
                            booking.combo_children_set.exists()
                            or bool(MarketplaceCommission.get_pos_transaction(booking))
                        )
                        # combo parents and subbookings paid by pos are entitled to be payable,
                        # when appt is paid by pos
                    )
                    or (
                        not is_appointment_paid_by_pos
                        and not booking.deleted
                        # `deleted` is decisive when appt is not paid by pos
                    )
                    # in other cases booking is not payable
                )
            else:
                booking.chargeable = False

            booking.payable = self.payable and booking.chargeable

            if booking.chargeable != prev_chargeable or booking.payable != prev_payable:
                # save to db without triggering signals
                SubBooking.objects.filter(id=booking.id).update(
                    chargeable=booking.chargeable,
                    payable=booking.payable,
                )

    def update_boost_objects(self, prev_payable):
        """assuming 'payable' flags for appointment is already updated"""
        from webapps.boost.models import BoostAppointment

        if self.payable and self.status == AppointmentStatus.FINISHED:
            boost_appointment = BoostAppointment.get_or_create_by_appointment(self)
            boost_appointment.update_amount()
        elif prev_payable and not self.payable:
            BoostAppointment.disable_payable_objects(self)

    def get_chargeable(self, additional_chargeable_statuses=None):
        from webapps.marketplace.models import BoostClientCard

        chargeable_statuses = {
            Appointment.STATUS.FINISHED,
            *Appointment.STATUSES_OCCUPYING_TIME_SLOTS,
            *(additional_chargeable_statuses or []),
        }

        if not self.booked_for_id:
            return False

        originally_first_appointment = (
            self.booked_for.originally_first_appointment
            or self
            # self - it is the case, when the very first appt of this client is created
            # and not saved yet
        )
        chargeable = bool(
            originally_first_appointment.type == Appointment.TYPE.CUSTOMER
            and originally_first_appointment.source.chargeable
            and self.booked_for.client_type == BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
            and self.booked_for.first_appointment_id == self.id
            and self.status in chargeable_statuses
        )

        if chargeable:
            boost_card_status = (
                BoostClientCard.objects.filter(client_card=self.booked_for)
                .values_list('status', flat=True)
                .first()
            )
            if boost_card_status and boost_card_status == BoostClientCardStatus.EXEMPTED:
                return False

        return chargeable

    def get_payable(self):
        if not self.booked_for_id:
            return False
        originally_first_appointment = self.booked_for.originally_first_appointment
        return bool(
            self.chargeable
            and self.business.has_promotion(date=originally_first_appointment.created)
        )

    def get_chargeable_for_analytics(self):
        return self.get_chargeable(
            additional_chargeable_statuses=[Appointment.STATUS.NOSHOW],
        )

    @staticmethod
    def _check_lead_times(create, booked_from, business, who_makes_change=Who.CUSTOMER):
        """
        Check booking dates with business lead times.
        This works only for customer change, business and system are always
        valid.
        :param who_makes_change: 'c' or 'b' or 's'
        :return: bool
        """
        if who_makes_change == Who.CUSTOMER:
            if create:
                return business.check_booking_create_time(booked_from)
            return business.check_booking_update_time(booked_from)

        return True

    def get_impossible_statuses(self, who_makes_change: str) -> t.Tuple[AppointmentStatus, ...]:
        impossible_statuses = ()
        if who_makes_change == Who.BUSINESS and self.id and self.payable:
            if any(hasattr(subbooking, 'review') for subbooking in self.subbookings):
                impossible_statuses = (Appointment.STATUS.NOSHOW, Appointment.STATUS.CANCELED)

        return impossible_statuses

    @classmethod
    def get_possible_statuses_for_data(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        cls,
        create,
        old_status,
        booked_from,
        booked_till,
        business,
        who_makes_change=Who.CUSTOMER,
        impossible_statuses: t.Tuple[AppointmentStatus, ...] = None,
    ):
        """
        Return list of possible statuses reachable from previous status.
        """
        now = tools.tznow()
        if not cls._check_lead_times(
            create=create,
            booked_from=booked_from,
            business=business,
            who_makes_change=who_makes_change,
        ):
            ret = {old_status}
            if cls.is_active_status(old_status):
                # always allow cancel from active status
                ret.add(cls.STATUS.CANCELED)

            # HACK: we allow accepting of proposed bookings even when
            #       the lead times are over (see #15655)
            if (
                who_makes_change == Who.CUSTOMER
                and old_status == cls.STATUS.PROPOSED
                and cls.is_active_status(old_status)
                and not booked_till <= now
            ):
                ret.update((cls.STATUS.ACCEPTED, cls.STATUS.MODIFIED, cls.STATUS.CANCELED))

            # Hack customer can also set noshow insted of cancel
            # Ticket: 48703
            if (
                who_makes_change == Who.CUSTOMER  # pylint: disable=using-constant-test
                and booked_from <= now <= booked_till
                and old_status != cls.STATUS.FINISHED
            ):
                ret.add(cls.STATUS.NOSHOW)

            return {status for status in ret if status not in (impossible_statuses or [])}

        ret = SubBooking.get_allowed_transitions(
            old_status=old_status,
            booking_mode=business.booking_mode,
            who_makes_change=who_makes_change,
        )

        # Some status changes require that booking be active
        booking_active = Appointment.is_active_status(old_status) and not booked_till <= now

        # for new booking this is ignored
        if not booking_active and not create:
            lst = {
                Who.BUSINESS: (Appointment.STATUS_CHANGE_NEEDS_ACTIVE_BY_BUSINESS),
                Who.CUSTOMER: (Appointment.STATUS_CHANGE_NEEDS_ACTIVE_BY_CUSTOMER),
            }.get(who_makes_change, [])
            ret.difference_update(lst)

        if (
            who_makes_change == Who.BUSINESS
            and booked_from <= now
            and old_status
            not in [
                Appointment.STATUS.CANCELED,
                Appointment.STATUS.UNCONFIRMED,
            ]
        ):
            ret.add(Appointment.STATUS.NOSHOW)

        return {status for status in ret if status not in (impossible_statuses or [])}

    def get_appointment_sms_deeplink(self, sms_source):
        if self.booked_for and self.booked_for.user:
            customer_email = self.booked_for.user.email or ''
            customer_phone = self.booked_for.user.cell_phone or ''
        else:
            customer_email = self.customer_email or ''
            customer_phone = self.customer_phone or ''

        customer_phone = parse_phone_number(customer_phone).smart_short

        return BookingMixin.get_appointment_sms_deeplink(
            appointment_id=self.id,
            source=sms_source,
            customer_email=customer_email,
            customer_phone=customer_phone,
            secret=self.secret,
        )

    @property
    def service_data(self):
        """Merged service_data from all subbookings"""
        return merge_service_data([subbooking.service_data for subbooking in self.subbookings])

    @cached_property
    def prepayment_deadline(self):
        if not self.id or self.status != Appointment.STATUS.PENDING_PAYMENT:
            return None
        return (
            (self.created + timedelta(hours=consts.HOURS_TO_PAY_FOR_PREPAYMENT))
            .astimezone(self.business.get_timezone())
            .isoformat()[:16]
        )


class RepeatingBooking(ArchiveModel, VersionedModel):
    MAX_EXTRA_BOOKINGS = 30
    DELTAS = REPEAT_DELTAS

    id = models.AutoField(primary_key=True, db_column='repeating_id')
    repeat = models.CharField(max_length=1, choices=RepeatType.choices())
    # used only if self.repeat != self.REPEAT__CUSTOM
    end_type = models.CharField(
        max_length=1,
        choices=RepeatEndType.choices(),
        default=RepeatEndType.AFTER_N_BOOKINGS,
        null=True,
    )
    # used only if self.end_type == self.END__ON_DATE
    repeat_till = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Repeat till (UTC)',
    )
    # used only if self.end_type == self.END__AFTER_N_BOOKINGS
    repeat_number = models.PositiveSmallIntegerField(null=True, blank=True)

    # to able to do something like
    # rb = RepeatingBooking(); rb.children.all()
    parent = models.ForeignKey(
        'self',
        related_name='children',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    class Meta:
        verbose_name = 'Repeating appointment'

    def __str__(self):
        return (
            f'{self.__class__.__name__} id={self.id} repeat={self.repeat} '
            f'end={self.end_type} till={self.repeat_till} number={self.repeat_number}'
        ).format(self)

    @property
    def is_deleted(self):
        return self.deleted is not None

    @property
    def first_appointment(self):
        if self.is_deleted:
            return self.extra_appointments_unlimited.first()
        return self.appointments.first()

    @property
    def extra_appointments(self):
        """In RepeatingBooking all not deleted appointmes are active"""
        if self.is_deleted:
            # return empty qs if repeating is deleted
            return Appointment.objects.none()
        return self.appointments.filter(
            deleted__isnull=True,
        ).order_by(
            'booked_from'
        )[: self.MAX_EXTRA_BOOKINGS]

    @property
    def extra_appointments_unlimited(self):
        qs = self.extra_appointments
        # remove MAX_EXTRA_BOOKINGS limit
        qs.query.high_mark = None
        return qs

    def number_future_appointments(self, from_date):
        return (
            self.appointments.exclude(
                status=Appointment.STATUS.CANCELED,
            )
            .filter(deleted__isnull=True, booked_from__gte=from_date)
            .count()
        )

    @property
    def business(self):
        return self.first_appointment.business

    @cached_property
    def get_start_end_deleted(self):
        """
        Return start and and of deleted repeating.
        But booking is still alive
        :return: two element tuple,
            booked_from of first booking and booked_til of last
            Can be None if no bookings left i query set
        """
        qs = self.appointments.filter(deleted__isnull=True).order_by('booked_from')
        if not qs.exists():
            return None, None
        first_appointment = qs.first()
        booked_till = qs.values_list('booked_till', flat=True).last()
        tz = first_appointment.business.get_timezone()

        return (first_appointment.booked_from.astimezone(tz), booked_till.astimezone(tz))

    @transaction.atomic
    def update_repeating_booking(  # pylint: disable=too-many-branches,too-many-positional-arguments,too-many-arguments
        self,
        prototype: Appointment,
        subbooking=None,
        force_status_transition=False,
        update_future=UpdateFutureBooking.NO,
        who_makes_change=Who.CUSTOMER,
        **params,
    ):
        """
        This is a copy of Appointment.update_appointment_fields
        to allow replacing it with decoupled Appointment.update_appointment

        After fully switching RepeatingBookingDecoupling flag
        refactor it further.

        This method should be called on update of any booking attributes, except:
        booked_from, booked_till, resources.
        """
        s_kwargs, a_kwargs = SubBooking.split_params_to_appointment(params)
        if subbooking is None and s_kwargs.keys() - {'deleted'}:
            raise BookingConflict(str(_('Specify subbooking')))

        a_kwargs['updated'] = tools.tznow(tz=prototype.booked_from.tzinfo)
        booked_till_before = prototype.booked_till
        booking_lock = BusinessVersion.create_booking_lock(prototype.business_id)
        prev_status = prototype.status
        if subbooking:
            bookings_list = [subbooking]
        else:
            # update subbookings (active or deleted)
            bookings_list = prototype.subbookings or prototype.bookings.all().order_by(
                *SubBookingQuerySet.bookings_order
            )

        if s_kwargs:
            SubBooking.objects.filter(id__in=(b.id for b in bookings_list)).update(
                updated=a_kwargs['updated'], **SubBooking.split_params_to_appointment(params)[0]
            )
            for booking in bookings_list:
                booking.refresh_from_db()
                booking.appointment = prototype

        if 'status' in a_kwargs:
            if prototype.status != a_kwargs['status']:
                a_kwargs['status_changed'] = a_kwargs['updated']

            if not force_status_transition:
                prototype.status = a_kwargs['status']
                prototype.verify_appointment_status_transition(
                    who_makes_change=who_makes_change,
                )

        if 'deleted' in a_kwargs:
            prototype.deleted = a_kwargs['deleted']

        for field, value in a_kwargs.items():
            setattr(prototype, field, value)
        prototype.subbookings_cache_clear()
        prototype._set_booked_and_deleted_date()

        if update_future != UpdateFutureBooking.SKIP:
            # action cancel and type is repeat
            self._appointment_update_repeating_bookings(
                prototype, update_future, who_makes_change, bookings_list
            )

        if prototype.booked_till and prototype.booked_till != booked_till_before:
            prototype.transactions.update(charge_date=prototype.booked_till)

        BusinessVersion.release_booking_lock(prototype.business_id, booking_lock)
        appointment_changed_event.send(prototype.id, previous_status=prev_status)
        prototype.send_appointment_changed_message_if_needed(prev_status)

    def _appointment_update_repeating_bookings(
        self, prototype: Appointment, update_future, who_makes_change, bookings_list
    ):
        if self.repeat == RepeatType.CUSTOM:
            # TODO: delete all future repeating bookings
            self.cancel_after_booking(
                booking=prototype,
                updated_by=prototype.updated_by,
            )
        elif self.repeat == RepeatType.GROUP:
            self.update_group_booking(prototype, prototype.updated_by, who_makes_change, True)
        else:
            self.split_repeating_by_booking(
                appointment=prototype,
                update_future=update_future,
                overbooking=True,
                # update_booking shouldn't be rescheduling
                who_makes_change=who_makes_change,
                updated_by=prototype.updated_by,
                addons_to_update=bookings_list[0].addons,
                resources=[bookings_list[0].staffer, bookings_list[0].appliance],
            )

    # pylint: disable=too-many-positional-arguments, too-many-branches, too-many-statements, cyclic-import, too-many-arguments
    @transaction.atomic
    def make_repeating_booking(
        self,
        data,
        bookings_list,
        deleted_appointments,
        updated_by,
        _who_makes_change,
        overbooking,
        business,
        _use_lock=True,
    ):
        """
        When updating any booking attributes besides booked_from,
        booked_till and resources rather use 'update' method.

        `bookings_list` - list of pairs (booking, resources_list)
        """
        from webapps.zoom.models import ZoomMeeting

        if _use_lock:
            booking_lock = BusinessVersion.create_booking_lock(business.id)

        # save RepeatingBooking object
        data.pop('_version', None)
        tools.set_model_attributes(self, data)
        last_edit = tznow()
        # bump _version
        self.last_edit = last_edit
        self.save()

        # first delete some bookings
        SubBooking.delete_appointments(deleted_appointments, updated_by)

        # create or update rest of the bookings
        bookings_list = sorted(bookings_list, key=lambda x: x.booked_from)

        resource_required = True
        if self.repeat == RepeatType.GROUP:
            from webapps.booking.timeslots.v1.draw_dispatchers import (
                GroupBookingDrawDispatcher,
            )

            dispatcher_class = GroupBookingDrawDispatcher
            resource_required = False
        else:
            from webapps.booking.timeslots.v1.draw_dispatchers import (
                RepeatingBookingDrawDispatcher,
            )

            dispatcher_class = RepeatingBookingDrawDispatcher

        dispatcher = dispatcher_class(
            repeating=self,
            subbookings=bookings_list,
            resource_required=resource_required,
        )
        try:
            with business_context(business):
                validated_bookings = dispatcher.validate_draw_resources(
                    subbookings=bookings_list,
                    validate=not overbooking,
                    force_overbooking=overbooking,
                )
        except BookingConflict as e:
            if _use_lock:
                BusinessVersion.release_booking_lock(business.id, booking_lock)
            raise e

        appointments_to_create = []
        appointments_to_update = []
        bookings_to_create = []
        bookings_to_update = []

        for booking in validated_bookings:
            booking.appointment.repeating = self
            booking.appointment.repeating_id = self.id
            booking.last_edit = last_edit

            booking.appointment._set_booked_and_deleted_date(
                [booking],
                save=False,
            )
            booking.appointment.last_edit = last_edit

            if booking.id:
                booking.updated = last_edit
                booking.appointment.updated = last_edit
                appointments_to_update.append(booking.appointment)
                bookings_to_update.append(booking)
            else:
                appointments_to_create.append(booking.appointment)
                bookings_to_create.append(booking)

        if appointments_to_create:
            self._create_appointments(appointments_to_create, bookings_to_create)

        if appointments_to_update:
            self._update_appointments(appointments_to_update, bookings_to_update)

        BookingResource.objects.filter(subbooking__in=validated_bookings).delete()
        BookingResource.objects.bulk_create(
            BookingResource(subbooking_id=s.id, resource_id=r.id)
            for s in validated_bookings
            for r in [s.staffer, s.appliance]
            if r
        )

        appointment_ids = set()
        for booking in validated_bookings:
            ZoomMeeting.create_meeting_for_booking_if_necessary(booking)
            appointment_ids.add(booking.appointment_id)
        for id_ in appointment_ids:
            repeated_appointment_changed_event.send(
                None, sender_model='booking.Appointment', sender_id=id_
            )

        # update _version
        self.save()

        if _use_lock:
            BusinessVersion.release_booking_lock(business.id, booking_lock)

        return self

    @staticmethod
    def _validate_booking_ranges(subbookings, business, overbooking, who_makes_change):
        from webapps.booking.time_slot_tools import (
            BookingRanges,  # pylint: disable=cyclic-import
        )

        omit_errors = (
            {
                BookingRangesErrors.BOOKING_LEADTIME_ERROR,
                BookingRangesErrors.BOOKING_SERVICE_INACTIVE_ERROR,
                BookingRangesErrors.BOOKING_SLOT_ERROR,
            }
            if who_makes_change == Who.BUSINESS
            else set()
        )

        if overbooking:
            omit_errors |= {
                BookingRangesErrors.BOOKING_OVERBOOKING_ERROR,
                BookingRangesErrors.BOOKING_WORKING_HOURS_ERROR,
                BookingRangesErrors.BOOKING_TIMEOFF_ERROR,
            }

        validated_bookings, errors = BookingRanges(
            bookings=subbookings,
            business=business,
            ignore_invisible_staff=who_makes_change == Who.CUSTOMER,
        ).validate_make_booking(who_makes_change, omit_errors)
        return errors, validated_bookings

    @staticmethod
    def _create_appointments(appointments_to_create, bookings_to_create):
        Appointment.objects.bulk_create(appointments_to_create)
        for booking in bookings_to_create:
            booking.appointment_id = booking.appointment.id
        SubBooking.objects.bulk_create(bookings_to_create)
        addons_to_create = []
        for booking in bookings_to_create:
            for addon in booking.addons or []:
                addon.subbooking_id = booking.id
                addons_to_create.append(addon)
            booking.addons_cache_clear()
        ServiceAddOnUse.objects.bulk_create(addons_to_create)

    @staticmethod
    def _update_appointments(appointments_to_update, bookings_to_update):
        appointment_dirty_fields = {
            field.name for field in appointments_to_update[0]._meta.fields
        } - {'id', 'created'}

        Appointment.objects.bulk_update(
            appointments_to_update,
            appointment_dirty_fields,
        )
        booking_fields = {field.name for field in bookings_to_update[0]._meta.fields} - {
            'id',
            'created',
        }
        if booking_fields:
            SubBooking.objects.bulk_update(
                bookings_to_update,
                booking_fields,
            )

        # update or create(during editing new addons can be added) addons of bookings
        Appointment.update_subbookings_addons(bookings_to_update)

    @staticmethod
    def get_repeat_text(first_booking: SubBooking, repeating: 'RepeatingBooking', language=None):
        """Get canonical repeating booking text.

        :param first_booking: a first_booking of a RepeatingBooking
            or a BookingChange of a first_booking.
        :param repeating: a RepeatingBooking or a BookingChange
            of first_booking of a RepeatingBooking

        """
        if first_booking.appointment.repeating_id is None:
            return None

        info = repeating.repeating_series_info
        # info is empty, when repeating is deleted
        if not info:
            return None

        # info is empty for group booking
        if info.repeat == RepeatType.GROUP:
            return None

        start_date = format_datetime(info.starting_on, 'date_ymd', language)
        end_date = format_datetime(info.ending_on, 'date_ymd', language) if info.ending_on else None

        repeat_phrase = REPEATED_PHRASES_SHORT[info.repeat]
        dates_phrase = (
            _(
                'from %(start_date)s to %(end_date)s'
                % {'start_date': start_date, 'end_date': end_date}
            )
            if end_date is not None
            else _(
                'from %(start_date)s, never ends'  # pylint: disable=consider-using-f-string
                % {'start_date': start_date}
            )
        )
        return format_lazy(
            '{repeat_phrase}, {dates_phrase}',
            repeat_phrase=repeat_phrase,
            dates_phrase=dates_phrase,
        )

    def replan_repeating(self):
        """Refresh RepeatingBooking extra_bookings based on policy changes.

        This is triggered when:
        * business.booking_max_lead_time changes
        * every day when booking_max_lead_time window allows new bookings

        """
        if self.deleted or self.repeat in [RepeatType.CUSTOM, RepeatType.GROUP]:
            # RepeatingBooking was canceled, so replan is disabled
            return None, 0

        # get current extra bookings
        if hasattr(self, '_prefetched_extra_appointments_unlimited'):
            extra_appointments = self._prefetched_extra_appointments_unlimited
        else:
            extra_appointments = list(
                self.extra_appointments_unlimited.select_related(
                    'business',
                ).prefetch_related(
                    'bookings',
                )
            )
        if not extra_appointments:
            return None, 0

        return self._replan_appointments(extra_appointments)

    def make_plan(self, extra_appointments):
        """Makes a new repeating plan based on first_booking.

        Shouldn't use DB.

        """
        from service.booking.repeating_matching import RepeatingMatcher

        first_appointment = extra_appointments[0]
        business = first_appointment.business
        tz = business.get_timezone()
        # calculate how many extra_bookings are needed
        delta = self.DELTAS[self.repeat]
        end_date = RepeatingMatcher.calculate_end_date(
            booked_from=first_appointment.booked_from.astimezone(tz),
            delta=delta,
            end_type=self.end_type,
            repeat_till=self.repeat_till and self.repeat_till.astimezone(tz),
            repeat_number=self.repeat_number,
            booking_max_lead_time=business.booking_max_lead_time,
            tz=tz,
        )
        duration = relativedelta(
            seconds=(first_appointment.booked_till - first_appointment.booked_from).total_seconds()
        )
        current = [
            {
                'id': bk.id,
                'booked_from': bk.booked_from.astimezone(tz),
                'booked_till': bk.booked_till.astimezone(tz),
            }
            for bk in extra_appointments
        ]
        replanned = current[:1] + RepeatingMatcher.prepare_draft(
            start_date=first_appointment.booked_from.astimezone(tz),
            end_date=end_date,
            delta=delta,
            duration=duration,
            extra_appointments=current[1:],  # do not include first_appointment
        )
        return replanned

    def _replan_appointments(self, extra_appointments):
        # EXECUTE REPLAN
        replanned = self.make_plan(extra_appointments)
        # SHRINK, EXPAND OR IGNORE
        if len(extra_appointments) == len(replanned):
            return None, 0
        if len(extra_appointments) > len(replanned):
            to_remove_appts = extra_appointments[len(replanned) :]
            to_remove = [b for a in to_remove_appts for b in a.bookings.all()]
            SubBooking.delete_appointments(to_remove_appts, get_system_user())
            return "shrinked", len(to_remove)
        # len(extra_bookings) < len(replanned):
        to_clone = replanned[len(extra_appointments) :]
        new_appointments = self.clone_appointments(extra_appointments[-1], to_clone)
        # ensure reminder tasks exists
        for new_appointment in new_appointments:
            repeated_appointment_expanded_signal.send(new_appointment)
            start_scenario(
                BookingChangedScenario,
                appointment_id=new_appointment.id,
                action=None,  # only do the reminders
                repeating_use_all=False,  # process single appointment
                send_booking_created_analytics=True,
            )
        return "expanded", len(to_clone)

    @classmethod
    @transaction.atomic
    def clone_appointments(cls, src, draft_appointments):
        now = tznow()
        system_user = get_system_user()
        first_booking = src.first_booking
        appointments = []
        for draft_appointment in draft_appointments:
            appointment, subbooking = cls.clone_appointment(
                src,
                first_booking,
                draft_appointment,
            )
            appointment.status = Appointment.STATUS.ACCEPTED
            appointment.created = appointment.updated = now
            appointment.updated_by = system_user
            subbooking.created = subbooking.updated = now
            subbooking.staffer = first_booking.staffer  # noqa
            subbooking.appliance = first_booking.appliance  # noqa
            subbooking.appointment.make_appointment(
                [subbooking],
                who_makes_change=Who.STAFF,
                overbooking=True,
            )
            appointments.append(appointment)
        return appointments

    @staticmethod
    def clone_appointment(src_appointment, src_subbooking, draft, repeat_type=None, **kwargs):
        """Create a subbooking with appointment based on src subbooking
         and dates from draft.

        Use kwargs to optionally pass preexisting 'id' or 'created' date,
        but also to overwrite ones from src.

        """
        appointment_attrs = {
            'id': kwargs.get('id'),
            # from draft
            'booked_from': draft['booked_from'],
            'booked_till': draft['booked_till'],
            # from src
            'status': src_appointment.status,
            'business_id': src_appointment.business_id,
            'repeating_id': src_appointment.repeating_id,
            'source_id': src_appointment.source_id,
            'booked_for_id': src_appointment.booked_for_id,
            'type': src_appointment.type,
            'business_note': src_appointment.business_note,
            'business_secret_note': src_appointment.business_secret_note,
            'customer_note': src_appointment.customer_note,
            'customer_name': src_appointment.customer_name,
            'customer_email': src_appointment.customer_email,
            'customer_phone': src_appointment.customer_phone,
            'updated_by': src_appointment.updated_by,
        }
        subbooking_attrs = {
            'id': kwargs.pop('subbooking_id', None),
            'appointment_id': appointment_attrs['id'],
            'created': kwargs.get('created'),
            'service_variant_id': src_subbooking.service_variant_id,
            'service_name': src_subbooking.service_name,
            'autoassign': src_subbooking.autoassign,
            'is_staffer_requested_by_client': src_subbooking.is_staffer_requested_by_client,
            'is_highlighted': (
                src_subbooking.is_highlighted if repeat_type == RepeatType.GROUP else False
            ),
            # from draft
            'booked_from': draft['booked_from'],
            'booked_till': draft['booked_till'],
        }

        appointment_attrs['total_value'] = src_appointment.total_value
        appointment_attrs['total_type'] = src_appointment.total_type
        appointment_attrs['total_discount'] = src_appointment.total_discount

        # possibly overwrite some
        appointment_attrs.update(kwargs)
        if repeat_type == RepeatType.GROUP and src_appointment.service_questions:
            appointment_attrs['service_questions'] = src_appointment.service_questions.copy()
        appointment = Appointment(
            **appointment_attrs,
        )
        subbooking = SubBooking(appointment=appointment, **subbooking_attrs)
        subbooking.staffer = src_subbooking.staffer  # noqa
        subbooking.appliance = src_subbooking.appliance  # noqa

        subbooking.service_data = src_subbooking.service_data

        addons = []
        for addon in src_subbooking.addons or []:
            addons.append(
                addon_use_make(
                    addon=addon.service_addon,
                    quantity=addon.quantity,
                )
            )
        subbooking.addons = addons
        return appointment, subbooking

    def cancel_after_booking(self, booking, updated_by):
        """
        Cancel all repeating bookings after given booking
        If no booking left will delete repeatin
        :param booking:
        :param updated_by:
        :return: None
        """
        from_date = booking.booked_from
        appointments_id = (
            self.extra_appointments_unlimited.filter(
                booked_from__gte=from_date,
            )
            .values_list('id', flat=True)
            .distinct()
        )
        # cancel all bookings
        # do not use deleted
        # because bookings must
        # be visible in repeating history
        now = tznow()
        SubBooking.objects.filter(appointment_id__in=appointments_id).update(
            updated=now,
        )
        Appointment.objects.filter(id__in=appointments_id).update(
            updated_by=updated_by,
            status=Appointment.STATUS.CANCELED,
            updated=now,
        )
        repeat_number = self.number_future_appointments(from_date)
        if not self.extra_appointments_unlimited.count():
            self.deleted = tznow()
            self.repeat_number = 0
        else:
            self.repeat_number = repeat_number
        self.save()

    def trim_to_appointment_id(self, appointment_id):
        """Trim repeating definition to selected booking.

        Returns a list of unneeded bookings to unconnect (delete).
        Use this method only in new_repeating case - in all other cases
        use the split_repeating_by_booking.
        new_repeating is a simpler case - instead of split, we trim
        old repeating to the left side of booking_id and return right side
        for removal during new_repeating creation.

        """
        appointments = list(self.extra_appointments_unlimited.all().only('id'))
        appointment_ids = [bk.id for bk in appointments]
        if appointment_id not in appointment_ids:
            return []
        index = appointment_ids.index(appointment_id)  # it's ordered by booked_from
        # bookings before booking_id are kept in existing repeating
        to_keep = appointments[:index]
        # bookings after booking_id can be unconnected or deleted
        to_unconnect = appointments[index + 1 :]

        if len(to_keep) <= 1:
            # no or one bookings left - simply delete the repeating booking
            self.soft_delete()

            # and if there is one - make it single booking again
            if to_keep:
                single = to_keep[0]
                Appointment.all_objects.filter(id=single.id).update(
                    repeating_id=None,
                )
            return to_unconnect

        # calculate a new definition
        if self.repeat == RepeatType.CUSTOM:
            pass  # nothing is needed - it stays custom
        elif self.end_type == RepeatEndType.ON_DATE:
            self.repeat_till = to_keep[-1].booked_till  # DB call
        else:  # RepeatingBooking.END__AFTER_N_BOOKINGS or NEVER
            self.end_type = RepeatEndType.AFTER_N_BOOKINGS
            self.repeat_number = len(to_keep)
        self.save()

        return to_unconnect

    def update_group_booking(
        self,
        prototype: Appointment,
        updated_by,
        who_makes_change,
        overbooking,
    ):
        """
        will replace coupled appointment._update_group_bookings method
        after RepeatingBookingDecoupling flag
        """
        # only booked_from/till/status are allowed to be updated spreading to all appointments
        # no staffers, repeat_number can be updated
        appointments = list(
            self.extra_appointments_unlimited.prefetch_related(
                'bookings',
            ).exclude(status=Appointment.STATUS.CANCELED)
        )

        force_cancel = prototype.status == Appointment.STATUS.CANCELED
        prototype_booking = prototype.first_booking
        booked_from = prototype_booking.booked_from
        status = (
            SubBooking.compute_new_status(
                booked_till=prototype_booking.booked_till,
                base_status=prototype.status,
            )
            if not force_cancel
            else Appointment.STATUS.CANCELED
        )

        bookings_list = []
        for appointment in appointments:
            booking = appointment.first_booking
            booking_duration = booking.booked_till - booking.booked_from
            booked_till = booked_from + booking_duration
            if updated_by:
                appointment.updated_by = updated_by
            appointment.booked_from = booked_from
            appointment.booked_till = booked_till
            appointment.status = status
            booking.booked_from = booked_from
            booking.booked_till = booked_till
            bookings_list.append(booking)

        self.make_repeating_booking(
            data={},  # correct data already in the instance
            bookings_list=bookings_list,
            deleted_appointments=[],
            updated_by=updated_by,
            _who_makes_change=who_makes_change,
            overbooking=overbooking,
            business=prototype_booking.appointment.business,
            _use_lock=False,
        )

    @transaction.atomic
    def split_repeating_by_booking(  # pylint: disable=too-many-positional-arguments, too-many-branches, too-many-statements, too-many-arguments
        self,
        appointment,
        update_future,
        overbooking=False,
        who_makes_change=Who.BUSINESS,
        updated_by=None,
        addons_to_update=None,
        resources=None,
    ):
        """Split repeating into two new ones.
           One of the cases when splitting is needed - when merchant wants to modify some part
           of appointments. For example: today 16.01, Business has EVERY_DAY repeating bookings
           and wants to modify future bookings but, let's say, only from 20.01.

        :param appointment: focal booking that was modified and requires
                        repeating booking to split
        :param update_future: should future bookings be updated with
                              changed elements of current booking?
        :param overbooking: if update_future, we make_repeating_booking
        :param who_makes_change: if update_future, we make_repeating_booking
        :param updated_by: if update_future, we make_repeating_booking
        :param addons_to_update: list of addons to make copy and apply to repeated bookings
        :param resources: staffer, appliance pair selected by user (can be AnyResource)

        if update_furure is False:
            repeating is split into [oldRepeating, booking, newRepeating]
        else:
            repeating is split into [oldRepeating, newRepeatingIncludingBooking]

        """
        if update_future and not (updated_by and resources):
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'When we update_future we need all the data needed to call '
                'RepeatingBooking.make_repeating_booking()'
            )

        if self.repeat in (RepeatType.CUSTOM, RepeatType.GROUP) or self.is_deleted:
            # do not split custom repeating or Group Booking
            # or when drag booking that was repeating
            # and repeating deleted
            return

        tz = appointment.business.get_timezone()
        appointments = list(
            self.extra_appointments_unlimited.prefetch_related(
                'bookings',
            ).all()
        )
        appointment_ids = [bk.id for bk in appointments]
        index = appointment_ids.index(appointment.id)  # it's ordered by booked_from

        # bookings before given booking are kept in oldRepeating
        to_keep = appointments[:index]

        # bookings after given booking will be moved to a newRepeating
        to_new = appointments[index + 1 :]

        # prepare unsaved newRepeating
        new_repeat_number = None
        if self.end_type == RepeatEndType.AFTER_N_BOOKINGS:
            # can't use len(to_new)
            # because not all bookings are already materialized - see .replan()
            new_repeat_number = self.repeat_number - len(to_keep) - (0 if update_future else 1)
        new_repeating = RepeatingBooking(
            repeat=self.repeat,  # CUSTOM already excluded
            end_type=self.end_type,  # stays the same
            repeat_till=self.repeat_till,  # stays the same
            repeat_number=new_repeat_number,
        )
        # trim oldRepeating
        if len(to_keep) > 1:
            if self.end_type == RepeatEndType.ON_DATE:
                self.repeat_till = to_keep[-1].booked_till
            else:  # RepeatingBooking.END__AFTER_N_BOOKINGS or NEVER
                self.end_type = RepeatEndType.AFTER_N_BOOKINGS
                self.repeat_number = len(to_keep)
            self.save()
        elif len(to_keep) == 1:
            # single booking left - transform to single booking
            single = to_keep[0]
            single.repeating = None
            single.save(update_fields=['repeating'])

            self.soft_delete()
        else:
            # no bookings left - delete repeating
            self.soft_delete()

        # assign appropriate repeating values to bookings
        if not update_future:
            # make given booking single again
            appointment.repeating = None
            appointment.save(update_fields=['repeating'])

            # handle bookings after the given one
            if not to_new:
                return  # nothing to do

            actual_repeats = (
                new_repeating.get_start_end([bk.booked_from for bk in to_new], tz=tz)[2]
                if new_repeating.end_type != RepeatEndType.NEVER
                else 1000  # a lot :)
            )
            if actual_repeats is not None and actual_repeats > 1:
                # save the new_repeating
                new_repeating.save()

                # set to_new bookings with newRepeating instance
                Appointment.objects.filter(id__in={appt.id for appt in to_new}).update(
                    repeating=new_repeating,
                )
            else:
                # single booking left - transform to single booking
                appointment = to_new[0]
                appointment.repeating = None
                appointment.save(update_fields=['repeating'])
            return  # we are done!

        # update the future! by calling a repeating planner...
        extra_appointments = [appointment] + to_new
        replanned = new_repeating.make_plan(extra_appointments)
        force_cancel = appointment.status == Appointment.STATUS.CANCELED
        booking = appointment.first_booking
        booking.addons = addons_to_update if addons_to_update else booking.addons
        booking.staffer, booking.appliance = resources
        bookings_list = [
            # the prototype booking
            booking,
        ] + [
            # repeated clones
            new_repeating.clone_appointment(
                appointment,
                booking,
                draft,
                id=appt.id,
                subbooking_id=appt.first_booking_id,
                created=appt.created,
                status=(
                    SubBooking.compute_new_status(
                        booked_till=draft['booked_till'],
                        base_status=appt.status,
                    )
                    if not force_cancel
                    else Appointment.STATUS.CANCELED
                ),
            )[1]
            for appt, draft in zip(to_new, replanned[1:])  # zip not izip_longest
        ]

        data = {}
        if new_repeat_number is not None and new_repeat_number < 1:
            # if self.repeat_number is not sync with real number of bookings
            # left it lead to negative new_repeat_number
            data['repeat_number'] = len(bookings_list)

        new_repeating.make_repeating_booking(
            data=data,  # correct data already in the instance
            bookings_list=bookings_list,
            deleted_appointments=to_new[len(replanned[1:]) :],  # delete if shrinked
            updated_by=updated_by,
            _who_makes_change=who_makes_change,
            overbooking=overbooking,
            business=appointment.business,
            _use_lock=False,  # it's being used within lock already
        )

        child = self.children.order_by('-created').first()
        if child is not None:
            child.parent = new_repeating
            child.save(update_fields=['parent'])
            from webapps.booking.tasks import update_repeating_by_parent_booking

            update_repeating_by_parent_booking.delay(
                child_id=child.id,
                who_makes_change=who_makes_change,
                updated_by_id=updated_by.id,
                resource_ids=[res.id if res else res for res in resources],
            )

    def get_start_end(self, booking_dates=None, tz=None):
        if self.is_deleted:
            start, end = self.get_start_end_deleted
            return start, end, 0

        if tz is None:
            tz = self.business.get_timezone()
        if booking_dates is None:
            booking_dates = list(
                self.extra_appointments_unlimited.values_list('booked_from', flat=True)
            )

        return calculate_repeating_start_end(
            tz=tz,
            booking_dates=booking_dates,
            repeat=self.repeat,
            end_type=self.end_type,
            repeat_till=self.repeat_till,
            repeat_number=self.repeat_number,
        )

    @transaction.atomic
    def split_too_long_repeating(self):
        """If MAX_EXTRA_BOOKINGS limit is reached, split the repeating booking."""
        if self.deleted:
            # RepeatingBooking was canceled, so replan is disabled
            return False
        if self.repeat in [RepeatType.CUSTOM, RepeatType.GROUP]:
            return False
        # get current extra bookings
        if hasattr(self, '_prefetched_extra_appointments_unlimited'):
            extra_appointments = self._prefetched_extra_appointments_unlimited
        else:
            extra_appointments = list(
                self.extra_appointments_unlimited.select_related('business').prefetch_related(
                    'bookings',
                )
            )
        if len(extra_appointments) < self.MAX_EXTRA_BOOKINGS:
            return False

        # EXECUTE SPLIT
        index = self.MAX_EXTRA_BOOKINGS - 5

        # bookings before given booking are kept in oldRepeating
        to_keep = extra_appointments[:index]

        # rest will be moved to a newRepeating
        to_new = extra_appointments[index:]

        # prepare unsaved newRepeating
        new_repeat_number = None
        if self.end_type == RepeatEndType.AFTER_N_BOOKINGS:
            # can't use len(to_new)
            # because not all bookings are already materialized - see .replan()
            new_repeat_number = self.repeat_number - len(to_keep)
        new_repeating = RepeatingBooking(
            repeat=self.repeat,  # CUSTOM already excluded
            end_type=self.end_type,  # stays the same
            repeat_till=self.repeat_till,  # stays the same
            repeat_number=new_repeat_number,
            parent=self,  # add child parent relation
        )

        # trim oldRepeating
        if self.end_type == RepeatEndType.ON_DATE:
            self.repeat_till = to_keep[-1].booked_till
        else:  # RepeatingBooking.END__AFTER_N_BOOKINGS or NEVER
            self.end_type = RepeatEndType.AFTER_N_BOOKINGS
            self.repeat_number = len(to_keep)
        self.save()
        # save the new_repeating
        new_repeating.save()

        # set to_new appointments with newRepeating instance
        Appointment.objects.filter(id__in={bk.id for bk in to_new}).update(
            repeating=new_repeating,
        )
        return True

    def get_series(self, using=DEFAULT_DB_ALIAS):
        """
        Series of RepeatingBooking instances related by parent/child current instance is part of.
        """
        sql_text = """
        WITH RECURSIVE booking_children AS (
                SELECT *, 0 AS position
                    FROM booking_repeatingbooking
                    WHERE repeating_id = %s AND deleted IS NULL
                UNION ALL
                SELECT child.*, parent.position + 1 AS position
                    FROM booking_repeatingbooking child
                JOIN booking_children parent
                    ON child.parent_id = parent.repeating_id
                    WHERE child.deleted IS NULL
                ),
        booking_parents AS (
                SELECT *, 0 AS position
                    FROM booking_repeatingbooking
                    WHERE repeating_id = %s AND deleted IS NULL
                UNION ALL
                SELECT parent.*, child.position - 1 AS position
                    FROM booking_repeatingbooking parent
                JOIN booking_parents child
                    ON child.parent_id = parent.repeating_id
                    WHERE parent.deleted IS NULL
                )
        SELECT DISTINCT repeating_id as id, *
        FROM booking_children
        UNION ALL 
        SELECT DISTINCT repeating_id as id, *
        FROM booking_parents
        WHERE repeating_id != %s
         ORDER BY position ASC;
        """
        return list(RepeatingBooking.objects.using(using).raw(sql_text, [self.id] * 3))

    @staticmethod
    def get_appointments_count(repeating_ids):
        return Appointment.objects.filter(
            deleted__isnull=True,
            repeating__in=repeating_ids,
        ).count()

    @property
    def repeating_info(self):
        return RepeatingInfo.from_repeating_booking(self)

    @property
    def repeating_series_info(self):
        if series := self.get_series():
            return RepeatingInfo.from_repeating_series(series)
        return None


class AppointmentChange(TimestampedModel):
    public_api_data_hash = models.CharField(
        max_length=32,
        null=True,
        blank=True,
    )
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.DO_NOTHING,
        null=False,
    )

    class Meta:
        get_latest_by = 'created'
        indexes = [
            models.Index(
                fields=['appointment', 'public_api_data_hash', '-created'],
                name='appt_public_api_created_idx',
            ),
        ]


class BookingChange(models.Model):
    """
    New approach to BookingHistory - this time new record will be inserted
    by API not by PostgreSQL trigger.
    """

    BY_SYSTEM = 'S'
    BY_BUSINESS = 'B'
    BY_CUSTOMER = 'C'

    BY_TYPES = (
        (BY_SYSTEM, _("By System")),
        (BY_BUSINESS, _("By Business")),
        (BY_CUSTOMER, _("By Customer")),
    )

    id = models.AutoField(primary_key=True)
    created = models.DateTimeField(
        db_index=True,
        verbose_name='Created (UTC)',
    )

    changed_by = models.CharField(max_length=1, choices=BY_TYPES)
    changed_user_id = models.IntegerField(default=0, null=True)
    changed_user_email = models.EmailField(max_length=75, null=True, blank=True)

    subbooking = models.ForeignKey(
        SubBooking,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    appointment = models.ForeignKey(
        Appointment,
        null=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        db_index=True,
        verbose_name='Appointment',
    )
    repeating = models.ForeignKey(
        RepeatingBooking,
        null=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        db_index=False,
    )

    booked_from = models.DateTimeField(verbose_name='Booked from (UTC)')
    booked_till = models.DateTimeField(verbose_name='Booked till (UTC)')
    status = models.CharField(
        max_length=1,
        choices=AppointmentStatusChoices.choices(),
    )

    business_id = models.IntegerField(null=True, blank=True)
    service_variant_id = models.IntegerField(default=0, null=True)
    service_name = models.CharField(max_length=50, null=True, blank=True)
    autoassign = models.BooleanField(help_text='first available staffer was selected by the system')
    staffers_ids = ArrayField(models.IntegerField(), default=list)
    appliances_ids = ArrayField(models.IntegerField(), default=list)
    customer_id = models.IntegerField(default=0, null=True)
    customer_email = models.EmailField(max_length=75, null=True, blank=True)
    customer_phone = BooksyPhoneNumberField(null=True, blank=True)
    customer_name = models.CharField(
        null=True, blank=True, max_length=(consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN + 1)
    )

    customer_note = models.CharField(
        max_length=consts.CUSTOMER_NOTE__MAX_LENGTH,
        null=True,
        blank=True,
    )
    business_note = models.CharField(
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
        null=True,
        blank=True,
    )
    business_secret_note = models.CharField(
        max_length=consts.BUSINESS_SECRET_NOTE__MAX_LENGTH,
        null=True,
        blank=True,
    )

    # RepeatingBooking stuff
    repeat = models.CharField(
        max_length=1,
        null=True,
        blank=True,
        choices=RepeatType.choices(),
    )
    end_type = models.CharField(
        max_length=1,
        null=True,
        blank=True,
        choices=RepeatEndType.choices(),
        default=RepeatEndType.AFTER_N_BOOKINGS,
    )
    repeat_till = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Repeat till (UTC)',
    )
    repeat_number = models.PositiveSmallIntegerField(null=True, blank=True)

    metadata = models.TextField()

    resolved_promotion = models.ForeignKey(
        'business.ServicePromotion',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        db_index=False,
        default=None,
    )
    resolved_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )
    resolved_promotion_type = models.CharField(
        choices=RESOLVED_BOOKING_PROMOTION_TYPES,
        max_length=2,
        blank=True,
        null=True,
        default=None,
    )
    resolved_discount = models.IntegerField(
        blank=True,
        null=True,
        default=None,
    )

    service_price_type = models.CharField(
        max_length=1,
        null=True,
        blank=True,
        choices=PriceType.choices(),
    )

    service_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = 'Sub booking change'

    def __str__(self):
        return (
            f'id={self.id} booking_id={self.subbooking_id} status={self.status} {self.booked_from}'
        )

    @property
    def staffer(self):
        if not self.staffers_ids:
            return None
        return Resource.objects.get(pk=self.staffers_ids[0])

    @property
    def service_variant(self):
        if not self.service_variant_id:
            return None
        return ServiceVariant.objects.get(pk=self.service_variant_id)

    @classmethod
    def add(  # pylint: disable=too-many-positional-arguments, too-many-arguments
        cls,
        some_booking,
        changed_by=None,
        changed_user=None,
        metadata=(),
        handler=None,
        change_created_at=None,
    ):
        """Add new entries to BookingChange basing on a booking."""
        from webapps.booking.appointment_wrapper import AppointmentWrapper

        # downgrade from AppointmentWrapper
        if isinstance(some_booking, AppointmentWrapper):
            some_booking = some_booking.appointment

        skip_external_update = False
        if isinstance(some_booking, SubBooking):
            skip_external_update = some_booking.appointment.source.name == MIGRATED_PARTNER
        elif isinstance(some_booking, Appointment):
            skip_external_update = some_booking.source.name == MIGRATED_PARTNER

        booking_list = cls._get_subbookings_for_booking(some_booking)
        if not booking_list:
            return

        changed_user_id = changed_user and changed_user.id
        changed_user_email = changed_user and changed_user.email
        change_created_at = change_created_at or some_booking.updated

        customer_data_dict = booking_list[0].get_customer_data_dict(get_counts=False)

        calculated_metadata = {}
        if handler is not None:
            calculated_metadata.update(get_meta_data_from_handler(handler))
        calculated_metadata.update(metadata)

        omit_booking_ids = cls.get_non_editable_booking_ids(booking_list)

        try:
            cls.objects.bulk_create(
                [
                    cls.single_booking_change(
                        booking,
                        change_created_at,
                        changed_by,
                        changed_user_id,
                        changed_user_email,
                        calculated_metadata,
                        customer_data_dict,
                    )
                    for booking in booking_list
                    if booking.id not in omit_booking_ids
                ]
            )
        except IntegrityError:
            # extremely rare IntegrityError that needs further investigation
            # DETAIL:  Key (booking_id)=(707262) is not present
            #          in table "booking_subbooking".
            _bk_change_log.exception('BookingChange.add error')
        if not skip_external_update:
            update_to_external_partners(
                EventType.BOOKING,
                business=booking_list[0].appointment.business,
                booking_list=booking_list,
                omit_booking_list=omit_booking_ids,
            )

    @staticmethod
    def _get_subbookings_for_booking(
        booking: SubBooking | Appointment | RepeatingBooking,
    ) -> list[SubBooking]:
        # upgrade from Booking
        if isinstance(booking, SubBooking):
            if (
                # upgrade to RepeatingBooking - if not deleted
                booking.appointment.repeating_id is not None
                and not booking.appointment.repeating.is_deleted
            ):
                booking = booking.appointment.repeating

        # extract booking_list
        if isinstance(booking, Appointment):
            # extract all subbookings
            result = list(
                booking.bookings.select_related(
                    'appointment',
                    'service_variant',
                    'service_variant__service',
                ).prefetch_related(
                    'resources',
                )
            )
        elif isinstance(booking, RepeatingBooking):
            if booking.is_deleted:
                return []
            # extract all bookings in a series
            qset = booking.extra_appointments_unlimited.prefetch_related(
                Prefetch(
                    'bookings',
                    queryset=SubBooking.objects.select_related(
                        'appointment',
                        'service_variant',
                        'service_variant__service',
                    ).prefetch_related(
                        'resources',
                    ),
                )
            )
            result = [b for appointment in qset for b in appointment.subbookings]
        else:
            result = [booking]

        return result

    @staticmethod
    def get_non_editable_booking_ids(booking_list):
        if len(booking_list) > 1:
            statuses = Appointment.STATUSES_NONEDITABLE
            omit_booking_ids = frozenset(
                booking_change.subbooking_id
                for booking_change in BookingChange.objects.filter(
                    status__in=Appointment.STATUSES_NONEDITABLE,
                    subbooking_id__in=(
                        b.id for b in booking_list if b.appointment.status in statuses
                    ),
                )
                .only('subbooking_id')
                .all()
            )
        else:
            omit_booking_ids = frozenset()
        return omit_booking_ids

    @classmethod
    def single_booking_change(  # pylint: disable=too-many-positional-arguments, too-many-arguments
        cls,
        booking,
        booking_updated,
        changed_by,
        changed_user_id,
        changed_user_email,
        calculated_metadata,
        customer_data_dict,
    ):
        """Add new entry to BookingChange basing on booking"""

        resources = list(booking.resources.all())

        service_price_type = None
        service_price = None
        if not BookingChange.objects.filter(subbooking_id=booking.id).exists():
            # that's enough to add it during Booking creation
            if booking.service_variant:
                service_price_type = booking.service_variant.type
                service_price = booking.service_variant.price

        entry = cls(
            created=booking_updated,
            changed_by=changed_by,
            changed_user_id=changed_user_id,
            changed_user_email=changed_user_email,
            subbooking=booking,
            appointment_id=booking.appointment_id,
            business_id=booking.appointment.business_id,
            repeating=booking.appointment.repeating,
            booked_from=booking.booked_from,
            booked_till=booking.booked_till,
            status=booking.appointment.status,
            service_variant_id=booking.service_variant_id,
            service_name=(
                booking.service_name
                if booking.service_variant is None
                else booking.service_variant.service.name
            ),
            service_price_type=service_price_type,
            service_price=service_price,
            autoassign=booking.autoassign,
            staffers_ids=[res.id for res in resources if res.type == Resource.STAFF],
            appliances_ids=[res.id for res in resources if res.type == Resource.APPLIANCE],
            customer_id=booking.appointment.booked_for_id,
            customer_email=customer_data_dict['email'],
            customer_name=customer_data_dict['name'],
            customer_phone=customer_data_dict['phone'],
            customer_note=booking.appointment.customer_note,
            business_note=booking.appointment.business_note,
            business_secret_note=booking.appointment.business_secret_note,
            # RepeatingBooking stuff
            repeat=(
                booking.appointment.repeating.repeat if booking.appointment.repeating else None
            ),
            end_type=(
                booking.appointment.repeating.end_type if booking.appointment.repeating else None
            ),
            repeat_till=(
                booking.appointment.repeating.repeat_till if booking.appointment.repeating else None
            ),
            repeat_number=(
                booking.appointment.repeating.repeat_number
                if booking.appointment.repeating
                else None
            ),
            metadata=safe_json.dumps(calculated_metadata),
        )
        return entry


class BookingResource(TimestampedModel):
    """A trough model between Booking and Resource."""

    subbooking = models.ForeignKey(
        SubBooking,
        related_name='bookingresources',
        on_delete=models.CASCADE,
    )
    resource = models.ForeignKey(
        'business.Resource',
        related_name='bookingresources',
        on_delete=models.CASCADE,
    )

    class Meta:
        verbose_name = 'Sub booking resource'


class AppointmentTraveling(ArchiveModel):
    id = models.AutoField(primary_key=True, db_column='booking_traveling_id')
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        default=None,
    )
    address_line_1 = models.CharField(
        _('address line 1'),
        blank=True,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    address_line_2 = models.CharField(
        _('address line 2'),
        blank=True,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    apartment_number = models.CharField(
        _('Apartment number'),
        blank=True,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    city = models.CharField(max_length=100, blank=True)
    zipcode = models.CharField(max_length=20, blank=True)
    latitude = models.FloatField(
        null=True,
        blank=True,
        validators=[validate_latitude],
    )
    longitude = models.FloatField(
        null=True,
        blank=True,
        validators=[validate_longitude],
    )

    @property
    def traveling_price(self) -> t.Optional[ServicePrice]:
        if self.price is not None:
            return ServicePrice(
                value=self.price, price_type=PriceType.FIXED if self.price > 0 else PriceType.FREE
            )
        return None


class AppointmentAnalytics(ArchiveModel):
    appointment = models.ForeignKey(
        'booking.Appointment', on_delete=models.CASCADE, related_name='analytics'
    )
    booking_source = models.CharField(max_length=100, blank=True, null=True)
    traffic_source = models.CharField(max_length=100, blank=True, null=True)
    traffic_channel = models.CharField(max_length=100, blank=True, null=True)
    traffic_initial_referrer = models.CharField(max_length=100, blank=True, null=True)
    traffic_referrer = models.CharField(max_length=100, blank=True, null=True)
    task_id = models.CharField(max_length=100, blank=True, null=True)


class NoShowLostMoney(ArchiveModel):
    business = models.ForeignKey(
        'business.Business',
        related_name='no_show_lost_money',
        on_delete=models.DO_NOTHING,
    )
    total_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=False,
        editable=False,
    )
    calculation_period = models.DateField(
        null=False,
    )  # its DateField but in fact the period we are interested in are year-month compilation
    # for simplicity, we use it date for easier comparison, we will create records
    # with first day of a given month that we run calculations
    no_show_visits_count = models.IntegerField(null=False)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=('business', 'calculation_period'),
                name='noshowlostmoney_business_calculation_period_unique',
            ),
        ]
