from dataclasses import dataclass
from datetime import datetime
from typing import (
    Dict,
    List,
)

from dataclasses_avroschema import AvroModel

from lib.payments.enums import PaymentProviderCode


@dataclass
class Requirements(AvroModel):
    currently_due: List[str]
    past_due: List[str]
    eventually_due: List[str]
    current_deadline: datetime | None
    disabled_reason: str | None


@dataclass
class Capabilities(AvroModel):
    currently_due: List[str]
    past_due: List[str]
    eventually_due: List[str]
    current_deadline: datetime | None
    disabled_reason: str | None


@dataclass
class ConnectedAccountUpdatedEvent(AvroModel):  # pylint: disable=too-many-instance-attributes
    business_id: int
    payment_provider_code: PaymentProviderCode
    created: datetime

    payouts_enabled: bool
    charges_enabled: bool

    requirements: Requirements
    capabilities: Dict[str, str]


@dataclass
class ConnectedAccountUpdatedEventKey(AvroModel):
    business_id: int
