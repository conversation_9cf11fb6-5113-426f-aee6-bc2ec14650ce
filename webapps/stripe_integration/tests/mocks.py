import json
import typing
from collections import OrderedDict
from datetime import datetime

import mock
import stripe
from dateutil.tz import gettz
from django.conf import settings

from django.utils.crypto import get_random_string

from webapps.stripe_integration.enums import (
    StripePaymentIntentStatus,
    StripeExternalAccountType,
    StripePayoutMethodType,
    StripePaymentIntentMetadata,
)
from webapps.stripe_integration.models import StripeAccount


### Functions declared here should be used as decorators
from webapps.stripe_integration.tests.data_samples import (
    sample_reader_action_success,
    sample_reader_action_in_progress,
)


def mock_stripe_account_create(obj):
    # https://stripe.com/docs/api/accounts/create?lang=python

    def effect(*args, **kwargs):
        resp = stripe.Account(id=get_random_string(12))
        resp.charges_enabled = False
        resp.payouts_enabled = False
        return resp

    return mock.patch('stripe.Account.create', side_effect=effect)(obj)


def mock_stripe_account_delete(obj):
    # https://stripe.com/docs/api/accounts/delete?lang=python

    def effect(*args, **kwargs):
        return {"id": "acct_1032D82eZvKYlo2C", "object": "account", "deleted": True}

    return mock.patch('stripe.Account.delete', side_effect=effect)(obj)


def mock_stripe_balancetransaction_list(obj):
    # https://stripe.com/docs/api/balance_transactions/list?lang=python
    def effect(*args, **kwargs):
        class DummyBalanceTransaction(stripe.BalanceTransaction):
            @classmethod
            def auto_paging_iter(cls, *_, **__):
                transactions = []
                transaction_type = kwargs.get('type')
                account = StripeAccount.objects.get(external_id=kwargs.get('stripe_account'))
                for payout in account.payouts.filter(method=StripePayoutMethodType.STANDARD):
                    for balance_transaction in payout.balance_transactions.all():
                        if balance_transaction.transaction_type == transaction_type:
                            transaction = stripe.BalanceTransaction(
                                id=balance_transaction.external_id
                            )
                            transaction.source = stripe.Payout(id=payout.external_id)
                            transaction.source.created = datetime(
                                2021, 3, 29, tzinfo=gettz('UTC')
                            ).timestamp()
                            transaction.transaction_type = transaction_type
                            transaction.amount = balance_transaction.amount
                            transactions.append(transaction)

                return transactions

        return DummyBalanceTransaction(id=get_random_string(12))

    return mock.patch('stripe.BalanceTransaction.list', side_effect=effect)(obj)


def mock_stripe_transfer_create(obj):
    # https://stripe.com/docs/api/transfers/create?lang=python

    def effect(*args, **kwargs):
        transfer = stripe.Transfer(id=get_random_string(12))
        return transfer

    return mock.patch('stripe.Transfer.create', side_effect=effect)(obj)


def mock_stripe_transfer_for_refund_fee_create(obj):
    # https://stripe.com/docs/api/transfers/create?lang=python

    def effect(*args, **kwargs):
        transfer = stripe.Transfer(
            id=get_random_string(12),
        )
        transfer.destination_payment = 'py_2412516541'
        return transfer

    return mock.patch('stripe.Transfer.create', side_effect=effect)(obj)


def mock_stripe_payout_create(
    payout_id: str = None,
    destination: typing.Union[stripe.Card, stripe.BankAccount, str] = stripe.Card(),
):
    def inner(obj):
        # https://stripe.com/docs/api/payouts/create?lang=python

        def effect(*args, **kwargs):
            payout = stripe.Payout(id=payout_id or get_random_string(12))
            payout.amount = kwargs.get('amount')
            payout.status = 'pending'
            payout.method = 'instant'
            payout.created = **********
            payout.destination = destination
            payout.arrival_date = **********

            return payout

        return mock.patch('stripe.Payout.create', side_effect=effect)(obj)

    return inner


def mock_stripe_payout_retrieve(
    destination: typing.Union[stripe.Card, stripe.BankAccount, str] = stripe.Card(
        id=get_random_string(12)
    ),
):
    def inner(obj):
        # https://stripe.com/docs/api/payouts/retrieve?lang=python

        def effect(*args, **kwargs):
            payout = stripe.Payout(id=kwargs.get('id'))
            payout.amount = 100.00
            payout.status = 'pending'
            payout.method = 'instant'
            payout.created = **********
            payout.destination = destination
            return payout

        return mock.patch('stripe.Payout.retrieve', side_effect=effect)(obj)

    return inner


def mock_stripe_payout_list(obj):
    def effect(stripe_account, *args, **kwargs):
        payouts_list = stripe.ListObject()
        payouts_list.has_more = False
        payouts_list.data = []

        stripe_account = StripeAccount.objects.get(external_id=stripe_account)
        for payout in stripe_account.payouts.all():
            payout_obj = stripe.Payout(id=payout.external_id)
            payout_obj.method = 'standard'
            payout_obj.created = **********
            payout_obj.amount = 1234
            payout_obj.status = 'pending'
            payout_obj.destination = get_test_stripe_card_obj([])
            payouts_list.data.append(payout_obj)

        return payouts_list

    return mock.patch('stripe.Payout.list', side_effect=effect)(obj)


def mock_stripe_balance_retrieve(
    balance_source_type: StripeExternalAccountType,
    amount_instant: int = None,
    amount_available: int = None,
    amount_pending: int = None,
):
    def inner(obj):
        # https://stripe.com/docs/api/balance/balance_retrieve?lang=python

        def effect(*args, **kwargs):
            balance = stripe.Balance(id=get_random_string(12))
            if amount_instant is not None:
                instant = stripe.stripe_object.StripeObject()
                instant.refresh_from(
                    {
                        'amount': amount_instant,
                        'currency': settings.CURRENCY_CODE,
                        'source_types': {
                            balance_source_type: amount_instant,
                        },
                    }
                )
                balance.instant_available = [instant]
            if amount_available is not None:
                available = stripe.stripe_object.StripeObject()
                available.refresh_from(
                    {
                        'amount': amount_available,
                        'currency': settings.CURRENCY_CODE,
                        'source_types': {
                            balance_source_type: amount_available,
                        },
                    },
                )
                balance.available = [available]
            if amount_pending is not None:
                pending = stripe.stripe_object.StripeObject()
                pending.refresh_from(
                    {
                        'amount': amount_pending,
                        'currency': settings.CURRENCY_CODE,
                        'source_types': {
                            balance_source_type: amount_pending,
                        },
                    },
                )
                balance.pending = [pending]
            return balance

        return mock.patch('stripe.Balance.retrieve', side_effect=effect)(obj)

    return inner


def get_test_stripe_card_obj(available_payout_methods=None):
    card = stripe.Card()
    card.brand = "Visa"
    card.exp_month = 8
    card.exp_year = 2023
    card.last4 = "4242"
    card.customer = "text"
    card.name = "Andrzej Jezrdna"
    card.object = StripeExternalAccountType.CARD
    card.available_payout_methods = available_payout_methods or []

    return card


def get_test_bank_account_obj(available_payout_methods=None):
    bank_account = stripe.BankAccount()
    bank_account.account_holder_name = "Jane Austen"
    bank_account.bank_name = "STRIPE TEST BANK"
    bank_account.last4 = "6789"
    bank_account.routing_number = "*********"
    bank_account.object = StripeExternalAccountType.BANK_ACCOUNT
    bank_account.available_payout_methods = available_payout_methods or []

    return bank_account


def mock_stripe_account_retrieve(
    retrieve_object_type: StripeExternalAccountType,
    available_payout_methods: typing.Optional[typing.List[str]] = None,
):
    if available_payout_methods is None:
        available_payout_methods = [StripePayoutMethodType.STANDARD, StripePayoutMethodType.INSTANT]

    def inner(obj):
        # https://stripe.com/docs/api/accounts/retrieve?lang=python

        def effect(*args, **kwargs):
            account = stripe.Account(id=get_random_string(12))

            external_account = None
            if retrieve_object_type == StripeExternalAccountType.CARD:
                external_account = get_test_stripe_card_obj(available_payout_methods)
            elif retrieve_object_type == StripeExternalAccountType.BANK_ACCOUNT:
                external_account = get_test_bank_account_obj(available_payout_methods)

            account.external_accounts = [external_account] if external_account else []
            return account

        return mock.patch('stripe.Account.retrieve', side_effect=effect)(obj)

    return inner


def mock_stripe_account_link_create(obj):
    # https://stripe.com/docs/api/account_links/create?lang=python

    def effect(*args, **kwargs):
        acc_link_resp = stripe.AccountLink()
        acc_link_resp.url = f"https://connect.stripe.com/express/onboarding/{get_random_string(12)}"
        return acc_link_resp

    return mock.patch('stripe.AccountLink.create', side_effect=effect)(obj)


def mock_stripe_reader_create(obj):
    # https://stripe.com/docs/api/terminal/readers/create?lang=python

    def effect(registration_code, location, label):  # pylint: disable=unused-argument
        if registration_code == 'INVALID':
            raise stripe.error.InvalidRequestError("Invalid param", "invalid")

        reader = stripe.terminal.Reader(id=get_random_string(12))
        reader.serial_number = get_random_string(12)
        return reader

    return mock.patch('stripe.terminal.Reader.create', side_effect=effect)(obj)


def mock_stripe_reader_retrieve(obj):
    # https://stripe.com/docs/api/terminal/readers/retrieve?lang=python

    def effect(reader_id):
        if reader_id == 'does_not_exist':
            raise stripe.error.InvalidRequestError("Invalid param", "invalid")

        reader = stripe.terminal.Reader(id=reader_id)
        reader.location = reader_id if reader_id == "correct_location_id" else "incorrect_location"
        reader.label = "old_label"
        return reader

    return mock.patch('stripe.terminal.Reader.retrieve', side_effect=effect)(obj)


def mock_stripe_reader_modify(obj):
    # https://stripe.com/docs/api/terminal/readers/retrieve?lang=python

    def effect(reader_id, label):
        reader = stripe.terminal.Reader(id=reader_id)
        reader.label = label
        return reader

    return mock.patch('stripe.terminal.Reader.modify', side_effect=effect)(obj)


def mock_stripe_reader_list(obj):
    # https://stripe.com/docs/api/terminal/readers/list?lang=python

    def effect(location, limit=10):  # pylint: disable=unused-argument
        reader1 = stripe.terminal.Reader(id=get_random_string(12))
        reader1.action = sample_reader_action_success
        reader1.serial_number = get_random_string(12)
        reader1.location = location
        reader1.device_type = "stripe_m2"

        reader2 = stripe.terminal.Reader(id=get_random_string(12))
        reader2.action = sample_reader_action_in_progress
        reader2.serial_number = get_random_string(12)
        reader2.location = location
        reader2.device_type = "stripe_m2"

        # tap to pay reader - should be filtered out from responses
        reader3 = stripe.terminal.Reader(id=get_random_string(12))
        reader3.action = sample_reader_action_success
        reader3.serial_number = get_random_string(12)
        reader3.location = location
        reader3.device_type = "mobile_phone_reader"

        class Resp:
            pass

        resp = Resp()
        resp.auto_paging_iter = lambda: [reader1, reader2, reader3]
        return resp

    return mock.patch('stripe.terminal.Reader.list', side_effect=effect)(obj)


def mock_stripe_reader_list_empty(obj):
    # https://stripe.com/docs/api/terminal/readers/list?lang=python

    def effect(location, limit=10):  # pylint: disable=unused-argument
        class Resp:
            pass

        resp = Resp()
        resp.auto_paging_iter = lambda: []
        return resp

    return mock.patch('stripe.terminal.Reader.list', side_effect=effect)(obj)


def mock_stripe_location_create(obj):
    # https://stripe.com/docs/api/terminal/locations/create?lang=python

    def effect(*args, **kwargs):
        location = stripe.terminal.Location(id="correct_location_id")
        return location

    return mock.patch('stripe.terminal.Location.create', side_effect=effect)(obj)


def mock_stripe_connection_token_create(obj):
    # https://stripe.com/docs/api/terminal/connection_tokens/create?lang=python

    def effect(location=None):
        token = stripe.terminal.ConnectionToken()
        token.secret = get_random_string(12)
        if location is not None:
            token.secret += "_LOCATION"
        return token

    return mock.patch('stripe.terminal.ConnectionToken.create', side_effect=effect)(obj)


def mock_stripe_payment_intent_create(obj):
    # https://stripe.com/docs/api/payment_intents/create?lang=python

    def effect(*args, **kwargs):
        intent = stripe.PaymentIntent(id=get_random_string(12))
        intent.client_secret = get_random_string(12)
        intent.metadata = {StripePaymentIntentMetadata.TERMINAL_PAYMENT: True}
        return intent

    return mock.patch('stripe.PaymentIntent.create', side_effect=effect)(obj)


def mock_stripe_payment_intent_capture(obj):
    # https://stripe.com/docs/api/payment_intents/capture?lang=python

    def effect(intent_id):
        intent = stripe.PaymentIntent(id=intent_id)
        intent.status = StripePaymentIntentStatus.SUCCEEDED
        intent.metadata = {StripePaymentIntentMetadata.TERMINAL_PAYMENT: True}
        return intent

    return mock.patch('stripe.PaymentIntent.capture', side_effect=effect)(obj)


def mock_stripe_payment_intent_retrieve(obj):
    # https://stripe.com/docs/api/payment_intents/capture?lang=python

    def effect(intent_id):
        intent = stripe.PaymentIntent(id=intent_id)
        intent.client_secret = get_random_string(12)
        intent.metadata = {StripePaymentIntentMetadata.TERMINAL_PAYMENT: True}
        return intent

    return mock.patch('stripe.PaymentIntent.retrieve', side_effect=effect)(obj)


def mock_stripe_payment_intent_retrieve_with_tip(base_amount, tip_amount):
    # https://stripe.com/docs/api/payment_intents/capture?lang=python

    def effect(intent_id):
        intent = stripe.PaymentIntent(id=intent_id)
        intent.client_secret = get_random_string(12)
        intent.metadata = {StripePaymentIntentMetadata.TERMINAL_PAYMENT: True}
        intent.amount_details = {'tip': {'amount': tip_amount}}
        intent.amount = base_amount + tip_amount
        return intent

    return mock.patch('stripe.PaymentIntent.retrieve', side_effect=effect)


def mock_stripe_payment_intent_cancel(obj):
    # https://stripe.com/docs/api/payment_intents/cancel?lang=python

    def effect(intent_id):
        intent = stripe.PaymentIntent(id=intent_id)
        intent.status = StripePaymentIntentStatus.CANCELED
        intent.metadata = {StripePaymentIntentMetadata.TERMINAL_PAYMENT: True}
        return intent

    return mock.patch('stripe.PaymentIntent.cancel', side_effect=effect)(obj)


def mock_stripe_payment_intent_modify(obj):
    # https://stripe.com/docs/api/payment_intents/cancel?lang=python

    def effect(intent_id, **kwargs):
        intent = stripe.PaymentIntent(id=intent_id)
        intent.status = StripePaymentIntentStatus.CANCELED
        intent.metadata = {StripePaymentIntentMetadata.TERMINAL_PAYMENT: True}
        return intent

    return mock.patch('stripe.PaymentIntent.modify', side_effect=effect)(obj)


def mock_stripe_refund_create(obj):
    # https://stripe.com/docs/api/payment_intents/refund?lang=python

    def effect(*args, **kwargs):
        refund = stripe.Refund(id=kwargs['payment_intent'])
        refund.status = StripePaymentIntentStatus.SUCCEEDED
        return refund

    return mock.patch('stripe.Refund.create', side_effect=effect)(obj)


def mock_stripe_webhook_construct_event(obj):
    def effect(payload, sig_header, secret):  # pylint: disable=unused-argument
        if sig_header != "CORRECT":
            raise stripe.error.SignatureVerificationError(
                message="incorrect",
                sig_header="incorrect",
            )

        if hasattr(payload, "decode"):
            payload = payload.decode("utf-8")

        data = json.loads(payload, object_pairs_hook=OrderedDict)

        event = stripe.Event(data.get("id"))

        event.refresh_from(data)

        return event

    return mock.patch('stripe.Webhook.construct_event', side_effect=effect)(obj)


def mock_stripe_cancel_reader_action(obj):
    # https://stripe.com/docs/api/terminal/readers/cancel_action?lang=python

    def effect(*args, **kwargs):
        reader = stripe.terminal.Reader(
            get_random_string(12),
        )
        return reader

    return mock.patch('stripe.terminal.Reader.cancel_action', side_effect=effect)(obj)


def mock_stripe_process_payment_intent(obj):
    # https://stripe.com/docs/api/terminal/readers/process_payment_intent?lang=python

    def effect(*args, **kwargs):
        intent = stripe.PaymentIntent(id=get_random_string(12))
        return intent

    return mock.patch('stripe.terminal.Reader.process_payment_intent', side_effect=effect)(obj)
