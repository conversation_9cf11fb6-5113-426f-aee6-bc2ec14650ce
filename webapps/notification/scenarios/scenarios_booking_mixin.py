import re
import urllib.parse

from django.conf import settings
from django.db.models import Q
from django.utils.encoding import force_str
from django.utils.translation import get_language, activate

from lib.booksy_sms import (
    PhoneNumber,
    parse_phone_number,
    select_mobile_phone_number,
    send_sms,
)
from lib.cache import lru_booksy_cache
from lib.deeplink import (
    BranchIOAppTypes,
    Deeplink,
    DeepLinkCache,
)
from lib.deeplink.utils import encode_url_cell_phone
from lib.email import send_email
from lib.email_internal import is_private_email
from lib.feature_flag.feature import CriticalPY1902Flag
from lib.feature_flag.feature.notification import (
    EnableSendingAdditionalBookingReminderFlag,
    NotifyOwnerThroughEmailAboutNewBookingFlag,
)
from lib.feature_flag.feature.security import BlockSendingAppointmentSmsToRecipients
from lib.serializers import safe_get
from lib.time_24_hour import format_datetime
from lib.tools import dict_merge, tznow
from settings.defaults import NO_REPLY_EMAIL
from webapps.family_and_friends.booking_notifications import (
    get_additional_member_info,
    recipients_data_for_customer_booking_sender,
)
from webapps.kill_switch.models import KillSwitch
from webapps.notification.enums import CustomerBookingReminderTaskTypes
from webapps.notification.scenarios.base import (
    ScenarioSkipped,
    reminder_format,
    reminder_was_sent,
    resolve_sms_priority,
)
from webapps.notification.scenarios.scenarios_booking_variables import (
    BookingVariables,
)
from webapps.notification.scenarios.sms_limits import sms_limits_notification


DONT_SEND_CANCELLATION_EMAIL = 'dont_send_email_cancelation'
DONT_SEND_CANCELLATION_SMS = 'dont_send_sms_cancelation'
NOTIFICATION_ACTION_MAPPING = {
    'changed': 'booking_status_change',
}


class BookingMixin:
    @classmethod
    def get_marketplace_url(cls, parts, params=None):
        """
        Generate link to web-customer. Those deeplinks are defined in file:
        https://gl2.booksy.net:8443/booksy/web-customer-2019/-/blob/live-2021-01-07/src/deeplink-controller.js
        Controller from web-customer check if device is mobile then redirect to
        mobile deeplink in another case it redirect to another web-customer view
        """
        marketplace_url = urllib.parse.urljoin(
            settings.MARKETPLACE_URL,
            '/{}/{}'.format(
                settings.MARKETPLACE_LANG_COUNTRY,
                '/'.join(force_str(i) for i in parts),
            ),
        )
        if params:
            params_string = urllib.parse.urlencode(
                {param: value for param, value in params.items() if value is not None}
            )
            if params_string:
                marketplace_url = '{}?{}'.format(marketplace_url, params_string)

        return marketplace_url

    @classmethod
    def get_formatted_datetime(cls, localized_datetime, language, prefix=''):
        time = format_datetime(localized_datetime, 'time_hm', language)
        date_long = format_datetime(localized_datetime, 'long_date_ymwd', language)
        date_short = format_datetime(localized_datetime, 'date_ymd', language)

        return {
            prefix + 'booking_time': time,
            prefix + 'booking_date': date_long,
            prefix + 'booking_date_long': date_long,
            prefix + 'booking_date_short': date_short,
        }

    @classmethod
    def _get_booking_service_info(cls, booking, timezone):
        localized_booked_from = booking.booked_from.astimezone(timezone)
        result = {
            'booking_from': localized_booked_from,
        }

        return result

    @classmethod
    def get_booking_info_do_booking(cls, booking, language, extra_params):
        from webapps.booking.enums import AppointmentStatusChoices
        from webapps.booking.models import Appointment
        from webapps.notification.base import Channel
        from webapps.user.models import User

        status_name = AppointmentStatusChoices(booking.appointment.status).label
        booking_finished = booking.appointment.status == Appointment.STATUS.FINISHED

        booking_created_by = ''
        created_by_id = (
            booking.bookingchange_set.values_list('changed_user_id', flat=True)
            .order_by('created')
            .first()
        )
        if created_by_id:
            created_by_user = (
                User.objects.filter(
                    id=created_by_id,
                )
                .only('first_name', 'last_name')
                .first()
            )
            if created_by_user:
                booking_created_by = created_by_user.full_name

        booking_last_modified_by = ''
        last_modified_by_id = (
            booking.bookingchange_set.values_list('changed_user_id', flat=True)
            .order_by('created')
            .last()
        )
        if last_modified_by_id == created_by_id:
            booking_last_modified_by = booking_created_by
        elif last_modified_by_id:
            last_modified_by_user = (
                User.objects.filter(
                    id=last_modified_by_id,
                )
                .only('first_name', 'last_name')
                .first()
            )
            if last_modified_by_user:
                booking_last_modified_by = last_modified_by_user.full_name

        if booking.appointment.booked_for and booking.appointment.booked_for.user:
            customer_email = booking.appointment.booked_for.user.email or ''
            customer_phone = booking.appointment.booked_for.user.cell_phone or ''
        else:
            customer_email = booking.appointment.customer_email or ''
            customer_phone = booking.appointment.customer_phone or ''

        if customer_phone:
            customer_phone = encode_url_cell_phone(customer_phone)

        secret = str(booking.appointment.secret or '')

        mobile_url = 'show-appointment/{}/?{}'.format(
            booking.appointment_id,
            urllib.parse.urlencode(
                {
                    'customer_email': customer_email,
                    'customer_phone': customer_phone,
                    'secret': secret,
                }
            ),
        )
        appointment_marketplace_url = cls.get_marketplace_url(
            ['dl', 'appointment'],
            {
                'appointmentUid': booking.appointment_id,
                'appointmentId': booking.appointment_id,
                'email': customer_email,
                'phone': customer_phone,
                'secret': secret,
            },
        )

        appointment_sms_deeplink = cls.get_appointment_sms_deeplink(
            appointment_id=booking.appointment_id,
            source=extra_params.get('source'),
            secret=secret,
            customer_email=customer_email,
            customer_phone=customer_phone,
        )
        appt_type = booking.appointment.type

        if booking.appointment.is_booksy_pay_payment_window_open:
            booksy_pay_appointment_sms_deeplink = cls.get_appointment_sms_deeplink(
                appointment_id=booking.appointment_id,
                source=extra_params.get('source'),
                secret=secret,
                customer_email=customer_email,
                customer_phone=customer_phone,
                base_url='show-appointment-detail-for-booksy-pay',
            )
            booksy_pay_extra_viariables = cls.get_extra_variables_for_booksy_pay(
                appointment=booking.appointment, language=language
            )
        else:
            booksy_pay_appointment_sms_deeplink = appointment_sms_deeplink
            booksy_pay_extra_viariables = {}

        return {
            'booking': booking,
            'booking_id': booking.id,
            'booking_status': status_name,
            'booking_finished': booking_finished,
            'booking_created_by': booking_created_by,
            'booking_last_modified_by': booking_last_modified_by,
            'booking_service_name_full': booking.service_name or '',
            'booking_by_business': appt_type == Appointment.TYPE.BUSINESS,
            'booking_by_customer': appt_type == Appointment.TYPE.CUSTOMER,
            'booking_sms_deeplink': appointment_sms_deeplink,
            'booking_marketplace_url': appointment_marketplace_url,
            'booking_join_meeting_url': booking.appointment.join_meeting_url,
            'is_booksy_pay_payment_window_open': (
                booking.appointment.is_booksy_pay_payment_window_open
            ),
            'booksy_pay_booking_sms_deeplink': booksy_pay_appointment_sms_deeplink,
            **booksy_pay_extra_viariables,
        }

    @classmethod
    def get_appointment_sms_deeplink(
        cls,
        appointment_id,
        source,
        customer_email,
        customer_phone,
        secret,
        base_url='show-appointment',
    ):
        from webapps.notification.base import Channel

        mobile_url = '{}/{}/?{}'.format(
            base_url,
            appointment_id,
            urllib.parse.urlencode(
                {
                    'customer_email': customer_email,
                    'customer_phone': customer_phone,
                    'secret': secret,
                }
            ),
        )
        appointment_marketplace_url = cls.get_marketplace_url(
            ['dl', 'appointment'],
            {
                'appointmentUid': appointment_id,
                'appointmentId': appointment_id,
                'email': customer_email,
                'phone': customer_phone,
                'secret': secret,
            },
        )
        appointment_deeplink_data = {
            'mobile_deeplink': mobile_url,
            '$ios_deeplink_path': mobile_url,
            '$deeplink_path': mobile_url,
            '$desktop_url': appointment_marketplace_url,
            '~keyword': '{}-{}'.format(
                settings.API_COUNTRY,
                appointment_id,
            ),
        }
        return Deeplink(
            app_type=BranchIOAppTypes.CUSTOMER,
            data=appointment_deeplink_data,
            feature=source,
            channel=Channel.Type.SMS,
            failsafe_cache='my_bookings',
        )

    @classmethod
    @lru_booksy_cache(timeout=60 * 60)
    def is_semilac_footer_visible(cls, business) -> bool:
        """
        Check if given business has NAIL_SALONS category.
        For those business, that are located in Poland we should show Semilac footer in emails.
        """

        from country_config.enums import Country
        from lib.feature_flag.feature.ecommerce import EmailSemilacFooterFlag
        from webapps.business.enums import BusinessCategoryEnum
        from webapps.business.models import BusinessCategory

        if business.country_code != Country.PL:
            return False

        if not EmailSemilacFooterFlag():
            return False

        nails_category = BusinessCategory.objects.filter(
            internal_name=BusinessCategoryEnum.NAIL_SALONS
        ).first()
        if not nails_category:
            return False

        return business.categories.filter(id=nails_category.id).exists()

    @classmethod
    def get_extra_variables_for_booksy_pay(cls, appointment, language) -> dict:
        from webapps.booksy_pay.cashback import get_booksy_pay_cashback_variant_by_appt

        extra_variables = {}
        cashback_variant = get_booksy_pay_cashback_variant_by_appt(appointment)

        if cashback_variant and cashback_variant.is_eligible(appointment):
            extra_variables.update(
                {
                    'booksy_pay_cashback_info': cashback_variant.customer_notification_content(
                        language=language,
                    ),
                    'booksy_pay_cashback_policy_url': cashback_variant.policy_url,
                    'booksy_pay_cashback_policy_text': cashback_variant.get_policy_url_text(
                        language=language
                    ),
                }
            )

        return extra_variables

    @classmethod
    def get_booking_info_do_business(cls, booking, language, extra_params):
        from webapps.business.models import Business
        from webapps.notification.base import Channel

        business = booking.appointment.business

        business_address = [
            business.address,
            business.address2,
            business.city_or_region_city,
        ]
        business_address = ', '.join(filter(bool, business_address))

        business_phone_obj = parse_phone_number(business.phone)
        business_phone = business_phone_obj.local_nice
        business_phone_short = business_phone_obj.local_short
        business_map_url = 'https://maps.google.com/maps?q=%.5f,%.5f' % (
            business.latitude or 0,
            business.longitude or 0,
        )

        mobile_url = 'show_business/{}'.format(business.id)
        business_marketplace_url = business.get_seo_url(dummy=False)
        business_deeplink_data = {
            'mobile_deeplink': mobile_url,
            '$ios_deeplink_path': mobile_url,
            '$deeplink_path': mobile_url,
            '$desktop_url': business_marketplace_url,
            '~keyword': '{}-{}'.format(
                settings.API_COUNTRY,
                booking.appointment_id,
            ),
        }
        business_sms_deeplink = Deeplink(
            app_type='C',
            data=business_deeplink_data,
            feature=extra_params.get('source'),
            channel=Channel.Type.SMS,
        )
        business_email_deeplink = Deeplink(
            app_type='C',
            data=business_deeplink_data,
            feature=extra_params.get('source'),
            channel=Channel.Type.EMAIL,
        )

        return {
            'business': business,
            'business_name': business.name,
            'business_name_short': business.short_name,
            'business_address': business_address,
            'business_phone': business_phone,
            'business_phone_short': business_phone_short,
            'business_is_automatic': business.booking_mode == Business.BookingMode.AUTO,
            'business_map_url': business_map_url,
            'business_sms_deeplink': business_sms_deeplink,
            'business_email_deeplink': business_email_deeplink,
            'business_webbiz_url': business.get_webbiz_url(),
            'business_webbiz_url_no_deeplink': business.get_webbiz_url(as_deeplink=False),
            'business_is_visible': business.visible,
            'business_is_semilac_footer_visible': cls.is_semilac_footer_visible(business),
        }

    @classmethod
    def get_booking_info_do_marketplace_urls(cls, booking, language, extra_params):
        return {
            'customer_marketplace_bookings_url': cls.get_marketplace_url(
                ['account', 'bookingsActive']
            ),
            'customer_marketplace_favorites_url': cls.get_marketplace_url(
                ['account', 'favourites']
            ),
            'customer_marketplace_reviews_url': cls.get_marketplace_url(['account', 'reviews']),
            'customer_marketplace_settings_url': cls.get_marketplace_url(['account', 'me']),
        }

    @classmethod
    def get_booking_info_do_customer(cls, booking, language, extra_params):
        bci = booking.appointment.booked_for
        if bci is not None:
            user = bci.user

            if not bci.visible_in_business:
                return {
                    'customer_name': f'Deleted user {bci.id}',
                    'customer_phone': '',
                    'customer_email': NO_REPLY_EMAIL,
                    'customer_id': bci.id,
                    'customer_has_account': user is not None,
                    'customer_add_card_link': '',
                }

            customer_name = customer_phone = customer_email = ''
            if user is not None:
                customer_name = user.get_full_name()
                customer_phone = user.cell_phone
                customer_email = user.email

            customer_name = customer_name or booking.appointment.customer_name
            customer_phone = customer_phone or booking.appointment.customer_phone
            customer_email = customer_email or booking.appointment.customer_email

            customer_phone = parse_phone_number(customer_phone).local_nice

            if is_private_email(customer_email):
                customer_email = ''

            return {
                'customer_name': customer_name,
                'customer_phone': customer_phone,
                'customer_email': customer_email,
                'customer_has_account': bci.user is not None,
                'customer_add_card_link': '',
            }
        else:
            return {
                'customer_name': None,
                'customer_phone': None,
                'customer_email': None,
                'customer_has_account': False,
                'customer_add_card_link': '',
            }

    @classmethod
    def get_booking_info_do_changes(cls, booking, language, extra_params):
        from webapps.booking.models import BookingChange

        result = {}

        timezone = booking.appointment.business.get_timezone()
        localize = lambda dt: dt.astimezone(timezone)

        bookingchanges = list(
            booking.bookingchange_set.order_by('created').only(
                'appointment_id',
                'booked_from',
                'booked_till',
                'created',
            )
        )

        if len(bookingchanges) >= 2:
            previous: 'BookingChange' = bookingchanges[-2]
            not_multibooking = booking.appointment.bookings.only('id').count() == 1
            if not_multibooking:
                changes = [previous]
            else:
                # Do not run filter and sort on db
                # because sometime it degrade and take abnormal time
                changes = list(
                    sorted(
                        filter(
                            lambda change: change.created == previous.created,
                            BookingChange.objects.filter(
                                appointment_id=previous.appointment_id,
                            ).only('booked_from', 'booked_till'),
                        ),
                        key=lambda change: change.booked_from,
                    )
                )
            result.update(
                cls.get_formatted_datetime(
                    localize(min(i.booked_from for i in changes)), language, prefix='previous_'
                )
            )
            result.update(
                cls.get_formatted_datetime(
                    localize(min(i.booked_from for i in changes)),
                    language,
                    prefix='previous_booked_from_',
                )
            )

            result.update(
                cls.get_formatted_datetime(
                    localize(max(i.booked_till for i in changes)),
                    language,
                    prefix='previous_booked_till_',
                )
            )

        return result

    @classmethod
    def get_bookings_info(cls, booking, language, extra_params, _calculate_booking_changes=False):
        return BookingVariables.get_bookings_info(
            booking,
            language,
            extra_params,
            _calculate_booking_changes=_calculate_booking_changes,
        )

    @classmethod
    def get_booking_info_do(cls, booking, language, extra_params, _calculate_booking_changes=False):
        business = booking.appointment.business
        timezone = business.get_timezone()

        localized_booked_from = booking.booked_from.astimezone(timezone)
        localized_booked_till = booking.booked_till.astimezone(timezone)

        result = {}

        result.update(cls.get_booking_info_do_booking(booking, language, extra_params))
        result.update(cls.get_booking_info_do_business(booking, language, extra_params))
        result.update(cls.get_booking_info_do_marketplace_urls(booking, language, extra_params))
        result.update(cls.get_booking_info_do_customer(booking, language, extra_params))
        if _calculate_booking_changes:
            result.update(cls.get_booking_info_do_changes(booking, language, extra_params))

        result.update(cls.get_formatted_datetime(localized_booked_from, language, prefix=''))
        result.update(
            cls.get_formatted_datetime(localized_booked_from, language, prefix='booked_from_')
        )
        result.update(
            cls.get_formatted_datetime(localized_booked_till, language, prefix='booked_till_')
        )

        result.update(cls._get_booking_service_info(booking, timezone))
        if booking.is_multibooking():
            result['extra_bookings'] = [
                cls._get_booking_service_info(eb, timezone)
                for eb in booking.appointment.subbookings[1:]
            ]

        result['bookings_info'] = cls.get_bookings_info(booking, language, extra_params)

        result['debug_info'] = '%s-%s#%d@%s' % (
            settings.DEPLOYMENT_LEVEL,
            booking.appointment.business.country_code,
            booking.id,
            tznow().strftime('%F_%T'),
        )

        return result

    @classmethod
    def get_booking_info(cls, booking, language, extra_params=(), _calculate_booking_changes=False):
        last_language = get_language()
        activate(language)
        try:
            result = cls.get_booking_info_do(
                booking,
                language,
                extra_params,
                _calculate_booking_changes=_calculate_booking_changes,
            )
        finally:
            activate(last_language)
        return result

    @classmethod
    def get_booking_template_variables(
        cls, booking, language, extra_params=(), _calculate_booking_changes=False
    ):
        booking_info = cls.get_booking_info(
            booking,
            language,
            extra_params=extra_params,
            _calculate_booking_changes=_calculate_booking_changes,
        )
        return {
            'booking_info': booking_info,
        }

    @classmethod
    def can_send_message_with_plea(cls, body, rcpt, method, method_kwargs):
        if body is None:
            return False, {'body_exist': False}
        if not bool(rcpt):
            return False, {'rcpt_exist': False}
        return method(**method_kwargs)

    @classmethod
    def is_customer_notifications_disabled(cls, user, action):
        """
        Check for given user notification status for each notification type.
        :param user: User model object/
        :param action: str. Example 'changed'
        :return: dict. With to keys:
            email_disabled
            push_disabled
        If any key set on true then this notification type must be skipped
        """
        from webapps.user.models import UserProfile
        from webapps.notification.models import UserNotification

        skipped = {
            'email_disabled': False,
            'push_disabled': False,
        }
        # get notification_action
        # example booking_status_change
        notification_action = NOTIFICATION_ACTION_MAPPING.get(action)
        # get profile
        profile = user.profiles.filter(
            profile_type=UserProfile.Type.CUSTOMER,
        ).first()

        if notification_action and profile:
            qs = profile.notifications.filter(
                Q(type=UserNotification.EMAIL_NOTIFICATION)
                | Q(type=UserNotification.PUSH_NOTIFICATION)
            )
            # check that notification enabled
            # for each type
            for notification in qs:
                if notification.type == UserNotification.EMAIL_NOTIFICATION:
                    key = 'email_disabled'
                elif notification.type == UserNotification.PUSH_NOTIFICATION:
                    key = 'push_disabled'
                else:
                    continue
                notification_status = notification.settings_data.get(notification_action)
                # if settings notification.settings['booking_status_change']
                # is not None and checked as False than
                # skip this notification type
                if notification_status is not None and not notification_status:
                    skipped[key] = True

        return skipped

    @classmethod
    def should_raise_skipped(
        cls,
        recipient,
        body,
        user,
        appointment,
        to_customer,
        history_data=None,
        pre_calculated_parts=None,
    ):
        can_send_push, can_send_push_plea = cls.can_send_push_with_plea(
            customer=user,
            to_customer=to_customer,
            business=appointment.business,
        )

        can_send_sms, can_send_sms_plea = cls.can_send_sms_with_plea(
            recipient.get('phone'),
            body,
            business=appointment.business,
            customer=user,
            to_customer=to_customer,
            history_data=history_data,
            pre_calculated_parts=pre_calculated_parts,
        )

        should_send_push, should_send_sms = resolve_sms_priority(
            appointment.business.sms_priority, can_send_push, can_send_sms
        )
        raise_params = {
            'reason': 'All type notification is set to False.',
            'appointment_id': appointment.id,
            'user.id': user.id,
            'message': 'All notifications types disabled',
        }
        if should_send_sms:
            if recipient.get('skip_sms', False) and recipient.get('skip_email', False):
                raise ScenarioSkipped(raise_params)
        else:
            # if nothing to send raise ScenarioSkipped
            if recipient.get('skip_email', False) and recipient.get('skip_push', False):
                raise ScenarioSkipped(raise_params)

    @classmethod
    def get_business_recipients(cls, booking, skip_email: set) -> list[dict]:
        from webapps.business.models import Resource
        from webapps.notification.push import notification_receivers_list

        result = []

        if NotifyOwnerThroughEmailAboutNewBookingFlag():
            owner = booking.appointment.business.owner
            skip = owner.email in skip_email
            result.append(
                {
                    'skip': skip,
                    'user': owner,
                    'unlocked': True,
                    'email': owner.email,
                    'phone': None,
                    'push': [],
                    'is_customer': False,
                }
            )

        # get receivers from booking staffers
        for resource in booking.resources.filter(
            type=Resource.STAFF,
            active=True,
        ):
            if not resource.staff_email:
                continue

            resource_user = cls.get_user_directly_or_from_email(
                resource.staff_user,
                resource.staff_email,
            )
            skip = resource.staff_email in skip_email
            result.append(
                {
                    'skip': skip,
                    'user': resource_user,
                    'unlocked': (
                        (resource.staff_access_level or Resource.STAFF_ACCESS_LEVEL_MANAGER)
                        not in Resource.STAFF_ACCESS_LEVELS_LOCKED
                    ),
                    'email': resource.staff_email,
                    'phone': select_mobile_phone_number([resource.staff_cell_phone]),
                    'push': (
                        notification_receivers_list(user_id=resource_user.id, customer=False)
                        if not skip and resource_user is not None
                        else []
                    ),
                    'is_customer': False,
                    'staffer': resource,
                }
            )

        # get receivers set up in internal notifications
        for email, user in cls.get_recievers_from_booking(booking):
            skip = email in skip_email
            result.append(
                {
                    'skip': skip,
                    'user': user,
                    'unlocked': True,
                    'email': email,
                    'phone': None,
                    'push': (
                        notification_receivers_list(user_id=user.id, customer=False)
                        if not skip and user is not None
                        else []
                    ),
                    'is_customer': False,
                }
            )
        return result

    @classmethod
    def generic_booking_communication_sender(
        cls,
        scenario_name: str,
        template_name: str,
        to_customer: bool,
        params: dict,
        extra_variables=None,
        history_data: dict | None = None,
        sms_deduction: int = 0,
        _calculate_booking_changes: bool = False,
        attachments: list | None = None,
        target: tuple | None = None,
    ):
        from webapps.notification.tasks.push import send_push_notification
        from webapps.notification.models import NotificationHistory
        from webapps.booking.models import Appointment
        from webapps.booking.notifications.booking_notifications.tools import (
            BookingNotificationTools,
        )
        from webapps.business.models import Resource
        from webapps.user.models import User, UnsubscribedEmail
        from webapps import consts

        if 'source' not in params:
            params['source'] = template_name

        try:
            params, appointment = cls.get_params_objects(params, 'appointment')
        except Appointment.DoesNotExist:
            raise ScenarioSkipped({'appointment.exist': False})

        if not to_customer:
            appointment.business.raise_if_business_notifications_are_disabled()

        customer_recipients_data = []
        bci = appointment.booked_for

        if to_customer:
            if bci and bci.user is None and bci.visible_in_business is False:
                raise ScenarioSkipped(
                    {
                        'appointment.booked_for.visible_in_business': False,
                        'appointment.booked_for.user': None,
                    }
                )

            customer_recipients_data = recipients_data_for_customer_booking_sender(appointment)
            if len(customer_recipients_data) == 0:
                raise ScenarioSkipped({'appointment.booked_for': None})
            appointment.business.raise_if_customer_notifications_are_disabled()

        if not appointment.business.active:
            raise ScenarioSkipped({'business.active': False})

        booking = cls.get_primary_booking(appointment)

        if appointment.deleted:
            raise ScenarioSkipped({'appointment.deleted': True})

        skip_email = set()
        if params.get('skip_email'):
            skip_email.add(params['skip_email'])

        recipients = []

        if to_customer:
            history_data_sender = NotificationHistory.SENDER_SYSTEM
            language = cls.get_language_from_customer_recipients(customer_recipients_data)
            from_data = (
                appointment.business.name,
                NO_REPLY_EMAIL,
            )
            for recipient_data in customer_recipients_data:
                cls.add_customer_recipient_from_bci(
                    recipient_data.bci,
                    recipients,
                    appointment,
                    params,
                    scenario_name,
                    history_data,
                    is_only_customer_recipient=recipient_data.is_only_customer_recipient,
                )
        else:
            # skip email of person who made last modification to the booking
            changed_user_id = appointment.bookingchange_set.values_list(
                'changed_user_id', flat=True
            ).last()
            if changed_user_id:
                email_to_skip = (
                    User.objects.filter(
                        id=changed_user_id,
                    )
                    .values_list('email', flat=True)
                    .first()
                )
                if email_to_skip:
                    skip_email.add(email_to_skip)

            language = cls.get_language(None, appointment.business)
            history_data_sender = NotificationHistory.SENDER_CUSTOMER
            from_data = cls.get_customer_name_and_email_from_booking(appointment)

            recipients.extend(cls.get_business_recipients(booking, skip_email))

        recipients_dict = {}
        for recipient in recipients:
            if recipient['email'] not in recipients_dict:
                recipients_dict[recipient['email']] = recipient
            else:
                current = recipients_dict[recipient['email']]

                current['unlocked'] = current['unlocked'] or recipient['unlocked']
                current['user'] = current['user'] or recipient['user']
                current['phone'] = current['phone'] or recipient['phone']
                current['push'] = current['push'] or recipient['push']

        recipients = list(recipients_dict.values())

        history_data = dict_merge(
            {
                'appointment_id': appointment.id,
                'booking_id': safe_get(appointment, ['subbookings', 0, 'id']),
                'business_id': appointment.business_id,
                'customer_id': None if bci is None else bci.user_id,
                'customer_card_id': None if bci is None else bci.id,
                'sender': history_data_sender,
            },
            history_data,
        )
        # Prevent overwriting sender (and charging) for sms sent to business
        # In that way we don't charge twice for booking notifications
        if history_data_sender != NotificationHistory.SENDER_BUSINESS:
            history_data['meta_overwrite_sender'] = False

        variables = cls.get_booking_template_variables(
            booking,
            language,
            extra_params=params,
            _calculate_booking_changes=_calculate_booking_changes,
        )
        if template_name == 'booking_reminder' and CriticalPY1902Flag():  # temporary fix
            base_customer_app_deeplink = DeepLinkCache.get('my_bookings')
            variables['booking_info']['booking_sms_deeplink'] = base_customer_app_deeplink

        if callable(extra_variables):
            extra_variables = extra_variables(
                appointment=appointment,
                language=language,
            )
        if isinstance(extra_variables, dict):
            variables.update(extra_variables)

        blacklisted_sms_recipients = cls._blacklisted_sms_recipients()

        for recipient in recipients:
            if recipient.get('skip', False):
                continue

            rcpt_push = recipient.get('push', [])
            rcpt_sms = recipient.get('phone')

            use_from_data = True
            if staffer := recipient.get('staffer'):
                use_from_data = staffer.staff_access_level != Resource.STAFF_ACCESS_LEVEL_STAFF

            variables['user'] = recipient.get('user')

            variables['booking_info']['customer_data_locked'] = not recipient.get('unlocked', True)
            variables['booking_info']['additional_booking_info'] = get_additional_member_info(
                appointment, recipient, language
            )

            email = recipient['email']
            can_send_email = UnsubscribedEmail.can_send_email(email)
            if not to_customer:
                to_name = variables['booking_info'].get('business_name')
            else:
                to_name = recipient.get('to_name') or variables['booking_info'].get('customer_name')

            if can_send_email and not recipient.get('skip_email', False):
                body_email = cls.render_template(
                    scenario_name,
                    template_name,
                    template_variables=variables,
                    language=language,
                    default=None,
                )

                if body_email is not None:
                    send_email(
                        email,
                        body_email,
                        history_data=history_data,
                        from_data=from_data if use_from_data else None,
                        to_name=to_name,
                        attachments=attachments,
                    )

            body_push = cls.render_template(
                scenario_name,
                template_name,
                extension='push',
                template_variables=variables,
                language=language,
                default=None,
            )
            body_sms = cls.render_template(
                scenario_name,
                template_name,
                extension='sms',
                template_variables=variables,
                language=language,
                default=None,
            )
            body_sms_copy = body_sms

            if cls._is_recipient_blocked(
                blacklisted=blacklisted_sms_recipients,
                phone_number=rcpt_sms,
            ):
                body_sms = None

            # Confirmations are disabled for customer bookings and in some countries or businesses
            # Confirmation is also blocked for customers with user object (BBs)
            if (
                template_name == 'customer_booking_confirmation'
                and BookingNotificationTools.is_booking_confirmation_sms_disabled(
                    business=booking.appointment.business,
                    appointment=appointment,
                )
            ):
                body_sms = None

            if (
                template_name == 'customer_booking_confirmation'
                and booking.appointment.source.name in [consts.INSTAGRAM, consts.INSTAGRAM_STAFFER]
            ):
                # always send confirmation SMS if booking made via INSTAGRAM
                if KillSwitch.alive(KillSwitch.System.SMS_WITH_DEEPLINKS_ENABLED):
                    # link to Booksy should already be a part of sms
                    body_sms = body_sms_copy
                else:
                    # also add a link to Booksy, so they will know about us
                    appointment_sms_deeplink = cls.get_appointment_sms_deeplink(
                        appointment_id=booking.appointment_id,
                        source=template_name,
                        customer_email=email,
                        customer_phone=rcpt_sms,
                        secret=str(booking.appointment.secret or ''),
                    )
                    body_sms = '{} {}'.format(
                        body_sms_copy.rstrip(),
                        str(appointment_sms_deeplink),
                    )

            can_send_push, can_send_push_plea = cls.can_send_message_with_plea(
                body_push,
                rcpt_push,
                cls.can_send_push_with_plea,
                dict(
                    customer=recipient.get('user'),
                    to_customer=to_customer,
                    business=appointment.business,
                ),
            )
            can_send_sms, can_send_sms_plea = cls.can_send_message_with_plea(
                body_sms,
                rcpt_sms,
                cls.can_send_sms_with_plea,
                dict(
                    phone_number=rcpt_sms,
                    sms_body=body_sms,
                    # Merchant first :P
                    # We allow to exceed prepaid/postpaid limit for
                    # booking notifications if remaining sms parts is too low
                    # for whole sms but still >= 1
                    pre_calculated_parts=1,
                    business=booking.appointment.business,
                    customer=None if bci is None else bci.user,
                    to_customer=to_customer,
                    history_data=history_data,
                ),
            )
            should_send_push, should_send_sms = resolve_sms_priority(
                sms_priority=booking.appointment.business.sms_priority,
                can_send_push=(can_send_push and not recipient.get('skip_push', False)),
                can_send_sms=(can_send_sms and not recipient.get('skip_sms', False)),
            )
            if history_data.get('task_type') == reminder_format(
                appointment.id
            ) and reminder_was_sent(appointment.id):
                should_send_sms = False

            # skip sending sms for additional reminder
            if (
                EnableSendingAdditionalBookingReminderFlag()
                and cls._is_task_a_additional_customer_booking_reminder(
                    task_id=history_data.get('task_id')
                )
            ):
                should_send_sms = False

            push_result = None
            if should_send_push:
                # push_result = {}
                push_result = send_push_notification(
                    receivers=rcpt_push,
                    alert=body_push,
                    target=target or ('booking', booking.id),
                    history_data=history_data,
                )

            sms_result = None
            if should_send_sms:
                sms_result = send_sms(
                    to=rcpt_sms,
                    message=body_sms,
                    history_data=history_data,
                    sms_deduction=sms_deduction,
                ).to_dict()
                sms_limits_notification(booking.appointment.business)

            recipient.update(
                {
                    '_can_send_push': can_send_push,
                    '_can_send_sms': can_send_sms,
                    '_sent_push': should_send_push,
                    '_sent_sms': should_send_sms,
                    '_can_send_push_plea': can_send_push_plea,
                    '_can_send_sms_plea': can_send_sms_plea,
                    '_sms_result': sms_result,
                    '_push_result': push_result,
                }
            )

        return {
            'booking_id': booking.id,
            'business_id': booking.appointment.business_id,
            'customer_id': None if bci is None else bci.user_id,
            'customer_card_id': None if bci is None else bci.id,
            'language': language,
            'recipients': recipients,
        }

    @classmethod
    def add_customer_recipient_from_bci(
        cls,
        bci,
        recipients,
        appointment,
        params,
        scenario_name,
        history_data,
        is_only_customer_recipient=True,
    ):
        email = cls.get_customer_email_from_bci(bci)
        phone = cls.get_customer_phone_from_bci(bci)
        if is_only_customer_recipient:
            email = email or appointment.customer_email
            phone = phone or appointment.customer_phone
        from webapps.notification.push import notification_receivers_list

        recipient = {
            'skip': False,
            'user': bci.user,
            'unlocked': True,
            'email': email,
            'phone': phone,
            'push': (
                notification_receivers_list(user_id=bci.user.id, customer=True)
                if bci.user is not None
                else []
            ),
            'is_customer': True,
            'to_name': bci.user.full_name if bci.user else bci.full_name,
        }

        # business check to not send cancellation:
        #
        # email
        recipient['skip_email'] = params.get(DONT_SEND_CANCELLATION_EMAIL, False)
        # sms
        recipient['skip_sms'] = params.get(DONT_SEND_CANCELLATION_SMS, False)
        user = bci.user
        # if bci user exists then
        # notification could be disabled
        if user:
            action = scenario_name.split('_')[-1]
            skipped = cls.is_customer_notifications_disabled(user, action)
            # don't overwrite current True status for skip_email
            if not recipient['skip_email'] and skipped.get('email_disabled'):
                recipient['skip_email'] = True
            # updated status for skip_push
            recipient['skip_push'] = skipped.get('push_disabled', False)
            # decide if should raise
            # Put empty string as sms body as we force sms parts count
            # to be 1 at this stage (we allow to exceed prepaid/postpaid
            # limit for booking notifications if remaining sms parts is
            # too low for whole sms but still >= 1)
            cls.should_raise_skipped(
                recipient, '', user, appointment, True, history_data, pre_calculated_parts=1
            )
        recipients.append(recipient)

    @classmethod
    def get_language_from_customer_recipients(cls, customer_recipients_data):
        for recipient_data in customer_recipients_data:
            if recipient_data.bci.user:
                return cls.get_language(recipient_data.bci.user, None)
        return cls.get_language(None, None)

    @staticmethod
    def _is_task_a_additional_customer_booking_reminder(task_id):
        if not isinstance(task_id, str):
            return False

        allowed_task_types = {
            CustomerBookingReminderTaskTypes.MORNING_8_AM.value,
            CustomerBookingReminderTaskTypes.TWO_HOURS_BEFORE.value,
        }
        parts = task_id.split(':')
        if len(parts) == 3 and parts[1] in allowed_task_types:
            return True
        return False

    @staticmethod
    def _blacklisted_sms_recipients() -> list[str]:
        flag_value = BlockSendingAppointmentSmsToRecipients()
        if not flag_value:
            return []

        return flag_value.get("blocked", [])

    @staticmethod
    def _is_recipient_blocked(
        blacklisted: list[str], phone_number: PhoneNumber | str | None
    ) -> bool:
        if not phone_number:
            return False

        parsed_phone_number = parse_phone_number(phone_number)

        for pattern in blacklisted:
            if re.match(pattern, parsed_phone_number.global_short):
                return True

        return False
