from django.conf import settings

from lib.payment_gateway.enums import BalanceTransactionStatus, DisputeType
from lib.payment_gateway.enums import PaymentStatus as WalletPaymentStatus
from lib.payment_providers.enums import (
    PaymentOperationStatus,
    PaymentOperationType,
    TransferFundStatus,
)
from lib.payment_providers.enums import PaymentStatus as PPPaymentStatus
from lib.payments.enums import Currency

# https://docs.stripe.com/currencies#minimum-and-maximum-charge-amounts
MINIMAL_PAYMENT_AMOUNT = {
    Currency.USD: 50,
    Currency.EUR: 50,
    Currency.GBP: 30,
    Currency.PLN: 200,
}.get(settings.CURRENCY_CODE.lower(), 50)

MINIMAL_PAYMENT_FEE_AMOUNT = 0
MINIMAL_TRANSFER_AMOUNT = 1


PAYMENT_PROVIDERS_PAYMENT_STATUS_TO_PAYMENT_STATUS_MAPPING = {
    PPPaymentStatus.NEW: WalletPaymentStatus.NEW,
    PPPaymentStatus.SENT_FOR_AUTHORIZATION: WalletPaymentStatus.SENT_FOR_AUTHORIZATION,
    PPPaymentStatus.ACTION_REQUIRED: WalletPaymentStatus.ACTION_REQUIRED,
    PPPaymentStatus.AUTHORIZED: WalletPaymentStatus.AUTHORIZED,
    PPPaymentStatus.AUTHORIZATION_FAILED: WalletPaymentStatus.AUTHORIZATION_FAILED,
    PPPaymentStatus.SENT_FOR_CAPTURE: WalletPaymentStatus.SENT_FOR_CAPTURE,
    PPPaymentStatus.CAPTURED: WalletPaymentStatus.CAPTURED,
    PPPaymentStatus.CAPTURE_FAILED: WalletPaymentStatus.CAPTURE_FAILED,
    PPPaymentStatus.CANCELED: WalletPaymentStatus.CANCELED,
}

PAYMENT_PROVIDERS_PAYMENT_OPERATION_TYPE_TO_DISPUTE_TYPE_MAPPING = {
    PaymentOperationType.CHARGEBACK: DisputeType.CHARGEBACK,
    PaymentOperationType.SECOND_CHARGEBACK: DisputeType.SECOND_CHARGEBACK,
    PaymentOperationType.REVERSED_CHARGEBACK: DisputeType.REVERSED_CHARGEBACK,
}


PAYMENT_PROVIDERS_PAYMENT_OPERATION_STATUS_TO_BT_STATUS_MAPPING = {
    PaymentOperationStatus.NEW: BalanceTransactionStatus.PROCESSING,
    PaymentOperationStatus.PROCESSING: BalanceTransactionStatus.PROCESSING,
    PaymentOperationStatus.SUCCESS: BalanceTransactionStatus.SUCCESS,
    PaymentOperationStatus.FAILED: BalanceTransactionStatus.FAILED,
}


PAYMENT_PROVIDERS_TRANSFER_FUND_STATUS_TO_BT_STATUS_MAPPING = {
    TransferFundStatus.NEW: BalanceTransactionStatus.PROCESSING,
    TransferFundStatus.PROCESSING: BalanceTransactionStatus.PROCESSING,
    TransferFundStatus.SUCCESS: BalanceTransactionStatus.SUCCESS,
    TransferFundStatus.FAILED: BalanceTransactionStatus.FAILED,
    TransferFundStatus.EXPIRED: BalanceTransactionStatus.FAILED,
}
