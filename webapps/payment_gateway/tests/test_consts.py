import pytest
from django.test import TestCase, override_settings
from parameterized import parameterized


@pytest.mark.django_db
class TestPaymentGatewayConsts(TestCase):

    @parameterized.expand(
        [
            ('USD', 50),
            ('EUR', 50),
            ('GBP', 30),
            ('PLN', 200),
        ]
    )
    def test_minimal_payment_amount_for_supported_currencies(self, currency, expected_amount):
        """Test that MINIMAL_PAYMENT_AMOUNT returns correct values for supported currencies"""
        with override_settings(CURRENCY_CODE=currency):
            # Re-import to get the updated value with new settings
            from importlib import reload
            from webapps.payment_gateway import consts

            reload(consts)

            self.assertEqual(consts.MINIMAL_PAYMENT_AMOUNT, expected_amount)

    @parameterized.expand(
        [
            ('JPY', 50),  # Unsupported currency should fallback to 50
            ('CAD', 50),  # Unsupported currency should fallback to 50
            ('AUD', 50),  # Unsupported currency should fallback to 50
            ('BRL', 50),  # Unsupported currency should fallback to 50
        ]
    )
    def test_minimal_payment_amount_fallback_for_unsupported_currencies(
        self, currency, expected_amount
    ):
        """Test that MINIMAL_PAYMENT_AMOUNT falls back to 50 for unsupported currencies"""
        with override_settings(CURRENCY_CODE=currency):
            # Re-import to get the updated value with new settings
            from importlib import reload
            from webapps.payment_gateway import consts

            reload(consts)

            self.assertEqual(consts.MINIMAL_PAYMENT_AMOUNT, expected_amount)
