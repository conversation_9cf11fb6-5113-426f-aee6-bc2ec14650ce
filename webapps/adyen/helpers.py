import json
import logging
import sys
import uuid
from copy import deepcopy
from json import <PERSON><PERSON><PERSON>ecode<PERSON><PERSON><PERSON>
from uuid import uuid4

import requests.exceptions
from django.conf import settings
from django.contrib.auth.models import User
from requests import Request, Session

from lib.tools import id_to_external_api, l_b
from webapps.adyen.consts import currency_codes, oper_result
from webapps.adyen.models import AdyenRequestLog
from webapps.adyen.typing import AmountDict
from webapps.kill_switch.models import KillSwitch
from webapps.notification.scenarios import BookingChangedScenario, start_scenario
from webapps.segment.tasks import segment_api_appointment_booked_task

log = logging.getLogger('booksy.adyen_requests')
error_log = logging.getLogger('booksy.adyen_requests_log_errors')

exc_map = {
    requests.exceptions.HTTPError: oper_result.CONN_ERR,
    requests.exceptions.ConnectionError: oper_result.CONN_ERR,
    requests.exceptions.ConnectTimeout: oper_result.CONN_TIMEOUT,
    requests.exceptions.ReadTimeout: oper_result.CONN_ERR,
}


def _sanitize_request_body(body):
    """
    Mask sensitive data (ccn, cvv) from given request dict.

    Args:
        body (dict): Request data.

    Returns:
        dict: Request data with masked sensitive data.
    """
    # ccn
    try:
        ccn = body['card']['number']
    except KeyError:
        pass
    else:
        sanitized_ccn = '{}{}{}'.format(ccn[:6], '*' * (len(ccn) - 10), ccn[-4:])
        body['card']['number'] = sanitized_ccn

    # cvv
    try:
        body['card']['cvc'] = '*' * len(body['card']['cvc'])
    except KeyError:
        pass

    # encrypted data
    try:
        encrypted_json = body['additionalData']['card.encrypted.json']
    except KeyError:
        pass
    else:
        body['additionalData']['card.encrypted.json'] = encrypted_json[:20]

    # photo upload in market pay
    try:
        body['documentContent'] = body['documentContent'][:20]
    except KeyError:
        pass

    return body


def _sanitize_response_body(body):
    try:
        return json.loads(body)
    except JSONDecodeError:
        return body


def save_log(resp):
    """
    Create log entry with request and response data.

    Args:
        resp (requests.Response): Response object to log.

    Returns:
        None
    """
    try:
        body = deepcopy(json.loads(l_b(resp.request.body)))
        sanitized_body = _sanitize_request_body(body)
        sanitized_response_body = _sanitize_response_body(resp.text)

        log_data = {
            'request': {
                'url': resp.url,
                'body': sanitized_body,
                'headers': dict(resp.request.headers),
            },
            'response': {
                'status_code': resp.status_code,
                'body': sanitized_response_body,
            },
        }
        log.info(json.dumps(log_data))
        AdyenRequestLog.objects.create(
            request=log_data['request'],
            response=log_data['response'],
        )

    except Exception:
        error_log.exception('Error while logging adyen request')


def save_exc_log(req, exc):
    """
    Create log entry for exceptional request.

    Args:
        req (requests.Request): Request object to log.
        exc (Exception): Exception object.

    Returns:
        None
    """
    try:
        body = deepcopy(l_b(json.loads(req.body)))
        sanitized_body = _sanitize_request_body(body)
        log_data = {
            'request': {
                'url': req.url,
                'body': sanitized_body,
                'headers': dict(req.headers),
            },
            'exc': str(type(exc)),
        }
        log.warning(json.dumps(log_data), exc_info=sys.exc_info())
    except Exception:
        error_log.exception('Error while logging adyen request exception')


def make_request(data, url, idempotency=False, market_pay=False, manual_payout=False, timeout=None):
    """
    Make request.

    Args:
        data (dict): Data to send.
        url (str): Target url.
        market_pay (bool): True if use MarketPay credential
        manual_payout (bool): True if use AdeynPayout credential

    Returns:
        requests.Response: response.
    """
    headers = {'Content-Type': 'application/json; charset=utf-8'}
    if idempotency:
        headers['Pragma'] = 'process-retry'

    if market_pay:
        auth = (settings.MARKET_PAY_USER, settings.MARKET_PAY_PASS)
    elif manual_payout:
        auth = (settings.ADYEN_PAYOUT_USER, settings.ADYEN_PAYOUT_PASS)
    else:
        auth = (settings.ADYEN_API_USER, settings.ADYEN_API_PASS)

    req = Request(
        'POST',
        url,
        json=data,
        auth=auth,
        headers=headers,
    ).prepare()

    try:
        with Session() as session:
            resp = session.send(req, timeout=timeout or settings.ADYEN_REQUEST_TIMEOUT)
    except requests.exceptions.RequestException as exc:
        save_exc_log(req, exc)
        raise
    else:
        save_log(resp)

    return resp


def get_reference():
    """
    Generate unique operation reference with api country prefix

    Returns:
        str: generated reference

    """
    return '{}-{}'.format(settings.API_COUNTRY, uuid4().hex)


def float_amount_to_cents(amount: AmountDict):
    """
    Changes float amount to cents.

    Args:
        amount (dict): amount dict with value and currency keys.

    Returns:
        dict: amount dict with updated value

    Raises:
        KeyError: If provide not supported curency key

    Example:
        >>> float_amount_to_cents({'value': 10.00, 'currency': 'EUR'})
        {'value': 1000, 'currency': 'EUR'}
    """
    currency = amount['currency']
    multipler = 10 ** currency_codes.exponents[currency]
    # TODO py3 delete cast to float after python3 migration
    # Decimal could be rounded in python3
    value = int(round(float(amount['value'] * multipler)))
    return {'value': value, 'currency': currency}


def cents_to_float_amount(amount: AmountDict):
    """
    Changes cents to float amount.

    Args:
        amount (dict): amount dict with value and currency keys.

    Returns:
        dict: amount dict with updated value

    Raises:
        KeyError: If provide not supported curency key

    Example:
        >>> cents_to_float_amount({'value': 1000, 'currency': 'EUR'})
        {'value': 10.00, 'currency': 'EUR'}`
    """
    currency = amount['currency']
    multipler = 10 ** currency_codes.exponents[currency]
    value = amount['value'] / multipler
    return {'value': value, 'currency': currency}


def generate_shopper_reference(user: User) -> str:
    """
    Generates shopper reference string value, unique for each user in Booksy
    system. This value should then be used to denote Adyen operations in which
    the provided user takes part in.

    Side note: if this function gets called on 'dev' environment it returns
    random uuid4 value for each call instead. This is due to the fact that all
    dev users share same id value.
    """
    if settings.LOCAL_DEPLOYMENT:
        shopper_reference = f'dev-{uuid.uuid4().hex}'
    else:
        shopper_reference = id_to_external_api(user.id)
    return shopper_reference


def cancel_appointment_after_failed_payment(
    transaction: 'Transaction',
    reason='Failed 3DSecure attempt',
) -> None:
    """
    Cancels booking that has been awaiting 3DSecure confirmation for too long.
    """
    from webapps.booking.enums import WhoMakesChange
    from webapps.booking.models import Appointment, BookingChange
    from webapps.user.tools import get_system_user

    appointment = transaction.appointment
    if not appointment:
        return

    status_before = appointment.status
    status_after = Appointment.STATUS.CANCELED

    appointment.update_appointment(
        updated_by=get_system_user(),
        status=status_after,
        who_makes_change=WhoMakesChange.BUSINESS,
    )
    BookingChange.add(
        appointment,
        changed_by=BookingChange.BY_SYSTEM,
        metadata={
            'reason': reason,
        },
    )


def confirm_appointment(transaction: 'Transaction'):
    """Method used in 3ds flow during prepayment booking.
    After the 3DS process confirmation/success, the next step is to confirm the 3DS related
    appointment.

    :param transaction: confirmed 3ds Transaction
    """
    from webapps.booking.events import customer_appointment_created_event
    from webapps.booking.tasks import update_bci_service_questions_task
    from webapps.df_creator.marketing_actions import first_cb

    appointment = transaction.appointment
    if not appointment:
        return

    first_booking = appointment.first_booking
    customer_appointment_created_event.send(appointment)
    start_scenario(
        BookingChangedScenario,
        appointment=appointment,
        action=BookingChangedScenario.CUSTOMER_CREATED,
    )
    segment_api_appointment_booked_task.delay(first_booking.id)
    update_bci_service_questions_task.delay(
        appointment.id,
        customer_user_id=transaction.customer.id,
    )
    if appointment.is_first_cb_for_business():
        first_cb(first_booking)
