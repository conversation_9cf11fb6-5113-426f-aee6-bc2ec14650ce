import inspect
import traceback
from typing import Optional

from lib.feature_flag.feature import EnableCancellationFeeAuthChangelogServiceFlag
from lib.point_of_sale.enums import CancellationFeeAuthStatus
from webapps.point_of_sale.models import Cancellation<PERSON><PERSON><PERSON><PERSON>, CancellationFeeAuthChangelog


class CancellationFeeAuthChangelogService:
    """
    Service for managing CancellationFeeAuth changelog entries.
    """

    @staticmethod
    def create_changelog_entry(
        cf_auth: CancellationFeeAuth,
        old_status: Optional[CancellationFeeAuthStatus],
        new_status: Optional[CancellationFeeAuthStatus],
        operator_id: Optional[int] = None,
        function_name: Optional[str] = None,
        metadata: Optional[dict] = None,
    ) -> Optional[CancellationFeeAuthChangelog]:
        """
        Create a changelog entry for a CancellationFeeAuth status change.

        Args:
            cf_auth: The CancellationFeeAuth instance
            old_status: Previous status (can be None for creation)
            new_status: New status (can be None for deletion)
            operator_id: ID of the user who made the change
            function_name: Name of the function that triggered the change
            metadata: Additional metadata about the change

        Returns:
            Created CancellationFeeAuthChangelog instance or None if feature flag is disabled
        """
        # Check if the feature flag is enabled
        if not EnableCancellationFeeAuthChangelogServiceFlag():
            return None

        # If function_name is not provided, try to get it from the call stack
        if function_name is None:
            function_name = CancellationFeeAuthChangelogService._get_calling_function_name()

        # Prepare metadata (without traceback)
        changelog_metadata = metadata or {}

        # Prepare data for HistoryModelAbstract (without traceback)
        changelog_data = {
            'cf_auth_id': str(cf_auth.id),
            'operator_id': operator_id,
            'old_status': old_status.value if old_status else None,
            'new_status': new_status.value if new_status else None,
            'function_name': function_name,
            'metadata': changelog_metadata,
        }

        return CancellationFeeAuthChangelog.objects.create(
            cf_auth=cf_auth,
            operator_id=operator_id,
            old_status=old_status.value if old_status else None,
            new_status=new_status.value if new_status else None,
            function_name=function_name,
            metadata=changelog_metadata,
            data=changelog_data,  # Required by HistoryModelAbstract
            traceback=traceback.format_stack(),  # Required by HistoryModelAbstract
        )

    @staticmethod
    def _get_calling_function_name() -> str:
        """
        Get the name of the function that called this method.
        Walks up the call stack to find the relevant function.
        """
        stack = inspect.stack()

        # Skip the first few frames (this method, create_changelog_entry, etc.)
        for frame_info in stack[3:]:  # Start from index 3 to skip internal calls
            frame = frame_info.frame
            function_name = frame.f_code.co_name
            module_name = frame.f_globals.get('__name__', '')

            # Skip internal Python functions and our own service methods
            if (
                not function_name.startswith('_')
                and 'cancellation_fee_auth' not in function_name.lower()
                and 'changelog' not in function_name.lower()
            ):

                # Return the most relevant function name
                if module_name:
                    return f"{module_name}.{function_name}"
                return function_name

        # Fallback
        return "unknown_function"

    @staticmethod
    def get_changelog_for_cf_auth(
        cf_auth: CancellationFeeAuth,
    ) -> list[CancellationFeeAuthChangelog]:
        """
        Get all changelog entries for a specific CancellationFeeAuth instance.

        Args:
            cf_auth: The CancellationFeeAuth instance

        Returns:
            List of changelog entries ordered by creation time (newest first)
        """
        return list(cf_auth.changelog.all().order_by('-created'))

    @staticmethod
    def get_changelog_by_operator(operator_id: int) -> list[CancellationFeeAuthChangelog]:
        """
        Get all changelog entries made by a specific operator.

        Args:
            operator_id: ID of the operator

        Returns:
            List of changelog entries made by the operator
        """
        return list(
            CancellationFeeAuthChangelog.objects.filter(operator_id=operator_id).order_by(
                '-created'
            )
        )

    @staticmethod
    def get_changelog_by_status_change(
        old_status: Optional[CancellationFeeAuthStatus] = None,
        new_status: Optional[CancellationFeeAuthStatus] = None,
    ) -> list[CancellationFeeAuthChangelog]:
        """
        Get changelog entries filtered by status changes.

        Args:
            old_status: Filter by old status
            new_status: Filter by new status

        Returns:
            List of matching changelog entries
        """
        queryset = CancellationFeeAuthChangelog.objects.all()

        if old_status:
            queryset = queryset.filter(old_status=old_status.value)

        if new_status:
            queryset = queryset.filter(new_status=new_status.value)

        return list(queryset.order_by('-created'))
