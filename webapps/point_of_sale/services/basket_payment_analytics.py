from dataclasses import asdict

from lib.payment_providers.entities import DeviceDataEntity
from lib.point_of_sale.enums import (
    BasketPaymentAnalyticsTrigger,
)
from webapps.point_of_sale.models import (
    BasketPayment,
    BasketPaymentAnalytics,
)


class BasketPaymentAnalyticsService:
    @staticmethod
    def create_analytics_data(
        basket_payment: BasketPayment,
        device_data: DeviceDataEntity | None,
        trigger: BasketPaymentAnalyticsTrigger | None,
    ) -> BasketPaymentAnalytics | None:
        """
        Method creates an instance of the BasketPaymentAnalytics model.
        :param basket_payment: A BasketPayment object representing the payment being made.
        :param device_data:  An optional DeviceDataEntity object representing the device
        related to action with the payment.
        :param trigger: An optional BasketPaymentAnalyticsTriggerSource object
        representing the source of the payment trigger.
        business and was paid by mobile_payment
        :return: If both the device_data and trigger parameters are not None
        then an instance of the BasketPaymentAnalytics model is created and returned.
        """
        if not device_data or not trigger:
            return

        return BasketPaymentAnalytics.objects.create(
            basket_payment=basket_payment,
            device_data=asdict(device_data),
            trigger=trigger,
        )
