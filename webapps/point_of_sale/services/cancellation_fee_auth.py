import uuid
from dataclasses import asdict

from lib.feature_flag.feature import CancellationFeeFullAmountAuthorizationFlag
from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)
from lib.payment_gateway.enums import PaymentStatus
from lib.payment_providers.entities import (
    AuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketPaymentStatus,
    CancellationFeeAuthStatus,
    PaymentMethodType,
)
from lib.point_of_sale.events import cancellation_fee_auth_status_updated_event
from lib.smartlock import SmartLock
from webapps.point_of_sale.adapters import (
    authorize_payment_adapter,
    cancel_balance_transaction_adapter,
    get_business_wallet_id_adapter,
    get_customer_wallet_id_adapter,
    initialize_payment_adapter,
)
from webapps.point_of_sale.consts import PAYMENT_STATUS_INTO_CANCELLATION_FEE_AUTH_STATUS_MAP
from webapps.point_of_sale.exceptions import OperationNotAllowed
from webapps.point_of_sale.models import CancellationFeeAuth
from webapps.point_of_sale.services.basket_payment import BasketPaymentService
from webapps.point_of_sale.services.cancellation_fee_auth_changelog import (
    CancellationFeeAuthChangelogService,
)


class CancellationFeeAuthService:
    @staticmethod
    def map_balance_transaction_payment_status(status: PaymentStatus) -> CancellationFeeAuthStatus:
        """Maps PaymentStatus into CancellationFeeAuthStatus."""
        return PAYMENT_STATUS_INTO_CANCELLATION_FEE_AUTH_STATUS_MAP[status]

    @staticmethod
    def _change_cf_auth_status(
        cf_auth: CancellationFeeAuth,
        new_status: CancellationFeeAuthStatus,
    ) -> CancellationFeeAuth:
        """
        Changes status of cancellation fee auth.
        SUCCESS, FAILED and CANCELED states are terminal.

        :param cf_auth: CancellationFeeAuth object
        :param new_status: status we want to set in provided CancellationFeeAuth
        """
        allowed_new_status = {
            CancellationFeeAuthStatus.PENDING: [
                CancellationFeeAuthStatus.PENDING,
                CancellationFeeAuthStatus.SUCCESS,
                CancellationFeeAuthStatus.FAILED,
                CancellationFeeAuthStatus.CANCELED,
            ],
            CancellationFeeAuthStatus.SUCCESS: [CancellationFeeAuthStatus.CANCELED],
            CancellationFeeAuthStatus.FAILED: [],
            CancellationFeeAuthStatus.CANCELED: [],
        }[CancellationFeeAuthStatus(cf_auth.status)]

        if new_status not in allowed_new_status:
            raise OperationNotAllowed(f"Invalid transition {cf_auth.status} -> {new_status}")

        cf_auth.status = new_status
        return cf_auth

    @staticmethod
    def update_status(
        cf_auth: CancellationFeeAuth,
        new_status: CancellationFeeAuthStatus,
        operator_id: int = None,
    ):
        """
        Updates status of cancellation_fee_auth.

        :param cf_auth: CancellationFeeAuth which will be edited
        :param new_status: status we want to set in provided CancellationFeeAuth
        :param operator_id: ID of the user making the change (optional)
        """
        with SmartLock(key=str(cf_auth.id)):
            cf_auth.refresh_from_db()
            old_status = CancellationFeeAuthStatus(cf_auth.status)

            # If CancellationFeeAuth is already in canceled status we don't want to set
            # it second time. Cancel process triggers it twice, because:
            # 1. First time is synchronous action in CancellationFeeAuthService.cancel_payment
            # 2. Second can be triggered as signal callback after canceling BalanceTransaction
            #    - we want to omit it
            if (
                cf_auth.status == CancellationFeeAuthStatus.CANCELED
                and new_status == CancellationFeeAuthStatus.CANCELED
            ):
                return cf_auth

            cf_auth = CancellationFeeAuthService._change_cf_auth_status(
                cf_auth=cf_auth, new_status=new_status
            )
            cf_auth.save()

            # Create changelog entry for the status change
            CancellationFeeAuthChangelogService.create_changelog_entry(
                cf_auth=cf_auth,
                old_status=old_status,
                new_status=new_status,
                operator_id=operator_id,
                metadata={
                    'change_type': 'status_update',
                    'balance_transaction_id': (
                        str(cf_auth.balance_transaction_id)
                        if cf_auth.balance_transaction_id
                        else None
                    ),
                },
            )

            cancellation_fee_auth_status_updated_event.send(asdict(cf_auth.entity))
            return cf_auth

    @staticmethod
    def get_auth(cf_auth_id: uuid.UUID) -> CancellationFeeAuth | None:
        """
        Gets CancellationFeeAuth if exists.

        :param cf_auth_id: if of CancellationFeeAuth
        """
        return CancellationFeeAuth.objects.filter(id=cf_auth_id).first()

    # pylint: disable=unused-argument
    @staticmethod
    def create_auth(  # pylint: disable=too-many-arguments
        amount: int,
        appointment_id: int,
        sender_user_id: int,
        business_id: int,
        payment_provider_code: PaymentProviderCode,
        payment_method_type: PaymentMethodType,
        payment_splits: PaymentSplitsEntity,
        refund_splits: RefundSplitsEntity,
        dispute_splits: DisputeSplitsEntity,
        operator_id: int = None,
        basket_id: uuid.UUID = None,
    ) -> CancellationFeeAuth:
        """
        Method creates CancellationFeeAuth and
        if FeatureFlag.FEATURE__CANCELLATION_FEE_FULL_AMOUNT_AUTHORIZATION == True,
        triggers creation in PaymentGateway app.
        """
        cf_auth = CancellationFeeAuth(
            amount=amount,
            appointment_id=appointment_id,
            business_id=business_id,
            payment_provider_code=payment_provider_code,
            basket_id=basket_id,
        )

        if CancellationFeeFullAmountAuthorizationFlag():
            sender_wallet_id = get_customer_wallet_id_adapter(sender_user_id)
            receiver_wallet_id = get_business_wallet_id_adapter(business_id)

            if sender_wallet_id and receiver_wallet_id:
                balance_transaction_id = initialize_payment_adapter(
                    amount=amount,
                    sender_id=sender_wallet_id,
                    receiver_id=receiver_wallet_id,
                    payment_method=payment_method_type,
                    payment_provider_code=payment_provider_code,
                    auto_capture=False,
                    payment_splits=payment_splits,
                    refund_splits=refund_splits,
                    dispute_splits=dispute_splits,
                )

                cf_auth.balance_transaction_id = balance_transaction_id
        cf_auth.save()

        # Create changelog entry for the creation
        CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=cf_auth,
            old_status=None,  # No previous status for creation
            new_status=CancellationFeeAuthStatus(cf_auth.status),
            operator_id=operator_id,
            metadata={
                'change_type': 'creation',
                'amount': amount,
                'appointment_id': appointment_id,
                'business_id': business_id,
                'basket_id': basket_id,
                'payment_provider_code': payment_provider_code.value,
                'balance_transaction_id': (
                    str(cf_auth.balance_transaction_id) if cf_auth.balance_transaction_id else None
                ),
            },
        )
        return cf_auth

    @staticmethod
    def authorize_cf_auth(
        cf_auth: CancellationFeeAuth,
        wallet_id: uuid.UUID,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        additional_data: AuthAdditionalDataEntity | None,
        off_session: bool | None = None,
        operator_id: int = None,
    ):
        with SmartLock(key=str(cf_auth.id)):
            authorize_payment_adapter(
                balance_transaction_id=cf_auth.balance_transaction_id,
                wallet_id=wallet_id,
                additional_data=additional_data,
                payment_method_data=payment_method_data,
                off_session=off_session,
            )

            # Create changelog entry for authorization attempt
            CancellationFeeAuthChangelogService.create_changelog_entry(
                cf_auth=cf_auth,
                old_status=CancellationFeeAuthStatus(cf_auth.status),
                new_status=CancellationFeeAuthStatus(
                    cf_auth.status
                ),  # Status might change via events
                operator_id=operator_id,
                metadata={
                    'change_type': 'authorization_attempt',
                    'wallet_id': str(wallet_id),
                    'off_session': off_session,
                },
            )

    @staticmethod
    def cancel_authorization(
        cf_auth: CancellationFeeAuth,
        wallet_id: uuid.UUID,
        operator_id: int = None,
    ) -> bool:
        with SmartLock(key=str(cf_auth.id)):
            result = True
            if cf_auth.basket:
                # Scenario when Cancellation is trigger for CF with already existing Basket.
                basket_payments = cf_auth.basket.payments.filter(
                    status=BasketPaymentStatus.PENDING,
                    balance_transaction_id__isnull=False,
                )

                for basket_payment in basket_payments:
                    result = BasketPaymentService.cancel_payment(
                        basket_payment=basket_payment,
                    )
                    if not result:
                        return False

            if cf_auth.balance_transaction_id:
                # Adyen is using offline authorization for now - we don't even try to authorize, so
                # object in PaymentGateway and PaymentProviders are not necessary.
                result = cancel_balance_transaction_adapter(
                    balance_transaction_id=cf_auth.balance_transaction_id,
                    wallet_id=wallet_id,
                )

            if result:
                CancellationFeeAuthService.update_status(
                    cf_auth=cf_auth,
                    new_status=CancellationFeeAuthStatus.CANCELED,
                    operator_id=operator_id,
                )
            return result
