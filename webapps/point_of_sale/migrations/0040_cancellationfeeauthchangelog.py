# Generated by Django 4.2.23 on 2025-06-26 21:21

import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('point_of_sale', '0039_alter_basket_managers'),
    ]

    operations = [
        migrations.CreateModel(
            name='CancellationFeeAuthChangelog',
            fields=[
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('traceback', models.TextField()),
                (
                    'operator_id',
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text='ID of the user who made the change',
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    'old_status',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('pending', 'Pending'),
                            ('success', 'Success'),
                            ('failed', 'Failed'),
                            ('canceled', 'Canceled'),
                        ],
                        help_text='Previous status before the change',
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    'new_status',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('pending', 'Pending'),
                            ('success', 'Success'),
                            ('failed', 'Failed'),
                            ('canceled', 'Canceled'),
                        ],
                        help_text='New status after the change',
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    'function_name',
                    models.CharField(
                        blank=True,
                        help_text='Name of the function that triggered the change',
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    'metadata',
                    models.JSONField(
                        default=dict, help_text='Additional metadata about the change'
                    ),
                ),
                (
                    'cf_auth',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='changelog',
                        to='point_of_sale.cancellationfeeauth',
                    ),
                ),
            ],
            options={
                'verbose_name': 'CancellationFeeAuth Changelog',
                'verbose_name_plural': 'CancellationFeeAuth Changelogs',
                'ordering': ('-created',),
            },
        ),
    ]
