import uuid
from decimal import Decimal

from django.test import TestCase
from mock import MagicMock, patch
from model_bakery import baker

from lib.feature_flag.feature import EnableCancellationFeeAuthChangelogServiceFlag
from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    RefundSplitsEntity,
    PaymentSplitsEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import (
    CancellationFeeAuthStatus,
    PaymentMethodType,
)
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import minor_unit
from webapps.point_of_sale.models import CancellationFeeAuth, CancellationFeeAuthChangelog
from webapps.point_of_sale.services.cancellation_fee_auth import CancellationFeeAuthService
from webapps.point_of_sale.services.cancellation_fee_auth_changelog import (
    CancellationFeeAuthChangelogService,
)


class CancellationFeeAuthChangelogTests(TestCase):
    def setUp(self):
        self.cf_auth = baker.make(
            CancellationFeeAuth,
            status=CancellationFeeAuthStatus.PENDING,
            amount=1000,
            business_id=1,
            appointment_id=123,
        )

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: True})
    def test_create_changelog_entry(self):
        """Test creating a changelog entry for status change."""
        old_status = CancellationFeeAuthStatus.PENDING
        new_status = CancellationFeeAuthStatus.SUCCESS
        operator_id = 456

        changelog_entry = CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=old_status,
            new_status=new_status,
            operator_id=operator_id,
            function_name='test_function',
            metadata={'test_key': 'test_value'},
        )

        self.assertIsNotNone(changelog_entry)
        self.assertEqual(changelog_entry.cf_auth, self.cf_auth)
        self.assertEqual(changelog_entry.operator_id, operator_id)
        self.assertEqual(changelog_entry.old_status, old_status.value)
        self.assertEqual(changelog_entry.new_status, new_status.value)
        self.assertEqual(changelog_entry.function_name, 'test_function')
        self.assertEqual(changelog_entry.metadata['test_key'], 'test_value')

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: False})
    def test_create_changelog_entry_feature_flag_disabled(self):
        """Test that no changelog entry is created when feature flag is disabled."""
        old_status = CancellationFeeAuthStatus.PENDING
        new_status = CancellationFeeAuthStatus.SUCCESS
        operator_id = 456

        changelog_entry = CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=old_status,
            new_status=new_status,
            operator_id=operator_id,
            function_name='test_function',
            metadata={'test_key': 'test_value'},
        )

        self.assertIsNone(changelog_entry)
        # Verify no changelog entry was created in the database
        self.assertEqual(CancellationFeeAuthChangelog.objects.count(), 0)

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: True})
    def test_create_changelog_entry_without_operator_id(self):
        """Test creating a changelog entry without operator_id."""
        changelog_entry = CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=CancellationFeeAuthStatus.PENDING,
            new_status=CancellationFeeAuthStatus.SUCCESS,
        )

        self.assertIsNotNone(changelog_entry)
        self.assertIsNone(changelog_entry.operator_id)
        self.assertIsNotNone(changelog_entry.function_name)

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: True})
    def test_get_changelog_for_cf_auth(self):
        """Test getting changelog entries for a specific CF auth."""
        # Create multiple changelog entries
        changelog1 = CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=CancellationFeeAuthStatus.PENDING,
            new_status=CancellationFeeAuthStatus.SUCCESS,
            operator_id=1,
        )
        changelog2 = CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=CancellationFeeAuthStatus.SUCCESS,
            new_status=CancellationFeeAuthStatus.CANCELED,
            operator_id=2,
        )

        # Create changelog for different CF auth
        other_cf_auth = baker.make(CancellationFeeAuth)
        CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=other_cf_auth,
            old_status=CancellationFeeAuthStatus.PENDING,
            new_status=CancellationFeeAuthStatus.FAILED,
            operator_id=3,
        )

        changelog_entries = CancellationFeeAuthChangelogService.get_changelog_for_cf_auth(
            self.cf_auth
        )

        self.assertEqual(len(changelog_entries), 2)
        self.assertEqual(changelog_entries[0], changelog2)  # Most recent first
        self.assertEqual(changelog_entries[1], changelog1)

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: True})
    def test_get_changelog_by_operator(self):
        """Test getting changelog entries by operator."""
        operator_id = 123

        # Create changelog entries for the operator
        changelog1 = CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=CancellationFeeAuthStatus.PENDING,
            new_status=CancellationFeeAuthStatus.SUCCESS,
            operator_id=operator_id,
        )

        # Create changelog entry for different operator
        CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=CancellationFeeAuthStatus.SUCCESS,
            new_status=CancellationFeeAuthStatus.CANCELED,
            operator_id=456,
        )

        changelog_entries = CancellationFeeAuthChangelogService.get_changelog_by_operator(
            operator_id
        )

        self.assertEqual(len(changelog_entries), 1)
        self.assertEqual(changelog_entries[0], changelog1)

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: True})
    def test_get_changelog_by_status_change(self):
        """Test getting changelog entries filtered by status changes."""
        # Create changelog entries with different status changes
        changelog1 = CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=CancellationFeeAuthStatus.PENDING,
            new_status=CancellationFeeAuthStatus.SUCCESS,
            operator_id=1,
        )
        CancellationFeeAuthChangelogService.create_changelog_entry(
            cf_auth=self.cf_auth,
            old_status=CancellationFeeAuthStatus.SUCCESS,
            new_status=CancellationFeeAuthStatus.CANCELED,
            operator_id=2,
        )

        # Filter by old status
        entries = CancellationFeeAuthChangelogService.get_changelog_by_status_change(
            old_status=CancellationFeeAuthStatus.PENDING
        )
        self.assertEqual(len(entries), 1)
        self.assertEqual(entries[0], changelog1)

        # Filter by new status
        entries = CancellationFeeAuthChangelogService.get_changelog_by_status_change(
            new_status=CancellationFeeAuthStatus.SUCCESS
        )
        self.assertEqual(len(entries), 1)
        self.assertEqual(entries[0], changelog1)

    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.get_customer_wallet_id_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.get_business_wallet_id_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.initialize_payment_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: True})
    def test_create_auth_creates_changelog(self):
        """Test that creating a CF auth creates a changelog entry."""
        operator_id = '789'

        cf_auth = CancellationFeeAuthService.create_auth(
            amount=minor_unit(123),
            appointment_id=1,
            sender_user_id=2,
            business_id=3,
            payment_provider_code=PaymentProviderCode.ADYEN,
            payment_method_type=PaymentMethodType.CARD,
            payment_splits=PaymentSplitsEntity(fixed_fee=1, percentage_fee=Decimal('0.00')),
            refund_splits=RefundSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            dispute_splits=DisputeSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            operator_id=operator_id,
        )

        # Check that changelog entry was created
        changelog_entries = CancellationFeeAuthChangelogService.get_changelog_for_cf_auth(cf_auth)
        self.assertEqual(len(changelog_entries), 1)

        changelog_entry = changelog_entries[0]
        self.assertEqual(changelog_entry.cf_auth, cf_auth)
        self.assertEqual(changelog_entry.operator_id, operator_id)
        self.assertIsNone(changelog_entry.old_status)  # No previous status for creation
        self.assertEqual(changelog_entry.new_status, CancellationFeeAuthStatus.PENDING.value)
        self.assertEqual(changelog_entry.metadata['change_type'], 'creation')

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: True})
    def test_update_status_creates_changelog(self):
        """Test that updating CF auth status creates a changelog entry."""
        operator_id = '456'

        CancellationFeeAuthService.update_status(
            cf_auth=self.cf_auth,
            new_status=CancellationFeeAuthStatus.SUCCESS,
            operator_id=operator_id,
        )

        # Check that changelog entry was created
        changelog_entries = CancellationFeeAuthChangelogService.get_changelog_for_cf_auth(
            self.cf_auth
        )
        self.assertEqual(len(changelog_entries), 1)

        changelog_entry = changelog_entries[0]
        self.assertEqual(changelog_entry.cf_auth, self.cf_auth)
        self.assertEqual(changelog_entry.operator_id, operator_id)
        self.assertEqual(changelog_entry.old_status, CancellationFeeAuthStatus.PENDING.value)
        self.assertEqual(changelog_entry.new_status, CancellationFeeAuthStatus.SUCCESS.value)
        self.assertEqual(changelog_entry.metadata['change_type'], 'status_update')

    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.get_customer_wallet_id_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.get_business_wallet_id_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.initialize_payment_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: False})
    def test_create_auth_no_changelog_when_flag_disabled(self):
        """Test that no changelog entry is created when feature flag is disabled."""
        operator_id = 789

        cf_auth = CancellationFeeAuthService.create_auth(
            amount=minor_unit(123),
            appointment_id=1,
            sender_user_id=2,
            business_id=3,
            payment_provider_code=PaymentProviderCode.ADYEN,
            payment_method_type=PaymentMethodType.CARD,
            payment_splits=PaymentSplitsEntity(fixed_fee=1, percentage_fee=Decimal('0.00')),
            refund_splits=RefundSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            dispute_splits=DisputeSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            operator_id=operator_id,
        )

        # Check that no changelog entry was created
        changelog_entries = CancellationFeeAuthChangelogService.get_changelog_for_cf_auth(
            self.cf_auth
        )
        self.assertEqual(len(changelog_entries), 0)

    @override_eppo_feature_flag({EnableCancellationFeeAuthChangelogServiceFlag.flag_name: False})
    def test_update_status_no_changelog_when_flag_disabled(self):
        """Test that no changelog entry is created when updating status and feature flag is disabled."""
        operator_id = 456

        CancellationFeeAuthService.update_status(
            cf_auth=self.cf_auth,
            new_status=CancellationFeeAuthStatus.SUCCESS,
            operator_id=operator_id,
        )

        # Check that no changelog entry was created
        changelog_entries = CancellationFeeAuthChangelogService.get_changelog_for_cf_auth(
            self.cf_auth
        )
        self.assertEqual(len(changelog_entries), 0)
