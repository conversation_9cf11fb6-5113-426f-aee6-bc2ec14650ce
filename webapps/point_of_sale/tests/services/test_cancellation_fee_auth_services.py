import uuid
from decimal import Decimal

from django.test import TestCase
from mock import MagicMock, patch
from model_bakery import baker
from parameterized import parameterized

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    RefundSplitsEntity,
    PaymentSplitsEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketPaymentStatus,
    CancellationFeeAuthStatus,
    PaymentMethodType,
)
from lib.tools import minor_unit
from webapps.point_of_sale.exceptions import OperationNotAllowed
from webapps.point_of_sale.models import Basket, BasketPayment, CancellationFeeAuth
from webapps.point_of_sale.services.cancellation_fee_auth import CancellationFeeAuthService


class CancellationFeeAuthTests(TestCase):
    @parameterized.expand(
        [
            (CancellationFeeAuthStatus.PENDING, CancellationFeeAuthStatus.PENDING),
            (CancellationFeeAuthStatus.PENDING, CancellationFeeAuthStatus.SUCCESS),
            (CancellationFeeAuthStatus.PENDING, CancellationFeeAuthStatus.FAILED),
            (CancellationFeeAuthStatus.PENDING, CancellationFeeAuthStatus.CANCELED),
            (CancellationFeeAuthStatus.SUCCESS, CancellationFeeAuthStatus.CANCELED),
        ]
    )
    def test_change_cf_auth_status__correct(self, initial_status, new_status):
        cf_auth = baker.make(CancellationFeeAuth, status=initial_status)
        returned_cf_auth = (
            CancellationFeeAuthService._change_cf_auth_status(  # pylint: disable=protected-access
                cf_auth=cf_auth, new_status=new_status
            )
        )

        self.assertEqual(returned_cf_auth.status, new_status)

    @parameterized.expand(
        [
            (CancellationFeeAuthStatus.SUCCESS, CancellationFeeAuthStatus.PENDING),
            (CancellationFeeAuthStatus.SUCCESS, CancellationFeeAuthStatus.SUCCESS),
            (CancellationFeeAuthStatus.SUCCESS, CancellationFeeAuthStatus.FAILED),
            (CancellationFeeAuthStatus.FAILED, CancellationFeeAuthStatus.PENDING),
            (CancellationFeeAuthStatus.FAILED, CancellationFeeAuthStatus.SUCCESS),
            (CancellationFeeAuthStatus.FAILED, CancellationFeeAuthStatus.FAILED),
            (CancellationFeeAuthStatus.FAILED, CancellationFeeAuthStatus.CANCELED),
            (CancellationFeeAuthStatus.CANCELED, CancellationFeeAuthStatus.PENDING),
            (CancellationFeeAuthStatus.CANCELED, CancellationFeeAuthStatus.SUCCESS),
            (CancellationFeeAuthStatus.CANCELED, CancellationFeeAuthStatus.FAILED),
            (CancellationFeeAuthStatus.CANCELED, CancellationFeeAuthStatus.CANCELED),
        ]
    )
    def test_change_cf_auth_status__incorrect(self, initial_status, new_status):
        cf_auth = baker.make(CancellationFeeAuth, status=initial_status)
        with self.assertRaises(OperationNotAllowed):
            CancellationFeeAuthService._change_cf_auth_status(  # pylint: disable=protected-access
                cf_auth=cf_auth, new_status=new_status
            )

    def test_update_status(self):
        cf_auth = baker.make(
            CancellationFeeAuth,
            amount=100,
            status=CancellationFeeAuthStatus.PENDING,
            business_id=1,
        )

        saved_cf_auth = CancellationFeeAuthService.update_status(
            cf_auth=cf_auth,
            new_status=CancellationFeeAuthStatus.SUCCESS,
        )
        self.assertEqual(saved_cf_auth.status, CancellationFeeAuthStatus.SUCCESS)

    def test_get_auth(self):
        cf_auth = baker.make(CancellationFeeAuth)

        returned_obj = CancellationFeeAuthService.get_auth(cf_auth_id=cf_auth.id)
        self.assertEqual(returned_obj, cf_auth)

    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.get_customer_wallet_id_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.get_business_wallet_id_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.initialize_payment_adapter',
        MagicMock(return_value=uuid.uuid4()),
    )
    def test_create_auth_wo_killswitch(self):
        cf_auth = CancellationFeeAuthService.create_auth(
            amount=minor_unit(123),
            appointment_id=1,
            sender_user_id=2,
            business_id=3,
            payment_provider_code=PaymentProviderCode.ADYEN,
            payment_method_type=PaymentMethodType.CARD,
            payment_splits=PaymentSplitsEntity(fixed_fee=1, percentage_fee=Decimal('0.00')),
            refund_splits=RefundSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            dispute_splits=DisputeSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
        )

        self.assertEqual(cf_auth.amount, minor_unit(123))
        self.assertEqual(cf_auth.appointment_id, 1)
        self.assertEqual(cf_auth.business_id, 3)
        self.assertNotEqual(cf_auth.id, None)

    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.BasketPaymentService.cancel_payment'
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.cancel_balance_transaction_adapter'
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.'
        'CancellationFeeAuthService.update_status'
    )
    def test_cancel_authorization_offline(
        self,
        cf_update_status_mock,
        cancel_bt_mock,
        cancel_bp_mock,
    ):
        cf_auth = baker.make(
            CancellationFeeAuth,
            basket_id=None,
            balance_transaction_id=None,
        )

        CancellationFeeAuthService.cancel_authorization(cf_auth, uuid.uuid4())

        cf_update_status_mock.assert_called_once_with(
            cf_auth=cf_auth,
            new_status=CancellationFeeAuthStatus.CANCELED,
            operator_id=None,
        )
        cancel_bp_mock.assert_not_called()
        cancel_bt_mock.assert_not_called()

    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.BasketPaymentService.cancel_payment'
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.cancel_balance_transaction_adapter'
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.'
        'CancellationFeeAuthService.update_status'
    )
    def test_cancel_authorization_online(
        self,
        cf_update_status_mock,
        cancel_bt_mock,
        cancel_bp_mock,
    ):
        cancel_bt_mock.return_value = True
        bt_id = uuid.uuid4()
        wallet_id = uuid.uuid4()

        cf_auth = baker.make(
            CancellationFeeAuth,
            basket_id=None,
            balance_transaction_id=bt_id,
        )

        CancellationFeeAuthService.cancel_authorization(cf_auth, wallet_id)

        cf_update_status_mock.assert_called_once_with(
            cf_auth=cf_auth,
            new_status=CancellationFeeAuthStatus.CANCELED,
            operator_id=None,
        )
        cancel_bt_mock.assert_called_once_with(
            balance_transaction_id=bt_id,
            wallet_id=wallet_id,
        )
        cancel_bp_mock.assert_not_called()

    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.BasketPaymentService.cancel_payment'
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.cancel_balance_transaction_adapter'
    )
    @patch(
        'webapps.point_of_sale.services.cancellation_fee_auth.'
        'CancellationFeeAuthService.update_status'
    )
    def test_cancel_authorization_online_with_basket(
        self,
        cf_update_status_mock,
        cancel_bt_mock,
        cancel_bp_mock,
    ):
        cancel_bt_mock.return_value = True
        bt_id = uuid.uuid4()
        wallet_id = uuid.uuid4()

        basket = baker.make(Basket)
        basket_payment1 = baker.make(
            BasketPayment,
            basket=basket,
            balance_transaction_id=uuid.uuid4(),
            status=BasketPaymentStatus.PENDING,
        )
        baker.make(
            BasketPayment,
            basket=basket,
            balance_transaction_id=uuid.uuid4(),
            status=BasketPaymentStatus.SUCCESS,
        )

        cf_auth = baker.make(
            CancellationFeeAuth,
            basket_id=basket.id,
            balance_transaction_id=bt_id,
        )

        CancellationFeeAuthService.cancel_authorization(cf_auth, wallet_id)

        cf_update_status_mock.assert_called_once_with(
            cf_auth=cf_auth,
            new_status=CancellationFeeAuthStatus.CANCELED,
            operator_id=None,
        )
        cancel_bt_mock.assert_called_once_with(
            balance_transaction_id=bt_id,
            wallet_id=wallet_id,
        )
        cancel_bp_mock.assert_called_once_with(basket_payment=basket_payment1)
