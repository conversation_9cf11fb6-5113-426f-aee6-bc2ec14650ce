# pylint: disable=cyclic-import
import datetime
from collections import defaultdict
from decimal import Decimal
from functools import reduce
from itertools import chain
from typing import (
    List,
    Optional,
)

from django.conf import settings
from django.db import transaction as db_transaction
from django.db.models import (
    Count,
    Max,
    Q,
    Sum,
)
from django.db.models.expressions import (
    F,
    OuterRef,
    Subquery,
    Value,
)
from django.db.models.fields import IntegerField
from django.db.models.functions import Coalesce
from django.db.models.query import QuerySet
from django.utils.functional import cached_property
from django.utils.translation import gettext as _
from rest_framework.fields import get_attribute as _get_attribute

from country_config.enums import Country
from lib.cache import lru_booksy_cache
from lib.db import retry_on_sync_error
from lib.french_certification.utils import french_certification_enabled
from lib.payments.enums import PaymentProviderCode
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.tools import (
    firstof,
    format_currency,
    format_currency_in_locale,
    format_float,
    major_unit,
    sget_v2,
    tznow,
)
from lib.unicode_utils import force_unicode
from webapps.booking.models import Appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    Resource,
    ServiceAddOn,
)
from webapps.notification.models import NotificationHistory
from webapps.notification.scenarios import (
    BookingChangedScenario as BCS,
    start_scenario,
)
from webapps.payment_gateway.consts import MINIMAL_PAYMENT_AMOUNT
from webapps.point_of_sale.ports import POSPort
from webapps.pos import enums
from webapps.pos.calculations import round_currency
from webapps.pos.enums import (
    COMMISSION_TYPE__EGIFT_CARD,
    COMMISSION_TYPE__MEMBERSHIP,
    COMMISSION_TYPE__PACKAGE,
    COMMISSION_TYPE__PRODUCT,
    COMMISSION_TYPE__SERVICE,
    PAY_BY_APP_ACTIVE_STATUSES,
    PAY_BY_APP_ENABLED,
    PAY_BY_APP_PENDING,
    POSPlanPaymentTypeEnum,
    POS_PLAN_FEE_DESCRIPTION_EXCLUDING_TAX_MAP,
    POS_PLAN_FEE_DESCRIPTION_MAP,
    PaymentTypeEnum,
    bank_account_type,
    receipt_status,
)
from webapps.pos.enums.tax_rates import COUNTRIES_TAX_RATES
from webapps.pos.models import (
    Commission,
    CommissionDefaults,
    CommissionRate,
    PaymentRow,
    POS,
    POSPlan,
    ServiceVariant,
    StaffCommissionDefault,
    StaffCommissionRate,
    Transaction,
    TransactionRow,
)
from webapps.pos.services import CommissionService
from webapps.pos.tasks import log_transaction_commission_changes_task
from webapps.user.models import User
from webapps.voucher.enums import VoucherType
from webapps.voucher.utils import VoucherMigrationConfig
from webapps.voucher.models import Voucher
from webapps.warehouse.models import Commodity


def get_attribute(instance, attrs):
    """rest_framework get_attribute exception safe"""
    try:
        return _get_attribute(instance, attrs)
    except KeyError:
        return None


def get_chargeable_booking_filters(pos_id):
    initiated_payment_qs = Transaction.objects.filter(
        pos__id=pos_id,
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    ).exclude(
        latest_receipt__status_code__in=receipt_status.PAYABLE_STATUSES,
    )
    # payable == (FINISHED or ongoing ACCEPTED)
    # and not initiated_payment
    # all ACCEPTED are payable, but we do not show it on chargeable listings
    return [
        Q(
            status__in=[
                Appointment.STATUS.FINISHED,
                Appointment.STATUS.ACCEPTED,
            ]
        ),
        Q(booked_from__lte=tznow()),  # only ongoing ACCEPTED
        Q(type__in=Appointment.TYPES_BOOKABLE),
        ~(Q(transactions__in=initiated_payment_qs)),
    ]


def get_chargeable_deposits_filters(pos_id):
    last_receipt_deposit_qs = Transaction.objects.filter(
        pos__id=pos_id,
        transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        latest_receipt__status_code__in=[
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            receipt_status.DEPOSIT_CHARGE_FAILED,
        ],
    )
    # chargeable == (NOSHOW or CANCELED) and authorized_deposit
    return [
        Q(
            status__in=[
                Appointment.STATUS.NOSHOW,
                Appointment.STATUS.CANCELED,
            ]
        ),
        Q(type__in=Appointment.TYPES_BOOKABLE),
        Q(transactions__in=last_receipt_deposit_qs),
    ]


def get_charge_filters(filter_name, pos_id):
    if filter_name == 'sales':
        return get_chargeable_booking_filters(pos_id)
    if filter_name == 'deposits':
        return get_chargeable_deposits_filters(pos_id)
    if filter_name == 'all':
        from operator import and_

        # get_chargeable_booking_filters or get_chargeable_deposits_filters
        # [(Q(...) & Q(...)) | (Q(...) & Q(...))]
        return [
            reduce(and_, get_chargeable_booking_filters(pos_id))
            | reduce(and_, get_chargeable_deposits_filters(pos_id))
        ]
    raise NotImplementedError


# <editor-fold desc="get_transaction_counts">
# Refactored in big hurry
def _get_raw_counts(ids):
    raw_counts = dict(
        Transaction.objects.filter(
            # Prefetches used in transactions_qs cause duplication of items.
            # New query is required
            id__in=ids
        )
        .values_list('transaction_type')  # GROUP BY transaction_type
        .annotate(count=Count('transaction_type'))
    )
    return raw_counts


def _format_counts(raw_counts):
    transaction_counts = {
        'all': sum(raw_counts.values()),
        'sales': raw_counts.get(Transaction.TRANSACTION_TYPE__PAYMENT, 0),
        'deposits': raw_counts.get(Transaction.TRANSACTION_TYPE__CANCELLATION_FEE, 0),
    }
    return transaction_counts


@lru_booksy_cache(timeout=3600)  # one hour timeout
def get_transaction_counts_by_id(transactions_ids: List[int]):
    raw_counts = _get_raw_counts(transactions_ids)
    return _format_counts(raw_counts)


def get_transaction_counts(transactions_qs):
    """Create POSFaceting for given Transaction QuerySet"""
    raw_counts = _get_raw_counts(transactions_qs.values_list('id', flat=True))
    return _format_counts(raw_counts)


# </editor-fold>


TRANSACTION_FILTERS = {
    'all': Q(),
    'sales': Q(transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT),
    'deposits': Q(transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE),
}
RECEIPT_STATUS_FILTERS = ['all'] + sorted(receipt_status.STATUS_TYPES)


class CommissionRater:
    def __init__(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        pos,
        resource=None,
        products=None,
        service_variants=None,
        voucher_templates=None,
        transaction=None,
    ):
        if products is None:
            products = []
        if service_variants is None:
            service_variants = []
        if voucher_templates is None:
            voucher_templates = []
        if transaction:
            assert not products and not service_variants

        self.pos = pos
        self.resource = resource
        self.products = products
        self.service_variants = service_variants
        self.voucher_templates = voucher_templates
        self.transaction = transaction

        # ensure commission_defaults exists
        try:
            self.global_default = self.pos.commission_defaults
        except CommissionDefaults.DoesNotExist:
            self.global_default = CommissionDefaults.objects.get_or_create(pos=self.pos)[0]

    def commission_defaults(self, resource_id: int = None):
        if resource_id:
            resource = Resource.objects.get(business=self.pos.business, id=resource_id)
            return StaffCommissionDefault.get_with_defaults(resource=resource)
        return self.global_default

    @cached_property
    def product_for_cashier(self):
        return (
            self.global_default.product_commission_for == CommissionDefaults.COMMISSION_FOR__CASHIER
        )

    def voucher_template_for_cashier(self, voucher_type: VoucherType):
        attribute = {
            VoucherType.EGIFT_CARD.value: 'egift_commission_for',
            VoucherType.PACKAGE.value: 'package_commission_for',
            VoucherType.MEMBERSHIP.value: 'membership_commission_for',
        }[voucher_type]

        return getattr(self.global_default, attribute) == CommissionDefaults.COMMISSION_FOR__CASHIER

    @cached_property
    def _product_ids(self):
        ids = (
            [r.product_id for r in self.transaction.rows.all()]
            if self.transaction
            else [p.id for p in self.products]
        )
        return [_f for _f in ids if _f]

    @cached_property
    def _service_variant_ids(self):
        ids = (
            [self._get_variant_id_from_row(row) for row in self.transaction.rows.all()]
            if self.transaction
            else [s.id for s in self.service_variants]
        )
        return [_f for _f in ids if _f]

    @cached_property
    def _voucher_templates_ids(self):
        ids = (
            [r.voucher_template.id for r in self.transaction.rows.all() if r.voucher_template]
            if self.transaction
            else [vt.id for vt in self.voucher_templates]
        )

        return [_f for _f in ids if _f]

    def get_commission_rates(self):
        commission_rates = CommissionRate.objects.filter(
            Q(product_id__in=self._product_ids)
            | Q(service_variant_id__in=self._service_variant_ids)
            | Q(voucher_template_id__in=self._voucher_templates_ids)
        )

        product_commissions = {}
        service_variant_commissions = {}
        egifts_commissions = {}
        membership_commissions = {}
        package_commissions = {}

        for c in commission_rates:
            if c.product_id:
                product_commissions[c.product_id] = c
            elif c.service_variant_id:
                service_variant_commissions[c.service_variant_id] = c
            elif c.voucher_template_id and c.voucher_template.egift_card:
                egifts_commissions[c.voucher_template_id] = c
            elif c.voucher_template_id and c.voucher_template.membership:
                membership_commissions[c.voucher_template_id] = c
            elif c.voucher_template_id and c.voucher_template.package:
                package_commissions[c.voucher_template_id] = c

        return (
            product_commissions,
            service_variant_commissions,
            egifts_commissions,
            membership_commissions,
            package_commissions,
        )

    def get_staff_rates(self):
        return StaffCommissionRate.objects.filter(
            Q(product_id__in=self._product_ids)
            | Q(service_variant_id__in=self._service_variant_ids)
            | Q(voucher_template_id__in=self._voucher_templates_ids),
            pos=self.pos,
        )

    def get_default(self, type_name, resource_id):
        resource_commission_defaults = self.commission_defaults(resource_id)

        return [
            getattr(
                resource_commission_defaults,
                f'{type_name}_commission_type',
            ),
            getattr(
                resource_commission_defaults,
                f'{type_name}_commission_rate',
            ),
        ]

    def get_annotated_objects(
        self,
        resource_id=None,
    ):  # pylint: disable=too-many-statements, possibly-used-before-assignment
        assert (
            bool(self.products) + bool(self.service_variants) + bool(self.voucher_templates)
        ) == 1  # XOR

        if self.products:
            type_name = COMMISSION_TYPE__PRODUCT
            type_id = 0
            type_id_name = 'product_id'
            objects = self.products
        elif self.service_variants:
            type_name = COMMISSION_TYPE__SERVICE
            type_id = 1
            type_id_name = 'service_variant_id'
            objects = self.service_variants
        elif self.voucher_templates and self.voucher_templates[0].egift_card:
            type_name = COMMISSION_TYPE__EGIFT_CARD
            type_id = 2
            type_id_name = 'voucher_template_id'
            objects = self.voucher_templates
        elif self.voucher_templates and self.voucher_templates[0].membership:
            type_name = COMMISSION_TYPE__MEMBERSHIP
            type_id = 3
            type_id_name = 'voucher_template_id'
            objects = self.voucher_templates
        elif self.voucher_templates and self.voucher_templates[0].package:
            type_name = COMMISSION_TYPE__PACKAGE
            type_id = 4
            type_id_name = 'voucher_template_id'
            objects = self.voucher_templates

        resource_commission_defaults = self.commission_defaults(resource_id)

        default_commission_type = getattr(
            resource_commission_defaults,
            f'{type_name}_commission_type',
        )
        default_commission_rate = getattr(
            resource_commission_defaults, f'{type_name}_commission_rate'
        )

        object_commissions = self.get_commission_rates()[type_id]

        if resource_id is None:
            # general listing
            alerts = {
                (rate[type_id_name], rate['type']): rate
                for rate in self.get_staff_rates()
                .values(type_id_name, 'type')
                .annotate(Max('rate'))
            }
            for obj in objects:
                specific = object_commissions.get(obj.id)

                obj.commission_type = specific.type if specific else default_commission_type
                obj.commission_rate = specific.rate if specific else default_commission_rate
                max_alert = alerts.get((obj.id, obj.commission_type), {})
                obj.alert = max_alert.get('rate__max', 0) > obj.commission_rate
        else:
            # specific staffer listing
            resource_id = int(resource_id)
            filter_kwargs = dict((('resource_id', resource_id), (f'{type_id_name}__isnull', False)))
            staff_rates = {
                getattr(rate, type_id_name): rate
                for rate in self.get_staff_rates().filter(**filter_kwargs)
            }
            for obj in objects:
                specific = object_commissions.get(obj.id)
                obj_type = specific.type if specific else default_commission_type
                obj_rate = specific.rate if specific else default_commission_rate

                if obj.id in staff_rates:
                    staff_specific = staff_rates[obj.id]
                    obj.commission_rate = staff_specific.rate
                    obj.commission_type = staff_specific.type
                    obj.alert = obj.commission_type == obj_type and obj.commission_rate > obj_rate
                else:
                    obj.commission_type = obj_type
                    obj.commission_rate = obj_rate
                    obj.alert = False

        return objects

    @classmethod
    def get_alerts(cls, pos, instance):
        """
        Alert of staff settings greater then given CommissionRate instance
        """
        return [
            {
                'resource_id': obj.resource_id,
                'resource_name': obj.resource.name,
                'type': obj.type,
                'rate': str(obj.rate),
            }
            for obj in StaffCommissionRate.objects.filter(
                pos=pos,
                product_id=instance.product_id,
                service_variant_id=instance.service_variant_id,
                type=instance.type,
                rate__gt=instance.rate,
            ).select_related('resource')
        ]

    @staticmethod
    def get_staffer_id(row):
        booking = get_attribute(row, ['subbooking'])
        return (
            reduce(
                lambda x, y: x or y,
                [r.id for r in booking.resources.all() if r.type == Resource.STAFF],
                None,
            )
            if booking
            else None
        )

    # support model instance and OrderedDict from dry_run
    def get_commission_staffer_id(
        self, row, first_staffer_id, operator=None
    ):  # pylint: disable=too-many-return-statements
        assert operator or self.transaction
        cashier_id = self.get_cashier_id(operator or self.transaction.operator)

        if get_attribute(row, ['commission_staffer_id']) == -1:
            return None
        if get_attribute(row, ['commission_staffer']):
            return get_attribute(row, ['commission_staffer']).id
        if get_attribute(row, ['product_id']) or get_attribute(row, ['product']):
            return (
                # cashier gets product commissions
                cashier_id
                if self.product_for_cashier
                else
                # first_staffer gets product commissions, fallback to cashier
                first_staffer_id or cashier_id
            )
        if get_attribute(row, ['voucher_template']):
            voucher_template = get_attribute(row, ['voucher_template'])

            return (
                # cashier gets voucher template commissions
                cashier_id
                if self.voucher_template_for_cashier(voucher_template.type)
                else
                # first_staffer gets  commissions, fallback to cashier
                first_staffer_id or cashier_id
            )
        # staffer who provides the service gets service commission,
        # fallback to first staffer and then to the cashier
        return self.get_staffer_id(row) or first_staffer_id or cashier_id

    def get_cashier_id(self, operator):
        """Get Resource.id for cashier (transaction.operator (User))"""
        if not operator:
            return None
        cashier_id = None
        try:
            # If same user is staffer in two businesses, we also need to
            # check if prefetch has only one staffer, if not we need to get
            # staffer related to self.pos.business
            if len(operator._prefetched_staffers) == 1:  # pylint: disable=protected-access
                # do not do if operator._prefetched_staffers
                # because it migth be truth but
                # operator._prefetched_staffers[0] will be empty
                cashier_id = operator._prefetched_staffers[0].id  # pylint: disable=protected-access
        except AttributeError:
            cashier_id = (
                operator.staffers.filter(
                    business=self.pos.business,
                )
                .values_list('id', flat=True)
                .first()
            )

        if cashier_id is None:
            # try to match owner by email
            cashier_id = (
                self.pos.business.resources.filter(
                    staff_email__iexact=operator.email,
                )
                .values_list('id', flat=True)
                .first()
            )

        return cashier_id

    @staticmethod
    def _calculate_commission_amount(commission_type, commission_rate, transaction_row) -> Decimal:
        if commission_type == CommissionDefaults.COMMISSION_TYPE__AMOUNT:
            if transaction_row.type == TransactionRow.TRANSACTION_ROW_TYPE__ADDON:
                return Decimal(0)

            return commission_rate

        return round_currency(transaction_row.discounted_total * commission_rate / Decimal(100))

    def get_commission_reports_data(self):
        assert self.transaction

        # prepare rates and staffers
        commission_rates = self.get_commission_rates()
        staff_rates = {
            (
                rate.resource_id,
                rate.product_id,
                rate.service_variant_id,
                rate.voucher_template_id,
            ): rate
            for rate in self.get_staff_rates()
        }

        rows = self.transaction.rows.all()
        first_staffer_id = firstof([_f for _f in [self.get_staffer_id(r) for r in rows] if _f])

        # calculate commissions for each row
        commissions = {}
        for row in rows:
            resource_id = self.get_commission_staffer_id(row, first_staffer_id)
            if resource_id is None:
                # no one gets a commission
                continue

            service_variant_id = self._get_variant_id_from_row(row)
            voucher_template_id = row.voucher_template.id if row.voucher_template else None

            if row.product_id:
                type_name = COMMISSION_TYPE__PRODUCT
                type_id = 0
            elif row.voucher_id and row.voucher.voucher_template.egift_card:
                type_name = COMMISSION_TYPE__EGIFT_CARD
                type_id = 2
            elif row.voucher_id and row.voucher.voucher_template.membership:
                type_name = COMMISSION_TYPE__MEMBERSHIP
                type_id = 3
            elif row.voucher_id and row.voucher.voucher_template.package:
                type_name = COMMISSION_TYPE__PACKAGE
                type_id = 4
            else:
                type_name = COMMISSION_TYPE__SERVICE
                type_id = 1

            commission_rate = staff_rates.get(
                (
                    resource_id,
                    row.product_id,
                    service_variant_id,
                    row.voucher and row.voucher.voucher_template_id,
                ),
                commission_rates[type_id].get(
                    row.product_id or service_variant_id or voucher_template_id
                ),
            )
            if commission_rate:
                type_, rate = commission_rate.type, commission_rate.rate
            else:
                type_, rate = self.get_default(type_name, resource_id)

            amount = self._calculate_commission_amount(type_, rate, row)

            # create instance for later bulk_create
            commission = Commission(
                row=row,
                resource_id=resource_id,
                amount=amount,
                rate=rate,
                type=type_,
            )
            commissions[row.id] = commission

        return commissions

    def create_commission_reports(self):
        assert self.transaction
        commissions = list(self.get_commission_reports_data().values())
        Commission.objects.bulk_create(commissions)

        # TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED
        if txn_refactor_stage2_enabled(self.transaction):
            CommissionService.create_commissions(commissions_v1=commissions)
        # <<< TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED

        log_transaction_commission_changes_task.delay(self.transaction.id)

    @staticmethod
    def _get_variant_id_from_row(row):
        return (
            row.service_variant_id
            if row.service_variant_id
            else row.subbooking.service_variant_id if row.subbooking_id else None
        )


def detect_card_type(card_number):
    card_numbers = [
        # https://en.wikipedia.org/wiki/Payment_card_number
        ('34', '34', enums.CARD_TYPE__AMERICAN_EXPRESS),
        ('37', '37', enums.CARD_TYPE__AMERICAN_EXPRESS),
        ('62', '62', enums.CARD_TYPE__CHINA_UNIONPAY),
        ('300', '305', enums.CARD_TYPE__DINERS),
        ('309', '309', enums.CARD_TYPE__DINERS),
        ('36', '36', enums.CARD_TYPE__DINERS),
        ('38', '39', enums.CARD_TYPE__DINERS),
        ('6011', '6011', enums.CARD_TYPE__DISCOVER),
        ('622126', '622925', enums.CARD_TYPE__DISCOVER),
        ('644', '649', enums.CARD_TYPE__DISCOVER),
        ('65', '65', enums.CARD_TYPE__DISCOVER),
        ('636', '636', enums.CARD_TYPE__INTERPAYMENT),
        ('3528', '3589', enums.CARD_TYPE__JCB),
        ('50', '50', enums.CARD_TYPE__MAESTRO),
        ('56', '59', enums.CARD_TYPE__MAESTRO),
        ('5019', '5019', enums.CARD_TYPE__DANKORT),
        ('2200', '2204', enums.CARD_TYPE__NSPK_MIR),
        ('51', '55', enums.CARD_TYPE__MASTERCARD),
        ('2221', '2720', enums.CARD_TYPE__MASTERCARD),
        ('4', '4', enums.CARD_TYPE__VISA),
        ('1', '1', enums.CARD_TYPE__UATP),
        ('506099', '506198', enums.CARD_TYPE__VERVE),
        ('650002', '650027', enums.CARD_TYPE__VERVE),
    ]
    for from_prefix, to_prefix, card_type in card_numbers:
        from_prefix += "0" * (19 - len(from_prefix))
        to_prefix += "9" * (19 - len(to_prefix))
        if from_prefix <= card_number <= to_prefix:
            return card_type
    return enums.CARD_TYPE__OTHER


def get_bank_account_choices():
    if settings.API_COUNTRY == Country.US:
        return [
            (bank_account_type.CHECKING, 'checking'),
            (bank_account_type.SAVINGS, 'savings'),
        ]
    return []


def unfinished_transactions_query(closing_timezones, receipt_status_code):
    """Returns all unfinished transactions for selected timezones."""

    booking_noshow_statuses = [
        Appointment.STATUS.NOSHOW,
        Appointment.STATUS.CANCELED,
    ]

    transaction_qs = Transaction.objects.filter(
        pos__business__time_zone_name__in=closing_timezones,
        latest_receipt__status_code=receipt_status_code,
        # There is situation where business first refund PREPAYMENT/BOOKSY_PAY then
        # cancels appointment. In this case Receipt still has PREPAYMENT_SUCCESS/BOOKSY_PAY_SUCCESS
        # status, because it should be still possible to checkout.
        # Payment Row related to this Receipt has REFUNDED status.
        # This kind of transaction is not possible to close by task, so it is
        # filtered below.
        latest_receipt__payment_rows__status=receipt_status_code,
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    )

    txn_part_1 = (
        transaction_qs.annotate(
            pr_sum=Sum('latest_receipt__payment_rows__amount'),
        )
        .filter(Q(pr_sum=F('total')) & Q(appointment__status=Appointment.STATUS.FINISHED))
        .values_list('id', flat=True)
    )

    txn_part_2 = transaction_qs.filter(
        appointment__status__in=booking_noshow_statuses,
    )

    txn = (
        transaction_qs.prefetch_related(
            'latest_receipt__payment_rows',
            'pos__business__owner',
        )
        .filter(Q(id__in=txn_part_1) | Q(id__in=txn_part_2))
        .exclude(children__isnull=False)
    )

    return txn


def unfinished_prepayment_query(closing_timezones):
    return unfinished_transactions_query(closing_timezones, receipt_status.PREPAYMENT_SUCCESS)


def unfinished_booksy_pay_query(closing_timezones):
    return unfinished_transactions_query(closing_timezones, receipt_status.BOOKSY_PAY_SUCCESS)


def open_register_if_needed(pos):
    """
    Opens register for selected POS if registers are enabled, but all are closed
    :param pos: POS object
    :return: Regiser object, Boolean - True if newly_opened
    """
    from webapps.register.models import Register

    # Check if is any open register
    register = pos.registers.filter(is_open=True).first()

    if not register and pos.registers_enabled:
        temp_register = Register(
            pos=pos,
            is_open=True,
            opened_by=pos.business.owner,
            opening_cash=0,
        )
        temp_register.save()
        return temp_register, True

    return register, False


def close_register(pos, temp_register):
    """
    Close register and recalculate statisics
    :param pos: POS object
    :param temp_register: Register object
    :return:
    """
    temp_register.is_open = False
    temp_register.closed_by = pos.business.owner
    temp_register.closed_at = pos.business.tznow

    # save RegisterClosingAmounts
    for summary in temp_register.all_summaries:
        if summary.countable:
            summary.save()

    temp_register.save()


# region POS validation
def check_tips(values):
    errors = []
    if not has_unique_default(values, 'default'):
        errors.append('One and only one tip rate should be default.')
    errors.append(check_unique_rate(values))
    return [_f for _f in errors if _f]


def check_tax_rates(values):
    errors = []
    if not has_unique_default(values, 'default_for_service'):
        errors.append(_('One and only one tax rate should be default for service.'))

    if not has_unique_default(values, 'default_for_product'):
        errors.append(_('One and only one tax rate should be default for product.'))
    errors.append(check_unique_rate(values))
    errors.append(check_tax_with_special_validator(values))
    return [_f for _f in errors if _f]


def check_payment_types(values):
    if not has_unique_default(values, 'default'):
        return _('One and only one payment type should be default.')


def check_pay_by_app_status(instance, value):
    if value == instance.pay_by_app_status:
        return  # No need to check validity if no update is done

    if value in PAY_BY_APP_ACTIVE_STATUSES and not settings.POS__PAY_BY_APP:
        return _('Mobile payments are disabled in your country.')

    if value == PAY_BY_APP_ENABLED and instance.pay_by_app_status != PAY_BY_APP_ENABLED:
        return _('Status is not allowed. Pending and disabled statuses are allowed.')

    if value == PAY_BY_APP_PENDING and instance.pay_by_app_status == PAY_BY_APP_ENABLED:
        return _('Mobile payments are already enabled.')


def has_unique_default(iterable, key):
    return len([elem for elem in iterable if elem.get(key)]) == 1


def has_unique_rate(field):
    rates = [d.get('rate') for d in field]
    return len(rates) == len(set(rates))


def check_unique_rate(field):
    if not has_unique_rate(field):
        return _('Rate values should be unique.')


def has_unique_amount(field):
    amounts = [d.get('amount') for d in field]
    return len(amounts) == len(set(amounts))


def check_unique_amount(field):
    if not has_unique_amount(field):
        return _('amount values should be unique.')


def check_tax_with_special_validator(tax_field):
    rates = (d.get('rate') for d in tax_field)
    for tax_rates in rates:
        if tax_rates and (0 < tax_rates < 1):
            return _('Invalid tax rate.')


# endregion POS validation


def get_receipt_data(pos, transaction, sender, language):
    from webapps.pos.serializers import get_transaction_serializer

    tz = pos.business.get_timezone()

    response = get_transaction_serializer(
        instance=transaction,
        context={'pos': pos, 'customer_api': sender == NotificationHistory.SENDER_CUSTOMER},
    )

    # Make some magic to get proper data in template
    prs = response.data['receipts'][0]['payment_rows']
    prs_unformatted = transaction.latest_receipt.payment_rows.all()

    for pr, pr_u in zip(prs, prs_unformatted):
        pr['created'] = pr_u.created.astimezone(tz)
        pr['card_type'] = pr_u.get_card_type_display()

        # To not to ovverride whole dict
        status = {
            'prepayment': 'success',
        }.get(pr['status'], pr['status'])

        pr['status_formatted'] = receipt_status.STATUS_TYPES_DISPLAY[status]

    short_status_label = force_unicode(response.data['receipts'][0]['short_status_label'])
    short_status_description = force_unicode(
        response.data['receipts'][0]['short_status_description']
    )
    show_taxes_info = not (
        VoucherMigrationConfig.is_strict()
        and len(response.data['rows']) == 1
        and sget_v2(transaction.rows.first(), ['voucher_template', 'type'])
        == Voucher.VOUCHER_TYPE__EGIFT_CARD
    )

    can_show_fiscal_disclaimer = get_show_fiscal_disclaimer(pos)

    template_args = {
        '_': _,
        'format_currency': format_currency,
        'user': transaction.customer,
        'transaction': response.data,
        'instance': transaction,
        'latest_receipt': transaction.latest_receipt,
        'latest_receipt_serialized': response.data['receipts'][0],
        'receipt_created': transaction.latest_receipt.created.astimezone(tz),
        'transaction_created': transaction.created.astimezone(tz),
        'payment_rows': prs,
        'short_status': response.data['receipts'][0]['short_status'],
        'short_status_label': short_status_label,
        'short_status_description': short_status_description,
        'show_polish_fiscal_disclaimer': language == 'pl',
        'show_fiscal_disclaimer': can_show_fiscal_disclaimer,
        'show_taxes_info': show_taxes_info,
    }
    receipt_number = response.data.get('status', {}).get('receipt_number')
    return template_args, receipt_number


def get_show_fiscal_disclaimer(pos):
    return settings.API_COUNTRY == Country.PL or french_certification_enabled(pos.business_id)


@retry_on_sync_error
def create_pos(business):
    if not settings.POS:
        return
    if business.is_b_listing() or business.is_venue():
        return
    with db_transaction.atomic():
        pos, created = POS.objects.get_or_create(business=business)
        if created:
            pos.load_defaults()
    POSPort.get_or_create_pos(business.id)

    return pos


def online_rows(qs):
    """
    PaymentRowQueryset filter
    """

    return qs.filter(
        status__in=[
            receipt_status.REFUNDED,
            receipt_status.CHARGEBACK,
            receipt_status.CHARGEBACK_REVERSED,
            receipt_status.SECOND_CHARGEBACK,
            receipt_status.DEPOSIT_CHARGE_SUCCESS,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.BOOKSY_PAY_SUCCESS,
        ]
    )


def success_rows(qs):
    """
    PaymentRowQueryset filter
    """

    codes = [
        # offline
        PaymentTypeEnum.CASH,
        PaymentTypeEnum.CHECK,
        PaymentTypeEnum.CREDIT_CARD,
        PaymentTypeEnum.GIFT_CARD,
        PaymentTypeEnum.VOUCHER,
        PaymentTypeEnum.AMERICAN_EXPRESS,
        PaymentTypeEnum.PAYPAL,
        PaymentTypeEnum.SQUARE,
        PaymentTypeEnum.EGIFT_CARD,
        PaymentTypeEnum.MEMBERSHIP,
        PaymentTypeEnum.PACKAGE,
        PaymentTypeEnum.DIRECT_PAYMENT,
        # online
        PaymentTypeEnum.PAY_BY_APP,
        PaymentTypeEnum.STRIPE_TERMINAL,
        PaymentTypeEnum.TAP_TO_PAY,
        PaymentTypeEnum.BOOKSY_PAY,
    ]

    return qs.filter(status=receipt_status.PAYMENT_SUCCESS).filter(payment_type__code__in=codes)


def annotate_payout_transactions_count(
    queryset: QuerySet, pos: POS, exclude_manual_transfer: Optional[bool] = False
) -> QuerySet:
    # similar logic to the one in get_business_cash_flow_objects
    # returns the queryset with annotated transactions_count field

    from webapps.market_pay.enums import TransferFundsStatus
    from webapps.market_pay.models import FundTransfer

    payment_rows_qs = PaymentRow.objects.filter(payment_type__pos=pos.id).exclude(amount=0)
    payment_rows_qs = payment_rows_qs.filter(payout_reference=OuterRef("psp_reference"))

    # online PaymentRows
    online_pr_qs = online_rows(payment_rows_qs)
    transactions_count_online = Coalesce(
        Subquery(
            online_pr_qs.order_by()
            .values('payout_reference')
            .annotate(
                c=Count('created', distinct=True),
            )
            .values('c')[:1],
            output_field=IntegerField(),
        ),
        0,
    )

    # offline PaymentRows
    offline_pr_qs = success_rows(payment_rows_qs)
    transactions_count_offline = Coalesce(
        Subquery(
            offline_pr_qs.order_by()
            .values('payout_reference')
            .annotate(
                c=Count('created', distinct=True),
            )
            .values('c')[:1],
            output_field=IntegerField(),
        ),
        0,
    )

    # fund transfer
    fund_transfer_qs = FundTransfer.objects.filter(payout_reference=OuterRef("psp_reference"))
    if pos.account_holder:
        fund_transfer_qs = fund_transfer_qs.filter(
            (
                Q(source_account_code=pos.account_holder.account_code)
                | Q(destination_account_code=pos.account_holder.account_code)
            ),
            status=TransferFundsStatus.SUCCESS.value,
        )
    if exclude_manual_transfer:
        fund_transfer_qs = fund_transfer_qs.exclude(prize_refs=[])
    transactions_count_fundtransfer = (
        Coalesce(
            Subquery(
                fund_transfer_qs.order_by()
                .values('payout_reference')
                .annotate(
                    c=Count('created', distinct=True),
                )
                .values('c')[:1],
                output_field=IntegerField(),
            ),
            0,
        )
        if pos.account_holder
        else Value(0, output_field=IntegerField())
    )

    # sum 3 values together into final annotated field
    queryset = queryset.annotate(
        transactions_count=(
            transactions_count_online + transactions_count_offline + transactions_count_fundtransfer
        )
    )

    return queryset


def get_business_cash_flow_objects(
    pos, payout_reference=None, exclude_manual_transfer=False, page=None, per_page=0
):
    from webapps.market_pay.enums import TransferFundsStatus
    from webapps.market_pay.models import FundTransfer

    payment_rows_qs = PaymentRow.objects.filter(
        payment_type__pos=pos.id,
        receipt__transaction__deleted__isnull=True,
    ).exclude(amount=0)
    if payout_reference:
        payment_rows_qs = payment_rows_qs.filter(
            payout_reference=payout_reference,
        )
    payment_type_ids = pos.payment_types.filter(
        code__in=[
            # offline
            PaymentTypeEnum.CASH,
            PaymentTypeEnum.CHECK,
            PaymentTypeEnum.CREDIT_CARD,
            PaymentTypeEnum.GIFT_CARD,
            PaymentTypeEnum.VOUCHER,
            PaymentTypeEnum.AMERICAN_EXPRESS,
            PaymentTypeEnum.PAYPAL,
            PaymentTypeEnum.SQUARE,
            PaymentTypeEnum.EGIFT_CARD,
            PaymentTypeEnum.MEMBERSHIP,
            PaymentTypeEnum.PACKAGE,
            PaymentTypeEnum.DIRECT_PAYMENT,
            # online
            PaymentTypeEnum.PAY_BY_APP,
            PaymentTypeEnum.STRIPE_TERMINAL,
            PaymentTypeEnum.TAP_TO_PAY,
            PaymentTypeEnum.BOOKSY_PAY,
        ],
    ).values_list('id', flat=True)

    # distincts are necessary to prevent doubling some payment rows
    payment_rows_by_payment_type = list(
        payment_rows_qs.filter(
            Q(status=receipt_status.PAYMENT_SUCCESS, payment_type_id__in=list(payment_type_ids))
        )
        .values(
            'id',
            'status',
            'payment_type__code',
            'receipt__transaction__series_id',
        )
        .order_by(
            'status',
            'payment_type__code',
            'receipt__transaction__series_id',
        )
        .distinct(
            'status',
            'payment_type__code',
            'receipt__transaction__series_id',
        )
    )

    payment_rows_by_status = list(
        payment_rows_qs.filter(
            status__in=[
                receipt_status.REFUNDED,
                receipt_status.CHARGEBACK,
                receipt_status.CHARGEBACK_REVERSED,
                receipt_status.SECOND_CHARGEBACK,
                receipt_status.DEPOSIT_CHARGE_SUCCESS,
                receipt_status.PREPAYMENT_SUCCESS,
                receipt_status.BOOKSY_PAY_SUCCESS,
            ]
        )
        .values(
            'id',
            'status',
            'payment_type__code',
            'receipt__transaction__series_id',
        )
        .order_by(
            'created',
            'receipt__transaction__series_id',
        )
        .distinct(
            'created',
            'receipt__transaction__series_id',
        )
    )

    payment_rows_qs = PaymentRow.objects.filter(
        # Do to not do subquery here
        Q(id__in=[pr['id'] for pr in payment_rows_by_payment_type])
        | Q(id__in=[pr['id'] for pr in payment_rows_by_status])
    ).order_by('-created')

    # FundTransfers
    fund_transfer_qs = FundTransfer.objects.none()
    if pos.account_holder:
        fund_transfer_qs = FundTransfer.objects.filter(
            (
                Q(source_account_code=pos.account_holder.account_code)
                | Q(destination_account_code=pos.account_holder.account_code)
            ),
            status=TransferFundsStatus.SUCCESS.value,
        ).order_by('-created')
        if payout_reference:
            fund_transfer_qs = fund_transfer_qs.filter(
                payout_reference=payout_reference,
            )

    if exclude_manual_transfer:
        fund_transfer_qs = fund_transfer_qs.exclude(prize_refs=[])

    count = payment_rows_qs.count() + fund_transfer_qs.count()
    if page and per_page:
        limit = page * per_page
        payment_rows_qs = payment_rows_qs[:limit]
        fund_transfer_qs = fund_transfer_qs[:limit]

    return (
        list(
            sorted(
                chain(payment_rows_qs, fund_transfer_qs),
                key=lambda x: x.created,
                reverse=True,
            )
        ),
        count,
    )


def get_new_receipt_status(old_status, success):
    """
    :param old_status: old status of transaction
    :type old_status: str
    :param success: if transaction was successful or not
    :type success: bool

    :return: new transacton status
    :rtype: str

    :raises RuntimeError: if old status is not from awaiting group
    """
    if old_status not in (receipt_status.AWAITING_STATUSES + (receipt_status.SENT_FOR_REFUND,)):
        return old_status

    if success:
        return {
            receipt_status.PAYMENT_AWAITING: receipt_status.PAYMENT_SUCCESS,
            receipt_status.DEPOSIT_AUTHORISATION_AWAITING: (
                receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
            ),
            receipt_status.DEPOSIT_CHARGE_AWAITING: receipt_status.DEPOSIT_CHARGE_SUCCESS,
            receipt_status.DEPOSIT_CANCEL_AWAITING: receipt_status.DEPOSIT_CHARGE_CANCELED,
            receipt_status.SENT_FOR_REFUND: receipt_status.REFUNDED,
        }[old_status]

    return {
        receipt_status.PAYMENT_AWAITING: receipt_status.PAYMENT_FAILED,
        receipt_status.DEPOSIT_AUTHORISATION_AWAITING: receipt_status.DEPOSIT_AUTHORISATION_FAILED,
        receipt_status.DEPOSIT_CHARGE_AWAITING: receipt_status.DEPOSIT_CHARGE_FAILED,
        receipt_status.DEPOSIT_CANCEL_AWAITING: receipt_status.DEPOSIT_CANCEL_FAILED,
        receipt_status.SENT_FOR_REFUND: receipt_status.PAYMENT_SUCCESS,
    }[old_status]


def display_business_pba_pos_plan(business, show_net=False):
    """
    Calculate Mobile Payment POS plan details formatted for display.
    """
    if not POSPlan.objects.filter(individual=False).exists():
        return ''
    if business.pos.force_stripe_pba:
        plan = business.get_pos_plan(POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT)
    else:
        plan = business.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)
    if plan is None:
        return ''
    return display_pos_plan(
        plan,
        show_net=show_net,
    )


def display_pos_plan(plan, show_net=False):
    """
    Calculate POS plan details formatted for display.
    """
    provision_percent = 100 * plan.provision
    txn_fee = plan.txn_fee
    if show_net:
        tax_fee = COUNTRIES_TAX_RATES.get(settings.API_COUNTRY, 0)
        provision_percent = round_currency(provision_percent / (1 + tax_fee / 100))
        txn_fee = round_currency(txn_fee / (1 + tax_fee / 100))
    provision_percent = format_float(provision_percent, settings.CURRENCY_LOCALE)
    if plan.txn_fee == 0:
        return f'{provision_percent}%'
    fee = format_currency_in_locale(txn_fee, settings.CURRENCY_LOCALE)

    return f'{provision_percent}% + {fee}'


def get_pos_plan_info(
    business: Business,
    pos_plan_type: POSPlanPaymentTypeEnum,
    show_net=False,
):
    plan: POSPlan = business.get_pos_plan(pos_plan_type)
    if show_net:
        fee_description = POS_PLAN_FEE_DESCRIPTION_EXCLUDING_TAX_MAP[pos_plan_type]
    else:
        fee_description = POS_PLAN_FEE_DESCRIPTION_MAP[pos_plan_type]
    chargeback_fee = format_currency_in_locale(
        plan.chargeback_txn_fee,
        settings.CURRENCY_LOCALE,
    )

    chargeback_fee_description = (
        _(
            'In the unlikely event, one of your customers opens a dispute '
            'with their bank, you’ll be charged a %s '
            'non-refundable chargeback fee'
        )
        % chargeback_fee
    )

    if (  # todo it is ugly, probably it should be refactored in the future
        pos_plan_type
        in [
            POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
            POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        ]
        and not business.pos.marketpay_enabled
    ):
        if show_net:
            fee_description = _("Fee per mobile payment transaction (excluding tax)")
        else:
            fee_description = _("Fee per mobile payment transaction")
        chargeback_fee_description = None
    elif pos_plan_type == POSPlanPaymentTypeEnum.FAST_PAYOUT and business.pos.fast_payouts_visible:
        chargeback_fee_description = None

    if pos_plan_type == POSPlanPaymentTypeEnum.FAST_PAYOUT:
        from webapps.payment_gateway.models import Wallet
        from webapps.payment_gateway.services.wallet import WalletService

        wallet_fee_settings = WalletService.get_wallet_fee_settings(
            wallet=Wallet.objects.get(business_id=business.id),
            payment_provider_code=PaymentProviderCode.STRIPE,
        )
        plan.provision = float(wallet_fee_settings.fast_payout_provision_percentage / 100)
        plan.txn_fee = float(wallet_fee_settings.fast_payout_provision_fee / 100)
        # don't save, it's just for display purposes in pos_plan_display_by_plan

    return {
        'fee_description': fee_description,
        'terms_and_conditions': _('By enabling you agree to'),
        'rates': display_pos_plan(plan, show_net=show_net),
        'chargeback_fee_description': chargeback_fee_description,
    }


def annotate_correct_pos_plan_pk(poses: QuerySet[POS], plan_type: POSPlanPaymentTypeEnum):
    plans = list(POSPlan.objects.filter(individual=False, plan_type=plan_type)[:2])
    if len(plans) == 1:
        # this inexpensive check allows us to use a short path optimisation. Many environments
        # use only one pos plan of given type, so we can take advantage of this and calculate
        # number of transactions unnecessarily
        plan = plans[0]
        assert plan.min_txn_num == 0  # if only 1 pos plan, should be always true (sanity check)

        return poses.annotate(correct_pos_plan_pk=Value(plan.pk))

    provider, payment_type_codes = POSPlan.PLAN_TYPE_MAP.get(plan_type)
    poses = poses.annotate(
        _txn_num=Coalesce(
            Subquery(
                PaymentRow.objects.filter(
                    children__isnull=True,
                    status__in=[
                        receipt_status.PAYMENT_SUCCESS,
                        receipt_status.REFUNDED,
                        receipt_status.SENT_FOR_REFUND,
                        receipt_status.CHARGEBACK,
                        receipt_status.CHARGEBACK_REVERSED,
                        receipt_status.SECOND_CHARGEBACK,
                    ],
                    receipt__transaction__pos_id=OuterRef("id"),
                    payment_type__code__in=payment_type_codes,
                    created__gte=tznow() - datetime.timedelta(days=30),
                    provider=provider,
                )
                .order_by()
                .values('receipt__transaction__pos_id')
                .annotate(
                    c=Count('*'),
                )
                .values('c')[:1],
                output_field=IntegerField(),
            ),
            0,
        )
    )
    poses = poses.annotate(
        correct_pos_plan_pk=Subquery(
            POSPlan.objects.filter(
                individual=False, plan_type=plan_type, min_txn_num__lte=OuterRef('_txn_num')
            )
            .order_by("-min_txn_num")
            .values("pk")[:1]
        )
    )
    return poses


def apply_default_tax_rate_services(pos):
    tax_rate = (
        pos.tax_rates.filter(
            default_for_service=True,
        )
        .first()
        .rate
    )
    qs = pos.business.services.filter(
        active=True,
    )
    # apply tax rate
    qs.update(tax_rate=tax_rate)

    # update addons
    addons_qs = ServiceAddOn.objects.filter(
        deleted__isnull=True,
        business=pos.business,
    )
    addons_qs.update(tax_rate=tax_rate)


def apply_default_tax_rate_products(pos):
    tax_rate = pos.tax_rates.filter(
        default_for_product=True,
    ).first()
    qs = Commodity.objects.filter(
        business=pos.business,
        archived=False,
        deleted__isnull=True,
    )
    # apply tax rate
    qs.update(tax_rate=tax_rate)
    rate = tax_rate.rate or 0
    qs.update(tax=(rate / 100) * F('net_price'))


def create_stripped_data_for_batch_change_pos_plans(file_path, update_log, logger):
    from xlrd import XLRDError

    from lib.spreadsheet import load_spreadsheet_with_pandas
    from webapps.admin_extra.admin_import_s3 import AdminImporterS3
    from webapps.admin_extra.import_utils import strip_xlsx_data
    from webapps.pos.enums import POSPlanBatchUpdateLogStatus

    with AdminImporterS3.open_file(file_path, delete_file=True) as import_file:
        try:
            stripped_data = strip_xlsx_data(load_spreadsheet_with_pandas(import_file.read()))[1:]
            stripped_data = [
                [int(business_id), int(current_pos_plan_id), int(new_pos_plan_id)]
                for business_id, current_pos_plan_id, new_pos_plan_id in stripped_data
            ]
            return stripped_data
        except XLRDError as e:
            error_massage = 'Error while loading data: %s, %s', str(e), file_path
            logger.error(error_massage)
            save_update_log_entry(update_log, error_massage, POSPlanBatchUpdateLogStatus.FAILED)
            return
        except ValueError as e:
            error_massage = 'Invalid data in file: %s, %s', str(e), file_path
            logger.error(error_massage)
            save_update_log_entry(update_log, error_massage, POSPlanBatchUpdateLogStatus.FAILED)
            return


def check_duplicates_business_current_plan(data: list[list[int, int, int]]) -> dict:
    duplicates = {}
    business_current_plan = [(business_id, current_plan) for business_id, current_plan, _ in data]
    list_without_duplicates = set(business_current_plan)
    for record in list_without_duplicates:
        business_current_plan.remove(record)
    if business_current_plan:
        for record in business_current_plan:
            current_pos_data = {
                'business_id': record[0],
                'current_posplan_id': record[1],
                'new_posplan_id': 0,
            }
            reason = (
                'can update one posplan connected with business only one time in one update, '
                'please remove duplicates'
            )
            exclude_business_from_posplan_update(duplicates, reason, current_pos_data)
    return duplicates


def exclude_businesses_with_invalid_posplans(
    poses: QuerySet[POS], posplans: QuerySet[POSPlan], data: list[list[int, int, int]]
) -> dict:
    excluded_businesses = {}
    poses_business_id_lookup = poses.values_list('business_id', flat=True)
    posplans_id_lookup = {posplan.id: posplan for posplan in posplans}
    business_posplans_lookup = {
        pos.business.id: pos.pos_plans.values_list('id', flat=True) for pos in poses
    }
    posplan_types_lookup = {posplan.id: posplan.plan_type for posplan in posplans}
    for (
        business_id,
        current_posplan_id,
        new_posplan_id,
    ) in data:
        current_pos_data = {
            'business_id': business_id,
            'current_posplan_id': current_posplan_id,
            'new_posplan_id': new_posplan_id,
        }
        if business_id not in poses_business_id_lookup:
            reason = "business with given id doesn't exist"
            exclude_business_from_posplan_update(excluded_businesses, reason, current_pos_data)
            continue
        if new_posplan_id not in posplans_id_lookup or current_posplan_id not in posplans_id_lookup:
            reason = 'pos plan number is invalid, given pos plan is not yet created'
            exclude_business_from_posplan_update(excluded_businesses, reason, current_pos_data)
            continue
        if current_posplan_id not in business_posplans_lookup[business_id]:
            reason = 'invalid current posplan'
            exclude_business_from_posplan_update(excluded_businesses, reason, current_pos_data)
            continue
        if not is_only_one_pos_plan_per_type(
            current_pos_data, posplans_id_lookup, business_posplans_lookup, posplan_types_lookup
        ):
            reason = 'Only one POSPlan per type is allowed.'
            exclude_business_from_posplan_update(excluded_businesses, reason, current_pos_data)
    return excluded_businesses


def save_update_log_entry(update_log, error_massage, status):
    update_log.data = error_massage
    update_log.status = status
    update_log.save(update_fields=['data', 'status'])


def exclude_business_from_posplan_update(excluded_businesses: dict, reason: str, pos_data: dict):
    excluded_businesses[pos_data['business_id']] = {
        'reason': reason,
        'current posplan': pos_data['current_posplan_id'],
        'new posplan': pos_data['new_posplan_id'],
    }


def is_only_one_pos_plan_per_type(
    pos_data: dict,
    posplans_id_lookup: dict,
    business_posplans_lookup: dict,
    posplan_types_lookup: dict,
) -> bool:
    current_posplan = posplans_id_lookup[pos_data['current_posplan_id']]
    new_posplan = posplans_id_lookup[pos_data['new_posplan_id']]
    posplans = business_posplans_lookup[pos_data['business_id']]
    posplans_types = [posplan_types_lookup[posplan] for posplan in posplans]
    posplans_types.append(new_posplan.plan_type)
    posplans_types.remove(current_posplan.plan_type)
    if len(set(posplans_types)) != len(posplans):
        return False
    return True


def prepare_data_for_posplans_update(
    poses: QuerySet[POS], excluded_businesses: dict, data: list[list[int, int, int]]
) -> (dict, dict):
    pos_business_map = {
        pos.business.id: pos.id for pos in poses if pos.business_id not in excluded_businesses
    }
    business_current_posplan_map = defaultdict(list)
    for business_id, curr_plan_id, _ in data:
        if business_id not in excluded_businesses:
            business_current_posplan_map[business_id].append(curr_plan_id)

    business_new_posplan_map = defaultdict(list)
    for business_id, _, new_plan_id in data:
        if business_id not in excluded_businesses:
            business_new_posplan_map[business_id].append(new_plan_id)

    pos_new_posplan_map = {
        pos_business_map[business_id]: new_plan_id
        for business_id, new_plan_id in business_new_posplan_map.items()
    }
    return pos_new_posplan_map, business_current_posplan_map


def update_posplans(pos_new_posplan_map: dict, business_current_posplan_map: dict) -> (int, int):
    q_objects = Q()
    pos_posplan_list = []
    PosPosplanRelationship = POS.pos_plans.through  # pylint: disable=invalid-name

    for biz_id, posplans_ids in pos_new_posplan_map.items():
        for posplan_id in posplans_ids:
            pos_posplan_list.append(PosPosplanRelationship(pos_id=biz_id, posplan_id=posplan_id))

    for biz_id, posplans_ids in business_current_posplan_map.items():
        for posplan_id in posplans_ids:
            q_objects.add(Q(pos__business_id=biz_id, posplan_id=posplan_id), Q.OR)
    if q_objects:
        PosPosplanRelationship.objects.filter(q_objects).delete()
    created_plans = PosPosplanRelationship.objects.bulk_create(
        pos_posplan_list, ignore_conflicts=True
    )
    removed_plans = len(q_objects) - 1
    return len(created_plans), removed_plans


def get_service_variants_prices(business):
    # take all active services
    services_ids = (
        business.services.filter(
            deleted__isnull=True,
            active=True,
        )
        .values_list('id', flat=True)
        .distinct()
    )
    # get all active service_variants with related services and payments
    service_variant_prices = ServiceVariant.objects.filter(
        active=True,
        deleted__isnull=True,
        type__in=PriceType.has_price(),
        service_id__in=services_ids,
    ).values_list('id', 'price', 'service__name')
    # compress it to dict
    return {sv_id: (price, name) for sv_id, price, name in service_variant_prices}


def update_pending_payment_appointment(
    transaction: Transaction,
    changed_user: User,
):
    """
    If related appointment is in PENDING_PAYMENT status and
    current transaction has been paid successfully then we need to
    update appointment status because PENDING_PAYMENT status is for
    unpaid appointments awaiting a payment.

    If payment is successful appointment status should change.

    If appointment is in PENDING_PAYMENT status and current transaction fails
    then allow repayment (action "make_repayment" in CustomerTransactionActionHandler)
    """

    appointment_status = sget_v2(transaction, ['appointment', 'status'])
    if not appointment_status == Appointment.STATUS.PENDING_PAYMENT:
        return
    receipt_payment_status = sget_v2(transaction, ['latest_receipt', 'status_code'])
    appointment = transaction.appointment
    if receipt_payment_status == receipt_status.PREPAYMENT_SUCCESS:
        _handle_pending_payment_appointment_status_update(
            appointment=appointment,
            new_status=Appointment.STATUS.ACCEPTED,
            action=BCS.CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT,
            changed_user=changed_user,
        )


def get_minimal_pba_amount() -> Decimal:
    if settings.API_COUNTRY == Country.US:
        minimal_payment = major_unit(MINIMAL_PAYMENT_AMOUNT)
    else:
        minimal_payment_country = settings.MINIMAL_POS_PAY_BY_APP_PAYMENT
        minimal_payment = Decimal(
            minimal_payment_country.get(
                settings.API_COUNTRY, minimal_payment_country.get('default')
            )
        )
    return minimal_payment


def _handle_pending_payment_appointment_status_update(
    appointment: Appointment,
    new_status: Appointment.STATUS,
    action: BCS,
    changed_user: User,
):
    """Change pending appointment status based on transaction outcome"""
    from webapps.booking.models import BookingChange

    Appointment.objects.filter(
        id=appointment.id,
    ).update(
        status=new_status,
    )
    start_scenario(
        BCS,
        appointment_id=appointment.id,
        action=action,
    )
    BookingChange.add(
        appointment,
        changed_by=BookingChange.BY_CUSTOMER,
        changed_user=changed_user,
        metadata={'action': action},
    )
