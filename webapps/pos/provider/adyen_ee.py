#!/usr/bin/env python
import logging
from datetime import timedelta

from typing import Optional, Tuple, Union

from django.conf import settings
from django.core.cache import cache
from django.db import transaction as db_transaction
from django.utils.translation import gettext_lazy as _

from lib.feature_flag.feature.payment import RemoveAdyenEEPaymentProviderFlag
from lib.tools import tznow
from webapps.adyen import flow
from webapps.adyen.consts import oper_result
from webapps.adyen.consts.timeouts import CARD_REMOVAL_TIMEOUT
from webapps.adyen.exceptions import (
    DuplicatedCardException,
    ThreeDSecureAuthenticationRequired,
    UnconfirmedCardRemovalException,
)
from webapps.adyen.flow import refund
from webapps.adyen.helpers import float_amount_to_cents, generate_shopper_reference, get_reference
from webapps.adyen.models import Auth, Capture
from webapps.adyen.typing import DeviceDataDict, RecurringModel
from webapps.business.models import Business
from webapps.pos import enums
from webapps.pos.enums import PaymentProviderEnum, PaymentTypeEnum, receipt_status
from webapps.pos.events import chargeback_received_event
from webapps.pos.provider.base import PaymentProvider, PaymentProviderError
from webapps.pos.provider.typing import NewCardDetails
from webapps.user.models import User

log = logging.getLogger('booksy.adyen_ee_provider')


# pylint: disable=too-many-positional-arguments,broad-exception-raised


def _auth_transaction(  # pylint: disable=too-many-arguments
    transaction: 'Transaction',
    payment_method: 'PaymentMethod',
    value: Union[int, float],
    device_data: Optional[DeviceDataDict],
    extra_data: dict = None,
    marketpay_splits: dict = None,
):
    """
    Args:
        transaction: webapps.pos.models.Transaction object
        provider_ref (str): Reference to first auth
        value (int, float): Auth value as float, NOT in cents
        extra_data (dict, optional): extra data from request

    Returns:
        Result of webapps.adyen.flow.auth function
    """
    from webapps.pos.models import Transaction

    if RemoveAdyenEEPaymentProviderFlag():
        raise NotImplementedError()

    transaction: Transaction
    currency = transaction.currency_symbol
    extra_data = extra_data or {}
    amount = float_amount_to_cents({'currency': currency, 'value': value})
    cardholder = {
        'email': transaction.customer.email,
        'ip': extra_data.get('cardholder_id'),
    }
    additional_data = _additional_data(
        transaction,
        payment_method.provider_ref,
        extra_data,
    )
    if marketpay_splits:
        additional_data.update(
            _marketpay_splits(
                marketpay_splits,
                transaction.pos.business_id,
            ),
        )
    cancellation_fee_transaction = (
        transaction.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
    )
    automatically_finished_booking = (
        transaction.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT
        and transaction.customer.is_payment_auto_accept_possible
    )
    booking_confirmed_in_manual_mode = (
        transaction.latest_receipt.status_code == receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
        and transaction.pos.business.booking_mode == Business.BookingMode.SEMIAUTO
    )
    recurring_model: RecurringModel = (
        'UnscheduledCardOnFile'
        if (
            cancellation_fee_transaction
            or automatically_finished_booking
            or booking_confirmed_in_manual_mode
        )
        else 'CardOnFile'
    )

    return flow.auth(
        cardholder,
        amount,
        payment_method,
        device_data,
        additional_data,
        use_marketpay=bool(marketpay_splits),
        recurring_model=recurring_model,
    )


def _basket_item(row, get_amount):
    '''TransactionRow -> (dict) Adyen risk data basket item'''

    if RemoveAdyenEEPaymentProviderFlag():
        raise NotImplementedError()
    item_id = None
    item_title = None

    if row.type in [row.TRANSACTION_ROW_TYPE__SERVICE, row.TRANSACTION_ROW_TYPE__DEPOSIT]:
        service_variant = row.service_variant or row.subbooking and row.subbooking.service_variant
        item_id = service_variant.id if service_variant else row.subbooking_id
        item_title = (
            service_variant
            and service_variant.service.name
            or row.subbooking
            and row.subbooking.service_name
            or row.name_line_1
        )
    elif row.type == row.TRANSACTION_ROW_TYPE__PRODUCT:
        item_id = row.product_id
        item_title = row.product.name

    return {
        'itemID': item_id,
        'productTitle': item_title,
        'amountPerItem': get_amount(row.item_price),
        'currency': row.transaction.currency_symbol,
        'quantity': row.quantity,
        'manufacturer': row.transaction.pos.business_id,
        # level23 fields
        '_type': row.type,
        '_totalAmount': get_amount(row.total),
    }


def _additional_data(transaction, provider_ref, extra_data=None):
    """
    Args:
        transaction: webapps.pos.models.Transaction object
        provider_ref (str): Reference to first auth
        value (int, float): Auth value as float, NOT in cents
        extra_data (dict): extra data from request
    """
    if RemoveAdyenEEPaymentProviderFlag():
        raise NotImplementedError()
    if extra_data is None:
        extra_data = {}

    first_auth = Auth.objects.filter(reference=provider_ref).first()

    def get_amount(value):
        return float_amount_to_cents({'currency': transaction.currency_symbol, 'value': value})[
            'value'
        ]

    # prepare items in riskdata.basket format
    basket_items = [_basket_item(row, get_amount) for row in transaction.rows.all()]
    # https://docs.adyen.com/developers/api-reference/payments-api#level23dataforvisaandmastercard
    level23 = {
        'customerReference': str(
            first_auth.card.cardholder_id if first_auth and first_auth.card else None
        ),
        'totalTaxAmount': get_amount(sum(s.tax_amount for s in transaction.tax_subtotals.all())),
        'orderDate': transaction.charge_date.strftime('%d%m%y'),
    }
    # map riskdata.basket format to enhancedSchemeData
    level23.update(
        {
            f'itemDetailLine{i}': {
                'commodityCode': r.pop('_type'),  # pop: sic!
                'description': (r['productTitle'] or '')[:26],
                'productCode': (str(r['itemID']) or '')[:12],
                'quantity': r['quantity'],
                # https://help.usaepay.info/developer/reference/umcodes/
                'unitOfMeasure': 'BX',
                'unitPrice': r['amountPerItem'],
                'totalAmount': r.pop('_totalAmount'),
            }
            for i, r in enumerate(basket_items)
        }
    )

    # https://docs.adyen.com/developers/api-reference/payments-api#riskdatafields
    riskdata = {
        'basket': {f'item{i}': item for i, item in enumerate(basket_items)},
    }

    if first_auth:
        riskdata.update(
            {
                'accountCreationDate': first_auth.created.strftime('%Y-%m-%d %H:%M:%S'),
                'previousSuccessfulOrders': first_auth.children.filter(
                    oper_result=oper_result.SUCCESS
                ).count(),
            }
        )

    additional_data = {
        'riskdata': riskdata,
        'enhancedSchemeData': level23,
    }

    return additional_data


def _marketpay_splits(splits, business_id):
    """Convert to additionalData format

    # https://docs.adyen.com/developers/api-reference/payments-api#splitpaymentfields
    """
    if RemoveAdyenEEPaymentProviderFlag():
        raise NotImplementedError()
    if splits:
        reference = f'{settings.API_COUNTRY}-{business_id}'
        return {
            'split.api': 1,
            'split.nrOfItems': 3,
            'split.totalAmount': splits['total_amount'],
            'split.currencyCode': splits['currency_code'],
            'split.item1.amount': splits['txn_fee'],
            'split.item1.type': 'PaymentFee',
            'split.item1.reference': 'PF-' + reference,
            'split.item2.amount': splits['provision'],
            'split.item2.type': 'Commission',
            'split.item2.reference': 'CM-' + reference,
            'split.item3.amount': splits['amount'],
            'split.item3.type': 'MarketPlace',
            'split.item3.reference': splits['reference'],
            'split.item3.account': splits['account_code'],
        }
    return {}


class AdyenEEPaymentProvider(PaymentProvider):
    codename = PaymentProviderEnum.ADYEN_PROVIDER.value
    label = PaymentProviderEnum.ADYEN_PROVIDER.label

    ###########################################################################
    # helpers
    ###########################################################################

    @staticmethod
    def add_payment_method_err_msg(payment_row: 'PaymentRow' = None, _oper_result: int = None):
        """Provider specific. Returns reject reason. Requires either payment_row or _oper_result

        :param payment_row: PaymentRow instance
        :param _oper_result: integer
        :return dict(
            'type': PaymentRow.PAYMENT_ROW__REJECT_REASON,
            'msg': text
        )

        Can't yet be removed unfortunately. Used i.e. in ReceiptNoteSerializer
        """

        assert payment_row or _oper_result
        from webapps.pos.models import PaymentRow

        three_d_secure_problem = [
            oper_result.THREE_DS_AUTHENTICATION_REQUIRED,
        ]
        cvv_problem = [
            oper_result.CVC_DECLINED,
            oper_result.INVALID_CVC_LENGTH,
            oper_result.INVALID_CVC,
        ]
        expiry_date_problem = [
            oper_result.EXPIRED_CARD,
            oper_result.INVALID_EXPIRY_DATE,
            oper_result.INVALID_EXPIRY_MONTH_OR_BEFORE_NOW,
            oper_result.INVALID_EXPIRY_MONTH,
        ]
        ccn_problem = [
            oper_result.INVALID_CCN,
            oper_result.INVALID_CARD_NUMBER,
        ]
        bank_said_gtfo = [
            oper_result.REFUSED,
            oper_result.BLOCKED_CARD,
            oper_result.NOT_SUPPORTED,
            oper_result.RESTRICTED_CARD,
            oper_result.CONTRACT_NOT_FOUND,
            oper_result.TRANSACTION_NOT_PERMITTED,
            oper_result.PAYMENT_DETAILS_ARE_NOT_SUPPORTED,
        ]
        get_a_job = [
            oper_result.NOT_ENOUGH_BALANCE,
        ]
        fraud_problem = [
            oper_result.FRAUD,
        ]

        result = payment_row.oper_result if payment_row else _oper_result

        if result in three_d_secure_problem:
            msg = _('This transaction requires 3DSecure authentication flow')
        elif result in expiry_date_problem:
            msg = _('This card has expired. Please try another card.')
        elif result in cvv_problem:
            msg = _(
                'The provided security code (CVC/CVV) is incorrect. Please verify '
                'and try again. If the problem persists, please try a different '
                'card.'
            )
        elif result in ccn_problem:
            msg = _(
                'The provided card number is incorrect. Please verify and try '
                'again. If the problem persists, please try a different card.'
            )
        elif result in bank_said_gtfo:
            msg = _(
                'The issuing bank did not approve this action. Please try a '
                'different card or contact the bank.'
            )
        elif result in get_a_job:
            msg = _(
                'There are insufficient funds or the transaction exceeds the '
                'limit. Please try another card.'
            )
        elif result in fraud_problem:
            msg = _(
                'Unfortunately, the payment service provider did not approve this '
                'action. Please try a different card or get in touch with '
                'Booksy\'s customer support.'
            )
        else:
            msg = _(
                'The issuing bank did not approve this action. Please check the '
                'card details or, if the problem persists, try a different card.'
            )

        return {
            'type': PaymentRow.PAYMENT_ROW__REJECT_REASON,
            'msg': msg,
            'error': '3DSecure' if result in three_d_secure_problem else '',
        }

    ###########################################################################
    # payment methods
    ###########################################################################

    def _add_payment_method(
        self,
        user: User,
        data: dict,
        device_data: Optional[DeviceDataDict],
        **ctx: dict,
    ):
        """
        Args:
            user: webapps.user.models.User instance
            data: dict.
                Example:
                    {
                        'encrypted_data': 'asdf',
                        'ip': '127.0.0.1',
                    }
            **ctx:  additional context data. Must contain "ip" key.

        Returns: dict.
            Example:
                {
                    'provider_ref': 1234,
                    'method_type': 'credit_card',
                }
            Description:
                provider_ref: reference for future usage
                method_type: payment method type: 'credit_card'

        Raises:
            PaymentProviderError: if not success
            DuplicatedCardException: if the card already existed
        """
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()

        # get request data
        cardholder = {'ip': ctx['ip'], 'email': user.email}
        amount = {'value': 0, 'currency': settings.CURRENCY_CODE}
        shopper_reference = generate_shopper_reference(user)

        with db_transaction.atomic():
            # This block is run within sub-transaction because:
            # * if the added card is determined to be duplicate we should
            #   revert creation of the Card and Auth objects
            # * in case of any Adyen-related errors (card authorized but not
            #   returned in shopper payments list) we should try to keep our
            #   internal representation as close to theirs as possible

            # first_auth_ee() method creates Card and Auth objects
            billing_address = data.get('billing_address')
            if billing_address:
                # https://booksy.atlassian.net/browse/FLS-2358
                if 'contry' in billing_address:
                    billing_address['country'] = billing_address['contry']
                    del billing_address['contry']

            result = flow.first_auth_ee(
                data['encrypted_data'],
                cardholder,
                amount,
                shopper_reference,
                device_data=device_data,
                billing_address=billing_address,
            )

            operation_result = result['oper_result']
            if operation_result == oper_result.THREE_DS_AUTHENTICATION_REQUIRED:
                auth = Auth.objects.get(reference=result['auth_ref'])
                raise ThreeDSecureAuthenticationRequired(
                    _('3DSecure authentication required'),
                    auth.three_d_secure_data,
                    auth.reference,
                )
            if operation_result != oper_result.SUCCESS:
                msg = self.add_payment_method_err_msg(_oper_result=result['oper_result'])
                raise PaymentProviderError(msg['msg'])

            # try to retrieve sanitized card details from the Adyen
            auth = Auth.objects.get(reference=result['auth_ref'])
            flow.details(auth.card.cardholder.shopper_reference)
            card_fields_to_update = [
                'last_4_digits',
                'brand',
                'alias',
                'expiry_month',
                'expiry_year',
            ]
            auth.card.refresh_from_db(fields=card_fields_to_update)
            auth.card.cardholder.refresh_from_db(fields=['name'])
            card_info_missing = any(
                not getattr(auth.card, field_name) for field_name in card_fields_to_update
            )
            if card_info_missing and not ctx.get('allow_empty', False):
                # Missing card information means Adyen detected card as
                # duplicate and rejected its creation
                raise DuplicatedCardException

        # prepare PaymentMethod attributes
        attrs = {
            'provider_ref': result['auth_ref'],
            'method_type': enums.PAYMENT_METHOD_TYPE__CREDIT_CARD,
            'card_last_digits': auth.card.last_4_digits,
            'card_type': auth.card.pos_card_type,
            'alias': auth.card.alias,
            'expiry_month': auth.card.expiry_month,
            'expiry_year': auth.card.expiry_year,
            'cardholder_name': auth.card.cardholder.name,
        }

        return attrs

    def _finish_3d_secure_1_auth(  # pylint: disable=arguments-differ
        self,
        auth_psp_reference: str,
        md: str,
        pa_response: str,
    ) -> Optional[NewCardDetails]:
        """
        Returns new card details only if the flow resulted in card addition
        :raises PaymentProviderError: in case of unsuccessful 3DS flow
        """
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()
        try:
            auth = flow.authorise_3d_secure_1_flow(
                auth_psp_reference,
                md,
                pa_response,
            )
        except Auth.DoesNotExist:
            success = False
        else:
            success = auth and auth.oper_result == oper_result.SUCCESS

        if not success:
            raise PaymentProviderError('3DSecure1 flow was unsuccessful.')

        return (
            NewCardDetails(
                provider_ref=auth.reference,
                method_type=enums.PAYMENT_METHOD_TYPE__CREDIT_CARD,
                card_last_digits=auth.card.last_4_digits,
                card_type=auth.card.pos_card_type,
                alias=auth.card.alias,
                expiry_month=auth.card.expiry_month,
                expiry_year=auth.card.expiry_year,
                cardholder_name=auth.card.cardholder.name,
            )
            if auth.card
            else None
        )

    def _remove_payment_method(self, payment_method):
        """
        :raises UnconfirmedCardRemovalException: when card has not been
                                                 confirmed yet
        """
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()
        try:
            auth = Auth.objects.select_related('card').get(reference=payment_method.provider_ref)
        except Auth.DoesNotExist:
            log.exception(
                'Auth for payment method (id:%s) not found',
                payment_method.id,
            )
            raise
        if not auth.card.adyen_confirmed or tznow() - auth.card.created < timedelta(
            seconds=CARD_REMOVAL_TIMEOUT
        ):
            raise UnconfirmedCardRemovalException
        shopper_reference = auth.card.cardholder.shopper_reference
        flow.disable(auth.card.recurr_detail_ref)

        card = auth.card
        card.active = False
        card.save()
        # Reload details of the remaining payment cards
        flow.details(shopper_reference)

    def _payment_method_callback(self, data):
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()

    ###########################################################################
    # payments
    ###########################################################################
    def _make_auth(  # pylint: disable=too-many-arguments
        self,
        transaction: 'Transaction',
        payment_method: 'PaymentMethod',
        payment_row: 'PaymentRow',
        device_data: Optional[DeviceDataDict],
        extra_data: Optional[dict],
        marketpay_splits: Optional[dict],
    ) -> dict:
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()
        auth_result = _auth_transaction(
            transaction,
            payment_method=payment_method,
            value=payment_row.amount,
            device_data=device_data,
            extra_data=extra_data,
            marketpay_splits=marketpay_splits,
        )

        # handle failed auth - return
        if auth_result['oper_result'] == oper_result.SUCCESS:
            if payment_row.payment_type.code == PaymentTypeEnum.PREPAYMENT:
                status = receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
            else:
                status = receipt_status.PAYMENT_SUCCESS
        elif (
            payment_row.payment_type.code == PaymentTypeEnum.PREPAYMENT
            and auth_result['oper_result'] == oper_result.THREE_DS_AUTHENTICATION_REQUIRED
        ):
            status = receipt_status.CALL_FOR_PREPAYMENT_3DS
        elif payment_row.payment_type.code == PaymentTypeEnum.PREPAYMENT:
            status = receipt_status.PREPAYMENT_AUTHORISATION_FAILED
        elif auth_result['oper_result'] == oper_result.THREE_DS_AUTHENTICATION_REQUIRED:
            status = receipt_status.CALL_FOR_PAYMENT_3DS
        else:
            status = receipt_status.PAYMENT_FAILED

        return {'transaction_status': status, **auth_result}

    def _make_capture(
        self,
        auth_ref: str,
        payment_row: 'PaymentRow',
    ) -> dict:
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()
        auth = Auth.objects.get(reference=auth_ref)
        capture_result = (
            flow.capture(auth_ref)
            if auth.oper_result == oper_result.SUCCESS
            else {
                'oper_result': oper_result.FAILED,
                'capture_ref': None,
            }
        )

        if capture_result['oper_result'] == oper_result.PENDING:
            if payment_row.payment_type.code == PaymentTypeEnum.PREPAYMENT:
                status = receipt_status.PREPAYMENT_SUCCESS
            else:
                status = receipt_status.PAYMENT_SUCCESS
        else:
            if payment_row.payment_type.code == PaymentTypeEnum.PREPAYMENT:
                status = receipt_status.PREPAYMENT_FAILED
            else:
                status = receipt_status.PAYMENT_FAILED

        return {'transaction_status': status, **capture_result}

    def _make_payment(  # pylint: disable=too-many-arguments
        self,
        transaction: 'Transaction',
        payment_method: 'PaymentMethod',
        payment_row: 'PaymentRow',
        device_data: Optional[DeviceDataDict],
        extra_data: Optional[dict],
        marketpay_splits: Optional[dict],
    ):
        """
        Args:
            transaction: webapps.pos.models.Transaction instance.
            payment_method: webapps.pos.models.PaymentMethod instance.
            cardholder_ip: str. Real ip of cardholder

        Returns: tuple.
            Example: ('P', 123)
            Description:
                [0]: status from Receipt consts
                [1]: charge operation reference for future use
        """
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()
        cache_lock_key = f'adyen.lock.{payment_row.id}'
        cached_response = cache.get(cache_lock_key)
        if cached_response:
            return cached_response

        auth_result = self._make_auth(
            transaction,
            payment_method=payment_method,
            payment_row=payment_row,
            device_data=device_data,
            extra_data=extra_data,
            marketpay_splits=marketpay_splits,
        )

        if auth_result['oper_result'] != oper_result.SUCCESS:
            # Calling make_auth method inside make_payment had degenerated status list
            # if authorization of prepayment was failed, it was assumed that whole payment will
            # be failed, so status PREPAYMENT_FAILED instead of PREPAYMENT_AUTHORIZATION_FAILED
            # was selected.
            if auth_result['transaction_status'] == receipt_status.PREPAYMENT_AUTHORISATION_FAILED:
                auth_result['transaction_status'] = receipt_status.PREPAYMENT_FAILED

            return (
                auth_result['transaction_status'],
                auth_result['auth_ref'],
                auth_result['oper_result'],
            )

        capture_result = self._make_capture(
            auth_ref=auth_result['auth_ref'],
            payment_row=payment_row,
        )

        resp = (
            capture_result['transaction_status'],
            capture_result['capture_ref'],
            capture_result['oper_result'],
        )
        if capture_result['transaction_status'] in [
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PAYMENT_SUCCESS,
        ]:
            cache.set(cache_lock_key, resp, timeout=120)
        return resp

    def _payment_callback(self, data):
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()

    ###########################################################################
    # deposits
    ###########################################################################

    def _authorize_deposit(  # pylint: disable=too-many-arguments
        self,
        transaction: 'Transaction',
        payment_method: 'PaymentMethod',
        payment_row: 'PaymentRow',
        extra_data: Optional[dict],
        marketpay_splits: Optional[dict],
    ) -> Tuple[str, str]:
        """Provider specific part of deposit authorization.

        :param transaction: Transaction instance
        :param payment_method: PaymentMethod instance
        :param payment_row: PaymentRow instance
        :param extra_data: dict. extra data from request
        :param marketpay_splits: dict. Data from get_marketpay_splits()
        :return: (status, pnref) for new Receipt

        """
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()

        is_deposit = payment_row.payment_type.code != PaymentTypeEnum.PREPAYMENT

        # To save paymentMethod used to accept booking we create fake Auth
        # object. It is neccessery for charge_deposit method.
        reference = get_reference()
        first_auth_ref = payment_method.provider_ref
        first_auth = Auth.objects.filter(reference=first_auth_ref).first()

        auth_obj = Auth(
            first_auth=first_auth,
            reference=reference,
            amount=0,
        )
        auth_obj.save()

        if is_deposit:
            status = receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        else:
            status = receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS

        return status, reference

    def _charge_deposit(
        self,
        transaction: 'Transaction',
        payment_row: 'PaymentRow',
        marketpay_splits: dict,
        device_data: Optional[DeviceDataDict],
        **kwargs,
    ) -> Tuple[str, str, str]:
        """
        Args:
            transaction: webapps.pos.models.Transaction instance.
        Returns: tuple.
            Example:
                ('P', 123)
            Description:
                [0]: status from Receipt consts
                [1]: charge operation reference for future use
                [2]: operation status
        """
        from webapps.pos.models import PaymentMethod

        if RemoveAdyenEEPaymentProviderFlag():
            return (
                receipt_status.DEPOSIT_CHARGE_FAILED,
                get_reference(),
                oper_result.UNKNOWN_CONN_ERR,
            )

        # auth
        zero_auth = Auth.objects.get(
            reference=transaction.latest_receipt.payment_rows.get().pnref,
        )

        zero_auth_ref = (
            zero_auth.first_auth.reference if zero_auth.first_auth else zero_auth.reference
        )

        payment_method = PaymentMethod.all_objects.get(provider_ref=zero_auth_ref)
        auth_result = _auth_transaction(
            transaction,
            payment_method=payment_method,
            value=payment_row.amount,
            marketpay_splits=marketpay_splits,
            device_data=device_data,
        )

        if auth_result['oper_result'] != oper_result.SUCCESS:
            status = (
                receipt_status.CALL_FOR_DEPOSIT_3DS
                if auth_result['oper_result'] == oper_result.THREE_DS_AUTHENTICATION_REQUIRED
                else receipt_status.DEPOSIT_CHARGE_FAILED
            )
            return status, auth_result['auth_ref'], auth_result['oper_result']

        # capture
        result = flow.capture(auth_result['auth_ref'])

        if result['oper_result'] == oper_result.PENDING:
            status = receipt_status.DEPOSIT_CHARGE_SUCCESS
        else:
            status = receipt_status.DEPOSIT_CHARGE_FAILED

        return status, result['capture_ref'], result['oper_result']

    def _cancel_deposit(self, transaction, payment_row):
        # do not remove this method, used i.e. in ReleaseDepositOnCancel
        return receipt_status.DEPOSIT_CHARGE_CANCELED, None, None

    def _deposit_callback(self, data):
        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()

    @classmethod
    def chargeback_transaction(
        cls,
        pnref: str,
        chargeback_details: dict,
    ):
        from webapps.notification.scenarios import start_scenario
        from webapps.pos.scenarios import ChargebackBusinessNotificationScenario

        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()

        chargeback_reason_code = chargeback_details['additionalData']['chargebackReasonCode']
        payment_method = chargeback_details['paymentMethod']
        chargeback_psp_reference = chargeback_details['pspReference']

        created_operation_fee = super().chargeback_transaction(pnref, chargeback_details)
        if created_operation_fee:
            start_scenario(
                ChargebackBusinessNotificationScenario,
                payment_row_id=created_operation_fee.payment_row_id,
                reason_code=chargeback_reason_code,
                payment_method=payment_method,
                chargeback_psp_reference=chargeback_psp_reference,
            )

            chargeback_received_event.send(created_operation_fee.payment_row)

    @staticmethod
    def send_for_refund(payment_row: 'PaymentRow', operator: User, from_admin: bool = True):
        from webapps.pos.models import PaymentRow, PaymentRowChange
        from webapps.pos.refund import is_refund_possible
        from webapps.pos.refund import create_operation_fee

        if RemoveAdyenEEPaymentProviderFlag():
            raise NotImplementedError()

        possible, _error = is_refund_possible(
            payment_row,
            check_requested=False,
            from_admin=from_admin,
        )
        if not payment_row.pnref or not possible:
            raise AssertionError(
                'That row cannot be refunded'
            )  # pylint: disable=broad-exception-raised

        with db_transaction.atomic():
            capture_obj = Capture.objects.get(reference=payment_row.pnref)

            PaymentRow.objects.filter(pnref=payment_row.pnref).update(settled=True)

            if not payment_row.refund_requested:
                payment_row.refund_requested = tznow()
                payment_row.refund_operator = operator
                payment_row.save(update_fields=['refund_requested', 'refund_operator'])

            # >>> Provider things
            pr_data, splits = payment_row.get_refund_splits()

            # here the provider logic is broken; direct call to adyen flow
            # result_data = cancel_or_refund(
            result_data = refund(
                capture_obj.auth.reference,
                splits=splits,
            )
            refund_ref = result_data.get('refund_ref')
            # <<<

            refund_row = payment_row.update_status(
                status=receipt_status.SENT_FOR_REFUND,
                settled=True,
                pnref=refund_ref,
                operator=operator,
                log_action=PaymentRowChange.SENT_FOR_REFUND,
                log_note=f'{"Admin" if from_admin else "User"} sent for refund action',
                marketpay_splits=pr_data,
            )
            # for marketpay clients only
            if splits is not None:
                create_operation_fee(refund_row)

            return refund_row
