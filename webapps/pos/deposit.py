#!/usr/bin/env python
from decimal import Decimal
from typing import TYPE_CHECKING

from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger
from lib.pos.utils import txn_refactor_stage2_enabled
from webapps.adyen.typing import DeviceDataDict
from webapps.pos.enums import PaymentTypeEnum, receipt_status, CARD_TYPE__KEYED_IN_PAYMENT
from webapps.pos.models import POS, PaymentRowChange, PaymentType, Transaction, PaymentMethod
from webapps.pos.provider.proxy import ProxyProvider
from webapps.pos.services import PaymentRowService, TransactionService
from webapps.user.models import User

if TYPE_CHECKING:
    from webapps.booking.appointment_checkout import AppointmentCheckout


def create_cancellation_fee_transaction(
    *,
    checkout: 'AppointmentCheckout',
    pos: 'POS',
    disable_promotions=False,
    user: 'User' = None,
):
    from webapps.pos.serializers import TransactionSerializer

    # TODO::FLS-3976
    data = {
        'transaction_type': Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        'deposits': [{'row': row} for row in checkout.cancellation_fee_rows],
        'dry_run': False,
        'payment_rows': [
            {
                'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                'amount': checkout.cancellation_fee.total,
            }
        ],
    }
    context = {
        'pos': pos,
        'business': pos.business,
        'operator': None,
        'deposit_mode': True,
        'compatibilities': {
            'new_checkout': True,
        },
        'disable_promotions': disable_promotions,
        'user': user,
    }

    # lets TransactionSerializer calculate the value to pay
    serializer = TransactionSerializer(data=data, context=context)
    if not serializer.is_valid():
        # should not happen as it does not depend on user provided data
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f"create_deposit error: {serializer.errors}"
        )  # pylint: disable=broad-exception-raised

    transaction = serializer.save()
    return transaction


def create_prepayment_transaction(  # pylint: disable=too-many-arguments
    *,
    prepaid_total: Decimal,
    subbookings_ids: list[int],
    pos: 'POS',
    prepayment: Decimal,
    tip: dict = None,
    disable_promotions=False,
    user: 'User' = None,
):
    from webapps.pos.serializers import TransactionSerializer

    data = {
        'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
        'bookings': [
            {
                'booking_id': _id,
            }
            for _id in subbookings_ids
        ],
        'tip': tip,
        'dry_run': False,
        'payment_rows': [
            {
                'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                'amount': prepayment,
                'tip_amount': tip['amount'] if tip and tip['amount'] else None,
            }
        ],
    }

    serializer = TransactionSerializer(
        data=data,
        context={
            'pos': pos,
            'business': pos.business,
            'operator': None,
            'prepayment': prepaid_total,
            'compatibilities': {
                'new_checkout': True,
            },
            'disable_promotions': disable_promotions,
            'user': user,
        },
    )
    if not serializer.is_valid():
        # should not happen as it does not depend on user provided data
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f"create_deposit error: {serializer.errors}"
        )  # pylint: disable=broad-exception-raised

    transaction = serializer.save()
    return transaction


def create_payment_type_for_booksy_gift_card_if_missed(pos: 'POS') -> None:
    from django.db.models import Max

    if pos is None:
        return
    if PaymentType.objects.filter(
        pos=pos,
        code=PaymentTypeEnum.BOOKSY_GIFT_CARD,
    ).exists():
        return None
    max_existing_order = (
        PaymentType.objects.filter(
            pos=pos,
        )
        .aggregate(Max('order'))
        .get('order__max')
        or 0
    )
    order = max_existing_order + 100  # it will not be visible so can be high
    booksy_gift_cards_by_app = PaymentType(
        pos=pos,
        order=order,
        code=PaymentTypeEnum.BOOKSY_GIFT_CARD,
        default=False,
        available=False,
        enabled=True,
    )
    booksy_gift_cards_by_app.save()


def create_gift_card_transaction(  # pylint: disable=too-many-arguments
    *,
    total_value: Decimal,
    subbookings_ids: list[int],
    pos: 'POS',
    available_balance: Decimal,
    tip: dict = None,
    disable_promotions=False,
    user: 'User' = None,
):
    from webapps.pos.serializers import TransactionSerializer

    create_payment_type_for_booksy_gift_card_if_missed(pos)

    if available_balance > total_value:
        amount = total_value
    else:
        amount = available_balance

    data = {
        'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
        'bookings': [
            {
                'booking_id': _id,
            }
            for _id in subbookings_ids
        ],
        'tip': tip,
        'dry_run': False,
        'payment_rows': [
            {
                'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                'amount': amount,
                'tip_amount': tip['amount'] if tip and tip['amount'] else None,
            }
        ],
    }

    serializer = TransactionSerializer(
        data=data,
        context={
            'pos': pos,
            'business': pos.business,
            'operator': None,
            # 'prepayment': appointment.checkout.prepayment.total,
            'compatibilities': {
                'new_checkout': True,
            },
            'disable_promotions': disable_promotions,
            'user': user,
            'is_bgc': True,
        },
    )
    if not serializer.is_valid():
        # should not happen as it does not depend on user provided data
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f"create_deposit error: {serializer.errors}"
        )  # pylint: disable=broad-exception-raised

    # todo create hold in Booksy Gift Card microservice
    # and if not then raise some exception
    transaction = serializer.save()
    return transaction


def create_booksy_pay_transaction(  # pylint: disable=too-many-arguments
    *,
    appointment_id: int,
    subbookings_ids: list[int],
    booksy_pay_total: Decimal,
    amount: Decimal,
    pos: 'POS',
    tip: dict | None = None,
    user: User | None = None,
) -> Transaction:
    from webapps.pos.serializers import TransactionSerializer

    data = {
        'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
        'bookings': [
            {
                'booking_id': _id,
            }
            for _id in subbookings_ids
        ],
        'tip': tip,
        'dry_run': False,
        'payment_rows': [
            {
                'payment_type_code': PaymentTypeEnum.BOOKSY_PAY,
                'amount': amount,
                'tip_amount': tip['amount'] if tip and tip['amount'] else None,
            }
        ],
    }

    # Required for retrying a failed transaction in 3DS flow (note: failed Booksy Pay transactions
    # are not saved in most of the cases, however, a 3DS flow is an exception here, due to the
    # transient CALL_FOR_BOOKSY_PAY_3DS status. In this case the transaction is already there
    # in the database, therefore it needs to be provided to the `TransactionSerializer` in order
    # to be able to retry a payment - hence `old_txn` in the context).
    old_txn = (
        Transaction.objects.by_appointment_id(appointment_id)
        .select_related(
            'latest_receipt',
        )
        .only(
            'id',
            'latest_receipt__status_code',
        )
        .filter(
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            children__isnull=True,
        )
        .exclude(
            latest_receipt__status_code=receipt_status.ARCHIVED,
        )
        .last()
    )

    serializer = TransactionSerializer(
        data=data,
        context={
            'pos': pos,
            'business': pos.business,
            'operator': None,
            'booksy_pay': booksy_pay_total,
            'compatibilities': {
                'new_checkout': True,
            },
            'user': user,
            'old_txn': old_txn,
        },
    )
    if not serializer.is_valid():
        # should not happen as it does not depend on user provided data
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f"create_deposit error: {serializer.errors}"
        )  # pylint: disable=broad-exception-raised

    transaction = serializer.save()
    return transaction


def charge_prepayment_on_confirm(
    appointment_id: int = None,
    device_data: DeviceDataDict = None,
    extra_data: dict = None,
):
    """Release deposit on booking cancel (or decline).

    If customer cancels booking it should be triggered only if it was done
    within business policy (pos.deposit_cancel_time).

    :param appointment_id: Appointment ID.
    :param device_data: information about device used for performing transaction
    :param extra_data: dict. optional
    """
    # get booking
    from webapps.booking.models import Appointment

    instance = Appointment.objects.filter(id=appointment_id).first()
    if instance is None:
        return

    from webapps.pos.provider import get_payment_provider

    transaction = (
        Transaction.objects.by_appointment_id(
            appointment_id,
        )
        .filter(
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            latest_receipt__status_code=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
        )
        .last()
    )

    if not transaction:
        return

    # charge prepayment

    # There can be only 1 pba row with CFP status
    prepayment_row = transaction.latest_receipt.payment_rows.filter(
        status=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
        payment_type__code=PaymentTypeEnum.PREPAYMENT,
    ).first()

    business_payment_provider_code = (
        PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
            TransactionService.get_payment_provider_code(
                pos=transaction.pos,
                payment_type_code=PaymentTypeEnum.PREPAYMENT,
                txn=transaction,
            )
        )
    )
    payment_method = transaction.customer.payment_methods.filter(
        default=True,
        provider=business_payment_provider_code,
    ).first()

    if not payment_method:
        # TODO Put smart handler here
        prepayment_row.update_status(
            status=receipt_status.PREPAYMENT_FAILED,
            log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
            log_note='charge_prepayment_on_confirm no payment method',
        )
        return

    provider = get_payment_provider(
        codename=payment_method.provider,
        txn=transaction,
    )

    if txn_refactor_stage2_enabled(transaction):
        return provider.make_capture(
            payment_row=prepayment_row,
            trigger=BasketPaymentAnalyticsTrigger.BUSINESS__PREPAYMENT_CONFIRM,
            device_data=device_data,
            extra_data=extra_data,
        )

    provider.make_payment(
        transaction,
        payment_method=payment_method,
        payment_row=prepayment_row,
        device_data=device_data,
        extra_data=extra_data,
    )


def pay_deposit_with_kip(appointment_id: int, token: str):
    transaction = Transaction.objects.by_appointment_id(appointment_id)[0]
    # Only 1 PaymentRow should exist
    deposit_payment_row = transaction.latest_receipt.payment_rows.get()
    payment_method = PaymentMethod(
        card_type=CARD_TYPE__KEYED_IN_PAYMENT,
    )
    payment_method.token = token
    ProxyProvider.make_payment(
        transaction=transaction,
        payment_method=payment_method,
        payment_row=deposit_payment_row,
        trigger=BasketPaymentAnalyticsTrigger.BUSINESS__PREPAYMENT_AUTH,
    )
