import typing as t
from django.utils.translation import gettext_lazy as _

from lib.tools import format_currency
from webapps.booking.notifications.contexts import (
    AppointmentContext,
    AppointmentPaymentContext,
)
from webapps.business.notifications.contexts import CustomerContext
from webapps.notification.base import (
    BaseNotification,
    PopupTemplate,
    PushTarget,
)
from webapps.notification.channels import (
    PopupChannel,
    PushChannel,
    push_from_popup,
)
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
)
from webapps.notification.recipients import (
    Managers,
    Reception,
    SystemSender,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import PaymentRow

if t.TYPE_CHECKING:
    # pylint: disable=ungrouped-imports, unused-import
    pass


class AppointmentMobileTransactionBaseNotification(BaseNotification):
    category = NotificationCategory.TRANSACTION

    sender = SystemSender
    recipients = (
        Reception,
        Managers,
    )
    channels = (Popup<PERSON>hannel, <PERSON>ushChannel)
    contexts = (AppointmentContext, AppointmentPaymentContext, CustomerContext)

    def __init__(self, transaction, **parameters):
        super().__init__(transaction, **parameters)

        self.transaction = transaction
        self.business = transaction.pos.business
        self.customer = transaction.customer_card
        self.appointment = transaction.appointment

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.transaction.id}'

    def get_context(self):
        return {
            'total_amount': format_currency(
                self.transaction.payment_rows.filter(payment_type__code=PaymentTypeEnum.PAY_BY_APP)
                .last()
                .amount
            )
        }

    def get_target(self):
        return PushTarget(type='booking', id=self.appointment.subbookings[0].id)


class CancellationFeeChargedNotification(AppointmentMobileTransactionBaseNotification):
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.MOBILE_PAYMENT,
        relevance=2,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _("You've received a {total_amount}"),
            _('Cancellation fee • {booking_date} • {booking_time}'),
            _('{customer_name} • {service_name} {addons}'),
        ],
        photo='{customer_photo_url}',
    )
    push_template = push_from_popup(popup_template)


class MobilePaymentNotification(AppointmentMobileTransactionBaseNotification):
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.MOBILE_PAYMENT,
        relevance=2,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _("You've received a payment of "),
            '{total_amount}',
            _('{customer_name} • {service_name} {addons}'),
        ],
        photo='{customer_photo_url}',
    )
    push_template = push_from_popup(popup_template)


class BooksyPayPaymentCompletedNotification(BaseNotification):
    category = NotificationCategory.BOOKSY_PAY

    sender = SystemSender
    recipients = (
        Reception,
        Managers,
    )
    channels = (PopupChannel, PushChannel)
    contexts = (AppointmentContext, CustomerContext)

    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.MOBILE_PAYMENT,
        relevance=2,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('New payment received'),
            _('{total_amount} received for upcoming appointment'),
            _('{customer_name} • {service_name} {addons}'),
        ],
        photo='{customer_photo_url}',
    )
    push_template = push_from_popup(popup_template)

    def __init__(self, transaction, **parameters):
        super().__init__(transaction, **parameters)

        self.transaction = transaction
        self.business = transaction.pos.business
        self.customer = transaction.customer_card
        self.appointment = transaction.appointment

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.transaction.id}'

    def get_context(self):
        return {
            'total_amount': format_currency(
                self.transaction.payment_rows.filter(payment_type__code=PaymentTypeEnum.BOOKSY_PAY)
                .last()
                .amount
            )
        }

    def get_target(self):
        return PushTarget(type='booking', id=self.appointment.subbookings[0].id)

    @staticmethod
    def should_send_notification(payment_row: PaymentRow) -> bool:
        return (
            payment_row.status == receipt_status.BOOKSY_PAY_SUCCESS
            and payment_row.payment_type.code == PaymentTypeEnum.BOOKSY_PAY
        )
