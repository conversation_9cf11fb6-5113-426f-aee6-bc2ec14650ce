from unittest.mock import patch

import pytest
from django.test import TestCase
from model_bakery import baker

from lib.feature_flag.feature import CancellationFeeFullAmountAuthorizationFlag
from lib.payments.enums import PaymentError
from lib.point_of_sale.enums import (
    BasketPaymentAnalyticsTrigger,
    PaymentMethodType,
)
from lib.tests.utils import override_feature_flag
from webapps.adyen.typing import DeviceDataDict
from webapps.business.models import Business
from webapps.point_of_sale.models import (
    BasketPayment,
    CancellationFeeAuth,
)

from webapps.pos.enums import PaymentTypeEnum, receipt_status, CARD_TYPE__KEYED_IN_PAYMENT
from webapps.pos.models import (
    POS,
    PaymentMethod,
    PaymentRow,
    PaymentType,
    Receipt,
    Transaction,
)
from webapps.pos.provider.proxy import ProxyProvider
from webapps.user.models import User


class ProxyProviderTestCase(TestCase):
    @patch('webapps.pos.provider.proxy.BasketPaymentAnalyticsService.create_analytics_data')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.pos.provider.proxy.BasketPaymentService.capture_payment')
    def test_make_capture(self, capture_mock, get_wallet_mock, analytics_mock):
        get_wallet_mock.return_value = 1
        basket_payment = baker.make(BasketPayment, user_id=1)
        payment_row = baker.make(PaymentRow, basket_payment_id=basket_payment.id)

        ProxyProvider.make_capture(
            payment_row=payment_row,
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            device_data=DeviceDataDict(
                fingerprint='123',
                phone_number='233',
                user_agent='333',
            ),
            extra_data={'cardholder_ip': '22222'},
        )

        self.assertEqual(capture_mock.call_count, 1)
        self.assertEqual(analytics_mock.call_count, 1)

    @patch('webapps.pos.provider.proxy.BasketPaymentAnalyticsService.create_analytics_data')
    @patch('webapps.pos.provider.proxy.BasketPaymentService.authorize_payment')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.pos.provider.proxy.TransactionService.get_fees_and_initialize_payment')
    def test_make_payment(
        self, get_fees_and_initialize_payment_mock, get_wallet_mock, auth_mock, analytics_mock
    ):
        get_wallet_mock.return_value = 1

        pos = baker.make(POS, business=baker.make(Business))
        user = baker.make(User)
        txn = baker.make(Transaction, pos=pos, customer=user)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()

        basket_payment = baker.make(BasketPayment, user_id=user.id)
        payment_row = baker.make(PaymentRow, receipt=receipt, basket_payment_id=basket_payment.id)

        ProxyProvider.make_payment(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=payment_row,
            device_data=DeviceDataDict(
                fingerprint='123',
                phone_number='233',
                user_agent='333',
            ),
            extra_data={'cardholder_ip': '22222'},
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
        )

        self.assertEqual(get_fees_and_initialize_payment_mock.call_count, 1)
        self.assertEqual(get_wallet_mock.call_count, 1)
        self.assertEqual(auth_mock.call_count, 1)
        self.assertEqual(analytics_mock.call_count, 1)

    @patch('webapps.pos.models.Transaction.update_payment_rows')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.pos.provider.proxy.BasketPaymentService.cancel_payment')
    def test_cancel_payment(self, cancel_mock, get_wallet_mock, update_payment_rows_mock):
        get_wallet_mock.return_value = 1

        pos = baker.make(POS)
        txn = baker.make(Transaction, pos=pos)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()

        basket_payment = baker.make(BasketPayment)
        payment_row = baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            basket_payment_id=basket_payment.id,
        )

        ProxyProvider.cancel_payment(payment_row)
        self.assertEqual(cancel_mock.call_count, 1)

        payment_row.status = receipt_status.PAYMENT_FAILED
        payment_row.save()

        ProxyProvider.cancel_payment(payment_row)
        self.assertEqual(update_payment_rows_mock.call_count, 1)
        self.assertEqual(cancel_mock.call_count, 1)

    @patch('webapps.pos.provider.proxy.TransactionService.create_basket_from_cf_auth')
    @patch('webapps.pos.provider.proxy.BasketPaymentAnalyticsService.create_analytics_data')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.pos.provider.proxy.BasketPaymentService.authorize_payment')
    def test_charge_deposit(
        self, auth_mock, customer_wallet_mock, analytics_mock, create_basket_from_cf_auth_mock
    ):
        basket_payment = baker.make(BasketPayment)

        customer_wallet_mock.return_value = 1
        create_basket_from_cf_auth_mock.return_value = basket_payment

        pos = baker.make(POS, business=baker.make(Business))
        txn = baker.make(Transaction, pos=pos, customer=baker.make(User))
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            basket_payment_id=basket_payment.id,
        )

        ProxyProvider().charge_deposit(
            transaction=txn,
            operator=baker.make(User),
            device_data=DeviceDataDict(
                fingerprint='123',
                phone_number='233',
                user_agent='333',
            ),
            register=None,
        )

        self.assertEqual(auth_mock.call_count, 1)
        self.assertEqual(analytics_mock.call_count, 1)

    @patch('webapps.pos.models.PaymentRow.update_status')
    @override_feature_flag({CancellationFeeFullAmountAuthorizationFlag.flag_name: False})
    def test_authorize_deposit_offline(self, update_status_mock):
        pos = baker.make(POS)
        cf_auth = baker.make(CancellationFeeAuth)
        txn = baker.make(Transaction, pos=pos, cancellation_fee_auth_id=cf_auth.id)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()
        basket_payment = baker.make(BasketPayment)
        payment_row = baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            basket_payment_id=basket_payment.id,
            payment_type=baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT),
        )

        with pytest.raises(RuntimeError):
            ProxyProvider.authorize_deposit(
                transaction=txn,
                payment_method=baker.make(PaymentMethod),
                payment_row=payment_row,
            )

        payment_row.payment_type.code = PaymentTypeEnum.PAY_BY_APP
        payment_row.save()

        ProxyProvider.authorize_deposit(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=payment_row,
        )
        self.assertEqual(update_status_mock.call_count, 1)

    @patch('webapps.pos.provider.proxy.CancellationFeeAuthAnalyticsService.create_analytics_data')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.pos.provider.proxy.CancellationFeeAuthService.authorize_cf_auth')
    @override_feature_flag({CancellationFeeFullAmountAuthorizationFlag.flag_name: True})
    def test_authorize_deposit_online(self, auth_cf_mock, business_wallet_mock, analytics_mock):
        business_wallet_mock.return_value = 1
        pos = baker.make(POS, business=baker.make(Business))
        cf_auth = baker.make(CancellationFeeAuth)
        txn = baker.make(
            Transaction,
            pos=pos,
            cancellation_fee_auth_id=cf_auth.id,
            customer=baker.make(User),
        )
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()
        basket_payment = baker.make(BasketPayment, payment_method=PaymentMethodType.CARD)
        payment_row = baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            basket_payment_id=basket_payment.id,
            payment_type=baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP),
        )

        ProxyProvider.authorize_deposit(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=payment_row,
            device_data=DeviceDataDict(
                fingerprint='123',
                phone_number='233',
                user_agent='333',
            ),
            extra_data={'cardholder_ip': '22222'},
        )

        self.assertEqual(auth_cf_mock.call_count, 1)
        self.assertEqual(analytics_mock.call_count, 1)

    @patch('webapps.pos.provider.proxy.CancellationFeeAuthService.cancel_authorization')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    def test_cancel_deposit(self, get_customer_wallet_mock, auth_mock):
        get_customer_wallet_mock.return_value = 1
        cf_auth = baker.make(CancellationFeeAuth)
        pos = baker.make(POS, business=baker.make(Business))
        txn = baker.make(
            Transaction,
            pos=pos,
            cancellation_fee_auth_id=cf_auth.id,
            customer=baker.make(User),
        )
        ProxyProvider.cancel_deposit(txn)

        self.assertEqual(auth_mock.call_count, 1)

    @patch('webapps.pos.provider.proxy.BasketPaymentService.initialize_refund_basket_payment')
    @patch('webapps.pos.models.PaymentRow.update_status')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    @patch('webapps.pos.provider.proxy.txn_refactor_stage2_enabled')
    def test_send_for_refund(
        self,
        txn_stage2_enabled_mock,
        is_refund_possible_mock,
        pr_update_status_mock,
        refund_mock,
    ):
        txn_stage2_enabled_mock.return_value = True
        is_refund_possible_mock.return_value = (False, None)
        pr_update_status_mock.return_value = baker.make(PaymentRow)
        refund_basket_payment = baker.make(BasketPayment)
        refund_mock.return_value = refund_basket_payment

        basket_payment = baker.make(BasketPayment)
        payment_row = baker.make(PaymentRow, basket_payment_id=basket_payment.id)

        with pytest.raises(AssertionError):
            ProxyProvider.send_for_refund(payment_row=payment_row, operator=baker.make(User))

        is_refund_possible_mock.return_value = (True, None)

        refund_row = ProxyProvider.send_for_refund(
            payment_row=payment_row, operator=baker.make(User)
        )

        self.assertEqual(pr_update_status_mock.call_count, 1)
        self.assertEqual(refund_mock.call_count, 1)
        self.assertEqual(refund_row.basket_payment_id, refund_basket_payment.id)

    def test_add_payment_method_err_msg(self):
        basket_payment = baker.make(BasketPayment, error_code=PaymentError.CARD_NOT_SUPPORTED)
        payment_row = baker.make(PaymentRow, basket_payment_id=basket_payment.id)

        error = ProxyProvider.add_payment_method_err_msg(payment_row)

        self.assertEqual(
            error,
            {
                'type': PaymentRow.PAYMENT_ROW__REJECT_REASON,
                'msg': 'Declined. This type of card isn\'t supported. Try another payment method.',
                'error': '',
            },
        )

    @patch('webapps.pos.provider.proxy.BasketPaymentAnalyticsService.create_analytics_data')
    @patch('webapps.pos.provider.proxy.BasketPaymentService.authorize_payment')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.pos.provider.proxy.TransactionService.get_fees_and_initialize_payment')
    def test_make_payment_with_keyed_in_payment(
        self, get_fees_and_initialize_payment_mock, get_wallet_mock, auth_mock, analytics_mock
    ):
        get_wallet_mock.return_value = 1

        pos = baker.make(POS, business=baker.make(Business))
        user = baker.make(User)
        txn = baker.make(Transaction, pos=pos, customer=user)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()

        basket_payment = baker.make(BasketPayment, user_id=user.id)
        payment_row = baker.make(PaymentRow, receipt=receipt, basket_payment_id=basket_payment.id)
        payment_method = baker.make(PaymentMethod, card_type=CARD_TYPE__KEYED_IN_PAYMENT)
        payment_method.token = 'kip_test_payment_token'
        ProxyProvider.make_payment(
            transaction=txn,
            payment_method=payment_method,
            payment_row=payment_row,
            trigger=BasketPaymentAnalyticsTrigger.BUSINESS__PREPAYMENT_AUTH,
        )

        self.assertEqual(get_fees_and_initialize_payment_mock.call_count, 1)
        self.assertEqual(get_wallet_mock.call_count, 0)
        self.assertEqual(auth_mock.call_count, 0)
        self.assertEqual(analytics_mock.call_count, 1)
