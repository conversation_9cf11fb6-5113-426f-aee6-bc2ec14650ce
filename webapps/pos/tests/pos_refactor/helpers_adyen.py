import responses
from django.conf import settings

from webapps.pos.tests.pos_refactor import TestTransactionSerializerBase


class AdyenMixin:
    @staticmethod
    def mock_auth_success():
        responses.add(
            responses.POST,
            settings.ADYEN_AUTH_URL,
            json={
                "pspReference": TestTransactionSerializerBase.generate_id(),
                "resultCode": "Authorised",
                "authCode": "65496",
            },
        )

    @staticmethod
    def mock_capture_success():
        data = {
            "pspReference": TestTransactionSerializerBase.generate_id(),
            "response": "[capture-received]",
        }
        responses.add(responses.POST, settings.ADYEN_CAPTURE_URL, json=data)
