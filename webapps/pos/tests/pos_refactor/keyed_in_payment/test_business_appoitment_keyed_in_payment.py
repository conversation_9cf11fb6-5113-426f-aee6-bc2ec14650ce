# pylint: disable=too-many-lines,too-many-statements
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import patch

from stripe.error import CardError

import pytest
from dateutil.relativedelta import relativedelta
from django.utils.crypto import get_random_string
from freezegun import freeze_time
from model_bakery import baker
from rest_framework.test import APIClient

from lib.feature_flag.feature.payment import FeatureDepositOnBusinessAppointment
from lib.tests.utils import override_eppo_feature_flag
from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import AppointmentStatusChoices
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Resource,
    Service,
    ServiceVariant,
)
from webapps.market_pay.models import AccountHolder
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import StripeAccountHolder
from webapps.pos.enums import (
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
)
from webapps.pos.enums.receipt_status import (
    PREPAYMENT_SUCCESS,
    CALL_FOR_PREPAYMENT,
    PREPAYMENT_FAILED,
)
from webapps.pos.models import (
    POS,
    PaymentType,
    TaxRate,
    POSPlan,
)
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeMixin
from webapps.stripe_app.models import PaymentIntent
from webapps.stripe_integration.models import StripeAccount
from webapps.user.models import User


@pytest.mark.django_db
class TestBusinessAppointmentKeyedInPayment(
    BaseAsyncHTTPTest, StripeMixin
):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()
        self.client = APIClient()
        self.client_secret = get_random_string(12)
        self.payment_intent_stripe_id = 'pi_123456789'

        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            pos_refactor_stage2_enabled=True,
            _force_stripe_pba=True,
            ba_deposit_enabled=True,
        )
        self.pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0.0235,  # not random just to force non-negative value of a fee
            txn_fee=0.1,  # not random just to force non-negative value of a fee
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        baker.make(
            StripeAccountHolder,
            account_holder_id=self.business_wallet.account_holder_id,
        )
        baker.make(StripeAccount, pos=self.business.pos, kyc_verified_at_least_once=True)
        self.pos.pos_plans.add(self.pos_plan)

        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=Decimal('10.00'))
        baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT, pos=self.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.KEYED_IN_PAYMENT, pos=self.pos)

        self.service_1 = baker.make(Service, business=self.business)
        self.service_variant = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )
        self.service_2 = baker.make(Service, business=self.business)
        self.service_variant_2 = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration=relativedelta(minutes=15),
            type=PriceType.VARIES,
        )

        booked_from1 = datetime(2019, 1, 1, 10, 0, tzinfo=timezone.utc)
        self.booking_1 = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + timedelta(minutes=15),
                    'service_variant': self.service_variant,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings[0]
        self.payment_failed_body = {
            'id': 'evt_failed',
            'type': 'payment_intent.payment_failed',
            'data': {
                'object': {
                    'id': self.payment_intent_stripe_id,
                    'status': 'failed',
                    'last_payment_error': {
                        'code': 'card_declined',
                        'message': 'Your card was declined.',
                    },
                }
            },
        }
        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        self.staffers = [
            self.owner,
            baker.make(
                Resource,
                type=Resource.STAFF,
                business=self.business,
                staff_user=baker.make(User),
            ),
        ]

    @override_eppo_feature_flag({FeatureDepositOnBusinessAppointment.flag_name: True})
    @freeze_time(datetime(2024, 10, 1, tzinfo=timezone.utc))
    def test_create_business_appointment_with_deposit_using_keyed_in_payment(self):
        deposit_amount = 30

        # when
        # dry_run to get if deposit available
        appointment_url = f'/business_api/me/businesses/{self.business.id}/appointments/dry_run'

        appointment_post_dry_run_resp = self.fetch(
            appointment_url,
            method='POST',
            body=self._get_appointment_POST_dry_run_body(
                self.owner.id,
                self.service_variant.id,
                self.service_1.id,
                self.service_variant_2.id,
                self.service_2.id,
            ),
        )

        self.assertTrue(appointment_post_dry_run_resp.json['appointment']['is_deposit_available'])

        # create Appointment and Transaction and PaymentIntent and confirm PaymentIntent
        with patch('stripe.PaymentIntent.create') as pi_retrieve_mock:
            pi_retrieve_mock.return_value = PaymentIntent(id=self.payment_intent_stripe_id)
            appointment_url = f'/business_api/me/businesses/{self.business.id}/appointments/'

            appointment_post_resp = self.fetch(
                appointment_url,
                method='POST',
                body=self._get_appointment_POST_body(
                    self.owner.id, self.service_variant.id, self.service_1.id, deposit_amount
                ),
            )
        self.assertEqual(appointment_post_dry_run_resp.code, 201)
        # front starts pooling for Transaction status
        transaction_id = appointment_post_resp.json['appointment']['payment_info']['transaction_id']
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{transaction_id}/last_receipt/"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')
        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], CALL_FOR_PREPAYMENT)

        # handle notification of PaymentIntent.succeeded event
        payment_succeeded_body = self._get_payment_intent_succeeded_notification_body(
            self.payment_intent_stripe_id, self.client_secret
        )
        notification_resp = self._simulate_stripe_incoming_webhook(
            data=payment_succeeded_body, connect=False, expect_new_webhook_handling=True
        )
        self.assertEqual(notification_resp.status_code, 201)

        # request for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}"
            f"/pos/transactions/{transaction_id}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        # # then
        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PREPAYMENT_SUCCESS)
        self.assertEqual(
            resp_last_receipt.json['receipt']['payment_type']['code'],
            PaymentTypeEnum.PREPAYMENT.value,
        )

        appointment = Appointment.objects.get(
            id=appointment_post_resp.json['appointment']['appointment_uid']
        )
        self.assertEqual(appointment.status, AppointmentStatusChoices.ACCEPTED)

        expected_remaining_to_pay_amount = self.service_variant.price - deposit_amount
        self.assertEqual(
            expected_remaining_to_pay_amount,
            float(resp_last_receipt.json['receipt']['remaining_unformatted']),
        )
        self.assertEqual(1, len(resp_last_receipt.json['receipt']['payment_rows']))
        self.assertEqual(
            deposit_amount, resp_last_receipt.json['receipt']['payment_rows'][0]['amount']
        )

    @override_eppo_feature_flag({FeatureDepositOnBusinessAppointment.flag_name: True})
    @freeze_time(datetime(2024, 10, 1, tzinfo=timezone.utc))
    def test_failed_business_appointment_using_keyed_in_payment(self):
        deposit_amount = 30

        # when
        # dry_run to get if deposit available
        appointment_url = f'/business_api/me/businesses/{self.business.id}/appointments/dry_run'
        #
        appointment_post_dry_run_resp = self.fetch(
            appointment_url,
            method='POST',
            body=self._get_appointment_POST_dry_run_body(
                self.owner.id,
                self.service_variant.id,
                self.service_1.id,
                self.service_variant_2.id,
                self.service_2.id,
            ),
        )
        self.assertEqual(
            True, appointment_post_dry_run_resp.json['appointment']['is_deposit_available']
        )

        # create Appointment and Transaction and PaymentIntent
        with patch('stripe.PaymentIntent.create') as pi_create_mock:
            pi_create_mock.side_effect = CardError(
                message="Your card was declined.",
                param=None,
                code="card_declined",
                http_status=402,
                json_body=self._get_payment_intent_fail_notification_body(
                    self.payment_intent_stripe_id, self.client_secret
                ),
            )
            appointment_url = f'/business_api/me/businesses/{self.business.id}/appointments/'

            appointment_post_resp = self.fetch(
                appointment_url,
                method='POST',
                body=self._get_appointment_POST_body(
                    self.owner.id, self.service_variant.id, self.service_1.id, deposit_amount
                ),
            )

        # payment is completed
        # front starts pooling for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{appointment_post_resp.json['appointment']['payment_info']['transaction_id']}"
            f"/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], CALL_FOR_PREPAYMENT)

        notification_resp = self._simulate_stripe_incoming_webhook(
            data=self.payment_failed_body, connect=False, expect_new_webhook_handling=True
        )

        self.assertEqual(notification_resp.status_code, 201)

        # request for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{appointment_post_resp.json['appointment']['payment_info']['transaction_id']}"
            f"/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        # cancel transaction
        cancel_resp = self.fetch(
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{appointment_post_resp.json['appointment']['payment_info']['transaction_id']}/action",
            body={'action': "cancel_payment"},
            method="POST",
        )

        self.assertEqual(200, cancel_resp.code)

        appointment = Appointment.objects.get(
            id=appointment_post_resp.json['appointment']['appointment_uid']
        )

        # # then PREPAYMENT failed and remaining to pay is full amount
        self.assertEqual(resp_last_receipt.json['receipt']['already_paid'], '$0.00')
        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PREPAYMENT_FAILED)
        self.assertEqual(
            resp_last_receipt.json['receipt']['payment_type']['code'],
            PaymentTypeEnum.PREPAYMENT.value,
        )
        self.assertEqual(appointment.status, AppointmentStatusChoices.ACCEPTED)

        expected_remaining_full_amount = self.service_variant.price

        self.assertEqual(
            expected_remaining_full_amount,
            float(resp_last_receipt.json['receipt']['remaining_unformatted']),
        )
        self.assertEqual(1, len(resp_last_receipt.json['receipt']['payment_rows']))
        self.assertEqual(30, resp_last_receipt.json['receipt']['payment_rows'][0]['amount'])

    @override_eppo_feature_flag({FeatureDepositOnBusinessAppointment.flag_name: True})
    @freeze_time(datetime(2024, 10, 1, tzinfo=timezone.utc))
    def test_failed_business_appointment_ba_deposit_not_enabled(self):
        self.pos.ba_deposit_enabled = False
        self.pos.save()
        # when
        # dry_run to get if deposit available
        appointment_url = f'/business_api/me/businesses/{self.business.id}/appointments/dry_run'

        appointment_post_dry_run_resp = self.fetch(
            appointment_url,
            method='POST',
            body=self._get_appointment_POST_dry_run_body(
                self.owner.id,
                self.service_variant.id,
                self.service_1.id,
                self.service_variant_2.id,
                self.service_2.id,
            ),
        )

        self.assertFalse(appointment_post_dry_run_resp.json['appointment']['is_deposit_available'])

    @staticmethod
    def _get_appointment_POST_dry_run_body(
        staffer_id: int,
        service_variant_id: int,
        service_id: int,
        service_variant_2_id: int,
        service_2_id: int,
    ) -> dict:
        return {
            "partner_app_data": {},
            "traveling": None,
            "consent_forms": [],
            "appointment_uid": None,
            "_version": None,
            "customer": {"mode": "walk-in"},
            "business_note": "",
            "business_secret_note": "",
            "subbookings": [
                {
                    "partner_app_data": {},
                    "booked_from": "2025-03-14T12:00",
                    "booked_till": "2025-03-14T13:45",
                    "staffer_id": staffer_id,
                    "autoassigned_staffer_id": None,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant_id, "version": 3, "mode": "variant"},
                    "addons": [],
                    "combo_children": [],
                    "is_staffer_requested_by_client": False,
                    "id": None,
                    "service": {
                        "partner_app_data": {},
                        "id": service_id,
                        "name": "Acrylic nails",
                        "variant": {
                            "id": service_variant_id,
                            "type": "X",
                            "label": None,
                            "price": 35,
                            "duration": 105,
                            "active": True,
                        },
                        "combo_type": None,
                        "category_name": None,
                        "service_category_id": None,
                        "color": 6,
                        "active": True,
                        "note_to_customer": None,
                        "treatment_internal_name": None,
                        "staffer_ids": [staffer_id],
                        "appliance_ids": [],
                        "is_traveling_service": False,
                    },
                    "service_price": "$35.00",
                    "staffer": {
                        "partner_app_data": {},
                        "id": staffer_id,
                        "type": "S",
                        "name": "NoName",
                        "active": True,
                        "visible": True,
                        "description": None,
                        "position": "",
                        "staff_user_exists": True,
                        "is_invited": False,
                    },
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    "is_highlighted": False,
                    "service_promotion": None,
                    "editable": True,
                    "_availability": {
                        "staffers": {
                            "306004": {
                                "free_from": None,
                                "ok": True,
                                "type": "ok",
                                "message": "Dostępny",
                            },
                            "-1": {
                                "free_from": None,
                                "ok": True,
                                "type": "info",
                                "message": "Wymagana ręczna zmiana",
                            },
                        },
                        "appliances": {},
                    },
                    "actions": {
                        "cancel": True,
                        "cancel_no_show": False,
                        "change": True,
                        "change_time_or_note": True,
                        "confirm": False,
                        "decline": False,
                        "no_show": False,
                    },
                },
                {
                    "partner_app_data": {},
                    "booked_from": "2025-03-14T12:00",
                    "booked_till": "2025-03-14T13:45",
                    "staffer_id": staffer_id,
                    "autoassigned_staffer_id": None,
                    "appliance_id": None,
                    "service_variant": {
                        "id": service_variant_2_id,
                        "version": 3,
                        "mode": "variant",
                    },
                    "addons": [],
                    "combo_children": [],
                    "is_staffer_requested_by_client": False,
                    "id": None,
                    "service": {
                        "partner_app_data": {},
                        "id": service_2_id,
                        "name": "Acrylic nails",
                        "variant": {
                            "id": service_variant_2_id,
                            "type": "X",
                            "label": None,
                            "price": 35,
                            "duration": 105,
                            "active": True,
                        },
                        "combo_type": None,
                        "category_name": None,
                        "service_category_id": None,
                        "color": 6,
                        "active": True,
                        "note_to_customer": None,
                        "treatment_internal_name": None,
                        "staffer_ids": [staffer_id],
                        "appliance_ids": [],
                        "is_traveling_service": False,
                    },
                    "service_price": "$35.00",
                    "staffer": {
                        "partner_app_data": {},
                        "id": staffer_id,
                        "type": "S",
                        "name": "NoName",
                        "active": True,
                        "visible": True,
                        "description": None,
                        "position": "",
                        "staff_user_exists": True,
                        "is_invited": False,
                    },
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    "is_highlighted": False,
                    "service_promotion": None,
                    "editable": True,
                    "_availability": {
                        "staffers": {
                            "306004": {
                                "free_from": None,
                                "ok": True,
                                "type": "ok",
                                "message": "Dostępny",
                            },
                            "-1": {
                                "free_from": None,
                                "ok": True,
                                "type": "info",
                                "message": "Wymagana ręczna zmiana",
                            },
                        },
                        "appliances": {},
                    },
                    "actions": {
                        "cancel": True,
                        "cancel_no_show": False,
                        "change": True,
                        "change_time_or_note": True,
                        "confirm": False,
                        "decline": False,
                        "no_show": False,
                    },
                },
            ],
            "appointment_id": None,
            "appointment_type": "single",
            "booked_from": "2025-03-14T12:00",
            "booked_till": "2025-03-14T13:45",
            "status": "A",
            "type": "B",
            "customer_note": None,
            "repeating": None,
            "repeating_series": None,
            "actions": {
                "cancel": True,
                "cancel_no_show": False,
                "change": True,
                "change_time_or_note": True,
                "confirm": False,
                "decline": False,
                "no_show": False,
            },
            "payment_info": {
                "deposit_cancel_time": {"days": 3},
                "payable": True,
                "transaction_id": None,
                "transaction_info": None,
                "handle_deposit": False,
                "auto_release_deposit_on_cancel": None,
                "deposit_id": None,
                "deposit_info": None,
                "booksy_pay": {
                    "is_available": False,
                    "is_payment_window_open": False,
                    "is_paid": False,
                },
            },
            "total": "$36.75",
            "total_value": 36.75,
            "total_discount_amount": 0,
            "total_tax_excluded": 1.75,
            "external_source": "",
            "service_questions": [],
            "join_meeting_url": None,
            "meeting_id": None,
            "from_promo": False,
            "new_repeating": None,
            "_resource_selection_required": False,
            "with_prepayment": False,
            "overbooking": False,
            "_notification_enabled": True,
            "_notify_about_reschedule": False,
            "_preserve_order": False,
        }

    def _get_appointment_POST_body(
        self, staffer_id: int, service_variant_id: int, service_id: int, deposit_amount: float
    ) -> dict:
        return {
            "deposit": {"deposit_amount": deposit_amount},
            "external_payment_method": {"partner": "keyed_in_payment", "token": "test_token_value"},
            "partner_app_data": {},
            "traveling": None,
            "consent_forms": [],
            "appointment_uid": None,
            "_version": None,
            "customer": {"mode": "walk-in"},
            "business_note": "",
            "business_secret_note": "",
            "subbookings": [
                {
                    "partner_app_data": {},
                    "booked_from": "2025-03-14T12:00",
                    "booked_till": "2025-03-14T13:45",
                    "staffer_id": staffer_id,
                    "autoassigned_staffer_id": None,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant_id, "version": 3, "mode": "variant"},
                    "addons": [],
                    "combo_children": [],
                    "is_staffer_requested_by_client": False,
                    "id": None,
                    "service": {
                        "partner_app_data": {},
                        "id": service_id,
                        "name": "Acrylic nails",
                        "variant": {
                            "id": service_variant_id,
                            "type": "X",
                            "label": None,
                            "price": 35,
                            "duration": 105,
                            "active": True,
                        },
                        "combo_type": None,
                        "category_name": None,
                        "service_category_id": None,
                        "color": 6,
                        "active": True,
                        "note_to_customer": None,
                        "treatment_internal_name": None,
                        "staffer_ids": [staffer_id],
                        "appliance_ids": [],
                        "is_traveling_service": False,
                    },
                    "service_price": "$35.00",
                    "staffer": {
                        "partner_app_data": {},
                        "id": staffer_id,
                        "type": "S",
                        "name": "NoName",
                        "active": True,
                        "visible": True,
                        "description": None,
                        "position": "",
                        "staff_user_exists": True,
                        "is_invited": False,
                    },
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    "is_highlighted": False,
                    "service_promotion": None,
                    "editable": True,
                    "_availability": {
                        "staffers": {
                            "306004": {
                                "free_from": None,
                                "ok": True,
                                "type": "ok",
                                "message": "Dostępny",
                            },
                            "-1": {
                                "free_from": None,
                                "ok": True,
                                "type": "info",
                                "message": "Wymagana ręczna zmiana",
                            },
                        },
                        "appliances": {},
                    },
                    "actions": {
                        "cancel": True,
                        "cancel_no_show": False,
                        "change": True,
                        "change_time_or_note": True,
                        "confirm": False,
                        "decline": False,
                        "no_show": False,
                    },
                },
                {
                    "partner_app_data": {},
                    "booked_from": "2025-03-14T12:00",
                    "booked_till": "2025-03-14T13:45",
                    "staffer_id": staffer_id,
                    "autoassigned_staffer_id": None,
                    "appliance_id": None,
                    "service_variant": {
                        "id": self.service_variant_2.id,
                        "version": 3,
                        "mode": "variant",
                    },
                    "addons": [],
                    "combo_children": [],
                    "is_staffer_requested_by_client": False,
                    "id": None,
                    "service": {
                        "partner_app_data": {},
                        "id": self.service_2.id,
                        "name": "Acrylic nails",
                        "variant": {
                            "id": self.service_variant_2.id,
                            "type": "X",
                            "label": None,
                            "price": 35,
                            "duration": 105,
                            "active": True,
                        },
                        "combo_type": None,
                        "category_name": None,
                        "service_category_id": None,
                        "color": 6,
                        "active": True,
                        "note_to_customer": None,
                        "treatment_internal_name": None,
                        "staffer_ids": [staffer_id],
                        "appliance_ids": [],
                        "is_traveling_service": False,
                    },
                    "service_price": "$35.00",
                    "staffer": {
                        "partner_app_data": {},
                        "id": staffer_id,
                        "type": "S",
                        "name": "NoName",
                        "active": True,
                        "visible": True,
                        "description": None,
                        "position": "",
                        "staff_user_exists": True,
                        "is_invited": False,
                    },
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    "is_highlighted": False,
                    "service_promotion": None,
                    "editable": True,
                    "_availability": {
                        "staffers": {
                            "306004": {
                                "free_from": None,
                                "ok": True,
                                "type": "ok",
                                "message": "Dostępny",
                            },
                            "-1": {
                                "free_from": None,
                                "ok": True,
                                "type": "info",
                                "message": "Wymagana ręczna zmiana",
                            },
                        },
                        "appliances": {},
                    },
                    "actions": {
                        "cancel": True,
                        "cancel_no_show": False,
                        "change": True,
                        "change_time_or_note": True,
                        "confirm": False,
                        "decline": False,
                        "no_show": False,
                    },
                },
            ],
            "appointment_id": None,
            "appointment_type": "single",
            "booked_from": "2025-03-14T12:00",
            "booked_till": "2025-03-14T13:45",
            "status": "A",
            "type": "B",
            "customer_note": None,
            "repeating": None,
            "repeating_series": None,
            "actions": {
                "cancel": True,
                "cancel_no_show": False,
                "change": True,
                "change_time_or_note": True,
                "confirm": False,
                "decline": False,
                "no_show": False,
            },
            "payment_info": {
                "deposit_cancel_time": {"days": 3},
                "payable": True,
                "transaction_id": None,
                "transaction_info": None,
                "handle_deposit": False,
                "auto_release_deposit_on_cancel": None,
                "deposit_id": None,
                "deposit_info": None,
                "booksy_pay": {
                    "is_available": False,
                    "is_payment_window_open": False,
                    "is_paid": False,
                },
            },
            "total": "$36.75",
            "total_value": 36.75,
            "total_discount_amount": 0,
            "total_tax_excluded": 1.75,
            "external_source": "",
            "service_questions": [],
            "join_meeting_url": None,
            "meeting_id": None,
            "from_promo": False,
            "new_repeating": None,
            "_resource_selection_required": False,
            "with_prepayment": False,
            "overbooking": False,
            "_notification_enabled": True,
            "_notify_about_reschedule": False,
            "_preserve_order": False,
        }

    @staticmethod
    def _get_payment_intent_succeeded_notification_body(
        payment_intent_id: str, client_secret: str
    ) -> dict:
        return {
            "id": "evt_3Quu0vJRQdKvDXrk17dMYE3F",
            "object": "event",
            "api_version": "2020-08-27",
            "created": 1740135808,
            "data": {
                "object": {
                    "id": payment_intent_id,
                    "object": "payment_intent",
                    "amount": 1310,
                    "amount_capturable": 0,
                    "amount_details": {"tip": {}},
                    "amount_received": 1310,
                    "application": None,
                    "application_fee_amount": 1410,
                    "automatic_payment_methods": None,
                    "canceled_at": None,
                    "cancellation_reason": None,
                    "capture_method": "automatic_async",
                    "client_secret": client_secret,
                    "confirmation_method": "automatic",
                    "created": 1740135805,
                    "currency": "usd",
                    "customer": None,
                    "description": None,
                    "invoice": None,
                    "last_payment_error": None,
                    "latest_charge": "ch_3Quu0vJRQdKvDXrk1XgaH3NH",
                    "livemode": False,
                    "metadata": {},
                    "next_action": None,
                    "on_behalf_of": None,
                    "payment_method": "pm_1Quu0xJRQdKvDXrkGxklgXuC",
                    "payment_method_configuration_details": None,
                    "payment_method_options": {
                        "card": {
                            "installments": None,
                            "mandate_options": None,
                            "network": None,
                            "request_three_d_secure": "automatic",
                        }
                    },
                    "payment_method_types": ["card"],
                    "processing": None,
                    "receipt_email": None,
                    "review": None,
                    "setup_future_usage": "off_session",
                    "shipping": None,
                    "source": None,
                    "statement_descriptor": None,
                    "statement_descriptor_suffix": None,
                    "status": "succeeded",
                    "transfer_data": {"destination": "acct_1QOOD6R6bIludG1R"},
                    "transfer_group": "group_pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                    "charges": {
                        "object": "list",
                        "data": [
                            {
                                "id": "ch_3Quu0vJRQdKvDXrk1XgaH3NH",
                                "object": "charge",
                                "amount": 1310,
                                "amount_captured": 1310,
                                "amount_refunded": 0,
                                "application": None,
                                "application_fee": None,
                                "application_fee_amount": 1410,
                                "balance_transaction": None,
                                "billing_details": {
                                    "address": {
                                        "city": None,
                                        "country": None,
                                        "line1": None,
                                        "line2": None,
                                        "postal_code": "60651",
                                        "state": None,
                                    },
                                    "email": None,
                                    "name": None,
                                    "phone": None,
                                },
                                "calculated_statement_descriptor": "Stripe",
                                "captured": True,
                                "created": 1740135807,
                                "currency": "usd",
                                "customer": None,
                                "description": None,
                                "destination": "acct_1QOOD6R6bIludG1R",
                                "dispute": None,
                                "disputed": False,
                                "failure_balance_transaction": None,
                                "failure_code": None,
                                "failure_message": None,
                                "fraud_details": {},
                                "invoice": None,
                                "livemode": False,
                                "metadata": {
                                    "provider_request_id": "5c20eb1e-545d-4c84-90c4-4ef63424b563"
                                },
                                "on_behalf_of": None,
                                "order": None,
                                "outcome": {
                                    "advice_code": None,
                                    "network_advice_code": None,
                                    "network_decline_code": None,
                                    "network_status": "approved_by_network",
                                    "reason": None,
                                    "risk_level": "normal",
                                    "risk_score": 8,
                                    "seller_message": "Payment complete.",
                                    "type": "authorized",
                                },
                                "paid": True,
                                "payment_intent": "pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                                "payment_method": "pm_1Quu0xJRQdKvDXrkGxklgXuC",
                                "payment_method_details": {
                                    "card": {
                                        "amount_authorized": 1310,
                                        "authorization_code": None,
                                        "brand": "visa",
                                        "checks": {
                                            "address_line1_check": None,
                                            "address_postal_code_check": "pass",
                                            "cvc_check": "pass",
                                        },
                                        "country": "US",
                                        "exp_month": 3,
                                        "exp_year": 2030,
                                        "extended_authorization": {"status": "disabled"},
                                        "fingerprint": "7KvhjvGGknrYO07H",
                                        "funding": "credit",
                                        "incremental_authorization": {"status": "unavailable"},
                                        "installments": None,
                                        "last4": "4242",
                                        "mandate": None,
                                        "multicapture": {"status": "unavailable"},
                                        "network": "visa",
                                        "network_token": {"used": False},
                                        "network_transaction_id": "557511810410611",
                                        "overcapture": {
                                            "maximum_amount_capturable": 1310,
                                            "status": "unavailable",
                                        },
                                        "regulated_status": "unregulated",
                                        "three_d_secure": None,
                                        "wallet": None,
                                    },
                                    "type": "card",
                                },
                                "radar_options": {},
                                "receipt_email": None,
                                "receipt_number": None,
                                "receipt_url": "https://pay.stripe.com/receipts/payment/"
                                "CAcaFwoVYWNjdF8xUTZDandKUlFkS3ZEWHJrKIC74b0GM"
                                "gZm8vev7Zg6LBa6bM93Zq6WCJbbbdmNSl64HNbNN1Opxtot"
                                "wBctL0Bf1B6xKQOex_KorM4g",
                                "refunded": False,
                                "refunds": {
                                    "object": "list",
                                    "data": [],
                                    "has_more": False,
                                    "total_count": 0,
                                    "url": "/v1/charges/ch_3Quu0vJRQdKvDXrk1XgaH3NH/refunds",
                                },
                                "review": None,
                                "shipping": None,
                                "source": None,
                                "source_transfer": None,
                                "statement_descriptor": None,
                                "statement_descriptor_suffix": None,
                                "status": "succeeded",
                                "transfer_data": {
                                    "amount": None,
                                    "destination": "acct_1QOOD6R6bIludG1R",
                                },
                                "transfer_group": "group_pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                            }
                        ],
                        "has_more": False,
                        "total_count": 1,
                        "url": "/v1/charges?payment_intent=pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                    },
                }
            },
            "livemode": False,
            "pending_webhooks": 2,
            "request": {
                "id": "req_TxmUIjNtmOyHl2",
                "idempotency_key": "c25fa0e8-76d5-414f-b571-fa4d25d4f859",
            },
            "type": "payment_intent.succeeded",
        }

    @staticmethod
    def _get_payment_intent_fail_notification_body(
        payment_intent_id: str, client_secret: str
    ) -> dict:
        return {
            "error": {
                "message": "Your card has insufficient funds.",
                "type": "card_error",
                "code": "card_declined",
                "decline_code": "insufficient_funds",
                "advice_code": "try_again_later",
                "doc_url": "https://stripe.com/docs/error-codes/card-declined",
                "charge": "ch_3R511KJRQdKvDXrk2MkroAz8",
                "payment_intent": {
                    "id": payment_intent_id,
                    "object": "payment_intent",
                    "amount": 30,
                    "currency": "usd",
                    "status": "requires_payment_method",
                    "client_secret": client_secret,
                    "created": **********,
                    "livemode": False,
                    "metadata": {"provider_request_id": "0b882614-a324-4027-818b-1250479d967b"},
                    "last_payment_error": {
                        "advice_code": "try_again_later",
                        "charge": "ch_3R511KJRQdKvDXrk2MkroAz8",
                        "code": "card_declined",
                        "decline_code": "insufficient_funds",
                        "message": "Your card has insufficient funds.",
                        "payment_method": {
                            "id": "pm_1R511JJRQdKvDXrkokGrbq6P",
                            "object": "payment_method",
                            "type": "card",
                        },
                        "type": "card_error",
                    },
                    "latest_charge": "ch_3R511KJRQdKvDXrk2MkroAz8",
                },
                "payment_method": {
                    "id": "pm_1R511JJRQdKvDXrkokGrbq6P",
                    "object": "payment_method",
                    "type": "card",
                },
            }
        }
