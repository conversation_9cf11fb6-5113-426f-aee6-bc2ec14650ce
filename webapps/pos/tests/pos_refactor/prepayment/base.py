from django.conf import settings
from model_bakery import baker

from lib.payment_gateway.enums import PaymentStatus
from lib.payments.enums import PaymentError, PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketPaymentAnalyticsTrigger,
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketTipSource,
    BasketTipType,
)
from lib.point_of_sale.enums import PaymentMethodType as PointOfSalePaymentMethodType
from lib.tools import (
    major_unit,
    minor_unit,
)
from service.booking.tools import AppointmentData
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_providers.consts.stripe import PaymentIntentStripeStatus
from webapps.payment_providers.models import Payment
from webapps.point_of_sale.models import (
    Basket,
    BasketItem,
    BasketPayment,
    BasketPaymentAnalytics,
    BasketTip,
)
from webapps.pos.enums import (
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    PaymentRow,
    PaymentType,
)
from webapps.pos.serializers import CustomerAppointmentTransactionSerializer
from webapps.pos.tests.pos_refactor import TestTransactionSerializerBase
from webapps.pos.tip_calculations import SimpleTip


# pylint: disable=too-many-statements
class TestTransactionSerializerPrepaymentBase(TestTransactionSerializerBase):
    def setUp(self):
        super().setUp()
        self.prepayment = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.PREPAYMENT,
        )
        self.pba = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.SPLIT)

        self.sv = baker.make(
            ServiceVariant,
            service=baker.make(Service, business=self.business),
            price=987.56,
            type=PriceType.FIXED,
            duration='0100',
            time_slot_interval='0000',
            gap_hole_duration='0000',
            gap_hole_start_after='0000',
        )

        self.svp = baker.make(
            ServiceVariantPayment,
            service_variant=self.sv,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=432.12,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        self.appointment = create_appointment(
            [
                {
                    'service_variant_id': self.sv.id,
                }
            ],
            business=self.business,
            booked_for=self.bci,
        )

        self.basket_payment_source = BasketPaymentSource.PREPAYMENT

    def create_prepayment_via_serializer(self):
        appointment = AppointmentWrapper(self.appointment.subbookings)
        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'payment_method': self.payment_method.id,
            },
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.bci.user,
                'business': self.business,
                'pos': self.pos,
                'appointment_checkout': appointment.checkout,
                'appointment_data': AppointmentData.build(appointment),
                'compatibilities': self.compatibilities,
                **self.device_data_context,
            },
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()

        return self.appointment.transactions.first()

    def make_payment(self, transaction, payment_method, payment_row):
        self.provider.make_payment(
            transaction=transaction,
            payment_method=payment_method,
            payment_row=payment_row,
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            **self.device_data_dict,
        )

    def check_payment(
        self,
        payment_row: PaymentRow,
        payment_status: PaymentStatus,
        is_payment_intent_created: bool = True,
        payment_intent_status=None,
    ):
        basket_payment = BasketPayment.objects.filter(id=payment_row.basket_payment_id).last()
        balance_transaction = BalanceTransaction.objects.get(
            id=basket_payment.balance_transaction_id,
        )
        payment = Payment.objects.get(id=balance_transaction.external_id)
        self.assertEqual(payment.status, payment_status)
        self.assertEqual(hasattr(payment, 'stripe_payment'), is_payment_intent_created)
        if payment_intent_status:
            self.assertEqual(payment.stripe_payment.stripe_status, payment_intent_status)
        return payment

    def _check_prepayment_cfp(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.CALL_FOR_PREPAYMENT)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.PAYMENT,
            balance_transaction_existing=False,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 0)

        # BasketPaymentAnalytics
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 0)

    def _check_prepayment_auth_semi_auth(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(
            txn.payment_rows[0].status, receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.PAYMENT,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZED,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_auth_semi_auth_update_with_booking(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 1)

        receipt_statuses = [
            receipt_status.ARCHIVED,
            receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            receipt_status.CALL_FOR_PREPAYMENT,
        ]
        self.assertEqual([x.status_code for x in txn.receipts.all()], receipt_statuses)
        self.assertEqual(
            txn2.receipts.get().status_code, receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
        )

        # Basket
        self.assertEqual(Basket.objects.count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)
        self.assertEqual(BasketItem.objects.count(), 1)
        self.assertEqual(BasketItem.all_objects.count(), 2)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.ARCHIVED)
        self.assertEqual(
            txn2.payment_rows[0].status, receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.PAYMENT,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZED,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        self.assertNotEqual(txn.payment_rows[0].basket_payment_id, None)
        self.assertEqual(
            txn.payment_rows[0].basket_payment_id, txn2.payment_rows[0].basket_payment_id
        )

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_auth_semi_auto_checkout_prepaid_task(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_auth_success(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(
            txn.payment_rows[0].status, receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            balance_transaction_related_obj_status=PaymentStatus.SENT_FOR_CAPTURE,  # auto-capture
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)
        self.assertFalse(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            )
            .first()
            .payment_link
        )

    def _check_prepayment_auth_3ds(self, txn, tds_data):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.CALL_FOR_PREPAYMENT_3DS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.ACTION_REQUIRED,
            balance_transaction_related_obj_status=PaymentStatus.ACTION_REQUIRED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            action_required_details=tds_data,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_auth_3ds_fls1838(self, txn, tds_data):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_AUTHORISATION_FAILED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZATION_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            action_required_details=tds_data,
            error_code=PaymentError.CARD_NOT_SUPPORTED,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_success(self, txn):
        txn.refresh_from_db()

        print([x.status_code for x in txn.receipts.all()])

        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)
        self.assertFalse(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            )
            .first()
            .payment_link
        )

    def _check_prepayment_success_update_transaction_with_booking(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()
        self.assertEqual(txn.receipts.all().count(), 4)
        self.assertEqual(txn2.receipts.all().count(), 1)
        receipt_statuses = [
            receipt_status.ARCHIVED,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            receipt_status.CALL_FOR_PREPAYMENT,
        ]
        self.assertEqual([x.status_code for x in txn.receipts.all()], receipt_statuses)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)

        # Basket
        self.assertEqual(Basket.objects.count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)
        self.assertEqual(BasketItem.objects.count(), 1)
        self.assertEqual(BasketItem.all_objects.count(), 2)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.ARCHIVED)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.assertNotEqual(txn.payment_rows[0].basket_payment_id, None)
        self.assertEqual(
            txn.payment_rows[0].basket_payment_id, txn2.payment_rows[0].basket_payment_id
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_success_update_transaction_with_booking_close_txn(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()
        self.assertEqual(txn.receipts.all().count(), 4)
        self.assertEqual(txn2.receipts.all().count(), 2)
        txn_receipt_statuses = [
            receipt_status.ARCHIVED,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            receipt_status.CALL_FOR_PREPAYMENT,
        ]
        txn2_receipt_statuses = [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_SUCCESS,
        ]
        self.assertEqual([x.status_code for x in txn.receipts.all()], txn_receipt_statuses)
        self.assertEqual([x.status_code for x in txn2.receipts.all()], txn2_receipt_statuses)

        # Basket
        self.assertEqual(Basket.objects.count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)
        self.assertEqual(BasketItem.objects.count(), 1)
        self.assertEqual(BasketItem.all_objects.count(), 2)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertNotEqual(txn.payment_rows[0].basket_payment_id, None)
        self.assertEqual(
            txn.payment_rows[0].basket_payment_id, txn2.payment_rows[0].basket_payment_id
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_success_blik(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.BLIK,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)
        self.assertFalse(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            )
            .first()
            .payment_link
        )

    def _check_prepayment_success_google_pay(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.GOOGLE_PAY,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)
        self.assertFalse(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            )
            .first()
            .payment_link
        )

    def _check_prepayment_success_apple_pay(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.APPLE_PAY,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)
        self.assertFalse(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            )
            .first()
            .payment_link
        )

    def _check_prepayment_success_with_fee(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            fee_amount=minor_unit(85.82),
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_success_semiauto(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__PREPAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_semiauto_error_during_capture(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_FAILED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURE_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=PaymentError.NOT_PERMITTED,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__PREPAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_semiauto_cancel_webhook(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, AppointmentStatus.ACCEPTED)  # remains untouched

    def _check_prepayment_semiauto_sent_for_capture_cancel_webhook(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__PREPAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, AppointmentStatus.ACCEPTED)  # remains untouched

    def _check_prepayment_success_3ds(self, txn, tds_data):
        # Basket
        txn.refresh_from_db()
        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            action_required_details=tds_data,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_blik_fail(
        self,
        txn,
    ):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_AUTHORISATION_FAILED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.BLIK,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZATION_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=PaymentError.INVALID_BLIK_CODE.value,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_auth_fail(
        self,
        txn,
        error_code,
        payment_method=PointOfSalePaymentMethodType.CARD,
    ):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_AUTHORISATION_FAILED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZATION_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=error_code,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_fail(self, txn):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_FAILED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURE_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=PaymentError.INCORRECT_CVC,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)
        self.assertFalse(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            )
            .first()
            .payment_link
        )

    def _check_prepayment_3ds_fail(self, txn, tds_data, error_code=PaymentError.INCORRECT_CVC):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_AUTHORISATION_FAILED)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZATION_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=error_code,
            action_required_details=tds_data,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_3ds_capture_fail(self, txn, tds_data):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_FAILED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURE_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=PaymentError.INCORRECT_CVC,
            action_required_details=tds_data,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_send_for_refund(self, txn, success_row):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.SENT_FOR_REFUND)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 2)  # payment + refund

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_refund(
        self,
        txn,
        success_row,
        receipts_count=5,
        payment_method=PointOfSalePaymentMethodType.CARD,
    ):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), receipts_count)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.REFUNDED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_refund_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_refund_with_fee(self, txn, success_row):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 5)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            fee_amount=minor_unit(85.82),
        )

        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.REFUNDED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            fee_amount=minor_unit(65.21),
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_refund_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_refund_fail(self, txn, success_row):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 5)

        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PREPAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_refund_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_chargeback(self, txn, success_row):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        # Chargeback row
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_chargeback_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_chargeback_with_fees(self, txn, success_row):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            fee_amount=minor_unit(85.82),
        )

        # Chargeback row
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
            fee_amount=minor_unit(65.21),
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_chargeback_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_chargeback_reversed(self, txn, success_row, chargeback_row):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 5)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        # Chargeback row
        self.assertEqual(chargeback_row.status, receipt_status.CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=chargeback_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )

        # Chargeback reversed row
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.CHARGEBACK_REVERSED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK_REVERSED,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self._check_chargeback_balance_transactions(chargeback_reversed=True)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_second_chargeback(
        self, txn, success_row, chargeback_row, chargeback_reversed_row
    ):
        txn.refresh_from_db()
        # Basket
        self.assertEqual(txn.receipts.all().count(), 6)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PREPAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        # Chargeback row
        self.assertEqual(chargeback_row.status, receipt_status.CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=chargeback_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )

        # Chargeback reversed row
        self.assertEqual(chargeback_reversed_row.status, receipt_status.CHARGEBACK_REVERSED)
        self.check_basket_payment(
            basket=basket,
            payment_row=chargeback_reversed_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK_REVERSED,
            balance_transaction_existing=True,
        )

        # Second chargeback row
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.SECOND_CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.SECOND_CHARGEBACK,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 4)
        self._check_chargeback_balance_transactions(
            chargeback_reversed=True,
            second_chargeback=True,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_success_100(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        print([x.status_code for x in txn.receipts.all()])
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 1)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=False
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_prepayment_success_cash(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()
        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 1)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
            balance_transaction_existing=False,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_success_pba_cfp(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()
        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 1)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.CALL_FOR_PAYMENT)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.PAYMENT,
            balance_transaction_existing=False,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_success_pba_success(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 2)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_prepayment_success_pba_send_for_refund(self, txn, txn2, success_row):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 3)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.SENT_FOR_REFUND)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 3)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_prepayment_success_pba_refunded(self, txn, txn2, success_row):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 4)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.REFUNDED)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 3)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_prepayment_success_pba_refund_fail(self, txn, txn2, success_row):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 4)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 3)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_prepayment_success_pba_success_double_send_for_refund(
        self, txn, txn2, pba_success_row, prepayment_success_row
    ):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 4)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.SENT_FOR_REFUND)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.SENT_FOR_REFUND)

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 4)

        self.check_basket_payment(
            basket=basket,
            payment_row=pba_success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=prepayment_success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 4)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 4)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_prepayment_success_pba_success_send_for_reund_refund(
        self,
        txn,
        txn2,
        pba_success_row,
        prepayment_success_row,
    ):
        txn.refresh_from_db()
        txn2.refresh_from_db()
        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 5)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.SENT_FOR_REFUND)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.REFUNDED)

        self.check_basket_payment(
            basket=basket,
            payment_row=pba_success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=prepayment_success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 4)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 4)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_prepayment_success_pba_success_double_refund(
        self, txn, txn2, pba_success_row, prepayment_success_row
    ):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 6)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.REFUNDED)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.REFUNDED)

        self.check_basket_payment(
            basket=basket,
            payment_row=pba_success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=prepayment_success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 4)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 4)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_prepayment_success_bcr_pending(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 1)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PENDING)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            balance_transaction_related_obj_status=PaymentStatus.NEW,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[1],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 55544)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[1].amount))

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_success_bcr_success(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 2)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[1],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 55544)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[1].amount))

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_success_with_tip_bcr_success(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 2)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[1],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 55544)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[1].amount))

        # BasketTip
        basket_tip = BasketTip.objects.filter(basket=basket)
        self.assertEqual(len(basket_tip), 1)
        self.assertEqual(BasketTip.all_objects.filter(basket=basket).count(), 1)

        self.assertEqual(basket_tip[0].rate, None)
        self.assertEqual(basket_tip[0].amount, minor_unit(10))
        self.assertEqual(basket_tip[0].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tip[0].staffer_id, self.staffer.id)
        self.assertEqual(basket_tip[0].source, BasketTipSource.PREPAYMENT)

        self.assertEqual(minor_unit(txn2.tip.amount), basket_tip[0].amount)
        self.assertEqual(txn2.tip.type, SimpleTip.TIP_TYPE__HAND)
        self.assertEqual(txn2.tip.rate, major_unit(basket_tip[0].amount))
        self.assertEqual(txn2.tip.basket_tip_id, None)  # There was no new tip created

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_success_bcr_success_with_tip(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 2)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[1],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 57756)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[1].amount))

        # BasketTip
        basket_tip = BasketTip.objects.filter(basket=basket)
        self.assertEqual(len(basket_tip), 1)
        self.assertEqual(BasketTip.all_objects.filter(basket=basket).count(), 1)

        self.assertEqual(basket_tip[0].rate, None)
        self.assertEqual(basket_tip[0].amount, minor_unit(22.12))
        self.assertEqual(basket_tip[0].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tip[0].staffer_id, self.staffer.id)
        self.assertEqual(basket_tip[0].source, BasketTipSource.BCR)

        self.assertEqual(minor_unit(txn2.tip.amount), basket_tip[0].amount)
        self.assertEqual(txn2.tip.type, SimpleTip.TIP_TYPE__HAND)
        self.assertEqual(txn2.tip.rate, major_unit(basket_tip[0].amount))
        self.assertEqual(txn2.tip.basket_tip_id, basket_tip[0].id)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_success_with_tip_bcr_success_with_tip(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 2)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[1],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 57756)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[1].amount))

        # BasketTip
        basket_tip = BasketTip.objects.filter(basket=basket).order_by('created')
        self.assertEqual(len(basket_tip), 2)
        self.assertEqual(BasketTip.all_objects.filter(basket=basket).count(), 2)

        self.assertEqual(basket_tip[0].rate, None)
        self.assertEqual(basket_tip[0].amount, minor_unit(10))
        self.assertEqual(basket_tip[0].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tip[0].staffer_id, self.staffer.id)
        self.assertEqual(basket_tip[0].source, BasketTipSource.PREPAYMENT)

        self.assertEqual(basket_tip[1].rate, None)
        self.assertEqual(basket_tip[1].amount, minor_unit(22.12))
        self.assertEqual(basket_tip[1].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tip[1].staffer_id, self.staffer.id)
        self.assertEqual(basket_tip[1].source, BasketTipSource.BCR)

        self.assertEqual(minor_unit(txn2.tip.amount), basket_tip[1].amount + basket_tip[0].amount)
        self.assertEqual(txn2.tip.type, SimpleTip.TIP_TYPE__HAND)
        self.assertEqual(txn2.tip.rate, major_unit(basket_tip[0].amount + basket_tip[1].amount))
        self.assertEqual(txn2.tip.basket_tip_id, basket_tip[1].id)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_success_with_tip_percent_bcr_success_with_tip(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 2)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 2)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[1],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 57756)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[1].amount))

        # BasketTip
        basket_tip = BasketTip.objects.filter(basket=basket).order_by('created')
        self.assertEqual(len(basket_tip), 2)
        self.assertEqual(BasketTip.all_objects.filter(basket=basket).count(), 2)

        self.assertEqual(basket_tip[0].rate, None)
        self.assertEqual(basket_tip[0].amount, minor_unit(98.76))
        self.assertEqual(basket_tip[0].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tip[0].staffer_id, self.staffer.id)
        self.assertEqual(basket_tip[0].source, BasketTipSource.PREPAYMENT)

        self.assertEqual(basket_tip[1].rate, None)
        self.assertEqual(basket_tip[1].amount, minor_unit(22.12))
        self.assertEqual(basket_tip[1].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tip[1].staffer_id, self.staffer.id)
        self.assertEqual(basket_tip[1].source, BasketTipSource.BCR)

        self.assertEqual(minor_unit(txn2.tip.amount), basket_tip[1].amount + basket_tip[0].amount)
        self.assertEqual(txn2.tip.type, SimpleTip.TIP_TYPE__HAND)
        self.assertEqual(txn2.tip.rate, major_unit(basket_tip[0].amount + basket_tip[1].amount))
        self.assertEqual(txn2.tip.basket_tip_id, basket_tip[1].id)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_prepayment_checkout_customer_app_bcr_tip(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 3)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[2].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[2],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 3)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[2],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 57756)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[2].amount))

        # BasketTip
        basket_tips = BasketTip.objects.filter(basket=basket)
        self.assertEqual(len(basket_tips), 3)
        self.assertEqual(BasketTip.all_objects.filter(basket=basket).count(), 4)

        self.assertEqual(basket_tips[0].rate, None)
        self.assertEqual(basket_tips[0].amount, minor_unit(98.76))
        self.assertEqual(basket_tips[0].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[0].staffer_id, self.staffer.id)
        self.assertEqual(basket_tips[0].source, BasketTipSource.PREPAYMENT)

        self.assertEqual(basket_tips[1].rate, None)
        self.assertEqual(
            basket_tips[1].amount, minor_unit(395.02)
        )  # 493.78 - basket_tips[0].amount
        self.assertEqual(basket_tips[1].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[1].staffer_id, self.staffer.id)
        self.assertEqual(basket_tips[1].source, BasketTipSource.CUSTOMER_APP)

        self.assertEqual(basket_tips[2].rate, None)
        self.assertEqual(basket_tips[2].amount, minor_unit(22.12))
        self.assertEqual(basket_tips[2].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[2].staffer_id, self.staffer.id)
        self.assertEqual(basket_tips[2].source, BasketTipSource.BCR)

        summed_amount = sum(tip.amount for tip in basket_tips)
        self.assertEqual(minor_unit(txn2.tip.amount), summed_amount)
        self.assertEqual(txn2.tip.type, SimpleTip.TIP_TYPE__HAND)
        self.assertEqual(txn2.tip.rate, major_unit(summed_amount))
        self.assertEqual(txn2.tip.basket_tip_id, basket_tips[2].id)

    def _check_prepayment_checkout_customer_app_bcr_tip_tip_rows(self, txn, txn2):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        # Basket
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 3)
        self.assertEqual(txn2.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        basket_item_1 = self.get_and_check_basket_item(txn)
        basket_item_2 = self.get_and_check_basket_item(txn2)
        self.compare_basket_item(
            basket_item=basket_item_1, basket_item2=basket_item_2, same_price=True
        )
        self.assertEqual(BasketItem.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BasketItem.all_objects.filter(basket=basket).count(), 2)

        # BasketPayment
        self.assertEqual(len(txn2.payment_rows), 3)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn2.payment_rows[2].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[2],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            source=BasketPaymentSource.PAYMENT,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 3)

        payment = self.check_payment(
            payment_row=txn2.payment_rows[2],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 57756)
        self.assertEqual(payment.amount, minor_unit(txn2.payment_rows[2].amount))

        # BasketTip
        basket_tips = BasketTip.objects.filter(basket=basket)
        self.assertEqual(len(basket_tips), 5)
        self.assertEqual(BasketTip.all_objects.filter(basket=basket).count(), 8)

        self.assertEqual(basket_tips[0].rate, None)
        self.assertEqual(basket_tips[0].amount, minor_unit(19.75))  # 0.2 * 98.76
        self.assertEqual(basket_tips[0].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[0].staffer_id, self.staffer.id)
        self.assertEqual(basket_tips[0].source, BasketTipSource.PREPAYMENT)

        self.assertEqual(basket_tips[1].rate, None)
        self.assertEqual(basket_tips[1].amount, minor_unit(79.01))  # 0.8 * 98.76
        self.assertEqual(basket_tips[1].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[1].staffer_id, self.staffer2.id)
        self.assertEqual(basket_tips[1].source, BasketTipSource.PREPAYMENT)

        self.assertEqual(basket_tips[2].rate, None)
        self.assertEqual(basket_tips[2].amount, minor_unit(79))  # 0.2 * 395.02
        self.assertEqual(basket_tips[2].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[2].staffer_id, self.staffer.id)
        self.assertEqual(basket_tips[2].source, BasketTipSource.CUSTOMER_APP)

        self.assertEqual(basket_tips[3].rate, None)
        self.assertEqual(basket_tips[3].amount, minor_unit(316.02))
        self.assertEqual(basket_tips[3].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[3].staffer_id, self.staffer2.id)
        self.assertEqual(basket_tips[3].source, BasketTipSource.CUSTOMER_APP)

        self.assertEqual(basket_tips[4].rate, None)
        self.assertEqual(basket_tips[4].amount, minor_unit(22.12))
        self.assertEqual(basket_tips[4].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tips[4].staffer_id, self.staffer.id)
        self.assertEqual(basket_tips[4].source, BasketTipSource.BCR)

        summed_amount = sum(tip.amount for tip in basket_tips)
        self.assertEqual(minor_unit(txn2.tip.amount), summed_amount)
        self.assertEqual(txn2.tip.type, SimpleTip.TIP_TYPE__HAND)
        self.assertEqual(txn2.tip.rate, major_unit(summed_amount))
        self.assertEqual(txn2.tip.basket_tip_id, basket_tips[4].id)

    def _check_appointment_accepted_finally(self):
        self.appointment.refresh_from_db()
        assert self.appointment.status == Appointment.STATUS.ACCEPTED
