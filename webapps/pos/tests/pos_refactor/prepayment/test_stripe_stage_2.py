from unittest.mock import patch
import pytest
from django.conf import settings
from django.test.utils import override_settings
from mock import mock
from model_bakery import baker
from stripe.error import InvalidRequestError

from lib.payment_gateway.enums import PaymentMethodType
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
)
from service.booking.tools import AppointmentData
from service.exceptions import ServiceError
from webapps.adyen.exceptions import ThreeDSecureAuthenticationRequired
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentTypeChoices
from webapps.booking.models import Appointment
from webapps.business.models import (
    Business,
    Resource,
)
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.consts.stripe import RefundStripeStatus
from webapps.payment_providers.consts.stripe_error_code import DeclineCode
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    StripeAccountHolder,
    <PERSON>e<PERSON>ust<PERSON>,
    StripePaymentIntent,
    StripeTokenizedPaymentMethod,
    TokenizedPaymentMethod,
    Payment,
)
from webapps.pos.deposit import charge_prepayment_on_confirm
from webapps.pos.enums import (
    CARD_TYPE__APPLE_PAY,
    CARD_TYPE__BLIK,
    CARD_TYPE__GOOGLE_PAY,
    PaymentProviderEnum,
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
)
from webapps.pos.exceptions import BlikActionRequired
from webapps.pos.models import (
    PaymentType,
    POSPlan,
    Transaction,
)
from webapps.pos.provider import get_payment_provider
from webapps.pos.serializers import (
    CustomerAppointmentTransactionSerializer,
    TransactionSerializer,
)
from webapps.pos.services import TransactionService
from webapps.pos.tasks import CheckoutPrepaidTransaction, close_transaction
from webapps.pos.tests.pos_refactor.helpers_stripe import (
    StripeMixin,
    StripeTestEventBody,
)
from webapps.pos.tests.pos_refactor.prepayment.base import TestTransactionSerializerPrepaymentBase
from webapps.pos.tip_calculations import SimpleTip
from webapps.stripe_integration.models import StripeAccount
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_payment_intent_retrieve,
    mock_stripe_payment_intent_retrieve_with_tip,
)
from webapps.user.models import User


# pylint: disable=too-many-public-methods
class TestTransactionSerializerPrepaymentStripeBaseClass(
    StripeMixin,
    TestTransactionSerializerPrepaymentBase,
):
    def setUp(self):
        super().setUp()
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        self.payment_method = baker.make(
            'pos.PaymentMethod',
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            card_last_digits='1234',
            user=self.bci.user,
            default=True,
        )

        self.provider = get_payment_provider(
            codename=self.payment_method.provider,
            txn=Transaction(pos=self.pos),
        )
        baker.make(
            AccountHolder,
        )
        business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        baker.make(
            StripeAccountHolder,
            account_holder_id=business_wallet.account_holder_id,
            external_id='123',
        )
        baker.make(
            StripeAccount,
            pos=self.pos,
            kyc_verified_at_least_once=True,
            external_id='123',
        )

        self.customer = Customer.objects.last()
        baker.make(
            StripeCustomer,
            customer=self.customer,
        )
        self.tpm = baker.make(
            TokenizedPaymentMethod,
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details={
                'brand': 'visa',
                'last_digits': '1234',
                'expiry_month': 1,
                'expiry_year': 2032,
                'cardholder_name': 'cardholder name',
            },
        )

        baker.make(
            StripeTokenizedPaymentMethod,
            external_id='1233333',
            tokenized_payment_method=self.tpm,
        )
        self.provider_code = PaymentProviderCode.STRIPE
        self.random_id = self.generate_id()

    def _test_prepayment_success(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):

        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_prepayment_via_serializer()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        self._check_prepayment_success(txn)

    def _test_prepayment_success_update_with_booking(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        TransactionSerializer.update_transaction_with_booking(
            appointment=self.appointment, ignore_analytics=True
        )
        txn2 = Transaction.objects.last()

        self._check_prepayment_success_update_transaction_with_booking(txn, txn2)

    def _test_prepayment_success_blik(
        self, create_payment_intent_mock, confirm_payment_intent_mock
    ):
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'external_payment_method': {
                    'partner': CARD_TYPE__BLIK,
                    'token': '123456',
                },
            },
            context=self._get_context(),
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        with pytest.raises(BlikActionRequired) as error:
            serializer.save()

        self.assertIsNotNone(error.value.balance_transaction_id)

        self._handle_capture_notification(success=True)

        txn = self.appointment.transactions.first()
        self._check_prepayment_success_blik(txn)

    def _test_prepayment_confirm_fail_blik(
        self, create_payment_intent_mock, confirm_payment_intent_mock
    ):
        self._mock_auth_response(
            confirm_payment_intent_mock,
            success=False,
            external_id=self.random_id,
            raise_exception=True,
            decline_code=DeclineCode.GENERIC_DECLINE,
            error_message="The code passed has expired. BLIK codes expire after 2 minutes, please request another code from the customer.",  # pylint: disable=line-too-long
        )
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'external_payment_method': {
                    'partner': CARD_TYPE__BLIK,
                    'token': '123456',
                },
            },
            context=self._get_context(),
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

        with pytest.raises(ServiceError) as _:
            serializer.save()

        self._handle_auth_notification(
            success=False,
            decline_code=DeclineCode.GENERIC_DECLINE,
        )

        txn = self.appointment.transactions.first()
        self._check_prepayment_blik_fail(
            txn,
        )

    def _test_prepayment_notification_fail_blik(
        self, create_payment_intent_mock, confirm_payment_intent_mock
    ):
        error_message = "The code passed has expired. BLIK codes expire after 2 minutes, please request another code from the customer."  # pylint: disable=line-too-long
        self._mock_auth_response(
            confirm_payment_intent_mock,
            success=True,
            external_id=self.random_id,
            error_message=error_message,
        )
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'external_payment_method': {
                    'partner': CARD_TYPE__BLIK,
                    'token': '123456',
                },
            },
            context=self._get_context(),
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        with pytest.raises(BlikActionRequired) as error:
            serializer.save()

        self.assertEqual(
            error.value.balance_transaction_id,
            BalanceTransaction.objects.last().id,
        )

        self._handle_auth_notification(
            success=False,
            decline_code=DeclineCode.GENERIC_DECLINE,
            error_message=error_message,
        )

        txn = self.appointment.transactions.first()
        self._check_prepayment_blik_fail(
            txn,
        )

    def _test_prepayment_success_google_pay(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__GOOGLE_PAY
        self.payment_method.token = '123'

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'external_payment_method': {
                    'partner': CARD_TYPE__GOOGLE_PAY,
                    'token': 123,
                },
            },
            context=self._get_context(),
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()
        txn = self.appointment.transactions.first()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        self._check_prepayment_success_google_pay(txn)

    def _get_context(self):
        return {
            'currency_symbol': settings.CURRENCY_CODE,
            'valid_currency': True,
            'user': self.bci.user,
            'business': self.business,
            'pos': self.pos,
            'appointment_checkout': AppointmentWrapper(self.appointment.subbookings).checkout,
            'appointment_data': AppointmentData.build(self.appointment),
            'compatibilities': self.compatibilities,
            **self.device_data_context,
        }

    def _test_fix_for_apple_pay_payload(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        data = {
            'dry_run': False,
            'payment_method': 11,
            'external_payment_method': {
                'partner': CARD_TYPE__APPLE_PAY,
                'token': 123,
            },
        }

        self._test_prepayment_success_apple_pay(
            capture_payment_intent_mock,
            confirm_payment_intent_mock,
            create_payment_intent_mock,
            data=data,
        )

    def _test_prepayment_success_apple_pay(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        data=None,
    ):
        if not data:
            data = {
                'dry_run': False,
                'external_payment_method': {
                    'partner': CARD_TYPE__APPLE_PAY,
                    'token': 123,
                },
            }

        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__APPLE_PAY
        self.payment_method.token = '123'

        serializer = CustomerAppointmentTransactionSerializer(
            data=data,
            context=self._get_context(),
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()
        txn = self.appointment.transactions.first()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        self._check_prepayment_success_apple_pay(txn)


# pylint: disable=too-many-public-methods
class TestTransactionSerializerStripePrepayment(TestTransactionSerializerPrepaymentStripeBaseClass):
    def create_success_prepayment(self):
        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()

        return txn

    def test_prepayment_cfp(self):
        txn = self.create_prepayment_transaction(self.appointment)
        self._check_prepayment_cfp(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_prepayment_auth_semi_auto(
        self, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        """Business semiauto"""
        self.appointment.type = AppointmentTypeChoices.CUSTOMER
        self.appointment.save()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        self._check_prepayment_auth_semi_auth(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.cancel_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_prepayment_auth_semi_auto_checkout_prepaid_task(
        self, confirm_payment_intent_mock, create_payment_intent_mock, _cancel_payment_intent
    ):
        """Business semiauto"""
        self.appointment.type = AppointmentTypeChoices.CUSTOMER
        self.appointment.save()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        CheckoutPrepaidTransaction(transaction_id=txn.id)

        self._check_prepayment_auth_semi_auto_checkout_prepaid_task(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_prepayment_auth_semi_auto_update_with_booking(
        self, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        """Business semiauto"""
        self.appointment.type = AppointmentTypeChoices.CUSTOMER
        self.appointment.save()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        TransactionSerializer.update_transaction_with_booking(
            appointment=self.appointment, ignore_analytics=True
        )
        txn2 = Transaction.objects.last()

        self._check_prepayment_auth_semi_auth_update_with_booking(txn, txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_auth_success(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        """Business auto"""
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        self._check_prepayment_auth_success(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_auth_3ds(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        """Business auto"""
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_3ds_required_response(confirm_payment_intent_mock, self.random_id)

        with pytest.raises(ThreeDSecureAuthenticationRequired) as error:
            self.create_prepayment_via_serializer()

        self.assertEqual(error.value.args[0], '3DSecure')
        txn = self.appointment.transactions.first()

        tds_data = Payment.objects.order_by('-created').first().action_required_details
        self._check_prepayment_auth_3ds(txn, tds_data)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_update_with_booking(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success_update_with_booking(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_update_with_booking_close_txn(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        txn_payment_rows = txn.payment_rows

        TransactionSerializer.update_transaction_with_booking(
            appointment=self.appointment, ignore_analytics=True
        )
        txn2 = Transaction.objects.last()
        payment_rows = txn2.payment_rows
        self.assertEqual(txn_payment_rows[0].status, payment_rows[0].status)
        self.assertEqual(txn_payment_rows[0].created, payment_rows[0].created)

        close_transaction(txn2, 'CheckoutPrepaidTransaction')

        txn2.refresh_from_db()
        self.assertEqual(txn2.payment_rows[0].created, payment_rows[0].created)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)

        self._check_prepayment_success_update_transaction_with_booking_close_txn(txn, txn2)

    @override_settings(POS__GOOGLE_PAY=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_google_pay(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success_google_pay(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @override_settings(POS__APPLE_PAY=True)
    def test_prepayment_success_apple_pay(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success_apple_pay(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @override_settings(POS__APPLE_PAY=True)
    def test_fix_for_apple_pay_payload(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_fix_for_apple_pay_payload(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_with_fee(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.stripe_plan.provision = 0.15
        self.stripe_plan.txn_fee = 21
        self.stripe_plan.save()

        txn = self.create_prepayment_via_serializer()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        self._check_prepayment_success_with_fee(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_semiauto(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self.appointment.type = AppointmentTypeChoices.CUSTOMER
        self.appointment.save()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        charge_prepayment_on_confirm(appointment_id=self.appointment.id, **self.device_data_dict)
        self._handle_capture_notification(success=True)

        self._check_prepayment_success_semiauto(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_semiauto_error_during_capture(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self.appointment.type = AppointmentTypeChoices.CUSTOMER
        self.appointment.save()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        capture_payment_intent_mock.side_effect = InvalidRequestError(
            message=(
                'This PaymentIntent could not be captured because it has a status of canceled. '
                'Only a PaymentIntent with one of the following statuses may be captured: '
                'requires_capture.'
            ),
            param=None,
            json_body={'error': {'code': 'payment_intent_unexpected_state'}},
        )

        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        charge_prepayment_on_confirm(appointment_id=self.appointment.id, **self.device_data_dict)

        self._check_prepayment_semiauto_error_during_capture(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_semiauto_cancel_webhook(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self.appointment.type = AppointmentTypeChoices.CUSTOMER
        self.appointment.save()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        self._handle_cancel_notification()

        self._check_prepayment_semiauto_cancel_webhook(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_semiauto_sent_for_capture_cancel_webhook(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self.appointment.type = AppointmentTypeChoices.CUSTOMER
        self.appointment.save()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)

        charge_prepayment_on_confirm(appointment_id=self.appointment.id, **self.device_data_dict)

        self._handle_cancel_notification()

        self._check_prepayment_semiauto_sent_for_capture_cancel_webhook(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_3ds(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_3ds_required_response(confirm_payment_intent_mock, self.random_id)

        with pytest.raises(ThreeDSecureAuthenticationRequired) as error:
            self.create_prepayment_via_serializer()

        self.assertEqual(error.value.args[0], '3DSecure')
        txn = self.appointment.transactions.first()

        self._success_payment_notifications(external_id=self.random_id)
        tds_data = Payment.objects.order_by('-created').first().action_required_details
        self._check_prepayment_success_3ds(txn, tds_data)

    @override_settings(POS__BLIK=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_prepayment_blik(
        self,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):

        self._test_prepayment_success_blik(
            create_payment_intent_mock=create_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
        )

    @override_settings(POS__BLIK=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_prepayment_blik_confirm_fail(
        self,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):

        self._test_prepayment_confirm_fail_blik(
            create_payment_intent_mock=create_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
        )

    @override_settings(POS__BLIK=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_prepayment_blik_notification_fail(
        self,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):

        self._test_prepayment_notification_fail_blik(
            create_payment_intent_mock=create_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_prepayment_auth_fail(self, confirm_payment_intent_mock, create_payment_intent_mock):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(
            confirm_payment_intent_mock,
            False,
            decline_code=DeclineCode.EXPIRED_CARD,
            raise_exception=True,
        )

        with pytest.raises(ServiceError) as error:  # catch quickassert
            self.create_prepayment_via_serializer()

        self.assertEqual(
            error.value.errors[0]['description'],
            PaymentError.EXPIRED_CARD.label,
        )

        txn = self.appointment.transactions.first()
        self._handle_auth_notification(success=False, decline_code=DeclineCode.EXPIRED_CARD)

        self._check_prepayment_auth_fail(txn, error_code=PaymentError.EXPIRED_CARD)

    def test_prepayment_auth_fail_pick_up(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.RESTRICTED_CARD,
            decline_code=DeclineCode.PICKUP_CARD,
        )

    def test_prepayment_auth_fail_fraudulent(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.FRAUDULENT,
            decline_code=DeclineCode.FRAUDULENT,
        )

    def test_prepayment_auth_fail_incorrect_number(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.INCORRECT_NUMBER,
            decline_code=DeclineCode.INCORRECT_NUMBER,
        )

    def test_prepayment_auth_fail_stolen_card(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.RESTRICTED_CARD,
            decline_code=DeclineCode.STOLEN_CARD,
        )

    def test_prepayment_auth_fail_lost_card(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.RESTRICTED_CARD,
            decline_code=DeclineCode.LOST_CARD,
        )

    def test_prepayment_auth_fail_invalid_account(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.INVALID_ACCOUNT_CARD,
            decline_code=DeclineCode.INVALID_ACCOUNT,
        )

    def test_prepayment_auth_fail_generic_decline_code(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.GENERIC_ERROR,
            decline_code=DeclineCode.GENERIC_DECLINE,
        )

    def test_prepayment_auth_fail_expired_card(self):
        self._test_prepayment_auth_fail(
            error_code=PaymentError.EXPIRED_CARD,
            decline_code=DeclineCode.EXPIRED_CARD,
            final_card_status=TokenizedPaymentMethod.INTERNAL_STATUS.EXPIRED,
        )

    def _test_prepayment_auth_fail(
        self,
        error_code: PaymentError,
        decline_code: DeclineCode,
        final_card_status=TokenizedPaymentMethod.INTERNAL_STATUS.INVALID,
    ):
        with (
            patch(
                'webapps.payment_providers.services.stripe.'
                'stripe.StripeProvider.create_payment_intent'
            ) as create_payment_intent_mock,
            patch(
                'webapps.payment_providers.services.stripe'
                '.stripe.StripeProvider.confirm_payment_intent'
            ) as confirm_payment_intent_mock,
            patch(
                'webapps.payment_providers.card_validation.'
                'payment_method_invalid_or_expired_event.send'
            ) as payment_method_invalid_or_expired_event_mock,
        ):
            create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
            self._mock_auth_response(
                confirm_payment_intent_mock, False, decline_code=decline_code, raise_exception=True
            )
            with pytest.raises(ServiceError) as error:  # catch quickassert
                self.create_prepayment_via_serializer()

            self.assertEqual(
                error.value.errors[0]['description'],
                error_code.label,
            )

            old_txn = self.appointment.transactions.first()
            self._handle_auth_notification(success=False, decline_code=decline_code)

            self._check_prepayment_auth_fail(old_txn, error_code=error_code)
            self.tpm.refresh_from_db()
            assert self.tpm.internal_status == final_card_status
            if final_card_status in [
                TokenizedPaymentMethod.INTERNAL_STATUS.INVALID,
                TokenizedPaymentMethod.INTERNAL_STATUS.EXPIRED,
            ]:
                assert self.tpm.error_notification is not None
                payment_method_invalid_or_expired_event_mock.assert_called_once()

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_fail(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_prepayment_via_serializer()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=False, decline_code=DeclineCode.INCORRECT_CVC)

        self._check_prepayment_fail(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_3ds_fail(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_3ds_required_response(confirm_payment_intent_mock, self.random_id)

        with pytest.raises(ThreeDSecureAuthenticationRequired) as error:
            self.create_prepayment_via_serializer()
        self.assertEqual(error.value.args[0], '3DSecure')

        self._handle_payment_intent_failed_3ds_notification(external_id=self.random_id)

        txn = self.appointment.transactions.first()
        tds_data = Payment.objects.order_by('-created').first().action_required_details
        self._check_prepayment_3ds_fail(
            txn, tds_data, error_code=PaymentError.AUTHENTICATION_REQUIRED
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    def test_prepayment_fail_expired_card(self, create_payment_intent_mock):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)

        expired_tpm = baker.make(
            TokenizedPaymentMethod,
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details={
                'brand': 'visa',
                'last_digits': '4321',
                'expiry_month': 1,
                'expiry_year': 2021,
                'cardholder_name': 'cardholder name',
            },
        )
        baker.make(
            StripeTokenizedPaymentMethod,
            external_id='1233334',
            tokenized_payment_method=expired_tpm,
        )
        baker.make(
            'pos.PaymentMethod',
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            card_last_digits='4321',
            user=self.bci.user,
            default=True,
            tokenized_pm_id=expired_tpm.id,
        )

        with pytest.raises(ServiceError) as error:
            self.create_prepayment_via_serializer()

        self.assertEqual(
            error.value.errors[0]['description'],
            PaymentError.EXPIRED_CARD.label,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_send_for_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        self._check_prepayment_send_for_refund(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        # Refund
        self._handle_refund_notification(status=RefundStripeStatus.SUCCEEDED)

        self._check_prepayment_refund(txn, success_row)

    @override_settings(POS__BLIK=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_refund_blik(
        self,
        refund_possible_mock,
        create_refund_mock,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        refund_possible_mock.return_value = (True, None)

        self._test_prepayment_success_blik(
            create_payment_intent_mock=create_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
        )

        txn = self.appointment.transactions.first()
        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        self._handle_refund_notification(
            status=RefundStripeStatus.PENDING,
        )
        self._handle_refunded_updated_notification(
            status=RefundStripeStatus.SUCCEEDED,
        )

        self._check_prepayment_refund(
            txn,
            success_row,
            receipts_count=4,
            payment_method=PaymentMethodType.BLIK,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_refund_with_fee(
        self,
        refund_possible_mock,
        create_transfer_mock,
        create_refund_mock,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        self.stripe_plan.provision = 0.15
        self.stripe_plan.txn_fee = 21
        self.stripe_plan.refund_provision = 0.1
        self.stripe_plan.refund_txn_fee = 22
        self.stripe_plan.save()

        refund_possible_mock.return_value = (True, None)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        # Refund
        self._handle_refund_notification(status=RefundStripeStatus.SUCCEEDED)

        self._check_prepayment_refund_with_fee(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_refund_fail(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        # Refund
        self._handle_refund_notification(status=RefundStripeStatus.FAILED)

        self._check_prepayment_refund_fail(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_prepayment_chargeback(
        self,
        create_transfer_mock,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.stripe_plan.chargeback_provision = 0.1
        self.stripe_plan.chargeback_txn_fee = 22
        self.stripe_plan.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Chargeback
        self._handle_chargeback_notification(amount=43212)

        self._check_prepayment_chargeback(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_prepayment_chargeback_with_fees(
        self,
        create_transfer_mock,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.stripe_plan.provision = 0.15
        self.stripe_plan.txn_fee = 21
        self.stripe_plan.chargeback_provision = 0.1
        self.stripe_plan.chargeback_txn_fee = 22
        self.stripe_plan.save()

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Chargeback
        self._handle_chargeback_notification(amount=43212)

        self._check_prepayment_chargeback_with_fees(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_prepayment_chargeback_reversed(
        self,
        create_transfer_mock,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_prepayment_via_serializer()
        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Chargeback
        self._handle_chargeback_notification(amount=43212)

        txn.refresh_from_db()
        chargeback_row = txn.payment_rows[0]

        # Chargeback reverse
        self._handle_reversed_chargeback_notification(amount=43212)

        self._check_prepayment_chargeback_reversed(txn, success_row, chargeback_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_100(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.sv.price = 543.21
        self.sv.save()

        txn = self.create_success_prepayment()

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            item_price=543.21,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                }
            ],
        )

        self._check_prepayment_success_100(txn, txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_cash(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_success_prepayment()

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 444.35,
                },
            ],
        )

        self._check_prepayment_success_cash(txn, txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_pba_cfp(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_success_prepayment()

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        self._check_prepayment_success_pba_cfp(txn, txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_pba_success(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_success_prepayment()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        # Make payment
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications()

        self._check_prepayment_success_pba_success(txn, txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_success_pba_send_for_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_success_prepayment()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        # Make payment
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications()

        # Send for refund
        txn2.refresh_from_db()
        success_row = txn2.payment_rows[1]
        success_row.send_for_refund(
            operator=self.user,
        )

        self._check_prepayment_success_pba_send_for_refund(txn, txn2, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_success_pba_refunded(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_success_prepayment()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        # Make payment
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications()

        # Send for refund
        txn2.refresh_from_db()
        success_row = txn2.payment_rows[1]
        success_row.send_for_refund(
            operator=self.user,
        )

        # Refund
        self._handle_refund_notification(status=RefundStripeStatus.SUCCEEDED)

        self._check_prepayment_success_pba_refunded(txn, txn2, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_success_pba_refund_failed(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_success_prepayment()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        # Make payment
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications()

        # Send for refund
        txn2.refresh_from_db()
        success_row = txn2.payment_rows[1]
        success_row.send_for_refund(
            operator=self.user,
        )

        # Refund
        self._handle_refund_notification(status=RefundStripeStatus.FAILED)

        self._check_prepayment_success_pba_refund_fail(txn, txn2, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_success_pba_success_double_send_for_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_success_prepayment()

        # New intent will be created
        new_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=new_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, new_id)
        create_refund_mock.return_value = mock.MagicMock(id=new_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        # Make payment
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications(new_id)

        # Send for refund
        txn2.refresh_from_db()
        pba_success_row = txn2.payment_rows[1]
        pba_success_row.send_for_refund(
            operator=self.user,
        )

        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        txn2.refresh_from_db()
        prepayment_success_row = txn2.payment_rows[0]
        txn2.payment_rows[0].send_for_refund(
            operator=self.user,
        )

        self._check_prepayment_success_pba_success_double_send_for_refund(
            txn, txn2, pba_success_row, prepayment_success_row
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_success_pba_success_send_for_refund_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_success_prepayment()

        # New intent will be created
        new_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=new_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, new_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        # Make payment
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications(new_id)

        # Send for refund
        create_refund_mock.return_value = mock.MagicMock(id=new_id)
        txn2.refresh_from_db()
        pba_success_row = txn2.payment_rows[1]
        pba_success_row.send_for_refund(
            operator=self.user,
        )

        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        txn2.refresh_from_db()
        prepayment_success_row = txn2.payment_rows[0]
        prepayment_success_row.send_for_refund(
            operator=self.user,
        )

        # Refund PBA
        self._handle_refund_notification(status=RefundStripeStatus.SUCCEEDED, external_id=new_id)

        self._check_prepayment_success_pba_success_send_for_reund_refund(
            txn,
            txn2,
            pba_success_row,
            prepayment_success_row,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_prepayment_success_pba_success_double_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_success_prepayment()

        # New intent will be created
        new_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=new_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, new_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 543.21,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 444.35,
                },
            ],
        )

        # Make payment
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications(new_id)

        # Send for refund
        create_refund_mock.return_value = mock.MagicMock(id=new_id)
        txn2.refresh_from_db()
        pba_success_row = txn2.payment_rows[1]
        pba_success_row.send_for_refund(
            operator=self.user,
        )

        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        txn2.refresh_from_db()
        prepayment_success_row = txn2.payment_rows[0]
        prepayment_success_row.send_for_refund(
            operator=self.user,
        )

        # Refund PBA
        self._handle_refund_notification(status=RefundStripeStatus.SUCCEEDED, external_id=new_id)

        # Refund PREPAYMENT
        self._handle_refund_notification(
            status=RefundStripeStatus.SUCCEEDED, external_id=self.random_id
        )

        self._check_prepayment_success_pba_success_double_refund(
            txn,
            txn2,
            pba_success_row,
            prepayment_success_row,
        )

    def bcr_make_success(self, txn, row_index=0, amount: int = None, tip_amount: int = None):
        _, intent_object = self.provider.create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[row_index]
        )

        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.payment_intent_amount_capturable_updated(
                payment_intent_external_id=intent_object.external_id,
                amount=amount,
                tip_amount=tip_amount,
            ),
            expect_new_webhook_handling=True,
        )
        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.payment_intent_succeeded(
                payment_intent_external_id=intent_object.external_id,
            ),
            expect_new_webhook_handling=True,
        )
        return intent_object

    def prepapare_stripe_terminal_objects(self):
        self.stripe_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_TERMINAL,
        )
        self.pos.pos_plans.add(self.stripe_plan)
        self.stripe = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.STRIPE_TERMINAL)

    @mock_stripe_payment_intent_retrieve
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_bcr_success(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        *args,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        self.prepapare_stripe_terminal_objects()

        txn = self.create_success_prepayment()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 432.12,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.STRIPE_TERMINAL,
                    'amount': 555.44,
                },
            ],
        )

        # Make payment
        self.bcr_make_success(txn=txn2, row_index=1)

        self._check_prepayment_success_bcr_success(txn, txn2)

    @mock_stripe_payment_intent_retrieve
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_with_tip_bcr_success(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        *args,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        self.prepapare_stripe_terminal_objects()
        appointment_wrapper = AppointmentWrapper(self.appointment.subbookings)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'payment_method': self.payment_method.id,
                'tip': {
                    'rate': 10,
                    'type': SimpleTip.TIP_TYPE__HAND,
                    'amount': 10,
                },
            },
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.bci.user,
                'business': self.business,
                'pos': self.pos,
                'appointment_data': AppointmentData.build(appointment_wrapper),
                'appointment_checkout': appointment_wrapper.checkout,
                'compatibilities': self.compatibilities,
                **self.device_data_context,
            },
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()

        txn = self.appointment.transactions.first()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 442.12,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.STRIPE_TERMINAL,
                    'amount': 555.44,
                },
            ],
        )

        # Make payment
        self.bcr_make_success(txn=txn2, row_index=1)

        self._check_prepayment_success_with_tip_bcr_success(txn, txn2)

    @mock_stripe_payment_intent_retrieve_with_tip(base_amount=55544, tip_amount=2211)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_bcr_success_with_tip(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        *args,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        self.prepapare_stripe_terminal_objects()

        txn = self.create_success_prepayment()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 432.12,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.STRIPE_TERMINAL,
                    'amount': 555.44,
                },
            ],
        )

        # Make payment
        self.bcr_make_success(txn=txn2, row_index=1, amount=57756, tip_amount=2212)

        self._check_prepayment_success_bcr_success_with_tip(txn, txn2)

    @mock_stripe_payment_intent_retrieve_with_tip(base_amount=55544, tip_amount=2211)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_with_tip_bcr_success_with_tip(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        *args,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        self.prepapare_stripe_terminal_objects()
        appointment_wrapper = AppointmentWrapper(self.appointment.subbookings)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'payment_method': self.payment_method.id,
                'tip': {
                    'rate': 10,
                    'type': SimpleTip.TIP_TYPE__HAND,
                    'amount': 10,
                },
            },
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.bci.user,
                'business': self.business,
                'pos': self.pos,
                'appointment_data': AppointmentData.build(appointment_wrapper),
                'appointment_checkout': appointment_wrapper.checkout,
                'compatibilities': self.compatibilities,
                **self.device_data_context,
            },
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()

        txn = self.appointment.transactions.first()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 442.12,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.STRIPE_TERMINAL,
                    'amount': 555.44,
                },
            ],
        )

        # Make payment
        self.bcr_make_success(txn=txn2, row_index=1, amount=57756, tip_amount=2212)

        self._check_prepayment_success_with_tip_bcr_success_with_tip(txn, txn2)

    @mock_stripe_payment_intent_retrieve_with_tip(base_amount=55544, tip_amount=2211)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_with_tip_percent_bcr_success_with_tip(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        *args,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        self.prepapare_stripe_terminal_objects()
        appointment_wrapper = AppointmentWrapper(self.appointment.subbookings)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'payment_method': self.payment_method.id,
                'tip': {
                    'rate': 10,
                    'type': SimpleTip.TIP_TYPE__PERCENT,
                    'amount': 98.76,
                },
            },
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.bci.user,
                'business': self.business,
                'pos': self.pos,
                'appointment_data': AppointmentData.build(appointment_wrapper),
                'appointment_checkout': appointment_wrapper.checkout,
                'compatibilities': self.compatibilities,
                **self.device_data_context,
            },
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()

        txn = self.appointment.transactions.first()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()

        self.assertEqual(
            capture_payment_intent_mock.call_count,
            StripePaymentIntent.objects.count(),
        )

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 530.88,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.STRIPE_TERMINAL,
                    'amount': 555.44,
                },
            ],
        )

        # Make payment
        self.bcr_make_success(txn=txn2, row_index=1, amount=57756, tip_amount=2212)

        self._check_prepayment_success_with_tip_percent_bcr_success_with_tip(txn, txn2)

        self.assertEqual(
            capture_payment_intent_mock.call_count,
            StripePaymentIntent.objects.count(),
        )

    @mock_stripe_payment_intent_retrieve_with_tip(base_amount=55544, tip_amount=2211)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_checkout_customer_app_bcr_tip(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        *args,
    ):
        """
        It's ultimate case where you can find all tips which can exists.
        1. Tip in prepayment during booking
        2. Tip Selected in checkout (PBA + BCR)
        3. Tip changed in customer app (PBA)
        4. Tip added at BCR terminal (BCR)
        """

        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        self.prepapare_stripe_terminal_objects()
        appointment_wrapper = AppointmentWrapper(self.appointment.subbookings)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'payment_method': self.payment_method.id,
                'tip': {
                    'rate': 10,
                    'type': SimpleTip.TIP_TYPE__PERCENT,
                    'amount': 98.76,
                },
            },
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.bci.user,
                'business': self.business,
                'pos': self.pos,
                'appointment_data': AppointmentData.build(appointment_wrapper),
                'appointment_checkout': appointment_wrapper.checkout,
                'compatibilities': self.compatibilities,
                **self.device_data_context,
            },
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()

        txn = self.appointment.transactions.first()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()

        self.assertEqual(
            capture_payment_intent_mock.call_count,
            StripePaymentIntent.objects.count(),
        )

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(  # 987.56
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 530.88,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 151.24,
                },
                {
                    'payment_type_code': PaymentTypeEnum.STRIPE_TERMINAL,
                    'amount': 455.44,
                },
            ],
            tip={
                'rate': 150,
                'type': SimpleTip.TIP_TYPE__HAND,
            },
        )

        TransactionService.change_pba_tip(
            txn=txn2,
            data={
                'tip': {
                    'rate': 50,
                    'type': SimpleTip.TIP_TYPE__PERCENT,
                },
                'row_id': txn2.payment_rows[1].id,
            },
        )

        # Make payment
        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications()

        txn2.refresh_from_db()
        # Make payment
        self.bcr_make_success(txn=txn2, row_index=2, amount=57756, tip_amount=2212)

        self._check_prepayment_checkout_customer_app_bcr_tip(txn, txn2)

        self.assertEqual(
            capture_payment_intent_mock.call_count,
            StripePaymentIntent.objects.count(),
        )

    @mock_stripe_payment_intent_retrieve_with_tip(base_amount=55544, tip_amount=2211)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_checkout_customer_app_bcr_tip_tip_rows(
        self,
        capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        *args,
    ):
        """
        It's ultimate case where you can find all tips which can exists.
        1. Tip in prepayment during booking
        2. Tip Selected in checkout (PBA + BCR)
        3. Tip changed in customer app (PBA)
        4. Tip added at BCR terminal (BCR)
        """
        self.staffer2 = baker.make(
            Resource,
            staff_user=baker.make(User),
            business=self.business,
            active=True,
            type=Resource.STAFF,
        )

        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        self.prepapare_stripe_terminal_objects()
        appointment_wrapper = AppointmentWrapper(self.appointment.subbookings)

        serializer = CustomerAppointmentTransactionSerializer(
            data={
                'dry_run': False,
                'payment_method': self.payment_method.id,
                'tip': {
                    'rate': 10,
                    'type': SimpleTip.TIP_TYPE__PERCENT,
                    'amount': 98.76,
                },
            },
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.bci.user,
                'business': self.business,
                'pos': self.pos,
                'appointment_data': AppointmentData.build(appointment_wrapper),
                'appointment_checkout': appointment_wrapper.checkout,
                'compatibilities': self.compatibilities,
                **self.device_data_context,
            },
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()

        txn = self.appointment.transactions.first()

        self._handle_auth_notification(success=True)
        self._handle_capture_notification(success=True)

        txn.refresh_from_db()

        self.assertEqual(
            capture_payment_intent_mock.call_count,
            StripePaymentIntent.objects.count(),
        )

        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn2 = self.edit_prepayment_transaction(  # 987.56
            old_txn=txn,
            payment_rows_data=[
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'amount': 530.88,
                    'id': txn.payment_rows[0].id,
                    'locked': True,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 151.24,
                },
                {
                    'payment_type_code': PaymentTypeEnum.STRIPE_TERMINAL,
                    'amount': 455.44,
                },
            ],
            tip={
                'rate': 150,
                'type': SimpleTip.TIP_TYPE__HAND,
                'tip_rows': [
                    {
                        'staffer_id': self.staffer.id,
                        'rate': 20,
                    },
                    {
                        'staffer_id': self.staffer2.id,
                        'rate': 80,
                    },
                ],
            },
        )

        TransactionService.change_pba_tip(
            txn=txn2,
            data={
                'tip': {
                    'rate': 50,
                    'type': SimpleTip.TIP_TYPE__PERCENT,
                },
                'row_id': txn2.payment_rows[1].id,
            },
        )

        # Make payment
        # New intent will be created
        self.random_id = self.generate_id()
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[1],
        )
        self._success_payment_notifications()

        txn2.refresh_from_db()
        # Make payment
        self.bcr_make_success(txn=txn2, row_index=2, amount=57756, tip_amount=2212)

        self._check_prepayment_checkout_customer_app_bcr_tip_tip_rows(txn, txn2)

        self.assertEqual(
            capture_payment_intent_mock.call_count,
            StripePaymentIntent.objects.count(),
        )


# pylint: disable=too-many-public-methods
class TestTransactionSerializerStripePrepaymentForPendingPaymentAppointment(
    TestTransactionSerializerPrepaymentStripeBaseClass,
):
    def setUp(self):
        super().setUp()
        self.appointment.status = Appointment.STATUS.PENDING_PAYMENT
        self.appointment.save()

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )
        self._check_appointment_accepted_finally()

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def _test_prepayment_success_update_with_booking(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success_update_with_booking(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )
        self._check_appointment_accepted_finally()

    @override_settings(POS__GOOGLE_PAY=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_prepayment_success_google_pay(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success_google_pay(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )
        self._check_appointment_accepted_finally()

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @override_settings(POS__APPLE_PAY=True)
    def test_prepayment_success_apple_pay(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        self._test_prepayment_success_apple_pay(
            capture_payment_intent_mock=capture_payment_intent_mock,
            confirm_payment_intent_mock=confirm_payment_intent_mock,
            create_payment_intent_mock=create_payment_intent_mock,
        )
        self._check_appointment_accepted_finally()
