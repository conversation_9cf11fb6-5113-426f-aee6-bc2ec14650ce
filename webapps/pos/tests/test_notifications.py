import datetime
import unittest
from decimal import Decimal

import pytest
import pytz
from model_bakery import baker

from country_config import CountryConfig, Country
from lib.test_utils import create_subbooking
from webapps import consts
from webapps.booking.baker_recipes.baker_recipes_appointment import (
    appointment_recipe,
    booking_recipe,
)
from webapps.booking.models import Appointment, BookingSources
from webapps.business.baker_recipes import business_recipe, bci_recipe
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.notification.tests.utils import get_popup_notification_messages
from webapps.pos.baker_recipes import receipt_recipe
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import POS, PaymentRow, PaymentType, Receipt, Transaction
from webapps.pos.notifications import (
    BooksyPayPaymentCompletedNotification,
    CancellationFeeChargedNotification,
    MobilePaymentNotification,
)
from webapps.user.models import User, UserProfile


def make_booking_and_transaction(business, pos, customer):
    booking_source, _ = BookingSources.objects.get_or_create(
        app_type=BookingSources.BUSINESS_APP,
        name=consts.WEB,
    )
    booked_from = datetime.datetime(2019, 5, 1, 10, 0, tzinfo=pytz.UTC)
    booked_till = booked_from + datetime.timedelta(minutes=30)
    booking = create_subbooking(
        business=business,
        booking_kws=dict(
            booked_from=booked_till,
            booked_till=booked_till,
            status=Appointment.STATUS.ACCEPTED,
            type=Appointment.TYPE.BUSINESS,
            booked_for=customer,
            service_name='Haircut',
        ),
        source=booking_source,
    )[0]
    transaction = baker.make(
        Transaction,
        pos=pos,
        appointment=booking.appointment,
        customer_card=customer,
        total=Decimal('10.59'),
    )
    return booking, transaction


@pytest.mark.django_db
class TestPaymentsNotifications(unittest.TestCase):

    def setUp(self) -> None:
        super().setUp()
        user = baker.make(User)
        business = business_recipe.make(owner=user)
        pos = baker.make(POS, business=business, active=True)
        self.customer = bci_recipe.make(
            first_name='John',
            last_name='Malkovich',
            cell_phone='+***********',
        )
        self.customer.reindex(refresh_index=True)

        _, self.transaction = make_booking_and_transaction(business, pos, self.customer)

        self.receipt = baker.make(
            Receipt, transaction=self.transaction, status_code=receipt_status.PAYMENT_SUCCESS
        )
        self.transaction.latest_receipt = self.receipt
        self.transaction.save()

        self.payment_type = baker.make(
            PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP, default=True
        )

        self.payment_row = PaymentRow.create_with_status(
            receipt=self.receipt,
            payment_type=self.payment_type,
            amount=10.59,
        )

        self.payment_row.save()

        self.appointment = appointment_recipe.make(
            business=business,
            updated_by=business.owner,
            booked_for=self.customer,
        )
        booking = booking_recipe.prepare(
            appointment=self.appointment,
            booked_from=self.appointment.booked_from,
            booked_till=self.appointment.booked_till,
        )
        booking.save(override=True)

    def test_cancellation_fee_charged_notification(self):
        notification = CancellationFeeChargedNotification(
            self.transaction,
        )

        context = notification.context
        assert context['customer_name'] == 'John Malkovich'
        assert context['service_name'] == 'Haircut'
        assert context['total_amount'] == '$10.59'
        for channel in notification.channels:
            assert channel(notification).get_content()

    def test_mobile_payment_notification(self):
        notification = MobilePaymentNotification(
            self.transaction,
        )

        context = notification.context
        assert context['customer_name'] == 'John Malkovich'
        assert context['service_name'] == 'Haircut'
        assert context['total_amount'] == '$10.59'
        for channel in notification.channels:
            assert channel(notification).get_content()

    def test_booksy_pay_payment_completed_notification(self):
        receipt = receipt_recipe.make(
            transaction=self.transaction,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()
        payment_type = baker.make(
            PaymentType, pos=self.transaction.pos, code=PaymentTypeEnum.BOOKSY_PAY, default=True
        )
        payment_row = PaymentRow.create_with_status(
            receipt=receipt,
            payment_type=payment_type,
            amount=10.59,
        )
        payment_row.save()
        notification = BooksyPayPaymentCompletedNotification(self.transaction)

        context = notification.context
        assert context['customer_name'] == 'John Malkovich'
        assert context['service_name'] == 'Haircut'
        assert context['total_amount'] == '$10.59'
        for channel in notification.channels:
            assert channel(notification).get_content()


@pytest.mark.django_db
class TestPaymentsNotificationsLanguage:
    def prepare_test_data(self, user_language):
        user = baker.make(User)
        baker.make(
            UserProfile, user=user, language=user_language, profile_type=UserProfile.Type.BUSINESS
        )
        business = baker.make(Business, owner=user)
        pos = baker.make(POS, business=business, active=True)
        customer = baker.make(
            BusinessCustomerInfo, user=baker.make(User), first_name='Johnny', last_name='Derp'
        )
        customer.reindex()

        _, self.transaction = make_booking_and_transaction(business, pos, customer)

        self.receipt = baker.make(
            Receipt, transaction=self.transaction, status_code=receipt_status.PAYMENT_SUCCESS
        )
        self.transaction.latest_receipt = self.receipt
        self.transaction.save()

        self.payment_type = baker.make(
            PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP, default=True
        )

        self.payment_row = PaymentRow.create_with_status(
            receipt=self.receipt,
            payment_type=self.payment_type,
            amount=10.59,
        )

        self.payment_row.save()

    @pytest.mark.parametrize(
        'user_language, expected_message',
        [
            (CountryConfig(Country.PL).language_code, "Wpłacono kwotę "),
            (CountryConfig(Country.ES).language_code, "Has recibido un pago por un importe de "),
            (CountryConfig(Country.PT).language_code, "Você recebeu um pagamento de"),
            (CountryConfig(Country.BR).language_code, "Você recebeu um pagamento de"),
        ],
    )
    def test_mobile_payment_notification(self, user_language, expected_message):
        self.prepare_test_data(user_language)

        notification = MobilePaymentNotification(self.transaction)
        message = get_popup_notification_messages(notification)[0]

        assert message == expected_message

    @pytest.mark.parametrize(
        'user_language, expected_message',
        [
            (CountryConfig(Country.PL).language_code, "Wpłacono kwotę {total_amount}"),
            (CountryConfig(Country.ES).language_code, "Has recibido un pago de {total_amount}"),
            (CountryConfig(Country.PT).language_code, "Você recebeu {total_amount}"),
            (CountryConfig(Country.BR).language_code, "Você recebeu {total_amount}"),
        ],
    )
    def test_cancellation_fee_charged_notification(self, user_language, expected_message):
        self.prepare_test_data(user_language)

        notification = CancellationFeeChargedNotification(self.transaction)
        message = get_popup_notification_messages(notification)[0]

        assert message == expected_message.format(
            total_amount=notification.get_context()['total_amount']
        )
