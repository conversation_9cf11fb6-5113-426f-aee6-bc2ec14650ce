from __future__ import annotations

# pylint: disable=protected-access, duplicate-code

import copy
import typing as t
from collections import defaultdict, namedtuple
from dataclasses import dataclass
from datetime import date, datetime, time, timedelta
from decimal import Decimal, ROUND_HALF_UP
from itertools import groupby
from operator import itemgetter

from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from django.db import transaction
from django.db.models import Max, Subquery
from django.utils.dateparse import parse_time
from django.utils.functional import cached_property
from django.utils.timezone import localdate, localtime, make_aware
from django.utils.translation import gettext, gettext_lazy as _
from elasticsearch_dsl.utils import AttrDict
from rest_framework import serializers
from rest_framework.fields import get_attribute

import lib.ranges
from lib.datetime_utils import get_week_day
from lib.feature_flag.feature.monetisation import EnablePeakHoursFlag
from lib.rivers import River, bump_document
from lib.serializers import safe_get
from lib.time_24_hour import time24hour
from lib.tools import format_currency, relativedelta_total_seconds, tznow
from webapps.booking.enums import WhoMakesChange
from webapps.booking.models import BookingChange, SubBooking
from webapps.business.context import validate_business_context
from webapps.business.enums import DiscountType, PriceType
from webapps.business.models import (
    Business,
    SERVICE_VARIANT_CLIENT_DISCOUNT,
    SERVICE_VARIANT_FLASH_SALE,
    SERVICE_VARIANT_HAPPY_HOURS,
    SERVICE_VARIANT_LAST_MINUTE,
    ServicePromotion,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.omnibus_price import get_service_latest_prices
from webapps.business.tasks import last_minute_incentive_task
from webapps.notification.models import NotificationHistory, UserNotification
from webapps.pos.calculations import round_currency
from webapps.schedule.ports import get_working_dthours_dict
from webapps.user.tools import get_system_user
from webapps.pos.models import TransactionRow
from webapps.premium_services.public import (
    SubBookingData,
    SubBookingSurchargeData,
    SubBookingSurchargeRepository,
    SurchargeResolver,
    SurchargeType,
)


PromotionRange = namedtuple('PromotionRange', 'start end')
TimeSlot = namedtuple('TimeSlot', 'start end')


def _invalidate_cached_properties(obj):
    for attr, value in obj.__class__.__dict__.items():
        if isinstance(value, cached_property):
            obj.__dict__.pop(attr, None)


def get_booking_base_price(subbooking: SubBooking | dict) -> Decimal | None:
    service_data = safe_get(subbooking, ['service_data'])
    variant = safe_get(subbooking, ['service_variant'])
    variant_price = service_data.service_variant_price
    variant_type = service_data.service_variant_type

    if variant and not variant.is_view:
        variant = copy.copy(variant)
        variant.make_view(service_data)

    if not variant_type and variant:
        variant_price = variant.service_price.value
        variant_type = variant.service_price.price_type

    if variant_type not in PriceType.has_price():
        return Decimal('0.00')

    return variant_price


def update_subbooking_surcharge(
    subbooking: SubBooking | dict, result: SubBookingSurchargeData | None
) -> None:
    """
    Apply surcharge.
    """
    if isinstance(subbooking, dict):
        subbooking['surcharge'] = result

        if result:
            subbooking.update(
                {
                    'resolved_price': result._resolved_price,
                    'resolved_discount': None,
                    'resolved_promotion': None,
                    'resolved_promotion_type': None,
                }
            )
    else:
        subbooking.surcharge = result

        if result:
            subbooking.resolved_price = result._resolved_price
            subbooking.resolved_discount = None
            subbooking.resolved_promotion = None
            subbooking.resolved_promotion_type = None

    combo_children = safe_get(subbooking, ['combo_children']) or []
    if result:
        for child, child_result in zip(combo_children, result._combo_children):
            update_subbooking_surcharge(child, child_result)
    else:
        for child in combo_children:
            update_subbooking_surcharge(child, None)


@dataclass(frozen=True)
class PromotionResolverResult:
    result: t.Optional[dict] = None
    promotion: t.Optional['Promotion'] = None
    combo_children_promotions: t.Optional[list] = None

    @classmethod
    def merge(
        cls,
        results: t.Iterable[dict],
        promotion: t.Optional['Promotion'] = None,
        combo_children_promotions: t.Optional[list] = None,
    ) -> 'PromotionResolverResult':
        """Sums up result dicts."""
        if not promotion:
            return PromotionResolverResult()

        merged_result = None
        for result in results:
            if not result:
                continue

            if merged_result is None:
                merged_result = copy.deepcopy(result)
                continue

            for key in ['discount_amount', '_price', 'price_unformatted', 'price_before_discount']:
                merged_result[key] += result[key]

        if merged_result:
            merged_result['price'] = format_currency(merged_result['price_unformatted'])

        return cls(
            result=merged_result,
            promotion=promotion,
            combo_children_promotions=combo_children_promotions,
        )


# region helper objects
def get_discount_price(amount, discount_amount, discount_type):
    amount = float(amount) if amount is not None else float(0)
    discount_amount = float(discount_amount)

    if discount_type == DiscountType.RATE:
        return round_currency((100 - discount_amount) * amount / 100)

    if discount_amount > amount:
        return 0
    return amount - discount_amount


def get_discount_price_dict(variant_price, variant_type, discount_amount, discount_type):
    price = get_discount_price(variant_price, discount_amount, discount_type)
    return {
        'price': price,
        'formatted_price': PriceType.format_service_price(
            price,
            variant_type,
        ),
    }


def get_discount_label(amount, discount_type=DiscountType.RATE):
    if discount_type == DiscountType.RATE:
        return f'{int(float(amount))}%'
    return format_currency(float(amount))


def discount_resolved_to_zero(promotion: PromotionResolverResult) -> bool:
    return promotion.result and not promotion.result.get('discount_value')


class IncentiveBusinessFinder:
    @staticmethod
    def timezones_for_localtime(local_time, skip_minutes=True):
        """List of timezones for current localtime.

        :param local_time: time to look for in timezones
        :type local_time: time

        :param skip_minutes: skip minute check when searching for timezone
        :type skip_minutes: bool

        :return: list of timezone names

        """
        timezones = set()

        timezones_qs = (
            Business.objects.filter(
                active=True,
                visible=True,
                time_zone_name__isnull=False,
            )
            .values_list(
                'time_zone_name',
                flat=True,
            )
            .distinct()
        )

        for tz_name in timezones_qs:
            now = tznow(tz_name)

            if (skip_minutes and now.hour != local_time.hour) or (
                not skip_minutes
                and (now.hour != local_time.hour or now.minute != local_time.minute)
            ):
                continue

            timezones.add(tz_name)

        return list(timezones)

    @staticmethod
    def business_ids_for_happy_hours_incentive(timezones):
        """List of business ids in timezone suitable for notification.

        :param timezones: timezones to search for businesses
        :type timezones: list

        :return set of business ids
        """
        if not timezones:
            return []

        biz_ids_with_active_hh = (
            ServicePromotion.objects.filter(
                type=SERVICE_VARIANT_HAPPY_HOURS,
                active=True,
                business__time_zone_name__in=timezones,
            )
            .order_by(
                'business_id',
            )
            .distinct(
                'business_id',
            )
            .values_list('business_id', flat=True)
        )

        # hence all timezones currently have the same time we can use first one
        _tz = list(timezones)[0]
        thirty_days_ago = tznow(_tz) - timedelta(days=30)
        biz_ids_already_notified = (
            NotificationHistory.objects.filter(
                sender=NotificationHistory.SENDER_SYSTEM,
                type=UserNotification.PUSH_NOTIFICATION,
                task_type=NotificationHistory.TASK_TYPE__HH_PROMO_INCENTIVE,
                created__gte=thirty_days_ago,
            )
            .order_by(
                'business_id',
            )
            .distinct(
                'business_id',
            )
            .values_list('business_id', flat=True)
        )

        biz_ids_in_timezone = Business.objects.filter(
            time_zone_name__in=timezones,
            boost_status__in=Business.BoostStatus.active_statuses(),
        ).values_list('id', flat=True)

        to_exclude = set(biz_ids_with_active_hh) | set(biz_ids_already_notified)
        return list(set(biz_ids_in_timezone) - set(to_exclude))

    @staticmethod
    def booking_ranges_from_localtime(time_to_resolve, offset=None):
        """Returns booking ranges regarding to AM or PM with offset.

        :param time_to_resolve: time to resolve bookings ranges for
        :type time_to_resolve: datetime
        :param offset: offset for noon
        :type offset: timedelta

        :return: tuple(bookings_form, bookings_till)

        """
        delta = timedelta(seconds=offset or 7200)
        _time = time_to_resolve - delta
        noon = time(12, 0)

        if (
            _time.day != time_to_resolve.day
            and _time.time() > noon
            or time_to_resolve.time() < noon
        ):
            time_from = time.min
            time_till = time.max.replace(hour=11)
        else:
            time_from = time.min.replace(hour=12)
            time_till = time.max

        bookings_from = datetime.combine(
            time_to_resolve,
            time_from,
        ).replace(tzinfo=time_to_resolve.tzinfo)
        bookings_till = datetime.combine(
            time_to_resolve,
            time_till,
        ).replace(tzinfo=time_to_resolve.tzinfo)

        return bookings_from, bookings_till


class BookingsGapFinder:
    """Searches for gaps between bookings."""

    gap_size = timedelta(seconds=7200)

    def __init__(self, business, date_from, date_till, gap_size=None):
        """
        :param business: Business instance with bookings prefetched
                         - canceled bookings filtered out
        :param date_from: start of searching rate
        :type date_from: datetime
        :param date_till: end of searching range
        :type date_till: datetime
        :param gap_size: required no booking time between bookings in seconds

        """
        self.business = business
        self.tz = self.business.get_timezone()
        self._date_from = date_from
        self._date_till = date_till
        if gap_size:
            self.gap_size = gap_size

    @cached_property
    def bookings(self):
        """List of bookings with resources converted for BookingRanges."""
        _bookings = SubBooking.objects.filter(
            appointment__business_id=self.business.id,
        ).order_by('booked_from')
        return _bookings

    @cached_property
    def working_hours(self):
        """Working hours for staffers."""
        start_date = self._date_from.astimezone(self.tz).date()
        end_date = self._date_till.astimezone(self.tz).date()
        resource_ids = self.business.resources.filter(
            active=True,
            visible=True,
        ).values_list('id', flat=True)
        working_hours = get_working_dthours_dict(
            business_id=self.business.id,
            tz=self.tz,
            resource_ids=resource_ids,
            start_date=start_date,
            end_date=end_date,
        )
        return {
            staffer_id: list(working_hours[staffer_id].values())
            for staffer_id in working_hours
            if list(working_hours[staffer_id].values())[0]
        }

    @property
    def booking_times_by_working_ranges(self):
        """A list of lists with booking times by working hours ranges."""
        booking_times = defaultdict(list)
        for booking in self.bookings:
            booking_times[booking.staffer.id].append(
                (
                    booking.booked_from.astimezone(self.tz),
                    booking.booked_till.astimezone(self.tz),
                )
            )

        result = []
        for staffer_id in self.working_hours:
            working_ranges = self.working_ranges(
                self.working_hours[staffer_id][0],
                booking_times.get(staffer_id, []),
            )
            result.extend(working_ranges)

        return result

    @property
    def booking_times_by_staffers_working_ranges(self):
        """A list of lists with booking times by staffer's working hours."""
        booking_times = defaultdict(list)
        for booking in self.bookings:
            booking_times[booking.staffer.id].append(
                (
                    booking.booked_from.astimezone(self.tz),
                    booking.booked_till.astimezone(self.tz),
                )
            )

        result = defaultdict(list)
        for staffer_id in self.working_hours:
            working_ranges = self.working_ranges(
                self.working_hours[staffer_id][0],
                booking_times.get(staffer_id, []),
            )
            result[staffer_id].extend(working_ranges)

        return result

    @classmethod
    def has_gap_in_working_range(cls, booking_times):
        """Checks weather booking_times have gap."""
        return any(cls.gaps_in_working_range(booking_times))

    @property
    def has_gap(self):
        return any(
            (
                self.has_gap_in_working_range(working_range)
                for working_range in self.booking_times_by_working_ranges
            )
        )

    @staticmethod
    def _is_gap_over_gap_size(
        booking_gap: t.Tuple[time24hour],
        gap_size: timedelta,
    ):
        """Checks if gap is greater than gap_size.

        :param booking_gap: end of previous booking and beginning od next
        :type booking_gap: tuple
        :param gap_size: gap duration
        :type gap_size: timedelta

        :returns bool
        """
        end, start = booking_gap
        return (start - end) >= gap_size

    @classmethod
    def gaps_in_working_range(cls, booking_times):
        """Checks for gaps greater than gap_size in booking_times."""
        gaps_cnt = len(booking_times)
        gaps = (slice(i, i + 2) for i in range(0, gaps_cnt, 2))
        return (cls._is_gap_over_gap_size(booking_times[gap], cls.gap_size) for gap in gaps)

    @cached_property
    def staffers_gaps(self):
        result = defaultdict(list)

        for staffer_id in self.booking_times_by_staffers_working_ranges:
            working_hours = self.booking_times_by_staffers_working_ranges[staffer_id]
            for booking_times in working_hours:
                result[staffer_id].extend(self.gaps_in_working_range(booking_times))

        return result

    @cached_property
    def gap_rate(self):
        """Percentage of staffers with gaps greater than gap_size."""
        staffers_cnt = len(self.staffers_gaps)
        staffers_with_gaps = 0

        for staffer_id in self.staffers_gaps:
            if any(self.staffers_gaps[staffer_id]):
                staffers_with_gaps += 1

        return int(round(100.0 * staffers_with_gaps / staffers_cnt))

    @staticmethod
    def working_ranges(working_hours, booking_times):
        """Splits booking times into lists limited by working hours.

        Each list starts with beginning of working hours followed by
        booked_form, booked_till and closes with end of working hours.
        """
        result = []
        for wh_from, wh_till in working_hours:
            _wh_booking_times = []

            for booking_time in booking_times:
                if wh_from <= booking_time[0] < wh_till:
                    _wh_booking_times.extend(booking_time)

            result.append([wh_from] + _wh_booking_times + [wh_till])

        return result


# endregion helper objects


# region serializer method subclasses
class CustomerAppointmentServicePromotionMixin:
    @staticmethod
    def push_last_minute_incentive(appointment, min_delta=None, max_delta=None):
        """Send push notification if appointment is cancelled in the time range.

        :param appointment: appointment
        :type appointment: appointment_wrapper object
        :param min_delta: minimum time before appointment cancellation
        :type min_delta: timedelta
        :param max_delta: maximum time before appointment cancellation
        :type max_delta: timedelta

        :return: mixed - celery task id or False

        """
        min_delta = min_delta or timedelta(hours=2)
        max_delta = max_delta or timedelta(hours=12)

        canceled_before = appointment.booked_from - appointment.status_changed
        if min_delta <= canceled_before <= max_delta:
            return last_minute_incentive_task.delay(
                appointment.id,
            )

        return False


class SubBookingSurchargeSerializer(serializers.Serializer):
    type = serializers.CharField(source='surcharge_type')
    rate = serializers.DecimalField(
        max_digits=3,
        decimal_places=0,
    )
    amount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
    )

    def to_representation(self, instance):
        return super().to_representation(instance) if instance else None


class SubbookingServicePromotionMixin:
    def get_service_promotion(self, instance):
        """Promotion applied to subbooking."""
        if (
            hasattr(instance, '_service_promotion')
            and instance._service_promotion
            and not discount_resolved_to_zero(instance._service_promotion)
        ):
            return instance._service_promotion.result

        if self.context.get('business'):
            business = self.context['business']
        else:
            business = get_attribute(instance, ['appointment', 'business'])
        resolver = ServicePromotionResolver(
            business=business,
        )
        if isinstance(instance, dict):
            bci = get_attribute(instance, ['appointment', 'booked_for'])
            promotion_result = resolver.from_data(instance, bci=bci)
        else:
            bci = (
                instance.appointment.booked_by
                if instance.appointment and instance.appointment.is_family_and_friends
                else None
            )
            promotion_result = resolver.from_instance(instance, bci=bci)

        setattr(instance, '_service_promotion', promotion_result)

        if not promotion_result:
            return None

        # we don't want apps to display price like: old 10.00 new 10.00
        if discount_resolved_to_zero(promotion_result):
            return None

        return promotion_result.result

    def get_surcharge(self, instance):
        if instance.surcharge:
            return SubBookingSurchargeSerializer(instance=instance.surcharge).data

        return None


class AppointmentServicePromotionMixin:
    def _inject_promotion(self, attrs):
        """Resolve service promotions from data."""
        business = self.context['business'] or self.business
        if not business:
            return attrs

        resolver = ServicePromotionResolver(
            business=business,
            who=self.WHO_MAKES_CHANGE,
        )

        bci_for_promotion = attrs.get('bci_for_promotion', None)

        resolved_variant_ids = {
            sbk.service_variant.id: bci_for_promotion
            or get_attribute(sbk, ['appointment', 'booked_for'])
            for sbk in getattr(self.instance, 'subbookings', [])
            if sbk.service_variant
        }
        bci = bci_for_promotion or attrs.get('booked_for', None)

        for sbk in attrs['subbookings']:
            # no service_variant no promotion
            if not sbk['service_variant']:
                continue

            # skip if business make changes to appointment with same BCI
            variant_id = sbk['service_variant'].id
            if (
                self.WHO_MAKES_CHANGE == WhoMakesChange.BUSINESS
                and resolved_variant_ids.get(variant_id, -1) == bci
            ):
                continue

            result = resolver.from_data(sbk, bci)
            resolver.update_booking_promotion(sbk, result)

        return attrs

    def _inject_surcharge(self, attrs):
        business = self.context['business'] or self.business
        if not business:
            return attrs

        resolver = SurchargeResolver(
            business_id=business.id,
            timezone=business.get_timezone(),
            date=None,
        )

        for subbooking in attrs['subbookings']:
            if '_service_promotion' not in subbooking:
                # Require that promotions have been resolved. For instance, promotions may not be
                # updated if business is making the changes.
                continue

            service_variant = safe_get(subbooking, ['service_variant'])
            combo_children = safe_get(subbooking, ['combo_children']) or []
            service_promotion_result = safe_get(subbooking, ['_service_promotion'])

            if not service_variant:
                result = None
            elif (
                service_promotion_result
                and service_promotion_result.promotion
                and not discount_resolved_to_zero(service_promotion_result)
            ):
                result = None
            else:
                result = resolver.resolve(
                    SubBookingData(
                        booked_from=safe_get(subbooking, ['booked_from']),
                        service_variant_id=service_variant.id,
                        price=get_booking_base_price(subbooking),
                        combo_children=[
                            SubBookingData(
                                booked_from=safe_get(child, ['booked_from']),
                                service_variant_id=safe_get(child, ['service_variant', 'id']),
                                price=get_booking_base_price(child),
                            )
                            for child in combo_children
                        ],
                    )
                )

            update_subbooking_surcharge(subbooking, result)

        return attrs


class PaymentTransactionServicePromotionMixin:
    def apply_promotions(self, data, bci_for_promotion: None):
        """Applies promotions discount for items in checkout."""
        # pylint: disable=too-many-branches
        if not self.context.get('pos') or not data.get('rows', []):
            return data

        bci = bci_for_promotion or data.get('customer_card')
        bci_discount = bci.discount if bci else Decimal(0)

        # do not calculate promotions from imports
        if self.context.get('import'):
            for row in data['rows']:
                row['discount_rate'] = 0
            return data

        # Turn off counting promotions - used in old endpoints (MP)
        if self.context.get('disable_promotions'):
            for row in data['rows']:
                # discount_rate = row.get('discount_rate') or bci_discount
                discount_rate = row.get('discount_rate') or 0
                row['discount_rate'] = Decimal(discount_rate)
            return data

        resolver = ServicePromotionResolver(business=self.context['pos'].business)
        use_peak_hours = EnablePeakHoursFlag()

        for row in data['rows']:
            subbooking = row.get('subbooking')
            if subbooking:
                # if row.get('discount_rate') is not None and bci:
                promotion = resolver.from_instance(subbooking, bci)

                if promotion and promotion.promotion and not discount_resolved_to_zero(promotion):
                    pass
                elif (
                    use_peak_hours
                    and subbooking.surcharge
                    and row['type'] == TransactionRow.TRANSACTION_ROW_TYPE__SERVICE
                ):
                    row['item_price'] = subbooking.resolved_price
                    if subbooking.surcharge.surcharge_type == SurchargeType.PREMIUM_HOURS:
                        premium_hours_label = gettext('Premium hours')
                        row['service_name'] = row['name_line_1'] = (
                            f"{row['name_line_1']} ({premium_hours_label})"
                        )

                discount_set_on_checkout = row.get('discount_rate') is not None
                if discount_set_on_checkout:
                    continue

                if not promotion.promotion:
                    row['discount_rate'] = Decimal(0)
                    continue

                row['discount_rate'] = promotion.promotion.discount_rate

            else:
                if not bci and row.get('discount_rate') is None:
                    row['discount_rate'] = Decimal(0)

                elif row.get('discount_rate') is None:
                    row['discount_rate'] = bci_discount

        return data

    @staticmethod
    def is_client_discount(promotion: dict, booking: SubBooking) -> bool:
        promotion_type = promotion.get('promotion_type')
        valid_resolved_types = (SERVICE_VARIANT_CLIENT_DISCOUNT, None)
        return (
            promotion_type == SERVICE_VARIANT_CLIENT_DISCOUNT
            and booking.resolved_promotion_type in valid_resolved_types
        )

    @staticmethod
    def client_discount_differs(promotion: dict, booking: SubBooking) -> bool:
        return (
            promotion.get('discount_amount') != booking.resolved_discount
            or promotion.get('price_unformatted') != booking.resolved_price
        )

    def update_resolved_client_discount(self, validated_data: dict) -> dict:
        """Update booking related fields for resolved client discount."""
        use_peak_hours = EnablePeakHoursFlag()

        for row in validated_data.get('rows', []):
            booking = safe_get(row, ['subbooking']) or {}
            promotion = safe_get(booking, ['_service_promotion'])
            surcharges_to_update = {}
            if (
                not promotion
                or not promotion.result
                or not self.is_client_discount(promotion.result, booking)
                or not self.client_discount_differs(promotion.result, booking)
            ):
                continue

            if use_peak_hours and booking and booking.surcharge:
                if promotion and promotion.promotion and not discount_resolved_to_zero(promotion):
                    booking.surcharge = None
                    surcharges_to_update[booking.id] = None
                else:
                    continue

            update = {
                'resolved_promotion_type': SERVICE_VARIANT_CLIENT_DISCOUNT,
                'resolved_discount': promotion.result['discount_value'],
                'resolved_price': promotion.result['price_unformatted'],
            }

            with transaction.atomic():
                result = SubBooking.objects.filter(
                    id=booking.id,
                ).update(
                    **update,
                )
                booking.refresh_from_db()

                BookingChange.add(
                    booking,
                    changed_by=BookingChange.BY_SYSTEM,
                    changed_user=get_system_user(),
                    metadata={
                        'action': 'update resolved client discount on checkout',
                        'status': 'success' if result else 'not updated',
                    },
                )

                if use_peak_hours:
                    SubBookingSurchargeRepository().update_for_subbookings(surcharges_to_update)

        return validated_data


# endregion serializer method subclasses


class ServicePromotions:
    """Service promotions for business."""

    @staticmethod
    def has_any_active_promotions(business: Business) -> bool:
        return ServicePromotion.objects.filter(
            active=True,
            type__in=(
                SERVICE_VARIANT_FLASH_SALE,
                SERVICE_VARIANT_LAST_MINUTE,
                SERVICE_VARIANT_HAPPY_HOURS,
            ),
            business=business,
        ).exists()

    @staticmethod
    def has_active_promotion(business, promotion_type):
        query_set = ServicePromotion.objects.filter(
            type=promotion_type,
            active=True,
            business=business,
        )
        if promotion_type == SERVICE_VARIANT_HAPPY_HOURS:
            query_set = query_set.values('happy_hours_week_day')

        return bool(query_set.annotate(Max('id')).count())

    @staticmethod
    def last_minute_promotion(business):
        return ServicePromotion.objects.filter(
            active=True,
            type=SERVICE_VARIANT_LAST_MINUTE,
            business=business,
        ).first()

    @staticmethod
    def disable_last_minute(business, log_entry=None):
        bump_document(River.BUSINESS, [business.id])

        filters = {
            'active': True,
            'business': business,
            'type': SERVICE_VARIANT_LAST_MINUTE,
        }

        return ServicePromotions.disable_promotions(filters, log_entry)

    @staticmethod
    def flash_sale_promotion(business):
        today = business.tznow.date()

        return ServicePromotion.objects.filter(
            active=True,
            type=SERVICE_VARIANT_FLASH_SALE,
            business=business,
            promotion_start__lte=today,
            promotion_end__gte=today,
        ).first()

    @staticmethod
    def disable_flash_sale(business, log_entry):
        bump_document(River.BUSINESS, [business.id])

        filters = {
            'active': True,
            'business': business,
            'type': SERVICE_VARIANT_FLASH_SALE,
        }

        return ServicePromotions.disable_promotions(filters, log_entry)

    @staticmethod
    @transaction.atomic
    def disable_expired_flash_sale_promotions():
        """Clears active flag on all expired Flash Sale service promotions."""
        promotions = ServicePromotion.objects.filter(
            active=True,
            type=SERVICE_VARIANT_FLASH_SALE,
        ).select_related('business')

        ids_to_disable = []
        docs_to_bump = []

        for promotion in promotions:
            if promotion.promotion_end < promotion.business.tznow.date():
                ids_to_disable.append(promotion.id)
                docs_to_bump.append(promotion.business.id)

        bump_document(River.BUSINESS, docs_to_bump)
        query_set = ServicePromotion.objects.select_for_update().filter(id__in=ids_to_disable)

        for instance in query_set:
            instance.active = False
            instance.add_to_log(action='disable')
            instance.save()

        return len(query_set)

    @staticmethod
    def happy_hours_promotion(business):
        latest_subquery = (
            ServicePromotion.objects.filter(
                active=True,
                type=SERVICE_VARIANT_HAPPY_HOURS,
                business=business,
            )
            .values('happy_hours_week_day')
            .annotate(
                created=Max('created'),
                max_id=Max('id'),
            )
        )

        return (
            ServicePromotion.objects.filter(id__in=Subquery(latest_subquery.values('max_id')))
            .order_by('happy_hours_week_day')
            .all()
        )

    @staticmethod
    @transaction.atomic
    def disable_promotions(filters, log_entry=None):
        """Disables promotions and ads log entry.

        :param filters: dict with django filters
        :param log_entry: dict with log entry

        :return int updated rows count

        """
        promotions = ServicePromotion.objects.select_for_update().filter(**filters)

        if not log_entry:
            log_entry = {'action': 'disable'}

        for instance in promotions:
            instance.active = False
            instance.add_to_log(**log_entry)
            instance.save()

        return promotions.count()

    @staticmethod
    def disable_happy_hours(business, days_of_week=None, log_entry=None):
        bump_document(River.BUSINESS, [business.id])

        filters = {'active': True, 'business': business, 'type': SERVICE_VARIANT_HAPPY_HOURS}
        if days_of_week:
            filters['happy_hours_week_day__in'] = days_of_week

        return ServicePromotions.disable_promotions(filters, log_entry)


# region promotion objects
class Promotion:
    names = {
        SERVICE_VARIANT_HAPPY_HOURS: _('Happy Hours'),
        SERVICE_VARIANT_FLASH_SALE: _('Flash Sale'),
        SERVICE_VARIANT_LAST_MINUTE: _('Last Minute'),
        SERVICE_VARIANT_CLIENT_DISCOUNT: _('Client Discount'),
    }
    ROUNDING_PRECISION = Decimal('0.00')

    def __init__(self, business: Business, promotion: ServicePromotion) -> None:
        self.business = business
        self.promotion = promotion
        self.instance = None
        self.booking = None
        self.variant = None
        self.addons = None

    @property
    def name(self):
        return str(self.names.get(self.promotion.type, ''))

    @property
    def is_amount(self):
        return self.discount_type == DiscountType.AMOUNT

    @cached_property
    def variant_price(self) -> Decimal:
        booking = self.booking or self.instance
        variant_price = safe_get(booking, ['service_data', 'service_variant_price'])
        variant_type = safe_get(booking, ['service_data', 'service_variant_type'])

        if not variant_type and self.variant:
            variant_price = self.variant.service_price.value
            variant_type = self.variant.service_price.price_type

        if variant_type not in PriceType.has_price():
            return Decimal('0.00')

        return variant_price

    @property
    def discount_type(self) -> DiscountType:
        """Discount type."""
        raise NotImplementedError()

    @property
    def rate(self):
        """Proxy for discount value - amount or rate."""
        raise NotImplementedError(f'rate must be declared in {self.__class__.__name__}')

    @cached_property
    def discount_rate(self):
        """Discount rate as percentage."""
        if self.discount_type == DiscountType.RATE:
            return self.rate
        if self.variant_price > 0:
            return ((self.rate / self.variant_price) * 100).quantize(
                self.ROUNDING_PRECISION,
                ROUND_HALF_UP,
            )
        return Decimal(0)

    @cached_property
    def discount(self):
        """Discount rate as amount."""
        if self.discount_type == DiscountType.AMOUNT:
            return format_currency(self.rate)
        return f'{self.rate:.0f}%'

    @cached_property
    def discounted_price(self):
        """Variant price with discount applied."""
        return self._value_after_discount(self.variant_price)

    def copy(self) -> 'Promotion':
        promotion = self.__class__(self.business, self.promotion)

        for attr in ['instance', 'booking', 'variant', 'addons']:
            setattr(promotion, attr, getattr(self, attr))

        return promotion

    def from_request(self, subbooking):
        """Calculate promotions using data from request.

        :param subbooking: subbooking dict
        :return: PromotionResolverResult

        """
        raise NotImplementedError()

    def from_instance(self, instance):
        """Calculate promotions using instance.

        :param instance: appointment instance
        :return: PromotionResolverResult

        """
        self.instance = instance
        self.variant = instance.service_variant

        if self.variant and not self.variant.is_view:
            self.variant = copy.copy(self.variant)
            self.variant.make_view(instance.service_data)

        if (
            not self.variant
            or self.variant.service_price.price_type not in PriceType.has_price()
            or not self.rate
        ):
            return None
        return self.result

    @staticmethod
    def is_resolved_promotion_supported(instance):
        """Helper for checking if resolved promotion is supported.

        After reverting migrations bookings can have resolved type that are
        'not yet' supported.
        """
        return instance.resolved_promotion_type in SERVICE_PROMOTION_TYPES

    def is_resolved_promotion_type_valid(self, instance):
        """Helper for checking if instance resolved type is same as promotion."""
        return instance.resolved_promotion_type == self.promotion.type

    def _parse_date(self, date_):
        return parse(date_, ignoretz=True).replace(
            tzinfo=self.business.get_timezone(),
        )

    def _value_after_discount(self, value):
        value = value if isinstance(value, Decimal) else Decimal(value)
        discount = self.discount_rate / Decimal(100) * value
        if discount < value:
            return round_currency(value - discount)
        return Decimal(0)

    @cached_property
    def discount_amount(self):
        return self.variant_price - self.discounted_price

    @cached_property
    def result(self) -> dict:
        """Promotion result as dict."""
        return {
            'type': self.name,  # DEPRECATED
            'name': self.name,
            'promotion_type': self.promotion.type,
            'discount': self.discount,
            'price': format_currency(self.discounted_price),
            '_price': self.discounted_price,  # TODO: remove in future
            'price_unformatted': self.discounted_price,
            'discount_value': self.discount_rate,
            'discount_amount': self.discount_amount,
            'price_before_discount': self.variant_price,
        }

    def __repr__(self):
        return f'{self.__class__.__name__} {self.business} {self.promotion}'


class ClientDiscount(Promotion):
    """BusinessCustomerInfo discount as service promotion.

    Uses bci.discount or instance.booked_for.discount for calculating
    best booking price for customer and checkout calculations.
    """

    def __init__(self, business, bci):
        if bci:
            setattr(bci, 'type', SERVICE_VARIANT_CLIENT_DISCOUNT)
        super().__init__(business, bci)

    @cached_property
    def variant_price(self) -> Decimal:
        booking = self.booking or self.instance
        variant_price = safe_get(booking, ['service_data', 'service_variant_price'])
        variant_type = safe_get(booking, ['service_data', 'service_variant_type'])

        if not variant_type and self.variant:
            variant_price = self.variant.service_price.value
            variant_type = self.variant.service_price.price_type

        if variant_type not in PriceType.has_price():
            return Decimal('0.00')

        addons_price = Decimal()
        for addon in self.addons:
            addons_price += (addon.price or Decimal()) * addon.quantity
        full_price = (variant_price or Decimal()) + addons_price
        return full_price

    @property
    def rate(self):
        if (
            hasattr(self, 'instance')
            and self.instance
            and self.is_resolved_promotion_supported(self.instance)
            and self.is_resolved_promotion_type_valid(self.instance)
            and self.check_is_proper_promotion_id()
        ):
            return Decimal(self.instance.resolved_discount)
        return Decimal(self.promotion.discount)

    def check_is_proper_promotion_id(self):
        if self.promotion.id == self.instance.appointment.booked_for_id:
            return True
        return (
            self.instance.appointment.booked_by
            and self.promotion.id == self.instance.appointment.booked_by.id
        )

    @property
    def discount_type(self):
        return DiscountType.RATE

    @property
    def do_not_show_discount_zero(self) -> bool:
        return self.booking and hasattr(self.booking, 'id')

    def from_request(self, subbooking) -> t.Optional[dict]:
        self.booking = subbooking
        self.variant = safe_get(self.booking, ['service_variant'])
        self.addons = safe_get(self.booking, ['addons']) or []

        _invalidate_cached_properties(self)

        if not self.booking or not self.variant or self.variant_price is None or self.rate is None:
            return None

        return self.result

    def from_instance(self, instance) -> t.Optional[dict]:
        self.instance = instance
        self.variant = instance.service_variant
        self.addons = instance.addons or []

        _invalidate_cached_properties(self)

        variant_type = instance.service_data.service_variant_type
        if not variant_type and self.variant:
            variant_type = self.variant.service_price.price_type

        if not self.variant or variant_type not in PriceType.has_price() or not self.rate:
            return None

        return self.result


class FlashSalePromotion(Promotion):
    @property
    def rate(self):
        return Decimal(self.promotion.promotion_options.get('discount_rate', 0))

    @property
    def discount_type(self):
        return DiscountType.RATE

    @property
    def is_valid_promotion_date(self):
        """Are we between promtoion start and end?"""
        today = tznow().date()
        promotion_start = self.promotion.created.date()
        promotion_end = promotion_start + timedelta(
            days=self.promotion.promotion_duration,
        )

        return promotion_start <= today <= promotion_end

    @property
    def is_valid_booking_date(self):
        """Is booking in prmotion range?"""
        if not self.booking['booked_from']:
            return False
        booking_start = self.promotion.booking_start
        booking_end = self.promotion.booking_end
        booking_date = self.booking['booked_from'].date()

        if booking_end:
            return booking_start <= booking_date <= booking_end
        return booking_start <= booking_date

    @property
    def is_variant_promoted(self):
        if not hasattr(self.variant, 'id'):
            return None

        booking = self.booking or self.instance
        variant_id = self.variant.id

        service_variant_combo_parent_id = safe_get(
            booking, ['service_data', 'service_variant_combo_parent_id']
        ) or safe_get(booking, ['combo_parent', 'service_variant_id'])
        if service_variant_combo_parent_id:
            variant_id = service_variant_combo_parent_id

        return variant_id in self.promotion.promotion_options.get('service_variant_ids', [])

    def from_request(self, subbooking):
        # pylint: disable=too-many-boolean-expressions
        self.booking = subbooking
        self.variant = self.booking.get('service_variant', None)

        if (
            self.promotion.active
            and self.booking
            and self.variant
            and self.variant_price is not None
            and self.is_variant_promoted
            and self.is_valid_promotion_date
            and self.is_valid_booking_date
            and self.rate
        ):
            return self.result

        return None


class LastMinutePromotion(Promotion):
    @property
    def rate(self):
        return Decimal(self.promotion.promotion_options.get('discount_rate', 0))

    @property
    def discount_type(self):
        return DiscountType.RATE

    @property
    def is_variant_promoted(self):
        if not hasattr(self.variant, 'id'):
            return None

        booking = self.booking or self.instance
        variant_id = self.variant.id

        service_variant_combo_parent_id = safe_get(
            booking, ['service_data', 'service_variant_combo_parent_id']
        ) or safe_get(booking, ['combo_parent', 'service_variant_id'])
        if service_variant_combo_parent_id:
            variant_id = service_variant_combo_parent_id

        return variant_id in self.promotion.promotion_options.get('service_variant_ids', [])

    @property
    def is_valid_time_slot(self):
        if not self.booking['booked_from']:
            return False

        max_time = timedelta(hours=self.promotion.last_minute_hours)
        time_diff = self.booking['booked_from'] - tznow()

        return timedelta() <= time_diff <= max_time

    def from_request(self, subbooking):
        # pylint: disable=too-many-boolean-expressions
        self.booking = subbooking
        self.variant = self.booking.get('service_variant', None)

        if (
            self.promotion.active
            and self.booking
            and self.variant
            and self.variant_price is not None
            and self.is_variant_promoted
            and self.is_valid_time_slot
            and self.rate
        ):
            return self.result
        return None


class HappyHoursPromotion(Promotion):
    @cached_property
    def promoted_variant(self):
        if not self.variant:
            return None

        booking = self.booking or self.instance
        variant_id = self.variant.id

        service_variant_combo_parent_id = safe_get(
            booking, ['service_data', 'service_variant_combo_parent_id']
        ) or safe_get(booking, ['combo_parent', 'service_variant_id'])
        if service_variant_combo_parent_id:
            variant_id = service_variant_combo_parent_id

        variants = self.promotion.promotion_options.get('service_variants', {})
        for variant in variants:
            if int(variant['service_variant_id']) == variant_id:
                return variant
        return None

    @property
    def discount_type(self):
        if self.promoted_variant:
            return self.promoted_variant.get('discount_type', None)
        return None

    @property
    def rate(self):
        if self.promoted_variant:
            return Decimal(self.promoted_variant.get('discount_amount', 0))
        return Decimal(0)

    @property
    def is_valid_day_of_week(self):
        if not self.booking['booked_from']:
            return False
        booked_week_day = get_week_day(self.booking['booked_from'])
        promotion_week_day = self.promotion.happy_hours_week_day

        return booked_week_day == promotion_week_day

    @property
    def is_valid_time_slot(self):
        if not self.booking['booked_from']:
            return False

        hour_from = parse_time(self.promoted_variant['hour_from'])
        hour_till = parse_time(self.promoted_variant['hour_till'])
        booking_hour_from = self.booking['booked_from'].time()

        return hour_from <= booking_hour_from < hour_till

    def from_request(self, subbooking):
        # pylint: disable=too-many-boolean-expressions
        self.booking = subbooking
        self.variant = subbooking.get('service_variant', None)
        try:
            del self.promoted_variant
        except AttributeError:
            pass

        if (
            self.promotion.active
            and self.booking
            and self.variant
            and self.variant_price is not None
            and self.promoted_variant
            and self.is_valid_day_of_week
            and self.is_valid_time_slot
            and self.rate
        ):
            return self.result
        return None

    def from_instance(self, instance):
        result = super().from_instance(instance)
        try:
            del self.promoted_variant
        except AttributeError:
            pass

        return result


# endregion promotion objects


class ServicePromotionResolver:
    def __init__(self, business, who=None, promotions_mapping=None):
        self._mapping = promotions_mapping or PROMOTION_MAPPING
        self.business = business
        self.instance = None
        self.bci = None
        self.who = who

    def _promotion(self, promotion_definition, promotion_type=None):
        """Creates promotion instance using promotion_mapping.

        :param promotion_definition: ServicePromotion object
        :param promotion_type: Override promotion type
        :return: promotion instance or None
        """
        _promo_class = self._mapping.get(
            promotion_type or getattr(promotion_definition, 'type', None)
        )

        if _promo_class:
            return _promo_class(self.business, promotion_definition)

        return None

    @property
    def _client_discount(self):
        """Clients discount promotion."""
        if not self.bci:
            return None
        return ClientDiscount(self.business, self.bci)

    @property
    def promotions(self):
        """List of promotion instances with BCI discount."""
        if self.who and self.who != WhoMakesChange.CUSTOMER:
            return [self._client_discount]

        business_promotions = self.business.service_promotions.filter(active=True)

        return [self._promotion(p) for p in business_promotions if p.type in self._mapping] + [
            self._client_discount
        ]

    @property
    def is_resolved_as_client_discount(self) -> bool:
        type_ = self.instance.resolved_promotion_type
        return self.instance and type_ == SERVICE_VARIANT_CLIENT_DISCOUNT

    def from_data(self, data, bci):
        self.bci = bci

        if combo_children := safe_get(data, ['combo_children']):
            discounts = []
            for promotion in self.promotions:
                if not promotion:
                    continue

                if not promotion.from_request(data):
                    continue

                combo_children_results = []
                combo_children_promotions = []
                for child_data in combo_children:
                    child_promotion = promotion.copy()
                    combo_children_results.append(child_promotion.from_request(child_data))
                    combo_children_promotions.append(child_promotion)

                discounts.append(
                    PromotionResolverResult.merge(
                        combo_children_results,
                        promotion,
                        combo_children_promotions=combo_children_promotions,
                    )
                )
        else:
            discounts = [
                PromotionResolverResult(promotion.from_request(data), promotion)
                for promotion in self.promotions
                if promotion
            ]

        try:
            winner = min(
                (d for d in discounts if d.result),
                key=lambda x: x.promotion.discounted_price,
            )

        except ValueError:
            winner = PromotionResolverResult()

        return winner

    @property
    def is_valid_resolved_promotion(self) -> bool:
        return self.instance.resolved_promotion_type in SERVICE_PROMOTION_TYPES

    def from_instance(
        self,
        instance: SubBooking,
        bci: t.Optional[BusinessCustomerInfo] = None,
    ) -> PromotionResolverResult:
        """Best customers promotion (lowest price) using SubBooking instance.

        :param instance: BookingPromotion object
        :param bci: BusinessClient object
        :return: namedtuple with result and promotion

        """
        self.instance = instance
        self.bci = bci or self.instance.get_bci_to_discount()

        can_resolve_promotion = self.is_valid_resolved_promotion or self.bci

        if not can_resolve_promotion:
            return PromotionResolverResult()

        can_be_resolved_as_client_discount = (
            self.is_resolved_as_client_discount
            or not self.instance.resolved_promotion_type
            or not self.instance.service_variant
        )

        if can_be_resolved_as_client_discount:
            promotion = self._client_discount
        else:
            promotion = self._promotion(instance.resolved_promotion)

        unpaid_and_client_discount = can_be_resolved_as_client_discount and not instance.paid

        if not promotion:
            result = PromotionResolverResult()
        elif instance.combo_children:
            combo_children_promotions = []
            combo_children_results = []
            for child in instance.combo_children:
                child_promotion = promotion.copy()
                child_promotion_result: dict = (
                    child_promotion.from_request(child)
                    if unpaid_and_client_discount
                    else child_promotion.from_instance(child)
                )
                combo_children_promotions.append(child_promotion)
                combo_children_results.append(child_promotion_result)
                setattr(
                    child,
                    '_service_promotion',
                    PromotionResolverResult(child_promotion_result, child_promotion),
                )

            if unpaid_and_client_discount:
                promotion.from_request(instance)
            else:
                promotion.from_instance(instance)

            result = PromotionResolverResult.merge(
                combo_children_results,
                promotion,
                combo_children_promotions=combo_children_promotions,
            )
        else:
            result_ = (
                promotion.from_request(instance)
                if unpaid_and_client_discount
                else promotion.from_instance(instance)
            )
            result = PromotionResolverResult(result_, promotion)

        setattr(instance, '_service_promotion', result)

        return result

    @staticmethod
    def update_booking_promotion(subbooking, promotion_result: PromotionResolverResult) -> None:
        """Saves subbooking service promotion.

        We cannot assing ClientDiscount to resolved_promotion.

        :type subbooking: dict or webapps.booking.SubBooking instance
        :type promotion_result: webapps.business.PromotionResolverResult

        """
        if promotion_result.promotion:
            resolved_discount = promotion_result.promotion.discount_rate
            resolved_price = promotion_result.promotion.discounted_price
            resolved_promotion_type = promotion_result.promotion.promotion.type
            if resolved_promotion_type != SERVICE_VARIANT_CLIENT_DISCOUNT:
                resolved_promotion = promotion_result.promotion.promotion
            else:
                resolved_promotion = None
        else:
            resolved_discount = None
            resolved_price = None
            resolved_promotion = None
            resolved_promotion_type = None

        if isinstance(subbooking, dict):
            subbooking.update(
                {
                    'resolved_discount': resolved_discount,
                    'resolved_price': resolved_price,
                    'resolved_promotion': resolved_promotion,
                    'resolved_promotion_type': resolved_promotion_type,
                }
            )
            subbooking['_service_promotion'] = promotion_result
        else:
            subbooking.resolved_discount = resolved_discount
            subbooking.resolved_price = resolved_price
            subbooking.resolved_promotion = resolved_promotion
            subbooking.resolved_promotion_type = resolved_promotion_type
            setattr(subbooking, '_service_promotion', promotion_result)

        combo_children = safe_get(subbooking, ['combo_children'])
        if combo_children and promotion_result.combo_children_promotions:
            for child, child_promotion in zip(
                safe_get(subbooking, ['combo_children']), promotion_result.combo_children_promotions
            ):
                ServicePromotionResolver.update_booking_promotion(
                    child, PromotionResolverResult(child_promotion.result, child_promotion)
                )

    def __repr__(self):
        return f'<{self.__class__.__name__}(business={self.business}, who={self.who})>'


class ServicePromotionListing:
    # pylint: disable=invalid-name, too-many-arguments
    # pylint: disable=unused-variable, unused-argument
    # pylint: disable=too-many-return-statements

    def __init__(self, business):
        """
        :param business: Business instance with services and promotions
               prefetched
               - non active services already filtered out!
               - promotions sorted by -created to get newest active
                 (should be one!)
        """

        self.business = business

    def get_variant_promotion(self, variant):
        variant = self.variants_dict.get(variant.id)
        if not variant:
            return None

        for promo in self.promotions_with_conditions:
            if variant.id in (promo['service_variant_ids'] or [variant.id]):
                return {
                    'rate': promo['rate'],
                    'discount_amount': promo['discount_amount'],
                    'discount_type': promo['discount_type'],
                    'constant': promo['constant'],
                    'price': get_discount_price_dict(
                        variant.service_price.value,
                        variant.service_price.price_type,
                        promo['discount_amount'],
                        promo['discount_type'],
                    ),
                }

        return None

    def get_variant_latest_prices(self, variant_id):
        return self.latest_prices.get(variant_id, [])

    @cached_property
    def latest_prices(self) -> dict[int, list]:
        start_date = tznow() - timedelta(days=30)
        return get_service_latest_prices(self.discountable_variants_set, start_date)

    @cached_property
    def _services(self):
        if 'services' in getattr(self.business, '_prefetched_objects_cache', {}):
            return list(
                self.business._prefetched_objects_cache['services']
            )  # pylint: disable:protected-access
        return self.business.services.active_service()

    @cached_property
    def discountable_variants_set(self):
        '''Ids of all variants with price'''
        return {
            sv.id
            for s in self._services
            for sv in s.active_variants
            if sv.service_price.price_type in PriceType.has_price()
        }

    @cached_property
    def non_discountable_variants_set(self):
        return {
            sv.id
            for s in self._services
            for sv in s.active_variants
            if sv.service_price.price_type not in PriceType.has_price()
        }

    @cached_property
    def variants_dict(self):
        '''Discountable variants by id'''
        return {
            sv.id: sv
            for s in self._services
            for sv in s.active_variants
            if sv.service_price.price_type in PriceType.has_price()
        }

    @cached_property
    def promotions(self):
        if 'service_promotions' in getattr(self.business, '_prefetched_objects_cache', {}):
            return list(
                self.business._prefetched_objects_cache['service_promotions']
            )  # pylint: disable:protected-access
        return self.business.service_promotions.filter(active=True).order_by('type', '-created')

    @cached_property
    def promotions_with_conditions(self):
        return sorted(
            (
                _f
                for _f in [
                    self.get_flash_sale(
                        self.promotions,
                        self.discountable_variants_set,
                        self.non_discountable_variants_set,
                    ),
                    self.get_last_minute(
                        self.promotions,
                        self.discountable_variants_set,
                        self.non_discountable_variants_set,
                    ),
                ]
                + self.get_happy_hours(
                    self.promotions,
                    self.variants_dict,
                    self.discountable_variants_set,
                )
                if _f
            ),
            key=lambda x: x['rate'],
            reverse=True,
        )

    @classmethod
    def get_flash_sale(cls, promotions, discountable_variants_set, non_discountable_variants_set):
        try:
            promo = next(
                iter(
                    filter(
                        lambda x: x.type == SERVICE_VARIANT_FLASH_SALE,
                        promotions,
                    )
                )
            )
        except StopIteration:
            return None

        opts = promo.promotion_options
        variants = opts['service_variant_ids']
        today = promo.business.tznow.date()

        return {
            'rate': float(opts['discount_rate']),
            'discount_amount': float(opts['discount_rate']),
            'discount_type': DiscountType.RATE,
            'service_variant_ids': (
                variants
                if (set(variants) - non_discountable_variants_set) != discountable_variants_set
                else []
            ),
            'constant': (promo.booking_start <= today and promo.booking_end is None),
        }

    @classmethod
    def get_last_minute(cls, promotions, discountable_variants_set, non_discountable_variants_set):
        try:
            promo = next(
                iter(
                    filter(
                        lambda x: x.type == SERVICE_VARIANT_LAST_MINUTE,
                        promotions,
                    )
                )
            )
        except StopIteration:
            return None

        opts = promo.promotion_options
        variants = opts['service_variant_ids']

        return {
            'rate': float(opts['discount_rate']),
            'discount_amount': float(opts['discount_rate']),
            'discount_type': DiscountType.RATE,
            'service_variant_ids': (
                variants
                if (set(variants) - non_discountable_variants_set) != discountable_variants_set
                else []
            ),
            'constant': False,
        }

    @classmethod
    def get_happy_hours(cls, promotions, variants_dict, discountable_variants_set):
        promotions = [x for x in promotions if x.type == SERVICE_VARIANT_HAPPY_HOURS]

        if not promotions:
            return []

        # group promos by service_variant
        variants = defaultdict(list)
        for promo in promotions:
            for p in promo.promotion_options['service_variants']:
                variant = variants_dict.get(p['service_variant_id'])
                if not variant:
                    continue
                service_price = variant.service_price
                if service_price.value is None:
                    continue
                rate = float(
                    p['discount_amount']
                    if p['discount_type'] == DiscountType.RATE
                    else Decimal(p['discount_amount']) / service_price.value * 100
                )
                variants[p['service_variant_id']].append(
                    (
                        rate,
                        p['discount_type'],
                        p['discount_amount'],
                        p['service_variant_id'],
                    )
                )

        # get best promo for each service variant
        max_promo_by_service = [max(v) for v in list(variants.values())]

        # group service variants with the same promo
        group_promo = sorted(
            [(k, list(v)) for k, v in groupby(sorted(max_promo_by_service), lambda x: x[:2])],
            reverse=True,
        )
        happy_hours = [
            {
                'constant': False,
                'rate': k[0],
                'discount_type': k[1],
                'discount_amount': float(v[0][2]),
                'service_variant_ids': (
                    [p[3] for p in v] if {p[3] for p in v} != discountable_variants_set else []
                ),
            }
            for k, v in group_promo
        ]
        return happy_hours

    @classmethod
    def apply_client_discount(cls, service_categories, client_discount):
        """Applies customer discount to service prices.

        :param service_categories: Service categories as in Business Document
            Hit from elasticsearch
        :param client_discount: Discount from BusinessCustomerInfo
        :return: service categories with possibly overwritten 'promotions'
        field. For example:
        [{
            "id": 248,
            "services": [{
                "id": 2381,
                "variants": [{
                    "id": 238181,
                    "price": 12,
                    "promotion": {
                        "rate": 20,
                        "constant": True,
                        "discount_type": "R",
                        "discount_amount": 20,
                        "price": {
                            "price": 9.6,
                            "formatted_price": "$9.60"
                        }
                    }
                }]
            }]
        }]
        """

        for category in service_categories:
            cls.apply_client_discount_flat_services(category.services, client_discount)
        return service_categories

    @staticmethod
    def apply_client_discount_flat_services(services, client_discount):
        for service in services:
            for variant in service.variants:
                if (
                    hasattr(variant, 'promotion')
                    and variant.promotion
                    and variant.promotion.rate > client_discount
                ):
                    continue

                if variant.type not in PriceType.has_price():
                    continue

                variant.promotion = AttrDict(
                    {
                        'rate': client_discount,
                        'constant': True,
                        'discount_type': DiscountType.RATE,
                        'discount_amount': client_discount,
                        'price': get_discount_price_dict(
                            variant.price,
                            variant.type,
                            client_discount,
                            DiscountType.RATE,
                        ),
                    }
                )


class LegacyServicePromotionSlots:
    # pylint: disable=invalid-name, too-many-arguments, too-many-positional-arguments
    # pylint: disable=unused-variable, unused-argument
    # pylint: disable=too-many-return-statements

    def __init__(
        self,
        start_date,
        end_date,
        client_discount=None,
        service_variant=None,
        scope_service_variant: 'LegacyPromotionServiceVariant' = None,
        business_id=None,
    ):
        validate_business_context(business_id)

        self.scope_service_variant = scope_service_variant
        if service_variant is not None:
            self.scope_service_variant = LegacyPromotionServiceVariant.from_service_variant(
                service_variant
            )

        self.start_date = start_date
        self.end_date = end_date
        self.client_discount = client_discount
        self.business_id = business_id

        self._slots = None

    def apply(self, time_slots, only_promotional=False):
        if self._slots is None:
            self._slots = dict(self.slots())
        interval = relativedelta_total_seconds(self.scope_service_variant.interval)
        return self.time_slots_and_promotions(
            time_slots,
            self._slots,
            interval,
            self.scope_service_variant,
            only_promotional=only_promotional,
        )

    @classmethod
    def time_slots_and_promotions(
        cls,
        time_slots,
        promotion_slots,
        interval,
        variant,
        only_promotional=False,
    ):
        result_slots = []
        promotions = {}

        for date_, slots in time_slots:
            d_slots, d_promotions = cls.day_slots_and_promotions(
                slots, promotion_slots[date_], interval, variant, only_promotional
            )
            result_slots.append((date_, d_slots))
            promotions[date_] = d_promotions
        return result_slots, promotions

    @classmethod
    def day_slots_and_promotions(
        cls,
        slots,
        promotions,
        interval,
        variant,
        only_promotional=False,
    ):
        if not promotions or not slots or variant.type not in PriceType.has_price():
            return ([], []) if only_promotional else (slots, [])

        r_slots = []
        r_promos = []

        for slot in slots:
            slot = TimeSlot(*slot)
            _slots = cls.fit_promotions_into_slot(promotions, slot, interval)

            slot_generator = cls.promo_slots_with_rates(_slots, promotions)
            if only_promotional:
                slot_generator = filter(itemgetter(1), slot_generator)

            for _s, _p in slot_generator:
                r_slots.append(_s)
                r_promos.append(_p)

        return r_slots, r_promos

    @classmethod
    def fit_promotions_into_slot(cls, promotions, slot, interval):
        minute = timedelta(minutes=1)

        _pits = [
            slot.start,
            *(_p for _p in cls.points_in_time(promotions) if slot.start < _p < slot.end),
            slot.end + minute,
        ]

        slots = [
            cls.adjust_with_interval((_start, _end - minute), interval, slot)
            for _start, _end in zip(_pits[:-1], _pits[1:])
        ]

        return slots

    @classmethod
    def promo_slots_with_rates(cls, slots, promotions):
        """Iterates over slots and matches best promotion."""
        for slot in slots:
            rates = list(
                filter(
                    None,
                    (cls.is_promo_in_slot(promo, slot) for promo in promotions),
                )
            )

            if rates:
                max_rate = max(rates, key=lambda x: x['_discount_rate'])
                rate = max_rate['discount_label']
            else:
                rate = None

            yield slot, rate

    @staticmethod
    def points_in_time(promotions):
        """Creates list of points"""
        result = set()
        for p_range, _ in promotions:
            result.add(p_range.start)
            result.add(p_range.end)

        return sorted(result)

    @staticmethod
    def is_promo_in_slot(promotion, slot):
        """Checks if given slot fits into promotion."""
        p_range, promo = promotion
        p_range = PromotionRange(*p_range)

        if (
            slot.start <= p_range.start < slot.end < p_range.end
            or p_range.start <= slot.start < slot.end <= p_range.end
        ):
            return promo
        return None

    @classmethod
    def adjust_with_interval(cls, slot, interval, original):
        """
        Adjusts slot start, end to interval

        :param slot: (start: datetime, end: datetime) to be adjusted
        :param interval: service variant time_slot_interval in seconds: int
        :param original: original_slot - the slot is identical with original
            or comes from spliting original

        start of original is the reference point to applying interval
        """
        if not slot:
            return slot

        start, end = slot
        o_start, o_end = original
        if start != o_start:
            # adjust forward
            diff = (start - o_start).total_seconds() % interval
            if diff != 0:
                start = start + timedelta(seconds=interval - diff)
        if end != o_end:
            diff = (end - o_start).total_seconds() % interval
            if diff != 0:
                end = end - timedelta(seconds=diff)
        return TimeSlot(start, end)

    def slots(self):
        # pylint: disable=possibly-used-before-assignment
        flash_sale, last_minute, happy_hours = self.promotions_with_conditions()

        if flash_sale:
            fs_rate, fs_range, fs_promotion = flash_sale
            if self.client_discount and self.client_discount >= fs_rate:
                flash_sale = None

        if last_minute:
            lm_rate, lm_range, lm_promotion = last_minute
            lm_date = lm_range[0].date()
            if self.client_discount and lm_rate <= self.client_discount:
                last_minute = None

        if happy_hours is None:
            happy_hours = {}

        cd = self.get_client_discount()

        for date_ in lib.ranges.date_range_gen(self.start_date, self.end_date):
            hh = happy_hours.get(date_.isoweekday() % 7)
            if hh:
                h_rate, h_range, h_promotion = hh
                hh = (
                    h_rate,
                    tuple(make_aware(datetime.combine(date_, x)) for x in h_range),
                    h_promotion,
                )
                if self.client_discount and h_rate <= self.client_discount:
                    hh = None

            fs = None
            if flash_sale and (not fs_range or fs_range[0] <= date_ <= fs_range[1]):
                fs = (fs_rate, None, fs_promotion)

            lm = None
            if last_minute and lm_date == date_:
                lm = last_minute

            yield (date_, self.day_slots([cd, fs, lm, hh], date_))

            # after the first day last_minute was applied it have no more chance
            if lm:
                last_minute = None
            # clear after range end
            if flash_sale and fs_range and fs_range[1] == date_:
                flash_sale = None

    def day_slots(self, promotions, date_):
        promotions = sorted(
            (_f for _f in promotions if _f),
            reverse=True,
            # compare by rate only
            key=lambda promotion: promotion[0],
        )

        slots = []
        for rate, p_range, promo in promotions:
            if not p_range:
                promo_ranges = lib.ranges.complement_ranges([r for r, p in slots], date_=date_)
            else:
                promo_ranges = lib.ranges.subtract_ranges([p_range], [r for r, p in slots])
            slots += [(TimeSlot(*r), promo) for r in promo_ranges]
            if not p_range:
                break
        return sorted(
            # sort by time slots and _discount_rates
            slots,
            key=lambda slot_and_promo: (slot_and_promo[0], slot_and_promo[1]['_discount_rate']),
        )

    def promotions_with_conditions(self):
        promotions = self.get_promotions()
        if not promotions:
            return None, None, None

        args = [
            promotions,
            self.scope_service_variant.id,
            self.start_date,
            self.end_date,
            self.scope_service_variant.price,
            self.scope_service_variant.type,
        ]

        flash_sale = self.get_flash_sale(*args)
        last_minute = self.get_last_minute(*args)
        happy_hours = self.get_happy_hours(*args)

        return flash_sale, last_minute, happy_hours

    def get_promotions(self):
        # non discountable variant
        if self.scope_service_variant.type not in PriceType.has_price():
            return []

        promotions = list(
            ServicePromotion.objects.filter(business_id=self.business_id, active=True).order_by(
                'type', '-created'
            )
        )

        return promotions

    @classmethod
    def get_flash_sale(
        cls,
        promotions,
        service_variant_id,
        start_date,
        end_date,
        variant_price,
        variant_type,
    ):
        try:
            promo = next(
                iter(
                    filter(
                        lambda x: x.type == SERVICE_VARIANT_FLASH_SALE,
                        promotions,
                    )
                )
            )
            opts = promo.promotion_options
        except StopIteration:
            return None

        today = localdate()
        if not promo.promotion_start <= today <= promo.promotion_end:
            return None

        if service_variant_id not in opts['service_variant_ids']:
            return None

        promo_start = promo.booking_start
        if promo_start:
            if end_date < promo_start:
                return None
        else:
            promo_start = start_date

        promo_end = promo.booking_end
        if promo_end:
            if promo_end < start_date:
                return None
        else:
            promo_end = end_date

        promo_range = (
            None
            if promo_start <= start_date and end_date <= promo_end
            else (promo_start, promo_end)
        )
        rate = float(opts['discount_rate'])
        promotion = {
            'discount_label': get_discount_label(opts['discount_rate']),
            'discount_amount': opts['discount_rate'],
            'discount_type': DiscountType.RATE,
            'price': get_discount_price_dict(
                variant_price, variant_type, opts['discount_rate'], DiscountType.RATE
            ),
            '_discount_rate': rate,
        }
        return rate, (promo_range), promotion

    @classmethod
    def get_last_minute(
        cls,
        promotions,
        service_variant_id,
        start_date,
        end_date,
        variant_price,
        variant_type,
    ):
        try:
            promo = next(
                iter(
                    filter(
                        lambda x: x.type == SERVICE_VARIANT_LAST_MINUTE,
                        promotions,
                    )
                )
            )
            opts = promo.promotion_options
        except StopIteration:
            return None

        now = localtime()

        if service_variant_id not in opts['service_variant_ids']:
            return None

        if not start_date <= now.date() <= end_date:
            return None

        rate = float(opts['discount_rate'])
        sale_end = now + timedelta(hours=promo.last_minute_hours)
        promotion = {
            'discount_label': get_discount_label(opts['discount_rate']),
            'discount_amount': opts['discount_rate'],
            'discount_type': DiscountType.RATE,
            'price': get_discount_price_dict(
                variant_price, variant_type, opts['discount_rate'], DiscountType.RATE
            ),
            '_discount_rate': rate,
        }

        return rate, (now, sale_end), promotion

    @classmethod
    def get_happy_hours(
        cls,
        promotions,
        service_variant_id,
        start,
        end,
        variant_price,
        variant_type,
    ):
        promotions = list(x for x in promotions if x.type == SERVICE_VARIANT_HAPPY_HOURS)

        if not promotions:
            return None

        # group by weekday, get variant relevant promo
        day_promotion = {}
        for promo in promotions:
            try:
                filtered_promotions = list(
                    filter(
                        lambda x: x['service_variant_id'] == service_variant_id,
                        promo.promotion_options['service_variants'],
                    )
                )
                opts = next(iter(filtered_promotions))
            except StopIteration:
                continue

            sale_range = [parse_time(x) for x in [opts.pop('hour_from'), opts.pop('hour_till')]]
            rate = (
                float(opts['discount_amount'])
                if opts['discount_type'] == DiscountType.RATE
                else float(opts['discount_amount']) / float(variant_price) * 100
            )
            del opts['service_variant_id']
            opts['price'] = get_discount_price_dict(
                variant_price, variant_type, opts['discount_amount'], opts['discount_type']
            )
            opts['discount_label'] = get_discount_label(
                opts['discount_amount'], opts['discount_type']
            )
            opts['_discount_rate'] = rate

            day_promotion[promo.happy_hours_week_day] = (rate, sale_range, opts)

        return day_promotion

    def get_client_discount(self):
        if self.client_discount:
            rate = self.client_discount
            promotion = {
                'discount_label': get_discount_label(rate),
                'discount_amount': rate,
                'discount_type': DiscountType.RATE,
                'price': get_discount_price_dict(
                    self.scope_service_variant.price,
                    self.scope_service_variant.type,
                    rate,
                    DiscountType.RATE,
                ),
                # TODO add test for this discount
                '_discount_rate': rate,
            }
            return rate, None, promotion
        return None


PROMOTION_MAPPING = {
    SERVICE_VARIANT_LAST_MINUTE: LastMinutePromotion,
    SERVICE_VARIANT_FLASH_SALE: FlashSalePromotion,
    SERVICE_VARIANT_HAPPY_HOURS: HappyHoursPromotion,
    SERVICE_VARIANT_CLIENT_DISCOUNT: ClientDiscount,
}

SERVICE_PROMOTION_NAMES = [str(v) for v in Promotion.names.values()]
SERVICE_PROMOTION_TYPES = list(Promotion.names.keys())


# for future multibooking promotions support:


@dataclass
class PromotionServiceVariant:
    service_variant_id: int
    price: Decimal = None
    type: PriceType = None


@dataclass
class PromotionScope:
    business_id: int
    service_variants: tuple[PromotionServiceVariant, ...]  # noqa
    start_date: date
    end_date: date
    client_discount: int = 0
    only_promotions: bool = False

    def is_aplicable(self):
        if len(self.service_variants) > 1:
            raise NotImplementedError()

        return self.service_variants[0].type.has_price()


@dataclass
class LegacyPromotionServiceVariant:
    id: int  # pylint: disable=invalid-name
    interval: relativedelta
    price: Decimal = None
    type: PriceType = None

    @classmethod
    def from_service_variant(cls, service_variant):
        return cls(
            id=service_variant.id,
            price=service_variant.price,
            type=service_variant.type,
            interval=service_variant.time_slot_interval,
        )
