# pylint: disable=redefined-outer-name

import pytest
from model_bakery import baker

from webapps.business._admin.forms import ResourceModelForm
from webapps.business.baker_recipes import staffer_recipe
from webapps.business.models import Resource
from webapps.user.enums import AuthOriginEnum


@pytest.mark.django_db
class TestResourceForm:

    def _get_resource_model_form(self, business, user, resource):
        return ResourceModelForm(
            {
                'business': business.id,
                'name': 'test_name',
                'type': 'S',
                'order': 0,
                'staff_user': user.id,
                'staff_email': user.email,
                'version': 1,
            },
            instance=resource,
        )

    def test_reject_staff_user_if_assigned_to_different_resource(
        self,
        user,
        business,
    ):
        resource = baker.make('business.Resource', business=business)
        # conflicting resource
        baker.make('business.Resource', business=business, staff_user=user)
        form = self._get_resource_model_form(business, user, resource)

        assert not form.is_valid()

    def test_reject_staff_user_if_owner_in_the_same_business(self, user):
        owned_business = baker.make('business.Business', owner=user)
        resource = baker.make('business.Resource', business=owned_business)
        # conflicting resource
        baker.make('business.Resource', business=owned_business, staff_user=user)
        form = self._get_resource_model_form(owned_business, user, resource)

        assert not form.is_valid()

    def test_accept_the_same_staff_user(self, user, business):
        resource = baker.make(
            'business.Resource', business=business, staff_user=user, staff_email=user.email
        )
        form = self._get_resource_model_form(business, user, resource)

        assert form.is_valid()

    def test_accept_new_staff_user(self, user, business):
        resource = baker.make('business.Resource', business=business)
        form = self._get_resource_model_form(business, user, resource)

        assert form.is_valid()

    def test_accept_staff_user_if_owner_in_other_business(
        self,
        user,
        business,
    ):
        baker.make('business.Business', owner=user)
        resource = baker.make('business.Resource', business=business)
        form = self._get_resource_model_form(business, user, resource)

        assert form.is_valid()

    def test_change_staff_access_level_logout_user(self, user, business):
        resource = baker.make(
            'business.Resource',
            business=business,
            staff_user=user,
            staff_email=user.email,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )
        session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        session_user = session.get_user()
        assert user == session_user

        form = ResourceModelForm(
            {
                'business': business.id,
                'name': 'test_name',
                'type': 'S',
                'order': 0,
                'staff_user': user.id,
                'staff_email': user.email,
                'staff_access_level': Resource.STAFF_ACCESS_LEVEL_RECEPTION,
                'version': 1,
            },
            instance=resource,
        )
        _valid = form.is_valid()
        form.save()

        session_user = session.get_user()
        assert session_user is None

    def test_disable_last_available_resource(self, user, business):
        resource = baker.make(
            Resource,
            business=business,
            staff_user=user,
            staff_email=user.email,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )

        form = ResourceModelForm(
            {
                'business': business.id,
                'name': 'test_name',
                'type': 'S',
                'order': 0,
                'staff_user': user.id,
                'staff_email': user.email,
                'visible': False,
                'version': 1,
            },
            instance=resource,
        )

        _valid = form.is_valid()
        form.save()

        business.refresh_from_db()
        assert business.visible is False

    def test_edit_available_resource_should_not_disable_business(self, user, business):
        staffer = staffer_recipe.make(business=business, staff_user=user)

        form = ResourceModelForm(
            {
                'business': business.id,
                'name': 'test_name',
                'type': 'S',
                'order': 0,
                'staff_user': user.id,
                'staff_email': user.email,
                'visible': True,
                'version': 1,
            },
            instance=staffer,
        )

        assert form.is_valid()
        form.save()

        business.refresh_from_db()
        assert business.visible is True
