import elasticsearch_dsl as dsl
from django.db.models.signals import post_save
from rest_framework import serializers

from lib.elasticsearch import fields as f
from lib.elasticsearch.document import Document
from lib.elasticsearch.serializers import DefaultZ<PERSON>FloatField
from lib.rivers import River, bump_on_signal
from service.signals import resource_reorder_signal
from webapps.business.models import Business, Resource
from webapps.public_partners.models import ResourceMetadata
from webapps.public_partners.ports import PartnerAppDataMixin
from webapps.reviews.tools import rank_to_stars


_DEFAULT_RESOURCE_POPULARITY = 0


class BusinessSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = ('id', 'name')


class ResourceDocumentSerializer(PartnerAppDataMixin, serializers.ModelSerializer):
    class Meta:
        model = Resource
        fields = (
            'id',
            'name',
            'type',
            'visible',
            'active',
            'description',
            'staff_email',
            'staff_user_exists',
            'photo',
            'photo_url',
            'order',
            'popularity',
            'reviews_rank_score',
            'reviews_stars',
            'business',
            '_is_active',
            'position',
            'partner_app_data',
        )

    reviews_rank_score = DefaultZeroFloatField()
    reviews_stars = serializers.SerializerMethodField()
    staff_user_exists = serializers.SerializerMethodField()
    photo_url = serializers.CharField(source='photo.full_url')
    popularity = serializers.SerializerMethodField()
    business = BusinessSerializer()
    _is_active = serializers.SerializerMethodField()

    @staticmethod
    def get_reviews_stars(instance):
        return rank_to_stars(instance.reviews_rank_avg)

    @staticmethod
    def get_staff_user_exists(instance):
        return bool(instance.staff_user_id)

    @staticmethod
    def get__is_active(instance):
        return (
            instance.active
            and instance.visible  # and
            # instance.services.filter(
            #     active=True, service_variants__active=True
            # ).exists()
        )

    @staticmethod
    def get_popularity(instance):
        return getattr(instance, 'popularity', _DEFAULT_RESOURCE_POPULARITY)

    def to_representation(self, instance):
        doc = super().to_representation(instance)
        if 'business' not in doc:
            doc['business'] = {'id': instance.business_id}
        return doc


class ResourceDocument(Document):
    class Meta:
        serializer = ResourceDocumentSerializer
        active_records_queryset = Resource.objects.filter(
            type=Resource.STAFF,
            active=True,
            visible=True,
        )
        queryset = Resource.objects.select_related('photo', 'business').filter(type=Resource.STAFF)

        id = 'id'
        routing = 'business.id'
        parent = 'business.id'
        active_record_property = '_is_active'

        es_bulk_size = 100

    @classmethod
    def get_queryset(cls, ids=None):
        queryset = super().get_queryset(ids)

        return queryset.with_partner_app_data(ResourceMetadata)

    id = dsl.Integer(index=True)
    business_id = dsl.Integer(index=True)
    name = f.text_multi
    type = dsl.Keyword(index=False)
    visible = dsl.Boolean(index=True)
    active = dsl.Boolean(index=True)
    description = f.text_multi
    staff_user_exists = dsl.Boolean(index=True)
    staff_email = dsl.Keyword(index=True)
    photo = dsl.Integer(index=False)
    photo_url = f.text_deprecated
    order = dsl.Integer(index=True)
    popularity = dsl.Integer(index=True)  # deprecated
    reviews_rank_score = dsl.Float(index=True)
    business = dsl.Object(enabled=False)
    position = dsl.Keyword(index=True)
    partner_app_data = dsl.Object(dynamic=True)


bump_on_signal(River.RESOURCE, post_save, 'business.Resource', 'id')
# active Services no longer needed for ResourceDocument to be active
# bump_on_signal(River.RESOURCE, m2m_changed, Resource.services.through, 'id')
bump_on_signal(River.RESOURCE, resource_reorder_signal, 'business.Resource', 'resource_ids')
