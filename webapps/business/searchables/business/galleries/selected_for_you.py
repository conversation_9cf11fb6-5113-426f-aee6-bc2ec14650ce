import random

import elasticsearch_dsl as dsl

from lib.searchables.searchables import FunctionScore, Searchable, V
from webapps.business.searchables.business import BusinessInsertSingleImages
from webapps.business.searchables.business.search_engine import VisibleBusinessSearchable
from webapps.business.searchables.business.sub_searchables import GenderBasedBusinessSearchable

MIN_INSPIRATION_PHOTOS = 4


# pylint: disable=use-dict-literal
class BusinessInsertOnlyCoverImage(Searchable):
    class Meta:
        bool_param = 'must'

    insert_images = dsl.query.Bool(
        must=(
            dsl.query.HasChild(
                type='image',
                query=dsl.query.Term(is_cover_photo=True),
                inner_hits=dict(
                    name='images.cover',
                    size=1,
                    _source=dict(excludes=['join', 'location']),
                    sort=dict(order='asc', created='desc'),
                ),
            ),
        )
    )


class BusinessInsertCoverAndInspirationsImages(Searchable):
    insert_images = dsl.query.Bool(
        must=[
            dsl.query.HasChild(
                type='image',
                query=dsl.query.Term(is_cover_photo=True),
                inner_hits=dict(
                    name='images.cover',
                    size=1,
                    _source=dict(excludes=['join', 'location']),
                    sort=dict(order='asc', created='desc'),
                ),
            ),
            dsl.query.HasChild(
                type='image',
                query=dsl.query.Term(category='inspiration'),
                inner_hits=dict(
                    name='images.inspiration',
                    size=MIN_INSPIRATION_PHOTOS,
                    _source=dict(excludes=['join', 'location']),
                    sort=dict(order='asc', created='desc'),
                ),
                min_children=MIN_INSPIRATION_PHOTOS,
            ),
        ]
    )


class SelectedForYouSimpleSearchable(Searchable):
    insert_single_images = BusinessInsertOnlyCoverImage()
    business_categories = dsl.query.Terms(business_categories__id=V('business_categories'))
    visible = VisibleBusinessSearchable()
    min_treatments = dsl.query.Range(
        treatment_count=dict(gte=V('min_treatment_count')),
    )
    gender_range = GenderBasedBusinessSearchable()
    location_filter = dsl.query.GeoDistance(
        distance=V('get_location_max_km_distance'),
        business_location__coordinate=V('location_geo'),
    )
    active = dsl.query.Range(active_from=dict(gte=V('get_max_active_days')))
    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
        ],
        score_mode='multiply',
    )

    @staticmethod
    def get_location_max_km_distance(data):
        max_km_distance = data.get('max_km_distance', 50)
        return f'{max_km_distance}km'

    @staticmethod
    def get_max_active_days(data):
        max_active_days = data.get('max_active_days', 300)
        return f'now-{max_active_days}d'


class BaseSelectedForYouSearchable(Searchable):
    """
    Base searchable for Selected For You with common filters and fields.
    Contains all shared logic without any scoring implementation.
    """

    insert_single_images = BusinessInsertSingleImages()
    business_categories = dsl.query.Terms(business_categories__id=V('business_categories'))
    visible = VisibleBusinessSearchable()
    min_treatments = dsl.query.Range(
        treatment_count=dict(gte=V('min_treatment_count')),
    )
    cover_photo = dsl.query.Bool(
        must=dsl.query.HasChild(
            type='image',
            query=dsl.query.Term(is_cover_photo=True),
        )
    )
    gender_range = GenderBasedBusinessSearchable()
    location_filter = dsl.query.GeoDistance(
        distance=V('get_location_max_km_distance'), business_location__coordinate=V('location_geo')
    )
    active = dsl.query.Range(active_from=dict(gte=V('get_max_active_days')))
    excluded = dsl.query.Bool(must_not=dsl.query.Terms(id=V('excluded', default=[])))
    boost = dsl.query.Term(promoted=V('is_boost'))

    @staticmethod
    def get_location_max_km_distance(data):
        max_km_distance = data.get('max_km_distance', 50)
        return f'{max_km_distance}km'

    @staticmethod
    def get_max_active_days(data):
        max_active_days = data.get('max_active_days', 300)
        return f'now-{max_active_days}d'


class SelectedForYouSearchable(BaseSelectedForYouSearchable):
    """
    Selected For You searchable with location and recency-based scoring.

    Scoring: Higher scores for businesses that are recently active and geographically
    closer to the user.
    1. Multiplie exponential decay - activity boost,
    2. Gaussian decay - proximity boost.
    """

    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
        ],
        score_mode='multiply',
    )


class SelectedForYouSearchableV4(BaseSelectedForYouSearchable):
    """
    Enhanced Selected For You searchable with advanced scoring and randomization.

    This version introduces several improvements over the base implementation:

    Scoring:
    1. Activity Boost: Exponential decay based on business activity (weight: 3)
    2. Proximity Boost: Gaussian decay based on distance from user location
    3. Promoted Penalty: Reduces score for promoted businesses (weight: 0.6)
    4. Randomization: Adds controlled randomness to prevent result stagnation (weight: 0.5)
    5. Review Boost: Conditional review count factor with 50% application chance

    Example:
        A business 5km away, active 7 days ago, with 50 reviews, not promoted:
        - Activity score: exp(-7/14) * 3 ≈ 1.8
        - Proximity score: gauss(5km/10km) ≈ 0.78
        - Promoted penalty: 1.0 (no penalty)
        - Random factor: 0.5 (varies per query)
        - Review boost: 1/(1 + 0.1*50) ≈ 0.17 (if applied)

        Final score ≈ 1.8 * 0.78 * 1.0 * 0.5 * 0.17 ≈ 0.12
    """

    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
            # Penalize promoted businesses
            dsl.function.ScoreFunction(
                filter=dsl.query.Term(promoted=True),
                weight=0.6,
            ),
            # Randomization of results
            dsl.function.RandomScore(
                seed=V(lambda data: random.randint(0, 1000000)),
                weight=0.5,
            ),
            # Review scoring function
            dsl.function.ScoreFunction(
                weight=V(lambda data: random.choice([0, 1])),
                function=dsl.function.FieldValueFactor(
                    field='review_count',
                    factor=0.1,
                    modifier='reciprocal',
                    missing=1,
                ),
            ),
        ],
        score_mode='multiply',
    )


class SelectedForYourPortfolioImagesExperimentSearchable(Searchable):
    insert_single_images = BusinessInsertCoverAndInspirationsImages()

    business_categories = dsl.query.Terms(business_categories__id=V('business_categories'))
    visible = VisibleBusinessSearchable()
    min_treatments = dsl.query.Range(
        treatment_count=dict(gte=V('min_treatment_count')),
    )
    gender_range = GenderBasedBusinessSearchable()
    location_filter = dsl.query.GeoDistance(
        distance=V('get_location_max_km_distance'),
        business_location__coordinate=V('location_geo'),
    )
    active = dsl.query.Range(active_from=dict(gte=V('get_max_active_days')))
    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
        ],
        score_mode='multiply',
    )

    @staticmethod
    def get_location_max_km_distance(data):
        max_km_distance = data.get('max_km_distance', 50)
        return f'{max_km_distance}km'

    @staticmethod
    def get_max_active_days(data):
        max_active_days = data.get('max_active_days', 300)
        return f'now-{max_active_days}d'
