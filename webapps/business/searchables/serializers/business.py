# UNFINISHED
import datetime
from urllib.parse import urljoin

from django.conf import settings
from django.utils.translation import gettext as _
from elasticsearch_dsl import AttrDict
from rest_framework import serializers

import lib.tools
from lib.elasticsearch.consts import ESAvailabilityNature, ONE_HOUR
from lib.elasticsearch.hit_serializer import (
    Co<PERSON><PERSON><PERSON><PERSON>,
    HitSerializer,
    ImageThumbnailsHitSerializer,
    Translated<PERSON>ield,
)
from lib.feature_flag.feature import BooksyGiftcardsEnabledFlag
from lib.feature_flag.feature.business import (
    DSABusinessVersumNameFlag,
    LimitServicesBusinessListingWebFlag,
)
from lib.feature_flag.feature.customer import S4UServiceVariantCountFixFlag
from lib.feature_flag.feature.monetisation import EnablePeakHoursFlag
from lib.serializers import safe_get
from lib.tools import format_currency
from webapps.best_of_booksy.cache import get_available_best_of_booksy_awards_badge_map
from webapps.business.business_categories.cache import get_category_or_subcategory_by_id
from webapps.business.consts import (
    TOP_BUSINESS_SERVICES_IDS,
    TOP_FEMALE_SERVICES_IDS,
    TOP_MALE_SERVICES_IDS,
    TOP_SERVICES_KEYS,
)
from webapps.business.elasticsearch.account import BusinessAccountDocument
from webapps.business.elasticsearch.business import BusinessDocument
from webapps.business.elasticsearch.open_hours import BusinessOpenHoursDocument
from webapps.business.elasticsearch.resources import ResourceDocument
from webapps.business.elasticsearch.reviews import ReviewDocument
from webapps.business.enums import PriceType
from webapps.business.omnibus_price import get_omnibus_price_from_latest
from webapps.business.searchables.enum import ResponseTypeEnum
from webapps.business.searchables.serializers import never_none_fields
from webapps.business.searchables.serializers.amenities import (
    AmenitiesHitSerializer,
)
from webapps.business.service_price import ServicePrice
from webapps.business.services.service_variants import VariantsAdapter
from webapps.images.elasticsearch import ImageDocument
from webapps.user.const import Gender
from webapps.images.tools import build_thumbnail_url


# region business help fields
class BusinessPhotoUrlField(serializers.Field):
    def to_representation(self, value):
        url = value
        if not url:
            return None
        return urljoin(settings.MEDIA_URL, url.lstrip('/'))


class PromotionHitSerializer(HitSerializer):
    class Meta:
        fields = [
            'rate',
            'discount_amount',
            'discount_type',
            'constant',
            'price',
        ]

    rate = serializers.FloatField()
    discount_amount = serializers.FloatField()

    discount_type = serializers.CharField()
    constant = serializers.BooleanField()

    price = serializers.SerializerMethodField()

    @staticmethod
    def get_price(instance):
        # instance dict of promotion
        # price is dict with two fields price abd formatted price
        # see
        # webapps.business.service_promotions.get_discount_price_dict
        return {
            # always make suer taht price returned as float
            'price': float(instance['price']['price']),
            'formatted_price': instance['price']['formatted_price'],
        }


class ComboChildHitSerializer(HitSerializer):
    class Meta:
        fields = [
            'id',
            'name',
            'label',
            'service_price',
        ]


class ServiceVariantHitSerializer(HitSerializer):
    class Meta:
        fields = [
            'id',
            'duration',
            'price',
            'type',
            'deposit_tax',
            'prepayment',
            'prepayment_tax',
            'promotion',
            'deposit',
            'label',
            'combo_children',
            'service_price',
            'staffer_id',
            'omnibus_price',
        ]

    promotion = PromotionHitSerializer(required=False)
    combo_children = ComboChildHitSerializer(many=True)
    service_price = serializers.SerializerMethodField()
    omnibus_price = serializers.SerializerMethodField()

    @staticmethod
    def get_service_price(instance) -> str:
        return ServicePrice(instance.get('price'), instance.get('type')).format_for_customer()

    @staticmethod
    def get_omnibus_price(instance) -> str | None:

        if not instance.get('promotion'):
            return None

        omnibus_price = None
        if latest_prices := instance.get('latest_prices', None):
            start_date = lib.tools.tznow() - datetime.timedelta(days=30)
            omnibus_price = get_omnibus_price_from_latest(latest_prices, start_date)

        omnibus_price = omnibus_price or instance.get('price')
        return format_currency(omnibus_price) if omnibus_price else None


# region services
class ServiceHitSerializer(HitSerializer):
    variants = ServiceVariantHitSerializer(many=True)

    class Meta:
        fields = [
            'id',
            'name',
            'active',
            'order',
            'description',
            'description_type',
            'variants',
            'staffer_id',
            # UTT fields
            'treatment',
            'treatment_id',
            'treatment_parent_id',
            'photos',
            'is_online_service',
            'is_traveling_service',
            'addons_available',
            'combo_type',
        ]


class ServiceCategoryHitSerializer(HitSerializer):
    services = ServiceHitSerializer(many=True)

    class Meta:
        fields = ['id', 'name', 'order', 'show_first', 'services']


# endregion services


# region images
class ImageHitSerializer(ImageThumbnailsHitSerializer):
    class Meta:
        document = ImageDocument
        exclude = ('location',)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # always ensure business_id is integer
        if 'business_id' in data:
            data['business_id'] = int(data['business_id'])
        return data


class ImagesHitSerializer(HitSerializer):
    cover = ImageHitSerializer(many=True)
    cover_orig = ImageHitSerializer(many=True)
    logo = ImageHitSerializer(many=True)
    biz_photo = ImageHitSerializer(many=True)
    inspiration = ImageHitSerializer(many=True)

    class Meta:
        fields = ['cover', 'cover_orig', 'logo', 'biz_photo', 'inspiration']

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['biz_photo_count'] = safe_get(instance, ['biz_photo', 'total', 'value'])
        data['biz_photo_per_page'] = 20
        return data


# endregion images


class ResourceHitSerializer(HitSerializer):
    class Meta:
        document = ResourceDocument
        fields = [
            'id',
            'business_id',
            'name',
            'type',
            'visible',
            'active',
            'description',
            'staff_user_exists',
            # EXCLUDED 'staff_email',
            'photo',
            'photo_url',
            'order',
            'reviews_rank_score',
            'business',
            'position',
        ]


class ReviewsHitSerializer(HitSerializer):
    class Meta:
        document = ReviewDocument
        fields = [
            'id',
            'rank',
            'title',
            'review',
            'user',
            'business',
            'reply_content',
            'reply_updated',
            'created',
            'updated',
            'services',
            'staff',
            'source',
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if data.get('user') is None:
            data['user'] = {}
        return data


# region hours
class HourField(serializers.Field):
    def to_representation(self, value):
        return f'{int(value[:2])}:{value[2:]}'


class CustomizationHoursSerializer(serializers.Serializer):
    hour_from = HourField(source='gte')
    hour_till = HourField(source='lte')


class OpenHoursCustomizationHitSerializer(HitSerializer):
    hours = CustomizationHoursSerializer(many=True, source='time_range')

    class Meta:
        document = BusinessOpenHoursDocument
        fields = [
            'date',
            'hours',
        ]


# endregion hours


# region traveling
class TravelingHitSerializer(HitSerializer):
    class Meta:
        fields = ['hide_address', 'traveling_only']


class TravelingDetailsHitSerializer(HitSerializer):
    class Meta:
        fields = [
            'hide_address',
            'traveling_only',
            'price',
            'price_type',
            'distance',
            'policy',
        ]


# endregion traveling
# endregion business help fields


class BusinessHitSerializer(HitSerializer):
    # low availability constants
    # la_scale is a factor required to gain linear time domain score
    # interpretation, although score still need to be squared
    la_lower_bound = ONE_HOUR
    la_upper_bound_one_day = 3 * ONE_HOUR
    la_upper_bound_tree_days = 8 * ONE_HOUR

    never_none_fields = {
        'images': never_none_fields.get_images_default,
        'service_categories': list,
        'staff': list,
        'reviews': list,
    }

    class Meta:
        document = BusinessDocument
        fields = '__all__'

    cover_photo = BusinessPhotoUrlField()
    photo = BusinessPhotoUrlField(source='cover_photo')
    primary_category = serializers.IntegerField(source='business_primary_category.id')
    thumbnail_photo = BusinessPhotoUrlField()
    booking_policy = ComputedField(source='booking_max_modification_time')
    credit_cards = TranslatedField()
    wheelchair_access = TranslatedField()
    location = ComputedField(source='business_location')
    booking_max_lead_months_count = ComputedField(source='booking_max_lead_time')
    service_categories = ServiceCategoryHitSerializer(many=True)
    images = ImagesHitSerializer()
    staff = ResourceHitSerializer(many=True)
    open_hours_customizations = OpenHoursCustomizationHitSerializer(many=True)
    top_business_services_ids = serializers.ListField(
        child=serializers.IntegerField(),
    )
    top_female_services_ids = serializers.ListField(
        child=serializers.IntegerField(),
    )
    top_male_services_ids = serializers.ListField(
        child=serializers.IntegerField(),
    )
    top_services = serializers.SerializerMethodField(read_only=True)
    reviews = ReviewsHitSerializer(many=True)
    is_recommended = serializers.SerializerMethodField(read_only=True)
    distance = serializers.SerializerMethodField(read_only=True)
    low_availability = serializers.SerializerMethodField(read_only=True)
    traveling = TravelingDetailsHitSerializer(read_only=True)
    amenities = AmenitiesHitSerializer(read_only=True)
    best_of_booksy_badge = serializers.SerializerMethodField(read_only=True)

    @staticmethod
    def get_location(value):
        """convert lat/lon -> latitude/longitude"""

        if not value:
            return value

        coordinate = value.get('coordinate')
        if not coordinate:
            return value

        value['coordinate'] = {'latitude': coordinate['lat'], 'longitude': coordinate['lon']}
        return value

    @staticmethod
    def get_booking_policy(value):
        if value:
            return _("Changes allowed up to {} before visit").format(
                lib.tools.duration_formatter(value, short=False)
            )
        return ''

    @staticmethod
    def get_is_recommended(obj):
        is_recommended = (
            settings.BOOST.DISPLAY_BOOKSY_RECOMMENDED_BADGE
            and obj.get('promoted', False)
            and (obj.get('reviews_rank_score', 0) > 0.74)
            and (obj.get('reviews_rank', 0) > 4.7)
        )
        return is_recommended

    def get_top_services(self, obj):
        if obj.get("hide_top_services", False):
            return []
        gender = self.context.get('data', {}).get('gender')
        if gender == Gender.Female.value:
            top_services_key = TOP_FEMALE_SERVICES_IDS
        elif gender == Gender.Male.value:
            top_services_key = TOP_MALE_SERVICES_IDS
        else:
            top_services_key = TOP_BUSINESS_SERVICES_IDS

        if top_services_key not in obj:
            return []
        key_sort = {service_id: order for order, service_id in enumerate(obj[top_services_key])}
        if not key_sort:
            return []
        return list(
            sorted(
                [
                    AttrDict(
                        {
                            'id': service['id'],
                            'name': service['name'],
                            'category_name': category['name'],
                            'variants': service['variants'],
                            'description': service['description'],
                            'is_online_service': service['is_online_service'],
                            'is_traveling_service': service.get('is_traveling_service', False),
                        }
                    )
                    for category in obj.get('service_categories', {})
                    for service in category.get('services', {})
                    if service['id'] in key_sort
                ],
                key=lambda service: key_sort[service.id],
            )
        )

    @staticmethod
    def get_distance(obj):
        distance = obj['script_fields'].get('distance', [None])[0]
        return int(distance) if distance is not None else None

    @staticmethod
    def get_booking_max_lead_months_count(value):
        return datetime.timedelta(minutes=value or 0).days // 30

    @staticmethod
    def get_real_availability_slot_length(slot, time_now):
        date = slot.get('date')['gte']
        if 'T' in date:
            # if we get datetime format get only date
            date = date.split('T')[0]
        start = slot.get('time_range', {}).get('gte')
        ret = slot.get('duration', 0)
        if start is not None and date is not None:
            date = datetime.datetime.strptime(date, settings.ES_DATE_FORMAT).date()
            start = datetime.datetime.strptime(str(start), settings.ES_TIME_FORMAT).replace(
                time_now.year, time_now.month, time_now.day, tzinfo=time_now.tzinfo
            )
            if date == time_now.date() and start < time_now:
                ret -= (time_now - start).total_seconds()
        return max(ret, 0)

    def get_low_availability(self, obj):
        data = self.context.get('data', {})
        low_availability = False
        if (data.get('treatment') is not None or data.get('category') is not None) and data[
            'la_date_interval'
        ] <= datetime.timedelta(days=3):
            time_now = lib.tools.tznow(obj.get('timezone_name'))
            near_availability = obj.get('near_availability')
            if near_availability:
                total_duration = sum(
                    self.get_real_availability_slot_length(elem, time_now)
                    for elem in near_availability['hits']
                    if elem.get('nature') == ESAvailabilityNature.REAL.value
                )
                low_availability = self.la_lower_bound <= total_duration <= data['la_upper_bound']
        return low_availability

    @staticmethod
    def get_best_of_booksy_badge(obj):
        if not (awards := obj.get('awards')):
            return None
        awards_badge_map = get_available_best_of_booksy_awards_badge_map()
        if available_awards := list(set(awards).intersection(awards_badge_map)):
            return awards_badge_map.get(available_awards[0])
        return None

    def update_data_for_low_availability(self, data):
        # This key is added to avoid recalculating of queried availability
        # interval for each document
        date_from = data.get('available_for', {}).get('date_from') or lib.tools.tznow().date()
        date_to = data.get('available_for', {}).get('date_to') or (
            date_from + datetime.timedelta(days=3)
            if data.get('available_for', {}).get('date_from') is None
            else date_from
        )
        data['la_date_interval'] = date_to - date_from
        data['la_upper_bound'] = (
            self.la_upper_bound_tree_days
            if data['la_date_interval'] >= datetime.timedelta(days=1)
            else self.la_upper_bound_one_day
        )

    @staticmethod
    def update_promoted_status(hit):
        if not hit.get('promoted'):
            hit['promoted'] = hit.get('manual_boost_score', 0) > 0

    @staticmethod
    def clean_fields(fields):
        # top services
        for key in TOP_SERVICES_KEYS:
            fields.pop(key, None)
        # is recommended
        # Always return promoted due to backward compatibility for iOS
        # fields.pop('promoted', None)
        fields.pop('reviews_rank_score', None)
        # low availability
        fields.pop('near_availability', None)
        fields.pop('timezone_name', None)


class BusinessSearchHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'slug',
            'subdomain',
            'description',
            'distance',
            'business_categories',
            'primary_category',
            'regions',
            'thumbnail_photo',
            'photo',
            'reviews_rank',
            'reviews_count',
            'reviews_stars',
            'pricing_level',
            'promoted',
            'location',
            'noindex',
            'images',
            'staff',
            'is_renting_venue',
            'contractors',
            'is_b_listing',
            'reviews',
            'has_online_services',
            'amenities',
            'hidden_on_web',
            'importer',
            'disable_customer_note',
            'best_of_booksy_badge',
            'accept_booksy_gift_cards',
            'accept_booksy_pay',
        ]


class OnlyIdBusinessSearchHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
        ]


class BusinessContactHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'reviews_stars',
            'reviews_count',
            'reviews_rank',
            'primary_category',
            'location',
            'thumbnail_photo',
            'images',
            'distance',
        ]


class BusinessPeopleAlsoBookedSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'reviews_stars',
            'reviews_count',
            'reviews_rank',
            'primary_category',
            'location',
            'thumbnail_photo',
            'images',
            'distance',
            'url',
        ]

    never_none_fields = {}

    url = serializers.CharField(source='sitemap_url', allow_null=True, allow_blank=True)


class VenueSearchHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'location',
            'cover_photo',
            'thumbnail_photo',
            'primary_category_id',
            'last_updated',
            'venue_location',
        ]

    never_none_fields = {}

    last_updated = serializers.DictField(source='venue_last_updated')


# region business details
class BusinessSearchDetailsHitSerializer(BusinessHitSerializer):
    # optimize all return fields
    class Meta:
        document = BusinessDocument
        fields = [
            # search
            'id',
            'name',
            'slug',
            'subdomain',
            'description',
            'business_categories',
            'primary_category',
            'regions',
            'thumbnail_photo',
            'photo',
            'pricing_level',
            'location',
            'distance',
            'reviews_rank',
            'reviews_count',
            'reviews_stars',
            'promoted',
            'manual_boost_score',
            'noindex',
            'images',
            'staff',
            'is_renting_venue',
            'contractors',
            'is_b_listing',
            # search details
            'service_categories',
            'reviews',
            'booking_mode',
            'booking_policy',
            # is_recommended badge
            'reviews_rank_score',
            'is_recommended',
            # special_offers badge
            'max_discount_rate',
            # low_availability badge
            'low_availability',
            'near_availability',
            'timezone_name',
            'has_online_services',
            'traveling',
            'best_of_booksy_badge',
            'accept_booksy_gift_cards',
            'accept_booksy_pay',
        ]

    def get_fields(self):
        data = self.context.get('data', {})
        self.update_data_for_low_availability(data)

        fields = super().get_fields()
        return fields

    def to_representation(self, instance):
        hit = super().to_representation(instance)
        self.update_promoted_status(hit)
        self.clean_fields(hit)
        return hit


class BusinessDetailsHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            # search
            'id',
            'name',
            'slug',
            'subdomain',
            'description',
            'business_categories',
            'primary_category',
            'regions',
            'thumbnail_photo',
            'photo',
            'location',
            'reviews_rank',
            'reviews_count',
            'reviews_stars',
            'reviews',
            'pricing_level',
            'promoted',
            'manual_boost_score',
            'noindex',
            'images',
            'staff',
            'hidden_on_web',
            # umbrella details
            'is_renting_venue',
            'umbrella_venue_name',
            # contractors info (business under umbrella)
            'contractors',
            'contractor_description',
            # b-listing
            'is_b_listing',
            # all details
            'service_categories',
            'booking_mode',
            'booking_policy',
            'phone',
            'website',
            'facebook_link',
            'instagram_link',
            'public_email',
            'credit_cards',
            'ecommerce_link',
            'booking_max_modification_time',
            'booking_max_lead_time',
            'pos_pay_by_app_enabled',
            'pos_market_pay_enabled',
            'deposit_cancel_time',
            'deposit_policy',
            'service_fee',
            'parking',
            'wheelchair_access',
            'show_similar_gallery',
            'opening_hours_note',
            'open_hours',
            'open_hours_customizations',
            'waitlist_disabled',
            # top services
            'hide_top_services',
            TOP_BUSINESS_SERVICES_IDS,
            TOP_FEMALE_SERVICES_IDS,
            TOP_MALE_SERVICES_IDS,
            'top_services',
            # is_recommended badge
            'reviews_rank_score',
            'is_recommended',
            # special_offers badge
            'max_discount_rate',
            # low_availability badge
            'low_availability',
            'near_availability',
            'timezone_name',
            'url',
            'salon_network',
            'donations_enabled',
            'has_online_services',
            'has_online_vouchers',
            'has_safety_rules',
            'traveling',
            'is_financial_institution',
            'pixel_id',
            'amenities',
            'printer_config',
            'partners',
            'disable_customer_note',
            'best_of_booksy_badge',
            'profile_type',
            'accept_booksy_gift_cards',
            'contact_name',
            'accept_booksy_pay',
        ]

    url = serializers.SerializerMethodField()
    business_categories = serializers.SerializerMethodField()
    service_categories = serializers.SerializerMethodField()
    contact_name = serializers.SerializerMethodField()

    @staticmethod
    def get_url(obj):
        return obj.get('sitemap_url')

    def get_fields(self):
        data = self.context.get('data', {})
        self.update_data_for_low_availability(data)

        fields = super().get_fields()
        return fields

    @staticmethod
    def get_business_categories(obj):
        categories = obj.get('business_categories')
        if categories:
            for category in categories:
                if category['id'] and (
                    category_cache := get_category_or_subcategory_by_id(category['id'])
                ):
                    category['internal_name'] = category_cache.get('internal_name')
            return categories

    @staticmethod
    def get_service_categories(obj):
        service_categories = obj.get('service_categories')
        if not service_categories:
            return []
        if ph_flag := EnablePeakHoursFlag():
            sv = VariantsAdapter(business_id=obj['id'])

        for service_category in service_categories:
            for service in service_category.get('services') or []:
                service['note'] = service.pop('note_text', None) or service.get('note', '')
                service['service_price'] = ServicePrice(
                    service.get('price'), service.get('type')
                ).format_for_customer()
                for variant in service.get('variants') or []:
                    # pylint: disable=possibly-used-before-assignment
                    if ph_flag and sv.is_peak_hour(variant['id']):
                        variant_price_type = PriceType.STARTS_AT
                    else:
                        variant_price_type = variant.get('type')

                    variant['service_price'] = ServicePrice(
                        variant.get('price'), variant_price_type
                    ).format_for_customer()

                    variant['omnibus_price'] = ServiceVariantHitSerializer.get_omnibus_price(
                        variant
                    )

        return service_categories

    def get_top_services(self, obj):
        """Prepare top_services for male, female and business. Proper
        top_services should be extracted later in handler method."""
        if obj.get("hide_top_services", False):
            return {key.value: [] for key in Gender}

        top_services_groups = {}
        key_sort = None
        for top_service_name, key in (
            (TOP_FEMALE_SERVICES_IDS, Gender.Female.value),
            (TOP_MALE_SERVICES_IDS, Gender.Male.value),
            (TOP_BUSINESS_SERVICES_IDS, Gender.Both.value),
        ):
            if top_service_name not in obj:
                top_services_groups[key] = []
                continue

            key_sort = {service_id: order for order, service_id in enumerate(obj[top_service_name])}
            if not key_sort:
                top_services_groups[key] = []
                continue

            top_services_groups[key] = list(
                sorted(
                    [
                        AttrDict(
                            {
                                'id': service['id'],
                                'name': service['name'],
                                'category_name': category['name'],
                                'variants': service['variants'],
                                'description': service['description'],
                                'is_online_service': service['is_online_service'],
                                'is_traveling_service': service.get('is_traveling_service', False),
                            }
                        )
                        for category in obj.get('service_categories', {})
                        for service in category.get('services', {})
                        if service['id'] in key_sort
                    ],
                    key=lambda service: key_sort[service.id],  # pylint: disable=cell-var-from-loop
                )
            )
        return top_services_groups

    @staticmethod
    def get_contact_name(obj):
        if DSABusinessVersumNameFlag():
            return obj.get('legal_name') or obj.get('name')

        if 'Versum' not in (obj.get('importer') or '') and obj.get('legal_name'):
            return obj.get('legal_name')
        return obj.get('name')

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        if not ret.get('pos_pay_by_app_enabled'):
            ret['deposit_cancel_time'] = {}
            ret['deposit_policy'] = ''

        self.update_promoted_status(ret)
        self.clean_fields(ret)
        return ret


# endregion business details


class BusinessWithSingleImagesHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'slug',
            'phone',
            'thumbnail_photo',
            'images',
            'location',
            'distance',
            'reviews_count',
            'reviews_stars',
            'reviews_rank',
            'is_b_listing',
            'url',
            'active',
            'visible',
            'primary_category',
            'traveling',
        ]

    # renames field sitemap_url
    url = serializers.SerializerMethodField()

    @staticmethod
    def get_url(obj):
        return obj.get('sitemap_url')


# region map business serializer
class BusinessMapImagesHitSerializer(ImagesHitSerializer):
    class Meta:
        fields = ['cover']


class BusinessMapHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'location',
            'distance',
            'active',
            'visible',
            'reviews_count',
            'reviews_stars',
            'reviews_rank',
            'images',
            'primary_category',
            'url',
            'sitemap_url',
            # is_recommended badge
            'reviews_rank_score',
            'is_recommended',
            'promoted',
            'manual_boost_score',
            # special_offers badge
            'max_discount_rate',
            # low_availability badge
            'low_availability',
            'near_availability',
            'timezone_name',
            'traveling',
            'accept_booksy_gift_cards',
            'accept_booksy_pay',
        ]
        # before editing that attribute please check it's value in parent class

    never_none_fields = {
        'images': never_none_fields.get_images_default,
    }

    images = BusinessMapImagesHitSerializer()
    # renames field sitemap_url
    url = serializers.SerializerMethodField()
    traveling = TravelingHitSerializer()

    @staticmethod
    def get_url(obj):
        return obj.get('sitemap_url')

    def get_fields(self):
        data = self.context.get('data', {})
        self.update_data_for_low_availability(data)

        fields = super().get_fields()

        if not BooksyGiftcardsEnabledFlag():
            fields.pop('accept_booksy_gift_cards', None)

        return fields

    def to_representation(self, instance):
        hit = super().to_representation(instance)

        if 'sitemap_url' in hit:
            del hit['sitemap_url']

        self.update_promoted_status(hit)
        return hit


# endregion map business serializer


# region listing business serializer
class BusinessListingImagesHitSerializer(ImagesHitSerializer):
    class Meta:
        fields = ['cover', 'inspiration', 'logo']


class BusinessListingHitSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'location',
            'distance',
            'reviews_count',
            'reviews_stars',
            'reviews_rank',
            'images',
            'primary_category',
            'service_categories',
            'promoted',
            'manual_boost_score',
            'hidden_on_web',
            # is_recommended badge
            'reviews_rank_score',
            'is_recommended',
            # special_offers badge
            'max_discount_rate',
            # low_availability badge
            'low_availability',
            'near_availability',
            'timezone_name',
            'has_online_services',
            'has_online_vouchers',
            'traveling',
            'best_of_booksy_badge',
            'accept_booksy_gift_cards',
            'accept_booksy_pay',
        ]

    images = BusinessListingImagesHitSerializer()
    traveling = TravelingHitSerializer()

    def get_fields(self):
        data = self.context.get('data', {})
        self.update_data_for_low_availability(data)

        fields = super().get_fields()
        if not data.get('include_treatment_services'):
            del fields['service_categories']

        return fields

    def to_representation(self, instance):
        hit = super().to_representation(instance)
        self.update_promoted_status(hit)
        self.clean_fields(hit)
        return hit


class BusinessListingWebHitSerializer(BusinessHitSerializer):

    DEFAULT_SERVICES_LIMIT = 3

    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'slug',
            'subdomain',
            'regions',
            'location',
            'distance',
            'reviews_count',
            'reviews_stars',
            'reviews_rank',
            'images',
            'primary_category',
            'service_categories',
            'promoted',
            'manual_boost_score',
            # is_recommended badge
            'reviews_rank_score',
            'is_recommended',
            # special_offers badge
            'max_discount_rate',
            # low_availability badge
            'low_availability',
            'near_availability',
            'timezone_name',
            # umbrella details
            'is_renting_venue',
            'umbrella_venue_name',
            # contractors info (business under umbrella)
            'contractors',
            'contractor_description',
            'url',
            'traveling',
            'best_of_booksy_badge',
            'accept_booksy_gift_cards',
            'accept_booksy_pay',
        ]

    images = BusinessListingImagesHitSerializer()
    # renames field sitemap_url
    url = serializers.SerializerMethodField()
    traveling = TravelingHitSerializer()

    def get_fields(self):
        data = self.context.get('data', {})
        self.update_data_for_low_availability(data)
        return super().get_fields()

    def to_representation(self, instance):
        hit = super().to_representation(instance)
        self.update_promoted_status(hit)
        self.clean_fields(hit)

        if LimitServicesBusinessListingWebFlag():
            self._limit_services(hit)

        return hit

    def _limit_services(self, hit, limit=DEFAULT_SERVICES_LIMIT):
        """
        Limit service_categories to first N services total across all categories.

        Args:
            hit: The hit dictionary containing service_categories
            limit: Maximum number of services to keep (default: 3)
        """
        if not hit.get('service_categories'):
            return

        services_count = 0
        categories_to_keep = []

        for category in hit['service_categories']:
            if services_count >= limit:
                break

            if category.get('services'):
                # Calculate how many services we can take from this category
                services_remaining = limit - services_count
                category_services = category['services'][:services_remaining]

                if category_services:  # Only keep categories that have services
                    # Create a copy of the category with trimmed services
                    trimmed_category = {
                        'id': category.get('id'),
                        'name': category.get('name'),
                        'order': category.get('order'),
                        'show_first': category.get('show_first'),
                        'services': category_services,
                    }
                    categories_to_keep.append(trimmed_category)
                    services_count += len(category_services)

        hit['service_categories'] = categories_to_keep

    @staticmethod
    def get_url(obj):
        return obj.get('sitemap_url')


# endregion listing business serializer


class BusinessWithBoostSerializer(BusinessHitSerializer):
    class Meta:
        document = BusinessDocument
        fields = ['id', 'promotion_boost']


BUSINESS_RESPONSE_TYPES = {
    ResponseTypeEnum.LISTING: BusinessListingHitSerializer,
    ResponseTypeEnum.LISTING_WEB: BusinessListingWebHitSerializer,
    ResponseTypeEnum.DETAILS: BusinessSearchDetailsHitSerializer,
    ResponseTypeEnum.MAP: BusinessMapHitSerializer,
}


class BusinessAccountHitSerializer(HitSerializer):
    class Meta:
        document = BusinessAccountDocument
        fields = [
            'id',
        ]


class _BusinessPhotoThumbnailUrlField(BusinessPhotoUrlField):
    def __init__(self, width: int, height: int, **kwargs):
        super().__init__(**kwargs)

        self.width = width
        self.height = height

    def to_representation(self, value):
        value = super().to_representation(value)
        return build_thumbnail_url(value, self.width, self.height)


class _BusinessQueryHintSerializer(HitSerializer):

    class Meta:
        document = BusinessDocument
        fields = (
            'id',
            'name',
            'slug',
            'url',
            'thumbnail_photo',
            'is_b_listing',
            'address',
        )

    id = serializers.IntegerField()
    name = serializers.CharField()
    slug = serializers.CharField()
    url = serializers.URLField(source='sitemap_url')
    thumbnail_photo = _BusinessPhotoThumbnailUrlField(width=100, height=100)
    is_b_listing = serializers.BooleanField()
    address = serializers.CharField(source='business_location.address')


class BusinessServiceHitSerializer(_BusinessQueryHintSerializer):

    # TODO: staffers should be filtered at search, but currently there is no index
    @staticmethod
    def _filter_staffers(staffers, service):
        staffer_ids = set(service.get('staffer_id') or [])

        return [staffer for staffer in staffers if staffer['id'] in staffer_ids]

    def to_representation(self, instance):
        business_data = super().to_representation(instance)

        service_category = safe_get(instance, ['service_category', 'hits', 0])
        service = safe_get(service_category, ['service', 'hits', 0])
        staff = safe_get(instance, ['staff', 'hits']) or []

        return {
            'business': business_data,
            'service_category': ServiceCategoryHitSerializer().to_representation(service_category),
            'service': ServiceHitSerializer().to_representation(service),
            'staff': ResourceHitSerializer(many=True).to_representation(
                self._filter_staffers(staff, service)
            ),
        }


class BusinessTreatmentHitSerializer(BusinessListingHitSerializer):
    never_none_fields = {
        'images': never_none_fields.get_images_default,
        'reviews': list,
    }

    class Meta:
        document = BusinessDocument
        fields = [
            'id',
            'name',
            'location',
            'distance',
            'reviews_count',
            'reviews_stars',
            'reviews_rank',
            'images',
            'primary_category',
            'promoted',
            'hidden_on_web',
            'top_services',
            'treatment_services',
            'service_categories',
            'reviews_rank_score',
            'is_recommended',
            'max_discount_rate',
            'timezone_name',
            'has_online_services',
            'has_online_vouchers',
            'traveling',
            'url',
            'best_of_booksy_badge',
            'accept_booksy_gift_cards',
            'accept_booksy_pay',
        ]

    treatment_services = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField()

    def get_treatment_services(self, obj):
        service_with_matching_treatment = []
        service_without_matching_treatment = []
        context_data = self.context.get('data', {})
        treatment_ids = context_data.get('business_categories', [])
        treatment_limit = context_data.get('treatment_limit', 0)

        for category in obj['service_categories']:
            for s in category['services']:
                data = AttrDict({'category_name': category['name'], **s})
                if 'treatment_id' in s and {s['treatment_id'], s['treatment_parent_id']} & set(
                    treatment_ids
                ):
                    service_with_matching_treatment.append(data)
                    if not S4UServiceVariantCountFixFlag():
                        if len(service_with_matching_treatment) >= treatment_limit:
                            return service_with_matching_treatment
                else:
                    service_without_matching_treatment.append(data)

        services = service_with_matching_treatment or service_without_matching_treatment
        services = services[:treatment_limit]
        if S4UServiceVariantCountFixFlag():
            left = treatment_limit - len(services)
            for service in services:
                service.variants = service.variants[: left + 1]
                left -= len(service.variants) - 1

        return services

    @staticmethod
    def get_url(obj):
        return obj.get('sitemap_url')
