from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass(frozen=True)
class BusinessDetailsEntity:
    id: int
    name: str
    address: str
    timezone: datetime.tzinfo


class BusinessDetailsPort:
    @staticmethod
    def get_business_details(business_id: int) -> Optional[BusinessDetailsEntity]:
        """
        Fetch business details by ID

        :param business_id: ID of the business
        :return: BusinessDetailsEntity or None if business not found
        """
        from webapps.business.models import Business

        business = Business.objects.filter(id=business_id).first()
        if not business:
            return None

        return BusinessDetailsEntity(
            id=business.id,
            name=business.name,
            address=business.get_location_name(with_city=True),
            timezone=business.get_timezone(),
        )
