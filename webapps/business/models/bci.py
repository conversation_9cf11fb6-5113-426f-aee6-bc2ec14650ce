#!/usr/bin/env python
from __future__ import annotations

import re
import typing as t
from dataclasses import dataclass
from typing import Any, Dict

from dateutil.relativedelta import relativedelta
from dirtyfields import DirtyFieldsMixin
from django.apps import apps as django_apps
from django.conf import settings
from django.contrib.postgres.fields import <PERSON>rray<PERSON>ield
from django.core.exceptions import ValidationError
from django.core.validators import (
    MaxValueValidator,
    MinValueValidator,
    RegexValidator,
)
from django.db import models, transaction
from django.db.models import (
    BooleanField,
    Case,
    Count,
    F,
    Index,
    JSONField,
    ManyToManyField,
    ManyToManyRel,
    OneToOneField,
    Prefetch,
    Q,
    QuerySet,
    Value,
    When,
)
from django.db.models.expressions import Func
from django.db.models.functions import Coalesce
from django.db.models.signals import m2m_changed, post_delete, post_save
from django.utils.functional import cached_property
from django.utils.translation import gettext
from django.utils.translation import gettext_lazy as _
from elasticsearch import ElasticsearchException

from lib.booksy_sms import parse_phone_number
from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import ESDocMixin, delete_document
from lib.email_internal import is_private_email
from lib.enums import StrChoicesEnum
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature import (
    CustomerQuickSignInUpFlag,
    LoyaltyProgramFlag,
)
from lib.fields.dataclass_field import DataClassField
from lib.fields.phone_number import BooksyPhoneNumberField
from lib.merge_helper import MergeConflictError, MergeError, MergeFunc, MergeHelper
from lib.models import (
    ArchiveModel,
    AutoAddHistoryModel,
    AutoAddHistoryQuerySet,
    AutoUpdateManager,
    AutoUpdateQuerySet,
    HistoryModel,
    UndeletableMixin,
)
from lib.rivers import bump_document, River
from lib.segment_analytics import InvitationSource
from lib.storage import storage_dispatcher
from lib.tools import tznow
from lib.validators import booksy_validate_email
from webapps import consts
from webapps.booking.factory.service_questions import QuestionsAndAnswersList
from webapps.business.enums import CustomerCardType, DocumentType
from webapps.business_customer_info.enums import BCIGetPreferWay
from webapps.business.tasks import business_customer_info_delete_task
from webapps.business_related.enums import ClaimLogReasons, ClaimLogStatuses, ClaimLogType
from webapps.business_related.models import ClaimLog
from webapps.family_and_friends.enums import INACTIVE_RELATIONSHIP_TYPES
from webapps.images.enums import ImageTypeEnum
from webapps.images.mixins import PhotoModelMixin
from webapps.images.storages import BusinessFilesStorage
from webapps.kill_switch.models import KillSwitch
from webapps.photo.models import Photo
from webapps.pos.enums import PaymentProviderEnum
from webapps.public_partners.query_sets import PartnerAppDataQuerySet
from webapps.user.elasticsearch.tools import append_to_user_search_data_fast_river

TAG_REGEX_PATTERN = r"\S{1,29}"
validate_tag = RegexValidator(
    f'^#{TAG_REGEX_PATTERN}$', message=_('"%(value)s" is not a valid tag'), flags=re.UNICODE
)


class _BCIFromContactRequest(t.NamedTuple):
    """Helper class for get_from_contact"""

    users_with_email: t.Set[int]
    users_with_phone: t.Set[int]
    contact_email: str  # can be empty string
    contact_cell_phone: str  # can be empty string


@dataclass(frozen=True)
class BCIFromContact:
    """Helper class for `get from contact`"""

    business: 'business.Business'
    customer_name: str = ''
    customer_email: str = ''
    customer_phone: str = ''
    defaults: Dict[str, Any] = None  # BCI extra params


class Tag(ArchiveModel):
    class Meta:
        unique_together = [('business', 'name')]
        ordering = ('name',)

    id = models.AutoField(primary_key=True, db_column='tag_id')
    name = models.CharField(max_length=30, validators=[validate_tag], db_index=True)
    business = models.ForeignKey(
        'business.Business',
        related_name='tags',
        on_delete=models.CASCADE,
    )

    objects = AutoUpdateManager()
    all_objects = models.Manager()

    def get_refcount(self):
        """Return references count, or in other words a total number of uses"""
        queryset = None
        for related_object in self._meta.related_objects:
            if not isinstance(related_object, ManyToManyRel):
                continue  # only count m2m relations

            filters = {}
            if issubclass(related_object.related_model, ArchiveModel):
                if related_object.through_fields:
                    link_field_name = related_object.through_fields[0]
                else:
                    link_field_name = related_object.name
                filters[f'{link_field_name}__deleted__isnull'] = True

            related_queryset = related_object.through.objects.filter(tag_id=self.id, **filters)
            if queryset is None:
                queryset = related_queryset
            else:
                queryset.union(related_queryset)

        return queryset.count() if queryset is not None else 0

    def __repr__(self):
        return f'<{self._meta.object_name}: {self.id}, {self.name}>'

    def __str__(self):
        return self.name or ''


class BusinessCustomerInfoTag(models.Model):
    class Meta:
        unique_together = [('customer', 'tag')]
        ordering = ('order',)

    id = models.AutoField(primary_key=True)
    customer = models.ForeignKey(
        to='business.BusinessCustomerInfo',
        on_delete=models.CASCADE,
        null=False,
    )
    tag = models.ForeignKey(
        to=Tag,
        on_delete=models.CASCADE,
        null=False,
    )
    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    order = models.IntegerField(default=0)

    @classmethod
    @transaction.atomic
    def save_bci_tags(cls, instance: 'BusinessCustomerInfo', tags: list[Tag]):
        tag_ids = []
        for order, tag in enumerate(tags):
            if tag.id is None:
                tag.save()

            cls.objects.update_or_create(customer=instance, tag=tag, defaults={'order': order})
            tag_ids.append(tag.id)

        cls.objects.filter(customer=instance).exclude(tag_id__in=tag_ids).delete()


class BCITypeDataQuerySet(AutoAddHistoryQuerySet, AutoUpdateQuerySet):
    pass


class BCITypeDataManager(AutoUpdateManager.from_queryset(BCITypeDataQuerySet)):
    pass


class BCITypeData(ArchiveModel, AutoAddHistoryModel):
    objects = BCITypeDataManager()

    all_objects = models.Manager()

    card_type = models.CharField(
        max_length=1, choices=CustomerCardType.choices(), default=CustomerCardType.PERSON
    )
    additional_data = models.JSONField(default=dict, blank=True, null=True)

    ADDITIONAL_DATA_FIELDS = {
        CustomerCardType.VEHICLE: [
            'manufacturer',
            'model',
            'registration_number',
            'vin_number',
            'year',
            'additional_info',
        ],
        CustomerCardType.PET: ['pet_type', 'breed', 'weight', 'additional_info'],
        CustomerCardType.PERSON: [],
    }

    READ_ONLY_FIELDS = {
        CustomerCardType.VEHICLE: ['additional_info'],
        CustomerCardType.PET: ['additional_info'],
        CustomerCardType.PERSON: [],
    }

    REQUIRED_FIELDS = {
        CustomerCardType.VEHICLE: ['registration_number'],
        CustomerCardType.PET: [],
        CustomerCardType.PERSON: [],
    }

    REQUIRED_BCI_FIELDS = {
        CustomerCardType.VEHICLE: [],
        CustomerCardType.PET: ['first_name'],
        CustomerCardType.PERSON: [],
    }

    HIDDEN_FIELDS = {
        # Key is a field that should be replace by Value in representation
        CustomerCardType.VEHICLE: {
            'first_name': '',
            'last_name': '',
            'birthday': None,
            'short_birthday': None,
            'allergens': '',
        },
        CustomerCardType.PET: {},
        CustomerCardType.PERSON: {},
    }


class BCITypeDataHistory(HistoryModel):
    model = models.ForeignKey(
        BCITypeData,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BusinessCustomerInfoQuerySet(
    PartnerAppDataQuerySet,
    AutoAddHistoryQuerySet,
    AutoUpdateQuerySet,
):
    def annotate_has_active_gift_cards(self):
        from webapps.voucher.models import Voucher

        is_active = Q(vouchers__status=Voucher.ACTIVE)
        is_gift_card = Q(
            vouchers__voucher_template__type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
        )

        return self.annotate(
            _active_gift_cards_count=Count(
                'vouchers',
                Q(is_active, is_gift_card),
                distinct=True,
            ),
            _has_active_gift_cards=Case(
                When(
                    _active_gift_cards_count__gt=0,
                    then=True,
                ),
                output_field=models.BooleanField(),
                default=False,
            ),
        )

    def annotate_any_mobile_customer_appointments(self):
        from django.db.models import Exists, OuterRef

        from webapps.search_engine_tuning.models import BusinessCustomerTuning

        return self.annotate(
            any_mobile_customer_appointments=Exists(
                BusinessCustomerTuning.objects.filter(
                    customer_id=OuterRef('id'),
                    any_mobile_customer_appointments=True,
                ),
            )
        )

    def annotate_can_invite(self):
        return self.annotate(
            invitation_possible=Case(
                When(
                    any_mobile_customer_appointments=True,
                    then=False,
                ),
                When(
                    excluded_from_delayed_invitation=True,
                    business__visible_delay_till__gte=tznow(),
                    then=True,
                ),
                When(
                    business__visible_delay_till__gte=tznow(),
                    then=False,
                ),
                default=True,
                output_field=BooleanField(),
            ),
        )

    def annotate_use_parent_data(self):
        from django.db.models import Exists, OuterRef

        from webapps.family_and_friends.models import BCIRelation

        return self.annotate(
            use_parent_data=Exists(
                BCIRelation.objects.filter(member_bci_id=OuterRef('id'), use_parent_data=True)
            ),
        )

    def annotate_not_active_member(self):
        from django.db.models import Exists, OuterRef

        from webapps.family_and_friends.models import BCIRelation

        return self.annotate(
            not_active_member=Exists(
                BCIRelation.objects.filter(member_bci_id=OuterRef('id')).filter(
                    Q(use_parent_data=True) | Q(relationship_type__in=INACTIVE_RELATIONSHIP_TYPES)
                )
            ),
        )

    def annotate_is_from_promo(self):
        return self.annotate(
            is_from_promo=Coalesce(
                F('boost_client_card__from_promo'),
                Value(False),
                output_field=BooleanField(),
            )
        )

    def select_related_business_fields(self, *fields):
        from webapps.business.models import Business

        # pylint: disable=not-callable
        return self.select_related('business').defer(
            *{
                f'business__{field.name}'
                for field in Business._meta.get_fields()
                if field.name not in fields
            }
        )

    def annotate_members_relations(self):
        """Family and friends members relations."""
        from django.db.models import OuterRef

        from webapps.user.models import UserProfile

        customer_profile_queryset = UserProfile.objects.filter(
            profile_type=UserProfile.Type.CUSTOMER,
        ).select_related('photo', 'region')

        relations_to_parents = BusinessCustomerInfo.objects.filter(
            id=OuterRef('pk'),
            relations_to_parents__parent_bci=OuterRef('parents__id'),
        )

        queryset = (
            self.annotate(
                relationship_type=relations_to_parents.values_list(
                    'relations_to_parents__relationship_type',
                ),
            )
            .annotate(
                use_parent_data=relations_to_parents.values_list(
                    'relations_to_parents__use_parent_data',
                ),
            )
            .annotate(
                is_parent=Value(False, output_field=BooleanField()),
            )
            .annotate_use_parent_data()
            .annotate_is_first_visit()
            .annotate_is_from_promo()
            .prefetch_related(
                Prefetch('tags', queryset=Tag.objects.order_by('businesscustomerinfotag__order')),
                Prefetch(
                    'user__profiles',
                    queryset=customer_profile_queryset,
                ),
            )
            .distinct()
        )

        return queryset

    def annotate_parents_relations(self):
        """Family and friends parents relations."""
        from django.db.models import OuterRef

        from webapps.user.models import UserProfile

        customer_profile_queryset = UserProfile.objects.filter(
            profile_type=UserProfile.Type.CUSTOMER,
        ).select_related('photo', 'region')

        relations_to_members = BusinessCustomerInfo.objects.filter(
            id=OuterRef('pk'), relations_to_members__member_bci=OuterRef('members__id')
        )

        queryset = (
            self.annotate(
                relationship_type=relations_to_members.values_list(
                    'relations_to_members__relationship_type',
                ),
            )
            .annotate(
                use_parent_data=relations_to_members.values_list(
                    'relations_to_members__use_parent_data',
                ),
            )
            .annotate(
                is_parent=Value(True, output_field=BooleanField()),
            )
            .annotate_not_active_member()
            .annotate_is_first_visit()
            .annotate_is_from_promo()
            .prefetch_related(
                Prefetch('tags', queryset=Tag.objects.order_by('businesscustomerinfotag__order')),
                Prefetch(
                    'user__profiles',
                    queryset=customer_profile_queryset,
                ),
            )
            .distinct()
        )

        return queryset

    def annotate_is_first_visit(self):
        from webapps.family_and_friends.models import Appointment

        return self.annotate(
            appointments_cnt=Count(
                'appointments__pk',
                filter=Q(
                    appointments__deleted__isnull=True,
                ),
            ),
            occupying_appointments_cnt=Count(
                'appointments__pk',
                filter=Q(
                    appointments__deleted__isnull=True,
                    appointments__status__in=Appointment.STATUSES_OCCUPYING_TIME_SLOTS,
                ),
            ),
            is_first_visit=Case(
                When(Q(appointments_cnt=1, occupying_appointments_cnt=1), then=True),
                output_field=models.BooleanField(),
                default=False,
            ),
        )

    def annotate_has_blocked_phone_number(self):
        from django.db.models import Exists, OuterRef

        from webapps.message_blast.models import BlockedPhoneNumber

        return self.annotate(
            has_blocked_phone_number=Exists(
                BlockedPhoneNumber.objects.filter(
                    cell_phone__in=Func(
                        OuterRef("cell_phone"),
                        OuterRef("user__cell_phone"),
                        template="(%(expressions)s)",
                    ),
                )
            )
        )

    def annotate_sms_marketing_consent(self):
        from django.db.models import Exists, OuterRef

        from webapps.message_blast.models import SMSBlastMarketingConsent

        if settings.API_COUNTRY in settings.DOUBLE_OPT_IN_COUNTRIES:
            return self.annotate(
                sms_marketing_consent=Exists(
                    SMSBlastMarketingConsent.objects.filter(
                        cell_phone__in=Func(
                            OuterRef("cell_phone"),
                            template="(%(expressions)s)",
                        ),
                        business_id=OuterRef("business_id"),
                        consented=True,
                    )
                )
            )

        return self.annotate(sms_marketing_consent=F("web_communication_agreement"))

    def basic_info_only(self):
        from webapps.user.models import User

        return self.prefetch_related(
            Prefetch('photo', queryset=Photo.objects.only('image_url')),
            Prefetch(
                'user',
                queryset=User.objects.prefetch_related('profiles').only(
                    'first_name', 'last_name', 'birthday', 'email', 'cell_phone'
                ),
            ),
        ).only(
            'first_name',
            'last_name',
            'birthday',
            'photo_id',
            'user_id',
            'business_id',
            'visible_in_business',
            'created',
            'email',
            'cell_phone',
        )


class BusinessCustomerInfoManager(AutoUpdateManager.from_queryset(BusinessCustomerInfoQuerySet)):
    pass


class BusinessCustomerInfo(  # pylint: disable=too-many-instance-attributes, too-many-public-methods
    PhotoModelMixin,
    ArchiveModel,
    DirtyFieldsMixin,
    ESDocMixin,
    AutoAddHistoryModel,
):
    """Customer's profile within a business.

    Also known as Client Card or Customer Card.

    """

    es_doc_type = ESDocType.BUSINESS_CUSTOMER
    photo_folder = ImageTypeEnum.CUSTOMER_CARD

    # This enum is never stored in DB, so there are long names
    REVIEW_ADD_STATUS_MISSING = 'missing'
    REVIEW_ADD_STATUS_SCHEDULED = 'scheduled'
    REVIEW_ADD_STATUS_AWAITING = 'awaiting'
    REVIEW_ADD_STATUS_EXPIRED = 'expired'
    REVIEW_ADD_BLOCKED_NO_SHOW = 'no-show'
    REVIEW_ADD_ALREADY_EXIST = 'exist'

    REVIEW_ADD_STATUSES = [
        (REVIEW_ADD_STATUS_MISSING, 'Customer has no visit'),
        (REVIEW_ADD_STATUS_SCHEDULED, 'Customer has booked visit'),
        (REVIEW_ADD_STATUS_AWAITING, 'Customer has finished visit'),
        (REVIEW_ADD_STATUS_EXPIRED, 'Customer has too old visit'),
        (REVIEW_ADD_BLOCKED_NO_SHOW, "Customer didn't come on the visit."),
        (REVIEW_ADD_ALREADY_EXIST, "Customer already add review"),
    ]
    CLIENT_TYPE__UNKNOWN = 'UN'
    CLIENT_TYPE__BUSINESS_DIRECTLY = 'BD'
    CLIENT_TYPE__BUSINESS_PHONE = 'BP'
    CLIENT_TYPE__BUSINESS_IMPORT = 'BI'
    CLIENT_TYPE__BUSINESS_INVITE = 'BV'
    CLIENT_TYPE__BUSINESS_SUB_DEP = 'BS'
    CLIENT_TYPE__BUSINESS_FACEBOOK = 'BF'
    CLIENT_TYPE__BUSINESS_WIDGET = 'BW'
    CLIENT_TYPE__CUSTOMER_RECURRING = 'CR'
    CLIENT_TYPE__CUSTOMER_NEW = 'CN'
    CLIENT_TYPE__GOOGLE_PARTNER = 'GP'
    CLIENT_TYPE__GROUPON_PARTNER = 'RP'
    CLIENT_TYPE__CLASSPASS_PARTNER = 'CP'  # deprecated
    CLIENT_TYPE__YELP_PARTNER = 'YP'  # deprecated
    CLIENT_TYPE__OLD = 'LO'
    CLIENT_TYPE__INSTAGRAM = 'IG'
    CLIENT_TYPE__INSTAGRAM_STAFFER = 'IS'
    CLIENT_TYPE__FAMILY_AND_FRIENDS = 'FF'
    CLIENT_TYPE__QRCODE = 'QR'

    CLIENT_TYPE_CHOICES = [
        (CLIENT_TYPE__UNKNOWN, 'Unknown'),
        (CLIENT_TYPE__BUSINESS_DIRECTLY, 'Added by business directly'),
        (CLIENT_TYPE__BUSINESS_PHONE, 'Added by business via cell phone'),
        (CLIENT_TYPE__BUSINESS_IMPORT, 'Added by business by import'),
        (CLIENT_TYPE__BUSINESS_INVITE, 'Added by business via invite'),
        (CLIENT_TYPE__BUSINESS_SUB_DEP, 'Added by business via subdomain/deeplink'),
        (CLIENT_TYPE__BUSINESS_FACEBOOK, 'Added by facebook'),
        (CLIENT_TYPE__BUSINESS_WIDGET, 'Added by widget'),
        (CLIENT_TYPE__CUSTOMER_RECURRING, 'Recurring customer'),
        (CLIENT_TYPE__CUSTOMER_NEW, 'New customer'),
        (CLIENT_TYPE__GOOGLE_PARTNER, 'Google Partner'),
        (CLIENT_TYPE__GROUPON_PARTNER, 'Groupon Partner'),
        (CLIENT_TYPE__CLASSPASS_PARTNER, 'ClassPass Partner'),
        (CLIENT_TYPE__YELP_PARTNER, 'Yelp Partner'),
        (CLIENT_TYPE__OLD, 'Legacy'),
        (CLIENT_TYPE__INSTAGRAM, 'Added by Instagram'),
        (CLIENT_TYPE__INSTAGRAM_STAFFER, 'Added by Staffers Instagram'),
        (CLIENT_TYPE__FAMILY_AND_FRIENDS, 'Family and Friends'),
        (CLIENT_TYPE__QRCODE, 'QR code'),
    ]

    SOURCE_CLIENT_TYPE_MAP = {
        consts.FACEBOOK: CLIENT_TYPE__BUSINESS_FACEBOOK,
        consts.INSTAGRAM: CLIENT_TYPE__INSTAGRAM,
        consts.INSTAGRAM_STAFFER: CLIENT_TYPE__INSTAGRAM_STAFFER,
        consts.WIDGET: CLIENT_TYPE__BUSINESS_WIDGET,
        consts.QR_CODE: CLIENT_TYPE__QRCODE,
    }

    id = models.AutoField(primary_key=True, db_column='business_customer_info_id')
    business = models.ForeignKey(
        'business.Business',
        related_name='business_customer_infos',
        on_delete=models.CASCADE,
    )
    user = models.ForeignKey(
        'user.User',
        related_name='business_customer_infos',
        null=True,
        blank=True,
        default=None,
        on_delete=models.SET_NULL,
    )
    import_uid = models.CharField(max_length=64, null=True, blank=True)

    # In Styleseat the same client has multiple ids (different for each staffer)
    import_uids = ArrayField(
        models.CharField(max_length=64),
        blank=True,
        null=True,
    )
    notes_import_uids = ArrayField(
        models.CharField(max_length=64),
        blank=True,
        null=True,
    )
    # FIELDS FOR CUSTOMER:
    client_type = models.CharField(
        blank=True,
        choices=CLIENT_TYPE_CHOICES,
        default=CLIENT_TYPE__UNKNOWN,
        max_length=2,
        null=True,
    )

    first_appointment = models.ForeignKey(  # field owned & managed by boost
        'booking.Appointment',
        null=True,
        blank=True,
        related_name='bci_as_first_appointment',
        on_delete=models.SET_NULL,
    )

    bookmarked = models.BooleanField(
        default=False, help_text=_('Added to the favourite businesses')
    )
    bookmarked_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Bookmarked date (UTC)',
    )
    birthday = models.DateField(
        null=True,
        blank=True,
        verbose_name='Birthday date (UTC)',
    )  # by default year 1900
    # FIELDS FOR BUSINESS:
    business_secret_note = models.CharField(
        _('business secret note'),
        max_length=consts.BCI_SECRET_NOTE__MAX_LENGTH,
        blank=True,
        help_text=_('Secret note made by business about the customer.'),
    )
    blacklisted = models.BooleanField(
        _('blacklisted'),
        default=False,
        help_text=_(
            'If checked, no more bookings by this customer will be allowed in this business'
        ),
    )
    visible_in_business = models.BooleanField(
        _('visible in business'),
        default=True,
        help_text=_("If checked, customer will appear in business' customers list"),
    )
    # FIELDS REPLICATING INFO FROM USER MODELS:
    # django.contrib.auth.models.User fields
    first_name = models.CharField(
        _('first name'),
        max_length=consts.FIRST_NAME_LEN,
        blank=True,
    )
    last_name = models.CharField(
        _('last name'),
        max_length=consts.LAST_NAME_LEN,
        blank=True,
    )
    # Has manual db_index with upper()
    email = models.EmailField(
        _('e-mail address'),
        max_length=75,
        blank=True,
        validators=[booksy_validate_email],
        db_index=True,
    )
    # webapps.user.models.User fields
    cell_phone = BooksyPhoneNumberField(db_index=True)
    # webapps.user.models.UserProfile fields
    address_line_1 = models.CharField(
        _('address line 1'),
        blank=True,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    address_line_2 = models.CharField(
        _('address line 2'),
        blank=True,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    city = models.CharField(max_length=100, null=True, blank=True)
    zipcode = models.CharField(max_length=20, null=True, blank=True)
    region = models.ForeignKey(
        'structure.Region',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    photo = models.ForeignKey(
        'photo.Photo',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    more_photos = models.ManyToManyField(
        to='photo.Photo',
        through='business.BCIPhoto',
        related_name='business_customers',
    )
    bookmarked_resources = models.ManyToManyField(
        to='business.Resource',
        related_name='bookmarking_customers',
    )
    discount = models.IntegerField(
        default=0, validators=[MaxValueValidator(100), MinValueValidator(0)]
    )
    allergens = models.TextField(blank=True)
    service_questions = DataClassField(QuestionsAndAnswersList, null=True)

    # GDPR AGREEMENTS
    # settings.WB_AGREEMENT
    web_communication_agreement = models.BooleanField(
        default=False,
    )
    processing_consent = models.BooleanField(default=False)
    ask_for_consent = models.BooleanField(default=False, null=True)

    # #45904 Trusted Clients
    trusted = models.BooleanField(default=False)

    marketplace_claim = models.TextField(null=True, blank=True)  # depricated - read only
    deny_claim_reason = models.ForeignKey(
        'marketplace.MarketplaceClaimDenyReason',
        related_name='business_customer_info',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    claimed = models.BooleanField(blank=True, default=False)
    boost_last_celebration_at = models.IntegerField(blank=True, default=0)
    tax_id = models.CharField(
        max_length=32,
        null=True,
        blank=True,
    )
    tags = ManyToManyField(
        Tag,
        through=BusinessCustomerInfoTag,
        through_fields=('customer', 'tag'),
        blank=True,
    )
    invited = models.BooleanField(blank=True, default=False)
    last_invite = models.DateTimeField(
        blank=True,
        null=True,
        default=None,
    )
    excluded_from_delayed_invitation = models.BooleanField(default=False)

    # region Family and Friends
    members = models.ManyToManyField(
        'self',
        through='family_and_friends.BCIRelation',
        through_fields=('parent_bci', 'member_bci'),
        related_name='+',
        symmetrical=False,
    )
    parents = models.ManyToManyField(
        'self',
        through='family_and_friends.BCIRelation',
        through_fields=('member_bci', 'parent_bci'),
        related_name='+',
        symmetrical=False,
    )

    type_data = OneToOneField(
        to=BCITypeData,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    # DEPRICATED TODO to be removed in PSE-732
    first_payment_link_received = models.DateField(
        null=True,
        blank=True,
    )

    @cached_property
    def is_active_member(self) -> bool:
        """Family and friends member with own contact data."""
        if hasattr(self, 'not_active_member'):
            return not self.not_active_member
        if hasattr(self, 'use_parent_data') and hasattr(self, 'relationship_type'):
            return (
                not self.use_parent_data
                and self.relationship_type not in INACTIVE_RELATIONSHIP_TYPES
            )
        return not self.relations_to_parents.filter(
            Q(use_parent_data=True) | Q(relationship_type__in=INACTIVE_RELATIONSHIP_TYPES),
        ).exists()

    # endregion Family and Friends

    FIELDS_FROM_USER = (
        'first_name',
        'last_name',
        'email',
        'cell_phone',
        'birthday',
    )
    FIELDS_FROM_USERPROFILE = (
        'address_line_1',
        'address_line_2',
        'city',
        'zipcode',
        'region',
        'photo',
    )
    FIELDS_FOR_BUSINESS = (
        'business_secret_note',
        'blacklisted',
        'visible_in_business',
        'discount',
        'allergens',
        'trusted',
        'tax_id',
        'tags',
        'type_data',
    )
    FIELDS_FOR_CUSTOMER = ('recurring', 'bookmarked')

    objects = BusinessCustomerInfoManager()

    all_objects = models.Manager()

    @property
    def timezone(self):
        return self.business.get_timezone()

    @property
    def recurring(self):
        return {
            None: None,
            BusinessCustomerInfo.CLIENT_TYPE__UNKNOWN: None,  # should ask
            BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW: False,
        }.get(self.client_type, True)

    @property
    def full_name(self):
        if self.user_id is None:
            user_active = False
        else:
            user_active = not bool(self.customer_data.deleted)
        return self.build_full_name(
            self.first_name,
            self.last_name,
            bool(self.visible_in_business),
            str(self.id),
            user_active,
        )

    @full_name.setter
    def full_name(self, value):
        self.first_name, self.last_name = self.split_full_name(value)

    @staticmethod
    def build_full_name(
        first_name, last_name, visible_in_business=True, bci_id='', user_active=False
    ):
        if not visible_in_business and not user_active:
            return gettext('Deleted user') + f' [{bci_id}]'
        return f'{first_name} {last_name}'.strip()

    @staticmethod
    def split_full_name(full_name):
        name_parts = full_name.strip().split(' ')
        if len(name_parts) == 1:
            # one word means a first name in case of BCIs
            first_name, last_name = name_parts[0], ''
        else:
            first_name = ' '.join(name_parts[:-1])
            last_name = ' '.join(name_parts[-1:])
        return (
            first_name[: consts.FIRST_NAME_LEN],
            last_name[: consts.LAST_NAME_LEN],
        )

    @classmethod
    def get_max_scored_bci(
        cls,
        candidates: t.List[t.Dict],
        request: '_BCIFromContactRequest',
        bci_get_prefer_way: 'BCIGetPreferWay' = BCIGetPreferWay.EMAIL,
    ) -> t.Optional[int]:
        """Score BCI based on email/phone match. get_from_contact helper"""
        # Nothing to score
        if not candidates:
            return None
        if len(candidates) == 1:
            return candidates[0]['id']

        applied_score = {
            BCIGetPreferWay.EMAIL: [8, 2, 4, 1],
            BCIGetPreferWay.PHONE: [4, 1, 8, 2],
        }.get(bci_get_prefer_way, [8, 2, 4, 1])

        max_score = 0
        best_scored = candidates[0]
        for bci in candidates:
            score = 0
            customer_email = request.contact_email
            customer_phone = request.contact_cell_phone
            if customer_email:
                if bci.get('user_id') in request.users_with_email:
                    score += applied_score[0]  # 1st case if prefer way is email
                if bci['email'].lower() == customer_email:
                    score += applied_score[1]  # 2nd case if prefer way is email
            if customer_phone:
                if bci.get('user_id') in request.users_with_phone:
                    score += applied_score[2]  # 3rd case if prefer way is email
                if bci['cell_phone'] == customer_phone:
                    score += applied_score[3]  # 4th case if prefer way is email
            if score > max_score:
                best_scored = bci
                max_score = score

        return best_scored['id']

    @classmethod
    def get_from_contact(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls,
        business,
        customer_email='',
        customer_phone='',
        exclude_id=None,
        prefetch_profiles=True,
        bci_get_prefer_way=BCIGetPreferWay.EMAIL,
    ) -> t.Optional[BusinessCustomerInfo]:
        """The only safe way to get BCI matching contact data.

        :returns: BusinessCustomerInfo object or None

        """
        customer_email = (customer_email or '').lower()
        customer_phone = customer_phone or ''
        if not customer_email and not customer_phone:
            # cannot get any BusinessCustomerInfo
            return None

        # pylint: disable=invalid-name
        DjangoUser = django_apps.get_model(settings.AUTH_USER_MODEL, require_ready=True)  #
        BooksyUser = django_apps.get_model('user.User', require_ready=True)
        # pylint: enable=invalid-name
        # prepare filters for candidates
        exclusion = {'id': exclude_id} if exclude_id is not None else {}

        # <---Justification--->
        # you may wonder way so many similar quiries
        # why we can't create join query with or statement,
        # and this will do the job.
        # Apperantly we can but this will not optimal and will
        # not use indexes on postgres tables.
        # That why this should be done in 4 small queries.
        # For more details see  #69578
        base_qs = business.business_customer_infos.exclude(**exclusion)
        # do not evaluate use subquery
        user_ids = base_qs.filter(user_id__isnull=False).values_list(
            'user_id',
            flat=True,
        )

        # region Get users with email and phone
        # we do we use separate queries for each user
        # Because in db it is two separate tables
        # with separate indexes
        # creating join query with or operator will not take
        # advantage of indexes
        users_with_email = set()
        users_with_phone = set()
        if user_ids:
            if customer_email:
                users_with_email = set(
                    DjangoUser.objects.filter(
                        email=customer_email,  # 1st case if prefer way is email
                        id__in=user_ids,
                    ).values_list('id', flat=True)
                )
            if customer_phone:
                users_with_phone = set(
                    BooksyUser.objects.filter(
                        cell_phone=customer_phone,  # 3rd case if prefer way is email
                        id__in=user_ids,
                    ).values_list('id', flat=True)
                )
        # endregion
        request = _BCIFromContactRequest(
            users_with_email,
            users_with_phone,
            customer_email,
            customer_phone,
        )
        # prepare filters for candidates
        filters = Q()
        # join filters with OR operator
        if request.contact_email:
            filters.add(
                Q(email=request.contact_email),  # 2nd case if prefer way is email
                Q.OR,
            )
        if request.contact_cell_phone:
            filters.add(
                Q(cell_phone=request.contact_cell_phone),  # 4th case if prefer way is email
                Q.OR,
            )
        user_with_data = users_with_phone | users_with_email
        if user_with_data:
            filters.add(
                Q(user__id__in=user_with_data),
                Q.OR,
            )
        # Exclude soft-deleted BCIs
        filters.add(
            Q(deleted__isnull=True),
            Q.AND,
        )
        candidates_qs = list(
            base_qs.filter(filters).values(
                'id',
                'email',
                'cell_phone',
                'user_id',
            )
        )
        best_candidate_id = cls.get_max_scored_bci(
            candidates_qs,
            request,
            bci_get_prefer_way,
        )
        if not best_candidate_id:
            return None
        best_candidate_qs = base_qs.filter(id=best_candidate_id).select_related('user')
        if prefetch_profiles:
            best_candidate_qs = best_candidate_qs.prefetch_related('user__profiles')
        return best_candidate_qs.first()

    @classmethod
    def get_or_create_from_contact(  # pylint: disable=too-many-arguments
        cls,
        bci_from_contact: BCIFromContact,
        dry_run=False,
        prefetch_profiles=True,
        bci_get_prefer_way=BCIGetPreferWay.EMAIL,
    ):
        """The only safe way to get or create BCI from contact data.

        It works differently than Django's cls.objects.get_or_create:
            - can return None instead of BCI object if contact data is
              insufficient
            - it updates existing BCI if it does not have user
              and is missing some fields

        :param bci_from_contact: BCIFromContact
        :param dry_run: dry run
        :param prefetch_profiles: extract related customer profiles
        :param bci_get_prefer_way: choice of prefer way to get BCI if there is more than one

        :returns: (BusinessCustomerInfo object or None, boolean)

        """
        customer_name = bci_from_contact.customer_name or ''
        customer_email = (bci_from_contact.customer_email or '').lower()
        customer_phone = bci_from_contact.customer_phone or ''

        if customer_phone:
            parsed = parse_phone_number(customer_phone)
            if parsed.is_valid:
                customer_phone = parsed.db_format
            else:
                customer_phone = ''

        if not customer_email and not customer_phone:
            # cannot get or create any BusinessCustomerInfo
            return None, False

        bci = cls.get_from_contact(
            bci_from_contact.business,
            customer_email,
            customer_phone,
            prefetch_profiles=prefetch_profiles,
            bci_get_prefer_way=bci_get_prefer_way,
        )

        # BCI doesn't exist yet - create new BusinessCustomerInfo
        if bci is None:
            # create new BusinessCustomerInfo
            first_name, last_name = cls.split_full_name(customer_name)
            if CustomerQuickSignInUpFlag() and is_private_email(customer_email):
                customer_email = ''
            params = {
                'business': bci_from_contact.business,
                'email': customer_email,
                'cell_phone': customer_phone,
                'first_name': first_name,
                'last_name': last_name,
            }
            bci = BusinessCustomerInfo(**{**(bci_from_contact.defaults or {}), **params})
            if not dry_run:
                bci.save()
            return bci, True

        # update non-User customers
        if bci.user is None:
            update_fields = []
            if bci.email == '' and customer_email:
                bci.email = customer_email
                update_fields.append('email')
            if bci.cell_phone == '' and customer_phone:
                bci.cell_phone = customer_phone
                update_fields.append('cell_phone')
            if bci.full_name == '' and customer_name:
                bci.full_name = customer_name
                update_fields.extend(['first_name', 'last_name'])

            if update_fields and not dry_run:
                bci.save(update_fields=update_fields)

        # return the best match
        return bci, False

    @classmethod
    def get_or_create_for_user(cls, user, business, recurring, source, defaults=None):
        # pylint: disable=too-many-arguments, too-many-positional-arguments
        defaults = defaults or {}
        update_fields = []

        bci, created = cls._create_bci_for_user_if_not_exists(user, business, defaults)

        source_type_map = cls.SOURCE_CLIENT_TYPE_MAP
        if created or bci.client_type == BusinessCustomerInfo.CLIENT_TYPE__UNKNOWN:
            if source.name in source_type_map:
                bci.set_client_type(source_type_map[source.name])
            elif (
                bci.cell_phone or (user and user.cell_phone)
            ) and BusinessCustomerInfo.objects.filter(
                business_id=business.id,
            ).filter(
                Q(cell_phone=bci.cell_phone or user.cell_phone)
                | Q(user__cell_phone=bci.cell_phone or user.cell_phone)
            ).exclude(
                id=bci.id
            ).exists():
                bci.set_client_type(bci.CLIENT_TYPE__BUSINESS_PHONE)
            elif recurring or business.is_user_from_recommending_salon(user_id=user.id):
                bci.set_client_type(bci.CLIENT_TYPE__CUSTOMER_RECURRING)
            else:
                bci.set_client_type(bci.CLIENT_TYPE__CUSTOMER_NEW)
            update_fields.append('client_type')

        if bci.visible_in_business is False:
            bci.visible_in_business = True
            update_fields.append('visible_in_business')
        if not bci.user:
            if CustomerQuickSignInUpFlag() and is_private_email(bci.email):
                update_fields.append('email')
                bci.email = ''
            bci.user = user
            update_fields.append('user')

        if update_fields:
            bci.save(update_fields=update_fields)
            bump_document(River.BUSINESS_CUSTOMER, bci.id)
        return bci

    @classmethod
    def get_or_create_for_user_invisible(
        cls, user: 'User', business: 'Business', defaults: dict = None
    ) -> tuple[BusinessCustomerInfo, bool]:
        defaults = defaults or {}
        defaults['visible_in_business'] = False
        bci, created = cls._create_bci_for_user_if_not_exists(user, business, defaults)
        return bci, created

    @classmethod
    def _create_bci_for_user_if_not_exists(
        cls, user: 'User', business: 'Business', defaults: dict
    ) -> tuple[BusinessCustomerInfo, bool]:
        bci = cls.find_for_user(user, business)
        if not bci:
            bci = BusinessCustomerInfo.objects.create(user=user, business=business, **defaults)
            return bci, True
        return bci, False

    @classmethod
    def find_for_user(cls, user, business) -> BusinessCustomerInfo | None:
        bci = user.business_customer_infos.filter(business=business, deleted__isnull=True).first()

        if bci:
            return bci

        if not user.email and not user.cell_phone:
            return None

        base_qs = cls.objects.filter(
            business=business,
            deleted__isnull=True,
            user__isnull=True,
        ).order_by('-updated')
        qs_by_email = base_qs.filter(email=user.email).exclude(email='')
        qs_by_phone = base_qs.filter(cell_phone=user.cell_phone, cell_phone__isnull=False).exclude(
            cell_phone=''
        )

        if not settings.SMS_REGISTRATION_CODE_REQUIRED:
            additional_conditions = (
                (Q(last_name__iexact=user.last_name) & ~Q(last_name=''))
                | (Q(first_name__iexact=user.first_name) & ~Q(first_name=''))
                | Q(
                    birthday=user.birthday,
                    birthday__isnull=False,
                )
            )
            qs_by_phone = qs_by_phone.filter(additional_conditions)

        if not user.email:
            bci = qs_by_phone.first()
        elif not user.cell_phone:
            bci = qs_by_email.first()
        else:
            bci = qs_by_email.first() or qs_by_phone.first()

        return bci

    @cached_property
    def ended_bookings(self):
        from webapps.booking.models import SubBooking

        return SubBooking.objects.ended_bookings().filter(
            Q(appointment__booked_for=self)
            | Q(appointment__id__in=self.actionable_booked_by_me_appointment_ids)
        )

    @cached_property
    def ended_appointments(self):
        from webapps.booking.models import Appointment

        appointment_ids = self.my_appointments_ids + self.actionable_booked_by_me_appointment_ids
        return Appointment.objects.ended_appointments().filter(id__in=appointment_ids)

    @cached_property
    def my_appointments_ids(self):
        if not self.id:
            return []
        return list(self.appointments.values_list('id', flat=True))

    @cached_property
    def actionable_booked_by_me_appointment_ids(self):
        if not self.id or not self.memberprofile_set.exists():
            return []
        inactive_member_bci_ids = []
        for member in self.memberprofile_set.first().inactive_members:
            if bci := member.bcis.filter(business=self.business).first():
                inactive_member_bci_ids.append(bci.id)
        return list(
            self.appointments_booked_by_me.filter(
                booked_for__id__in=inactive_member_bci_ids
            ).values_list('appointment__id', flat=True)
        )

    @cached_property
    def customer_review_status(self):  # pylint: disable=too-many-return-statements
        if not self.id:
            return self.REVIEW_ADD_STATUS_EXPIRED
        from webapps.booking.models import Appointment

        if not self.appointments.exists() and not self.actionable_booked_by_me_appointment_ids:
            return self.REVIEW_ADD_STATUS_MISSING

        if self.latest_booking is None:
            return self.REVIEW_ADD_STATUS_SCHEDULED

        if hasattr(self.latest_booking, 'review'):
            return self.REVIEW_ADD_ALREADY_EXIST
        latest_booked_till = self.latest_booking.booked_till
        latest_status = self.latest_booking.appointment.status

        if latest_status == Appointment.STATUS.NOSHOW:
            return self.REVIEW_ADD_BLOCKED_NO_SHOW

        if latest_booked_till > (tznow() - settings.CAN_ADD_REVIEW_LIMIT_PERIOD):
            return self.REVIEW_ADD_STATUS_AWAITING

        return self.REVIEW_ADD_STATUS_EXPIRED

    @cached_property
    def latest_appointment(self):
        return self.ended_appointments.first()

    @cached_property
    def latest_booking(self):
        appointment = self.latest_appointment
        if appointment and appointment.subbookings:
            return max(appointment.subbookings, key=lambda s: s.booked_till)

    @cached_property
    def originally_first_appointment(self) -> 'Appointment':
        """
        Returns the chronologically first appointment. It is used to determine if the client's first
        appointment was created when Boost was enabled or not.

        It is not always the same appointment that payment will be proceed for.
        """
        return self.appointments.order_by('created').first()

    @property
    def can_add_review(self):
        """
        DEPRECATED! Please use `can_add_review` field from within serialized
        appointment!

        Denotes if the Business can receive Review from this Customer.
        """
        if not self.id:
            return False
        return (
            self.business.active
            and self.business.visible
            and self.customer_review_status == 'awaiting'
        )

    def __repr__(self):
        return (
            f'<{self._meta.object_name}: ({self.id},'
            f' user_id={self.user_id}, email={self.email}, phone={self.cell_phone}>'
        )

    def __str__(self):
        data = self.as_customer_data()
        ret = f'[{self.id}] {data.full_name} {data.email} {data.cell_phone}'.strip()
        return ret

    class Meta:
        verbose_name = _('Business Customer Card')
        verbose_name_plural = _('Business Customer Cards')
        constraints = [
            models.UniqueConstraint(
                name='business_user_unique',
                fields=['business', 'user'],
                condition=Q(user__isnull=False, deleted__isnull=True),
            ),
            models.UniqueConstraint(
                name='business_email_unique',
                fields=['business', 'email'],
                condition=Q(user__isnull=True, deleted__isnull=True) & (~Q(email='')),
            ),
            models.UniqueConstraint(
                name='business_cell_phone_unique',
                fields=['business', 'cell_phone'],
                condition=Q(user__isnull=True, email='', deleted__isnull=True)
                & (~Q(cell_phone='')),
            ),
        ]

    def validate_unique(self, exclude=None):
        super().validate_unique(exclude)
        if not self.user and not self.email and self.cell_phone:
            bci_qs = BusinessCustomerInfo.objects.filter(
                business__id=self.business.id,
                deleted__isnull=True,
                user__isnull=True,
                email='',
                cell_phone=self.cell_phone,
            ).exclude(id=self.id)
            if bci_qs.exists():
                raise ValidationError(
                    {
                        'cell_phone': _(
                            'This Customer Card is duplicated. Please use another one with '
                            'the same phone number or connect duplicated cards.'
                        ),
                    }
                )

    def unique_error_message(self, model_class, unique_check):
        from django.utils.text import capfirst

        opts = model_class._meta

        params = {
            'model': self,
            'model_class': model_class,
            'model_name': str(capfirst(opts.verbose_name)),
            'unique_check': unique_check,
        }

        # A unique field
        if len(unique_check) == 1:
            field = opts.get_field(unique_check[0])
            params['field_label'] = str(capfirst(field.verbose_name))
            return ValidationError(
                message=field.error_messages['unique'],
                code='unique',
                params=params,
            )

        # unique_together
        # CUSTOM MESSAGES
        if 'email' in unique_check:
            msg = _('Customer Card with this email address already exists')
        elif 'cell_phone' in unique_check:
            msg = _('Customer Card with this phone number already exists')
        else:  # 'user' in unique_check
            msg = _('Customer Card for this user already exists')
        return ValidationError(
            message=msg,
            code='unique_together',
            params=params,
        )

    def save(self, *args, **kwargs):
        if self.email:
            self.email = self.email.lower()
        return super().save(*args, **kwargs)

    @property
    def customer_profile(self):
        if not hasattr(self, '_customer_profile_cache'):
            self._customer_profile_cache = self.get_customer_profile()
        return self._customer_profile_cache

    def get_customer_profile(self):
        if self.user is None:
            return None

        from webapps.user.models import UserProfile

        customer_profile = None
        if 'profiles' in getattr(self.user, '_prefetched_objects_cache', {}):
            # use prefetched data
            profiles = [
                profile
                for profile in self.user.profiles.all()
                if profile.profile_type == UserProfile.Type.CUSTOMER
            ]
            if profiles:
                customer_profile = profiles[0]
        else:
            # fetch data
            customer_profile = (
                self.user.profiles.filter(
                    profile_type=UserProfile.Type.CUSTOMER,
                )
                .select_related(
                    'photo',
                    'region',
                )
                .first()
            )

        if not customer_profile:
            customer_profile = self.user.profiles.get_or_create(
                profile_type=UserProfile.Type.CUSTOMER
            )[0]

        return customer_profile

    @property
    def from_promo(self):
        from webapps.marketplace.models import BoostClientCard

        return BoostClientCard.get_or_create_related(self).from_promo

    def as_customer_data(self):
        from webapps.business_customer_info.customer_data import (
            CustomerData,
        )

        return CustomerData(business_customer_info=self)

    def as_customer_data_with_relation(self):
        customer_data = self.as_customer_data()
        if self.is_active_member:
            return customer_data

        parent = self.parents.order_by(
            '-relations_to_members__use_parent_data',
            'relations_to_members__created',
        ).first()

        if not parent:
            return customer_data

        parent_data = parent.as_customer_data()
        customer_data.cell_phone = parent_data.cell_phone
        customer_data.email = parent_data.email
        return customer_data

    @cached_property
    def customer_data(self):
        return self.as_customer_data()

    def disable_post_save_indexing(self):
        """Disable indexing in post_save_handler_async."""
        self._signal__do_not_index = True

    @staticmethod
    def post_delete_handler(sender, instance, **kwargs):  # pylint: disable=unused-argument
        if getattr(instance, '_signal__do_not_index', False):
            return
        business_customer_info_delete_task.delay(
            instance.id, instance.business_id, refresh_index=False
        )

    @staticmethod
    def post_save_handler(
        sender, instance, created, *_args, **_kwargs
    ):  # pylint: disable=unused-argument
        from webapps.business.messages.bci import (
            BASIC_CUSTOMER_DATA_FIELDS,
            BasicCustomerDataChangedMessage,
        )

        dirty = instance.get_dirty_fields(check_relationship=True)
        if ({'user', 'bookmarked'}.intersection(dirty) or created) and instance.user:
            append_to_user_search_data_fast_river(instance.user)
        # we show this value only to merchant with loyal cards management
        user_data = UserData(key=f'business_{instance.business_id}')
        if (
            created or any(value in dirty for value in BASIC_CUSTOMER_DATA_FIELDS)
        ) and LoyaltyProgramFlag(user_data):
            transaction.on_commit(BasicCustomerDataChangedMessage(instance).publish)

    @staticmethod
    def post_user_related_save_handler(
        sender, instance, *_args, **_kwargs
    ):  # pylint: disable=unused-argument
        if getattr(instance, 'user', None):
            append_to_user_search_data_fast_river(instance.user)

    def set_client_type(self, client_type):
        if not self.client_type or self.client_type == self.CLIENT_TYPE__UNKNOWN:
            self.client_type = client_type
            return True
        return False

    def get_or_create_patient_file(
        self,
        force: bool = False,
    ) -> t.Optional[BCIPatientFile]:
        try:
            return self.patient_file  # pylint: disable=access-member-before-definition
        except BCIPatientFile.DoesNotExist:
            if not force and not self.business.patient_file_enabled:
                return None
            self.patient_file = BCIPatientFile.objects.create(bci=self)
            return self.patient_file

    def get_city(self):
        if self.city:
            return self.city
        if self.region is None:
            return None
        city_instance = self.region.get_parent_by_type(settings.ES_CITY_LVL)
        return city_instance and city_instance.name

    def get_zip(self):
        from webapps.structure.models import Region

        if self.zipcode:
            return self.zipcode
        if self.region is None or self.region.type != Region.Type.ZIP:
            return None
        return self.region.name

    @property
    def has_pay_by_app(self) -> bool:
        """User has payment_methods for pay_by_app"""
        return hasattr(self.user, 'payment_methods') and self.user.payment_methods.exists()

    def has_card(self, provider: PaymentProviderEnum) -> bool:
        return (
            hasattr(self.user, 'payment_methods')
            and self.user.payment_methods.filter(
                provider=provider,
            ).exists()
        )

    @cached_property
    def es_bookings(self) -> QuerySet('SubBooking'):
        from webapps.booking.models import Appointment, SubBooking
        from webapps.business.models import Resource

        app_ids = list(
            Appointment.objects.filter(
                booked_for_id=self.id,
                deleted__isnull=True,
            ).values_list(
                'id',
                flat=True,
            )
        )
        return (
            SubBooking.objects.filter(
                appointment__id__in=app_ids,
                appointment__booked_for_id=self.id,
                appointment__deleted__isnull=True,
                resources__type=Resource.STAFF,
            )
            .order_by(
                'booked_till',
            )
            .values(
                'appointment__status',
                'booked_till',
                'service_variant__service_id',
                'resources__id',
                'appointment__id',
            )
        )

    def get_first_visit(self) -> bool:
        """Returns true if BCI has first visit.

        ES_bookings is filled in BusinessCustomerDocumentSerializer."""
        from webapps.booking.models import Appointment

        return (
            len(self.es_bookings) == 1
            and self.es_bookings[0]['appointment__status']
            in Appointment.STATUSES_OCCUPYING_TIME_SLOTS
        )

    def invite(
        self,
        source=InvitationSource.CLIENT_INVITED,
        sms_only: bool = False,
        staffer_invite: bool = False,
        staffer_id: int = None,
    ):
        # pylint: disable=cyclic-import
        from lib.invite import CustomerContactDetails, CustomerInvitationResult, invite_customer

        # pylint: enable=cyclic-import

        cell_phone = self.cell_phone
        if not cell_phone and self.user:
            cell_phone = self.user.cell_phone
        email = self.email
        if not email and self.user:
            email = self.user.email
        if CustomerQuickSignInUpFlag() and is_private_email(email):
            email = None
        res = invite_customer(
            CustomerContactDetails(
                business=self.business,
                user=self.user,
                email=email if not sms_only else None,
                phone=cell_phone,
                source=source,
                graceful_return=True,
                bci_id=self.id,
                staffer_invite=staffer_invite,
                staffer_id=staffer_id,
            )
        )
        invitation_result = res.get('result') if res else None
        if invitation_result in [
            CustomerInvitationResult.SUCCESS_SMS,
            CustomerInvitationResult.SUCCESS_EMAIL,
            CustomerInvitationResult.SUCCESS_PUSH,
        ]:
            self.invited = True
            self.last_invite = tznow()
            self.business.integrations['last_customer_invitation_sent'] = tznow().isoformat()
            self.business.save(update_fields=['integrations'])
            self.save(update_fields=['invited', 'last_invite'])
        return invitation_result or CustomerInvitationResult.UNKNOWN_ERROR

    @staticmethod
    def _merge_client_type(value_1, value_2):
        if value_1 in (None, BusinessCustomerInfo.CLIENT_TYPE__UNKNOWN):
            return value_2

        return value_1

    @staticmethod
    def _merge_first_appointment_id(value_1, value_2):
        if value_1 is None:
            return value_2

        if value_2 is None:
            return value_1

        return min(value_1, value_2)

    @staticmethod
    def _merge_name(value_1, value_2):
        if not value_1:
            return value_2

        if not value_2:
            return value_1

        return value_1 if len(value_1) >= len(value_2) else value_2

    @staticmethod
    def _merge_address(data_1, data_2):
        if not data_1['address_line_1'] and data_2['address_line_1']:
            return data_2

        return data_1

    @staticmethod
    def _merge_service_questions(data_1, data_2):
        if not data_1:
            return data_2

        if not data_2:
            return data_1

        merged_data = data_2.copy()
        merged_data.update(data_1.filter_answered())
        return merged_data

    @staticmethod
    def _merge_bookmarked(data_1, data_2):
        return {
            'bookmarked': data_1['bookmarked'] or data_2['bookmarked'],
            'bookmarked_date': (data_1['bookmarked_date'] or data_2['bookmarked_date']),
        }

    @staticmethod
    def _merge_claim(data_1, data_2):
        return {
            'marketplace_claim': (data_1['marketplace_claim'] or data_2['marketplace_claim']),
            'deny_claim_reason_id': (
                data_1['deny_claim_reason_id'] or data_2['deny_claim_reason_id']
            ),
        }

    def merge(  # pylint: disable=too-many-arguments, too-many-branches, too-many-statements, too-many-positional-arguments
        self,
        other: BusinessCustomerInfo,
        overrides: Dict[str, Any] = None,
        on_conflict: MergeFunc = None,
        reindex: bool = True,
        merge_reason=ClaimLogReasons.MANUAL,
    ) -> BusinessCustomerInfo:
        """
        Merge other BusinessCustomerInfo into `self`.

        All bookings, transactions, etc will be merged too. The `other`
        card will be deleted.
        """
        if other.id == self.id:
            return self  # nothing to do

        if other.business_id != self.business_id:
            raise MergeError('Client cards should be within same business')

        if other.user_id != self.user_id and other.user_id is not None and self.user_id is not None:
            raise MergeError('Client cards belong to different users')

        if BCIPatientFile.objects.filter(bci__in=[self.id, other.id]).exists():
            raise MergeError('Customer card with patient file data cannot be merged')

        if not self.is_active_member or not other.is_active_member:
            raise MergeError('Inactive Family&Friends member cannot be merged')

        source_bci, target_bci = self.get_merge_source_target_bcis(other)

        merge_status = self._get_merge_status(
            merge_reason=merge_reason,
            target_bci=target_bci,
            source_bci=source_bci,
        )

        if overrides:
            if 'user' in overrides:
                if (
                    BusinessCustomerInfo.objects.filter(
                        user=overrides['user'],
                    )
                    .exclude(id__in=[target_bci.id, source_bci.id])
                    .exist()
                ):
                    raise MergeError('Client card with this user already exist')

            if 'email' in overrides:
                if (
                    BusinessCustomerInfo.objects.filter(
                        email=overrides['email'],
                    )
                    .exclude(id__in=[target_bci.id, source_bci.id])
                    .exist()
                ):
                    raise MergeError('Client card with this email already exist')

            if 'cell_phone' in overrides:
                if (
                    BusinessCustomerInfo.objects.filter(
                        cell_phone=overrides['cell_phone'],
                    )
                    .exclude(id__in=[target_bci.id, source_bci.id])
                    .exist()
                ):
                    raise MergeError('Client card with this cell phone already exist')

        merge_helper = MergeHelper(
            self.__class__,
            {
                'created': MergeFunc.MIN,
                'updated': MergeFunc.KEEP,
                'deleted': MergeFunc.KEEP,
                'business': MergeFunc.KEEP,
                'import_uids': MergeFunc.KEEP,
                'notes_import_uids': MergeFunc.KEEP,
                'business_secret_note': MergeFunc.APPEND_PARAGRAPH,
                'blacklisted': MergeFunc.BOOLEAN_AND,
                'visible_in_business': MergeFunc.BOOLEAN_OR,
                'allergens': MergeFunc.APPEND_PARAGRAPH,
                'web_communication_agreement': MergeFunc.BOOLEAN_OR,
                'trusted': MergeFunc.BOOLEAN_OR,
                'claimed': MergeFunc.BOOLEAN_OR,
                'boost_last_celebration_at': MergeFunc.MAX,
                'invited': MergeFunc.BOOLEAN_OR,
                'client_type': self._merge_client_type,
                'first_appointment': self._merge_first_appointment_id,
                'first_name': self._merge_name,
                'last_name': self._merge_name,
                'service_questions': self._merge_service_questions,
            },
            default_merge_func=MergeFunc.EITHER,
            on_conflict=on_conflict,
        )
        merge_helper.set_merge_func(['bookmarked', 'bookmarked_date'], self._merge_bookmarked)
        merge_helper.set_merge_func(['marketplace_claim', 'deny_claim_reason'], self._merge_claim)
        merge_helper.set_merge_func(
            ['address_line_1', 'address_line_2', 'city', 'zipcode', 'region'], self._merge_address
        )

        try:
            diff = merge_helper.prepare(target_bci, source_bci, overrides=overrides)
        except MergeConflictError as exc:
            log_row = ClaimLog(
                target_bci=target_bci,
                source_bci=source_bci,
                merge_reason=merge_reason,
                status=merge_status,
                type=ClaimLogType.BUSINESS_CUSTOMER_INFO,
            )
            log_row.save_source_model_related_objects(
                merge_helper.get_related_objects(target_bci, source_bci)
            )
            log_row.save()
            raise exc
        merged_bci = diff.apply(copy=True)
        merged_bci.disable_post_save_indexing()
        source_bci.disable_post_save_indexing()
        source_bci.visible_in_business = False

        if merge_status == ClaimLogStatuses.MERGED:
            merge_helper.commit(merged_bci, source_bci)

            if reindex:
                merged_doc = merged_bci.get_document(refresh=True)
                merged_doc.save()

            # source_bci has to disappear from business' BCI list immediately
            # (next step, i.e. deleting document, is async and takes time)
            source_doc = source_bci.get_document(refresh=True)
            source_doc.save(refresh=True)

            try:
                delete_document(ESDocType.BUSINESS_CUSTOMER, [source_bci.id])
            except ElasticsearchException:
                pass
            source_model_related_objects = merge_helper.source_model_related_objects
        else:
            source_model_related_objects = merge_helper.get_related_objects(merged_bci, source_bci)

        log_row = ClaimLog(
            target_bci=target_bci,
            source_bci=source_bci,
            merge_reason=merge_reason,
            status=merge_status,
            type=ClaimLogType.BUSINESS_CUSTOMER_INFO,
        )
        log_row.save_source_model_related_objects(source_model_related_objects)
        log_row.save()
        return merged_bci

    def _get_merge_status(self, merge_reason, target_bci, source_bci):
        if merge_reason == ClaimLogReasons.MANUAL:
            # force merge
            return ClaimLogStatuses.MERGED
        if KillSwitch.killed(KillSwitch.System.CLAIM_APPOINTMENTS_MERGE_BCI):
            return ClaimLogStatuses.PROPOSED
        target_cell_phone = self._get_cell_phone(target_bci)
        source_cell_phone = self._get_cell_phone(source_bci)
        if target_cell_phone != source_cell_phone or (
            not target_cell_phone and not source_cell_phone
        ):
            return ClaimLogStatuses.PROPOSED
        return ClaimLogStatuses.MERGED

    @staticmethod
    def _get_cell_phone(bci):
        """
        Only for internal purposes, if no bci.cell_phone
        then we can get bci.user.cell_phone so we should not show it
        to anyone without user agreement
        """
        cell_phone = bci.cell_phone
        if not cell_phone and bci.user:
            cell_phone = bci.user.cell_phone
        return cell_phone

    def get_merge_source_target_bcis(
        self,
        other: BusinessCustomerInfo,
    ):
        if (  # pylint: disable=too-many-boolean-expressions
            other.user_id
            and not self.user_id
            or other.visible_in_business
            and not self.visible_in_business
            or other.deleted is None
            and self.deleted is not None
        ):
            target_bci = other
            source_bci = self
        else:
            target_bci = self
            source_bci = other
        return source_bci, target_bci

    def get_all_tags(self) -> list[str]:
        return list(self.tags.values_list('name', flat=True))

    def remove_tag(self, tag):
        if tag_to_remove := self.tags.filter(name=tag).first():
            self.tags.remove(tag_to_remove)

    def unlink_parents(self) -> list:
        parents_ids = list(self.parents.values_list('id', flat=True))
        if parents_ids:
            self.parents.clear()
        if hasattr(self, 'is_active_member'):
            delattr(self, 'is_active_member')
        return parents_ids

    def unlink_members(self) -> list:
        members_ids = list(self.members.values_list('id', flat=True))
        if members_ids:
            self.members.clear()
        return members_ids


post_save.connect(
    BusinessCustomerInfo.post_save_handler,
    sender=BusinessCustomerInfo,
)
post_delete.connect(
    BusinessCustomerInfo.post_delete_handler,
    sender=BusinessCustomerInfo,
)

m2m_changed.connect(
    BusinessCustomerInfo.post_user_related_save_handler,
    sender=BusinessCustomerInfo.bookmarked_resources.through,
)


class BusinessCustomerInfoHistory(HistoryModel):
    model = models.ForeignKey(
        BusinessCustomerInfo,
        on_delete=models.CASCADE,
        related_name='history',
    )

    class Meta(HistoryModel.Meta):
        indexes = [
            Index(name='business_bci_created_idx', fields=['created']),
        ]


class BCIConsentChangelog(UndeletableMixin, models.Model):  # pylint: disable=abstract-method
    """
    Denotes changes done to the related BusinessCustomerInfo web_communication_agreement field.
    Each change has associated user that requested this modification (NULL
    value is present if the corresponding user account has been deleted).
    """

    business_customer_info = models.ForeignKey(
        BusinessCustomerInfo,
        related_name='changelogs',
        on_delete=models.CASCADE,
    )
    requested_by = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    data = JSONField(blank=True, default=dict)

    class Meta:
        ordering = ('-created',)

    def __str__(self) -> str:
        return (
            f'Business Customer Info #{self.business_customer_info.pk} consents change '
            f'by {self.requested_by}'
        )

    @classmethod
    def prepare_entry(
        cls,
        user: "User",
        business_customer_info: BusinessCustomerInfo,
    ) -> BCIConsentChangelog:
        from webapps.business_customer_info.serializers.serializers import (
            BusinessCustomerInfoConsentsSerializer,
        )

        representation = BusinessCustomerInfoConsentsSerializer(
            instance=business_customer_info
        ).data
        return cls(
            business_customer_info=business_customer_info,
            requested_by=user,
            data=representation,
        )

    @classmethod
    def create_entry(
        cls, user: "User", business_customer_info: BusinessCustomerInfo
    ) -> BCIConsentChangelog:
        instance = cls.prepare_entry(user, business_customer_info)
        instance.save()
        return instance


class BCIPhoto(PhotoModelMixin, models.Model):
    photo_folder = ImageTypeEnum.USER_MORE_PHOTO
    bci = models.ForeignKey(
        to='business.BusinessCustomerInfo',
        on_delete=models.CASCADE,
        null=False,
    )
    photo = models.ForeignKey(
        to='photo.Photo',
        on_delete=models.CASCADE,
        null=False,
    )
    order = models.PositiveSmallIntegerField(default=0)
    published_to = models.PositiveIntegerField(null=True, default=None)

    class Meta:
        ordering = ('order',)


def get_bci_attached_file_path(instance, filename):
    from webapps.images.tools import generate_business_customer_general_file_path

    business_id = instance.bci.business.id
    bci_id = instance.bci.id
    return generate_business_customer_general_file_path(
        business_id, bci_id, 'attached_files', filename
    )


def country_aware_bci_attached_file_path(instance, filename):
    """Generates country aware upload file path for Storage."""
    from webapps.business_customer_info.tools import (
        country_aware_business_customer_general_file_path,
    )

    return country_aware_business_customer_general_file_path(
        business_id=instance.bci.business.id,
        bci_id=instance.bci.id,
        folder_name='attached_files',
        file_name=filename,
    )


class BCIAttachedFile(models.Model):
    """Model used for storing additional files uploaded to customers.
    (do not confuse with BCIPatientFile is a physio patient card)
    """

    bci = models.ForeignKey(
        BusinessCustomerInfo,
        on_delete=models.CASCADE,
        related_name='attached_files',
    )
    original_name = models.CharField(max_length=150)
    uploaded_file = models.FileField(
        max_length=150,
        storage=BusinessFilesStorage(),
        upload_to=get_bci_attached_file_path,
    )
    attached_file = models.FileField(
        max_length=150,
        storage=storage_dispatcher.business_files(),
        upload_to=country_aware_bci_attached_file_path,
        blank=True,
    )

    def __str__(self):
        return f'{self.bci}, {self.original_name}'


class BCIPatientFile(ArchiveModel):
    """NOTE that verbose_name and help_text field properties are serialized in
    BCIPatientFileFieldsSerializer. Intended for dynamically rendering
    inputs in frontends.
    """

    bci = models.OneToOneField(
        to=BusinessCustomerInfo,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='patient_file',
    )
    file_number = models.CharField(
        verbose_name=_('File number'),
        help_text=_('Enter file number'),
        max_length=30,
        blank=True,
        null=True,
    )

    gender_choices = [
        ('', _('Gender not selected')),
        ('F', _('Female')),
        ('M', _('Male')),
    ]

    gender = models.CharField(
        verbose_name=_('Gender'),
        help_text=_('Select gender'),
        max_length=1,
        choices=gender_choices,
        null=True,
        blank=True,
    )

    national_identity_number = models.CharField(
        verbose_name=_('National identity number'),
        help_text=_('National identity number'),
        max_length=30,
        blank=True,
        null=True,
    )

    document_type = models.CharField(
        verbose_name=_('Document type'),
        help_text=_('Select document type'),
        max_length=30,
        blank=True,
        null=True,
        choices=DocumentType.choices(),
    )
    document_number = models.CharField(
        verbose_name=_('Document number'),
        help_text=_('Enter document number'),
        max_length=30,
        blank=True,
        null=True,
    )

    # Identyfikatory oddzialow wojewodzkich NFZ:
    # http://www.nfz.gov.pl/o-nfz/identyfikatory-oddzialow-wojewodzkich-nfz
    branch_choices = [
        ('', 'Nie wybrano'),
        ('01', 'Dolnośląski Oddział NFZ we Wrocławiu'),
        ('02', 'Kujawsko-Pomorski Oddział NFZ w Bydgoszczy'),
        ('03', 'Lubelski Oddział NFZ w Lublinie'),
        ('04', 'Lubuski Oddział NFZ w Zielonej Górze'),
        ('05', 'Łódzki Oddział NFZ w Łodzi'),
        ('06', 'Małopolski Oddział NFZ w Krakowie'),
        ('07', 'Mazowiecki Oddział NFZ w Warszawie'),
        ('08', 'Opolski Oddział NFZ w Opolu'),
        ('09', 'Podkarpacki Oddział NFZ w Rzeszowie'),
        ('10', 'Podlaski Oddział NFZ w Białymstoku'),
        ('11', 'Pomorski Oddział NFZ w Gdańsku'),
        ('12', 'Śląski Oddział NFZ w Katowicach'),
        ('13', 'Świętokrzyski Oddział NFZ w Kielcach'),
        ('14', 'Warmińsko-Mazurski Oddział NFZ w Olsztynie'),
        ('15', 'Wielkopolski Oddział NFZ w Poznaniu'),
        ('16', 'Zachodniopomorski Oddział NFZ w Szczecinie'),
    ]

    branch = models.CharField(
        verbose_name=_('NFZ branch'),
        help_text=_('Select NFZ branch'),
        max_length=30,
        choices=branch_choices,
        null=True,
        blank=True,
    )

    # Kody uprawnien dodatkowych:
    # https://www.gov.pl/web/zdrowie/dodatkowe-uprawnienia
    special_permissions_codes = [
        ("", "Brak"),
        ("AZ", "AZ"),
        ("IB", "IB"),
        ("IW", "IW"),
        ("PO", "PO"),
        ("IN", "IN"),
        ("WP", "WP"),
        ("ZK", "ZK"),
        ("S", "S"),
    ]

    special_permissions_code = models.CharField(
        verbose_name=_('Special permissions'),
        help_text=_('Select special permissions code'),
        max_length=2,
        choices=special_permissions_codes,
        null=True,
        blank=True,
    )

    customer_source_choices = (
        ('', _('Not selected')),
        ('facebook', 'Facebook'),
        ('www', _('Web page')),
        ('voucher', _('Voucher')),
        ('referral', _('Referral')),
        ('partners', _('Partners')),
        ('other', _('Other')),
    )

    customer_source = models.CharField(
        verbose_name=_('Customer source'),
        help_text=_("Select the customer's source of origin"),
        max_length=10,
        choices=customer_source_choices,
        blank=True,
        null=True,
    )

    def get_details(self):
        return {
            'file_number': self.file_number,
            'gender': self.gender,
            'national_identity_number': self.national_identity_number,
            'document_type': self.document_type,
            'document_number': self.document_number,
            'branch': self.branch,
            'special_permissions_code': self.special_permissions_code,
            'customer_source': self.customer_source,
        }


class DelayedBookmarkEvent(ArchiveModel):
    """
    Represents a delayed bookmark creation. Businesses can invite
    new customers by sending appropriate messages through email or
    mobile text messages. When the user registers using the email address
    or phone number that the invitation was sent to, the inviting business
    is automatically added to the user's (Customer) bookmarks.

    The bookmark is going to be created in the account creation handler.

    """

    customer_email = models.EmailField(
        _('e-mail address'),
        max_length=75,
        db_index=True,
        blank=True,
        validators=[booksy_validate_email],
    )
    customer_cell_phone = BooksyPhoneNumberField(db_index=True)
    business = models.ForeignKey('business.Business', on_delete=models.CASCADE)
    valid_till = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Valid till (UTC)',
    )
    completed = models.BooleanField(default=False)

    def create_bookmark(self, user):
        # check if this business already has a matching BCI,
        # if so, connect it to the newly created User
        bci = BusinessCustomerInfo.get_from_contact(
            business=self.business, customer_email=user.email, customer_phone=user.cell_phone
        )
        if bci:
            if bci.user and bci.user_id != user.id:
                # this card is for someone else - we need to create a new one
                bci = None
            else:
                # claim customer card
                bci.user = user

        if bci is None:
            # create a new one
            bci = BusinessCustomerInfo(
                business=self.business,
                user=user,
                email=self.customer_email,
                cell_phone=self.customer_cell_phone,
                first_name=user.first_name,
                last_name=user.last_name,
            )
        bci.set_client_type(BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_INVITE)
        bci.bookmarked = True
        bci.bookmarked_date = self.business.tznow
        bci.save()
        self.completed = True
        self.save()

    @classmethod
    def create_for_invite_type(cls, business, receivers, type_):
        """
        :param business: Orm business object
        :param receivers: List of receivers. Depend on type_ param.
        For param 'sms' receivers should have list of phone numbers, for 'email'
        list of emails
        :param type_: string ('sms', 'email')
        """
        if type_ not in ('sms', 'email'):
            raise AttributeError('Wrong type value')
        key = 'customer_cell_phone' if type_ == 'sms' else 'customer_email'
        data = {
            'business': business,
        }
        for receiver in receivers:
            data[key] = receiver
            obj, _created = cls.objects.get_or_create(**data)
            obj.valid_till = tznow() + relativedelta(months=1)
            obj.completed = False
            obj.save(update_fields=['valid_till', 'completed', 'updated'])


class BCIVersumAgreement(ArchiveModel):
    class AgreementType(StrChoicesEnum):
        NOTIFICATIONS = 'notifications', 'Notifications'
        BULK = 'bulk', 'Bulk Messages'
        BIRTHDAY = 'birthday', 'Birthday and Name Day wishes'
        LOYALTY = 'loyalty', 'Loyalty Program'
        REVIEWS = 'reviews', 'Appointments Reviews'

    agreement_type = models.CharField(
        max_length=13,
        choices=AgreementType.choices(),
    )
    sms = models.BooleanField(default=False)
    email = models.BooleanField(default=False)
    bci = models.ForeignKey(
        BusinessCustomerInfo,
        on_delete=models.CASCADE,
        related_name='versum_agreements',
    )
