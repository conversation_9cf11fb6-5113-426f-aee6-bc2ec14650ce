from webapps.google_business_profile.application.converters.location_verification_converter import (
    VerificationOptionsDTOConverter,
    LocationVerificationDTOConverter,
)
from webapps.google_business_profile.application.dtos.location_verification_options_dto import (
    VerificationOptionsPresentationDTO,
    LocationVerificationPresentationDTO,
)
from webapps.google_business_profile.domain.dtos import LocationVerificationDTO
from webapps.google_business_profile.domain.interfaces.location_verification_client import (
    VerifyLocationParams,
    CompleteLocationVerificationParams,
)
from webapps.google_business_profile.domain.interfaces.location_verification_gateway import (
    GoogleLocationVerificationGateway,
)
from webapps.google_business_profile.domain.value_types import LanguageCode


class LocationVerificationService:
    """
    Manages Google My Business location verification via the My Business Verifications API.

    1. get_verification_options handles fetching available verification options for location.
    The verification option represents how to verify the location (indicated by verification method)
    and where the verification will be sent to (indicated by display data).

    2. verify_location handles initiating verification using one of the chosen methods
    (EMAIL, PHONE_CALL, SMS, AUTO, VETTED_PARTNER) with a corresponding display data
    (phone_number, email, token). If the selected method is AUTO,
    the verification process is completed automatically without entering a PIN code.

    2. complete_location_verification handles completing the verification process
    by providing a PIN code.
    """

    def __init__(self, location_verification_gateway: GoogleLocationVerificationGateway):
        self.location_verification_gateway = location_verification_gateway
        self.verification_options_mapper = VerificationOptionsDTOConverter()
        self.location_verification_mapper = LocationVerificationDTOConverter()

    def get_verification_options(
        self, language_code: LanguageCode
    ) -> VerificationOptionsPresentationDTO:
        verification_options = self.location_verification_gateway.fetch_verification_options(
            language_code
        )
        return self.verification_options_mapper.to_presentation_dto(verification_options)

    def verify_location(
        self, verify_location_params: VerifyLocationParams
    ) -> LocationVerificationPresentationDTO:
        verify_location_response: LocationVerificationDTO = (
            self.location_verification_gateway.verify_location(verify_location_params)
        )
        return self.location_verification_mapper.to_presentation_dto(verify_location_response)

    def complete_location_verification(
        self, complete_params: CompleteLocationVerificationParams
    ) -> LocationVerificationPresentationDTO:
        complete_verification_response: LocationVerificationDTO = (
            self.location_verification_gateway.complete_location_verification(complete_params)
        )
        return self.location_verification_mapper.to_presentation_dto(complete_verification_response)
