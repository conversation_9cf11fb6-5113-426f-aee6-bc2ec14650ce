from typing import Protocol, Type<PERSON><PERSON>s, Union


from webapps.google_business_profile.application.dtos.location_verification_options_dto import (
    VerificationOptionsPresentationDTO,
    SimpleVerificationOptionDTO,
    LocationVerificationPresentationDTO,
)
from webapps.google_business_profile.domain.const import VerificationMethod
from webapps.google_business_profile.domain.dtos import (
    VerificationOptionsDTO,
    EmailVerificationOptionDTO,
    PhoneCallVerificationOptionDTO,
    SMSVerificationOptionDTO,
    VettedPartnerVerificationOptionDTO,
    LocationVerificationDTO,
)


VerificationOptionsDataToConvert: TypeAlias = Union[
    EmailVerificationOptionDTO,
    PhoneCallVerificationOptionDTO,
    SMSVerificationOptionDTO,
    VettedPartnerVerificationOptionDTO,
    None,  # since _to_simple_auto method does not take any argument
]


class VerificationConverter(Protocol):
    def __call__(self, data: VerificationOptionsDataToConvert) -> SimpleVerificationOptionDTO: ...


class VerificationOptionsDTOConverter:
    def __init__(self):
        self._conversion_dispatch: dict[str, VerificationConverter] = {
            VerificationMethod.EMAIL.value: self._to_simple_email,
            VerificationMethod.PHONE_CALL.value: self._to_simple_phone_call,
            VerificationMethod.SMS.value: self._to_simple_sms,
            VerificationMethod.AUTO.value: self._to_simple_auto,
            VerificationMethod.VETTED_PARTNER.value: self._to_simple_vetted_partner,
        }

    @staticmethod
    def _to_simple_email(option: EmailVerificationOptionDTO) -> SimpleVerificationOptionDTO:
        confirmation_data = f"{option.email_data.user}@{option.email_data.domain}"
        return SimpleVerificationOptionDTO(
            method=VerificationMethod.EMAIL, confirmation_data=confirmation_data
        )

    # pylint: disable=line-too-long
    @staticmethod
    def _to_simple_phone_call(
        option: PhoneCallVerificationOptionDTO,
    ) -> SimpleVerificationOptionDTO:
        confirmation_data = f"{option.phone_number}"
        return SimpleVerificationOptionDTO(
            method=VerificationMethod.PHONE_CALL, confirmation_data=confirmation_data
        )

    @staticmethod
    def _to_simple_sms(option: SMSVerificationOptionDTO) -> SimpleVerificationOptionDTO:
        confirmation_data = f"{option.phone_number}"
        return SimpleVerificationOptionDTO(
            method=VerificationMethod.SMS, confirmation_data=confirmation_data
        )

    @staticmethod
    def _to_simple_auto() -> SimpleVerificationOptionDTO:
        return SimpleVerificationOptionDTO(
            method=VerificationMethod.AUTO,
        )

    # pylint: disable=line-too-long
    @staticmethod
    def _to_simple_vetted_partner(
        option: VettedPartnerVerificationOptionDTO,
    ) -> SimpleVerificationOptionDTO:
        return SimpleVerificationOptionDTO(
            method=VerificationMethod.VETTED_PARTNER, confirmation_data=option.announcement
        )

    # pylint: disable=line-too-long
    def to_presentation_dto(
        self, options_dto: VerificationOptionsDTO
    ) -> VerificationOptionsPresentationDTO:
        converted_simple_options: list[SimpleVerificationOptionDTO] = []
        conversion_dispatch_map = self._conversion_dispatch

        for option_obj in options_dto.options:
            method_type = option_obj.verification_method

            # Skip VETTED_PARTNER option from presentation response
            # as it is not managed yet, keep implementation for future use
            if method_type == VerificationMethod.VETTED_PARTNER.value:
                continue

            converter_func = conversion_dispatch_map.get(method_type)
            converted_simple_option = converter_func(option_obj)
            converted_simple_options.append(converted_simple_option)

        return VerificationOptionsPresentationDTO(options=converted_simple_options)


class LocationVerificationDTOConverter:
    @staticmethod
    def to_presentation_dto(
        location_verification_data: LocationVerificationDTO,
    ) -> LocationVerificationPresentationDTO:
        return LocationVerificationPresentationDTO(
            verification_id=location_verification_data.verification_id,
            method=location_verification_data.method,
        )
