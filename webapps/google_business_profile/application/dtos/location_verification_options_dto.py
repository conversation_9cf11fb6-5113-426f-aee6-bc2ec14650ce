from dataclasses import dataclass

from webapps.google_business_profile.domain.const import VerificationMethod
from webapps.google_business_profile.shared import VerificationId


@dataclass(frozen=True)
class SimpleVerificationOptionDTO:
    method: str
    confirmation_data: str | None = None


@dataclass(frozen=True)
class VerificationOptionsPresentationDTO:
    options: list[SimpleVerificationOptionDTO]


@dataclass(frozen=True)
class LocationVerificationPresentationDTO:
    verification_id: VerificationId
    method: VerificationMethod
