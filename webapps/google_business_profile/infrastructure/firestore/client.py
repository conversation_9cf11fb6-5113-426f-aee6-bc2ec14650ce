# pylint: disable=broad-exception-raised,access-member-before-definition
import logging
from contextlib import contextmanager
from typing import Iterator, Any, Optional

from django.conf import settings
from google.cloud import firestore

from webapps.google_business_profile.domain.db_repository import (
    AbstractClient,
    BusinessAbstractRepository,
    BusinessProfileAbstractRepository,
)
from webapps.google_business_profile.domain.exceptions import (
    BusinessProfileSaveError,
    BusinessSaveError,
)
from webapps.google_business_profile.domain.models import Business, GoogleBusinessProfile
from webapps.google_business_profile.domain.services.postal_code import PostalCodeService
from webapps.google_business_profile.domain.value_objects import (
    LocationAddress,
    LocationCategory,
    LocationPhoneNumbers,
    BusinessIdentity,
    BusinessSupplementaryDetails,
    BusinessTemporalInfo,
    BusinessGbpDetails,
    BusinessPrimaryContact,
    BusinessGeoDetails,
    BooksyCategory,
    GoogleLocationResource,
    OpenInfo,
    GoogleLocationDetails,
    GoogleLocationLinkage,
    LocationCategories,
)
from webapps.google_business_profile.domain.value_types import (
    CountryCode,
    RegionCode,
    LanguageCode,
    Email,
)
from webapps.google_business_profile.infrastructure.firestore.errors_handler import (
    handle_firestore_exceptions,
)
from webapps.google_business_profile.shared import (
    UserId,
    BookingMode,
    BusinessId,
    BusinessStatus,
    PhoneNumber,
    WebsiteUri,
)

FIRESTORE_DATABASE_ID = f"booksy-{settings.API_COUNTRY}"


class FirestoreClient(AbstractClient):
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._client = None
        return cls._instance

    def _get_client(self):
        if self._client is None:
            self._client = firestore.Client(
                project=settings.GOOGLE_BUSINESS_PROFILE_FIREBASE_PROJECT_ID,
                database=FIRESTORE_DATABASE_ID,
            )
        return self._client

    @contextmanager
    def collection(self, collection_name: str = None) -> Iterator[Any]:
        client = self._get_client()
        try:
            yield client.collection(collection_name)
        except Exception as e:
            logging.error("Error accessing Firestore collection %s: %s", collection_name, e)
            raise Exception(
                f"Failed to access BusinessProfile Firestore collection '{collection_name}': {e}"
            ) from e


class BusinessFirestoreRepository(BusinessAbstractRepository):
    doc_id = "business"
    entity_class = Business

    def __init__(self, firestore_client: FirestoreClient):
        self._firestore_client = firestore_client
        self.postal_code_service = PostalCodeService()

    def _get_collection_name(self) -> str:
        return self.doc_id

    @handle_firestore_exceptions(BusinessSaveError)
    def get(self, entity_id: str) -> Optional[Business]:  # TODO: fix VO creation input data types
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            doc_ref = collection.document(entity_id)
            doc = doc_ref.get()

            if not doc.exists:
                return None

            data = doc.to_dict()
            if not data:
                return None

            identity = BusinessIdentity(
                name=data.get('name'), official_name=data.get('official_name')
            )
            supplementary_details = BusinessSupplementaryDetails(
                description=data.get('description'),
                renting_venue=data.get('renting_venue'),
                subdomain=data.get('subdomain'),
            )
            temporal_info = BusinessTemporalInfo(
                created=data.get('created'),
                updated=data.get('updated'),
                visible_from=data.get('visible_from'),
                active_from=data.get('active_from'),
                active_till=data.get('active_till'),
            )
            gbp_details = BusinessGbpDetails(
                synchronised_gbp=data.get('synchronised_gbp', False),
                has_gbp=data.get('has_gbp', False),
                disable_google_reserve=data.get('disable_google_reserve', False),
                verification=data.get('verification'),
            )
            primary_contact = BusinessPrimaryContact(
                phone=PhoneNumber(data.get('phone')) if data.get('phone') else None,
                website=WebsiteUri(data.get('website')) if data.get('website') else None,
                email=Email(data.get('email')) if data.get('email') else None,
            )
            geo_details = BusinessGeoDetails(
                longitude=data.get('longitude'),
                latitude=data.get('latitude'),
                address2=data.get('address2'),
                city_or_region_city=data.get('city_or_region_city'),
                region_id=data.get('region_id'),
            )
            address_data = data.get('address', {})
            address = LocationAddress(
                region_code=(
                    RegionCode(address_data.get('region_code'))
                    if address_data.get('region_code')
                    else None
                ),
                language_code=(
                    LanguageCode(address_data.get('language_code'))
                    if address_data.get('language_code')
                    else None
                ),
                postal_code=self.postal_code_service.create_postal_code(
                    address_data.get('postal_code'), address_data.get('region_code')
                ),
                administrative_area=address_data.get('administrative_area'),
                locality=address_data.get('locality'),
                address_lines=address_data.get('address_lines', []),
            )
            category_data = data.get('category', {})
            category = LocationCategory(
                name=category_data.get('name'),
                category_id=category_data.get('category_id'),
                display_name=category_data.get('display_name'),
            )
            booksy_category_data = data.get('booksy_category', {})
            booksy_category = BooksyCategory(
                id=booksy_category_data.get('id'),
                name=booksy_category_data.get('name'),
                slug=booksy_category_data.get('slug'),
            )

            return Business(
                business_id=BusinessId(data.get('business_id')),
                country_code=CountryCode(data.get('country_code')),
                owner_id=UserId(data.get('owner_id')),
                identity=identity,
                supplementary_details=supplementary_details,
                temporal_info=temporal_info,
                gbp_details=gbp_details,
                primary_contact=primary_contact,
                geo_details=geo_details,
                address=address,
                category=category,
                booksy_category=booksy_category,
                booking_mode=BookingMode(data.get('booking_mode')),
                status=BusinessStatus(data.get('status')),
                active=data.get('active'),
                visible=data.get('visible'),
            )

    @handle_firestore_exceptions(BusinessSaveError)
    def save(self, obj: Business) -> int:
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            data_to_save = {
                "business_id": str(obj.id),  # TODO: Check if types matches Business model
                "country_code": str(obj.country_code.value),
                "owner_id": obj.owner_id,
                "name": obj.identity.name,
                "official_name": obj.identity.official_name,
                "description": obj.supplementary_details.description,
                "renting_venue": obj.supplementary_details.renting_venue,
                "subdomain": obj.supplementary_details.subdomain,
                "created": obj.temporal_info.created,
                "updated": obj.temporal_info.updated,
                "visible_from": obj.temporal_info.visible_from,
                "active_from": obj.temporal_info.active_from,
                "active_till": obj.temporal_info.active_till,
                "synchronised_gbp": obj.gbp_details.synchronised_gbp,
                "has_gbp": obj.gbp_details.has_gbp,
                "disable_google_reserve": obj.gbp_details.disable_google_reserve,
                "verification": obj.gbp_details.verification,
                "phone": str(obj.primary_contact.phone),
                "website": (
                    str(obj.primary_contact.website) if obj.primary_contact.website else None
                ),
                "email": (
                    str(obj.primary_contact.email.value) if obj.primary_contact.email else None
                ),
                "longitude": obj.geo_details.longitude,
                "latitude": obj.geo_details.latitude,
                "address2": obj.geo_details.address2,
                "city_or_region_city": obj.geo_details.city_or_region_city,
                "region_id": obj.geo_details.region_id,
                "address": {
                    "region_code": str(obj.address.region_code.value),
                    "language_code": str(obj.address.language_code.value),
                    "postal_code": str(obj.address.postal_code),
                    "administrative_area": obj.address.administrative_area,
                    "locality": obj.address.locality,
                    "address_lines": obj.address.address_lines,
                },
                "category": {
                    "name": obj.category.name,
                    "category_id": obj.category.category_id,
                    "display_name": obj.category.display_name,
                },
                "booksy_category": {
                    "id": obj.booksy_category.id,
                    "name": obj.booksy_category.name,
                    "slug": obj.booksy_category.slug,
                },
                "booking_mode": obj.booking_mode,
                "status": obj.status,
                "active": obj.active,
                "visible": obj.visible,
            }

            doc_ref = collection.document(str(obj.id))
            doc_ref.set(data_to_save)
            return int(obj.id)

    @handle_firestore_exceptions(BusinessSaveError)
    def update(self, entity: Business) -> None:
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            data_to_save = {
                "business_id": str(entity.id),
                "country_code": str(entity.country_code.value),
                "owner_id": entity.owner_id,
                "name": entity.identity.name,
                "official_name": entity.identity.official_name,
                "description": entity.supplementary_details.description,
                "renting_venue": entity.supplementary_details.renting_venue,
                "subdomain": entity.supplementary_details.subdomain,
                "created": entity.temporal_info.created,
                "updated": entity.temporal_info.updated,
                "visible_from": entity.temporal_info.visible_from,
                "active_from": entity.temporal_info.active_from,
                "active_till": entity.temporal_info.active_till,
                "synchronised_gbp": entity.gbp_details.synchronised_gbp,
                "has_gbp": entity.gbp_details.has_gbp,
                "disable_google_reserve": entity.gbp_details.disable_google_reserve,
                "verification": entity.gbp_details.verification,
                "phone": str(entity.primary_contact.phone),
                "website": (
                    str(entity.primary_contact.website) if entity.primary_contact.website else None
                ),
                "email": (
                    str(entity.primary_contact.email.value)
                    if entity.primary_contact.email
                    else None
                ),
                "longitude": entity.geo_details.longitude,
                "latitude": entity.geo_details.latitude,
                "address2": entity.geo_details.address2,
                "city_or_region_city": entity.geo_details.city_or_region_city,
                "region_id": entity.geo_details.region_id,
                "address": {
                    "region_code": str(entity.address.region_code.value),
                    "language_code": str(entity.address.language_code.value),
                    "postal_code": str(entity.address.postal_code),
                    "administrative_area": entity.address.administrative_area,
                    "locality": entity.address.locality,
                    "address_lines": entity.address.address_lines,
                },
                "category": {
                    "name": entity.category.name,
                    "category_id": entity.category.category_id,
                    "display_name": entity.category.display_name,
                },
                "booksy_category": {
                    "id": entity.booksy_category.id,
                    "name": entity.booksy_category.name,
                    "slug": entity.booksy_category.slug,
                },
                "booking_mode": entity.booking_mode,
                "status": entity.status,
                "active": entity.active,
                "visible": entity.visible,
            }

            doc_ref = collection.document(str(entity.id))
            doc_ref.set(data_to_save, merge=True)

    @handle_firestore_exceptions(BusinessSaveError)
    def delete(self, entity_id: str) -> None:
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            collection.document(entity_id).delete()

    @handle_firestore_exceptions(BusinessSaveError)
    def list(self, filters: dict[str, Any] = None) -> list[Business]:
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            query = collection

            if filters:
                for field, value in filters.items():
                    query = query.where(field, "==", value)

            results = []
            for doc in query.stream():
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(self.entity_class(**data))

            return results


class BusinessProfileFirestoreRepository(BusinessProfileAbstractRepository):
    doc_id = "business-profile"
    agg_class = GoogleBusinessProfile

    def __init__(self, firestore_client: FirestoreClient):
        self._firestore_client = firestore_client
        self.postal_code_service = PostalCodeService()

    def _get_collection_name(self) -> str:
        return self.doc_id

    @handle_firestore_exceptions(BusinessProfileSaveError)
    def save(self, obj: GoogleBusinessProfile) -> int:
        if not obj.linkage or not obj.linkage.business_id:
            raise BusinessProfileSaveError(
                "Cannot save GoogleBusinessProfile without a valid business_id"
            )
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            entity_dict = {
                "location_id": str(obj.id) if obj.id else None,
                "name": obj.name,
                "title": obj.title,
                "website_uri": str(obj.website_uri),
                "open_info_status": obj.open_info.status,
                "account_id": str(obj.linkage.account_id),
                "business_id": str(obj.linkage.business_id),
                "country_code": str(obj.linkage.country_code.value),
                "phone_numbers": {
                    "primary_phone": str(obj.phone_numbers.primary_phone),
                    "additional_phones": [
                        str(phone_number) for phone_number in obj.phone_numbers.additional_phones
                    ],
                },
                "category": {
                    "name": obj.categories.primary_category.name,
                    "display_name": obj.categories.primary_category.display_name,
                    "category_id": obj.categories.primary_category.category_id,
                },
                "address": {
                    "region_code": str(obj.storefront_address.region_code.value),
                    "language_code": str(obj.storefront_address.language_code.value),
                    "postal_code": str(obj.storefront_address.postal_code),
                    "administrative_area": obj.storefront_address.administrative_area,
                    "locality": obj.storefront_address.locality,
                    "address_lines": obj.storefront_address.address_lines,
                },
            }

            document_id = str(obj.linkage.business_id)
            doc_ref = collection.document(document_id)
            doc_ref.set(entity_dict)

            return int(document_id)

    @handle_firestore_exceptions(BusinessProfileSaveError)
    def update(self, entity: GoogleBusinessProfile) -> None:
        if not entity.linkage or not entity.linkage.business_id:
            raise BusinessProfileSaveError(
                "Cannot update GoogleBusinessProfile without a valid business_id"
            )
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            entity_dict = {
                "location_id": str(entity.id) if entity.id else None,
                "name": entity.name,
                "title": entity.title,
                "website_uri": str(entity.website_uri),
                "open_info_status": entity.open_info.status,
                "account_id": str(entity.linkage.account_id),
                "business_id": str(entity.linkage.business_id),
                "country_code": str(entity.linkage.country_code.value),
                "phone_numbers": {
                    "primary_phone": str(entity.phone_numbers.primary_phone),
                    "additional_phones": [str(p) for p in entity.phone_numbers.additional_phones],
                },
                "category": {
                    "name": entity.categories.primary_category.name,
                    "display_name": entity.categories.primary_category.display_name,
                    "category_id": entity.categories.primary_category.category_id,
                },
                "address": {
                    "region_code": str(entity.storefront_address.region_code.value),
                    "language_code": str(entity.storefront_address.language_code.value),
                    "postal_code": str(entity.storefront_address.postal_code),
                    "administrative_area": entity.storefront_address.administrative_area,
                    "locality": entity.storefront_address.locality,
                    "address_lines": entity.storefront_address.address_lines,
                },
            }

            document_id = str(entity.linkage.business_id)
            doc_ref = collection.document(document_id)
            doc_ref.set(entity_dict, merge=True)

    @handle_firestore_exceptions(BusinessSaveError)
    def list(self, filters: dict[str, Any] = None) -> list[GoogleBusinessProfile]:
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            query = collection

            if filters:
                for field, value in filters.items():
                    query = query.where(field, "==", value)

            results = []
            for doc in query.stream():
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(self.agg_class(**data))

            return results

    @handle_firestore_exceptions(BusinessSaveError)
    def get(self, entity_id: str) -> GoogleBusinessProfile | None:
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            doc_ref = collection.document(entity_id)
            doc = doc_ref.get()

            if not doc.exists:
                return None

            data = doc.to_dict()
            if not data:
                return None

            address_data = data.get('address', {})
            address = LocationAddress(
                region_code=(
                    RegionCode(address_data.get('region_code'))
                    if address_data.get('region_code')
                    else None
                ),
                language_code=(
                    LanguageCode(address_data.get('language_code'))
                    if address_data.get('language_code')
                    else None
                ),
                postal_code=self.postal_code_service.create_postal_code(
                    address_data.get('postal_code'), address_data.get('region_code')
                ),
                administrative_area=address_data.get('administrative_area'),
                locality=address_data.get('locality'),
                address_lines=address_data.get('address_lines', []),
            )

            category_data = data.get('category', {})
            primary_category = LocationCategory(
                name=category_data.get('name'),
                display_name=category_data.get('display_name'),
                category_id=category_data.get('category_id'),
            )
            categories = LocationCategories(
                primary_category=primary_category, additional_categories=[]
            )

            phone_numbers_data = data.get('phone_numbers', {})
            phone_numbers = LocationPhoneNumbers(
                primary_phone=phone_numbers_data.get('primary_phone'),
                additional_phones=list(phone_numbers_data.get('additional_phones', [])),
            )

            location_id = data.get('location_id')
            location_name = data.get('name')
            account_id = data.get('account_id')
            business_id = data.get('business_id')
            country_code = data.get('country_code')

            resource = GoogleLocationResource(id=location_id, name=location_name)

            open_info_status = data.get('open_info_status')
            open_info = OpenInfo(status=open_info_status)
            details = GoogleLocationDetails(
                title=data.get('title'), website_uri=data.get('website_uri'), open_info=open_info
            )

            linkage = GoogleLocationLinkage(
                account_id=account_id, business_id=business_id, country_code=country_code
            )

            return self.agg_class(
                resource=resource,
                details=details,
                linkage=linkage,
                phone_numbers=phone_numbers,
                categories=categories,
                storefront_address=address,
            )

    @handle_firestore_exceptions(BusinessSaveError)
    def delete(self, entity_id: str) -> None:
        with self._firestore_client.collection(
            collection_name=self._get_collection_name()
        ) as collection:
            collection.document(entity_id).delete()
