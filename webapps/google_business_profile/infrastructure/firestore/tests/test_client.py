# pylint: disable=protected-access,too-many-return-statements,too-many-statements
import datetime
from unittest import TestCase

from unittest.mock import Mock, call
from contextlib import contextmanager

from webapps.google_business_profile.domain.const import Country
from webapps.google_business_profile.domain.exceptions import BusinessSaveError
from webapps.google_business_profile.domain.models import Business
from webapps.google_business_profile.domain.value_objects import (
    BusinessIdentity,
    BusinessSupplementaryDetails,
    BusinessTemporalInfo,
    BusinessGbpDetails,
    BusinessPrimaryContact,
    BusinessGeoDetails,
    LocationAddress,
    LocationCategory,
    BooksyCategory,
)
from webapps.google_business_profile.domain.value_types import (
    CountryCode,
    Email,
    RegionCode,
    LanguageCode,
    PostalCode,
)
from webapps.google_business_profile.shared import (
    BusinessId,
    UserId,
    PhoneNumber,
    WebsiteUri,
    BookingMode,
    BusinessStatus,
)
from webapps.google_business_profile.infrastructure.firestore.client import (
    BusinessFirestoreRepository,
    FirestoreClient,
)


class TestBusinessFirestoreRepository(TestCase):
    def setUp(self):
        self.mock_firestore_client = Mock(spec=FirestoreClient)
        self.repository = BusinessFirestoreRepository(self.mock_firestore_client)

        self.mock_collection = Mock()
        self.mock_doc_ref = Mock()
        self.mock_doc = Mock()

        @contextmanager
        def mock_collection_context(collection_name=None):
            yield self.mock_collection

        self.mock_firestore_client.collection = mock_collection_context
        self.mock_collection.document.return_value = self.mock_doc_ref

    def _create_test_business_domain_object(self) -> Business:
        """Create a test Business domain object for save/update operations"""
        identity = BusinessIdentity(name="Test Business", official_name="Test Business LLC")
        supplementary_details = BusinessSupplementaryDetails(
            description="A test business", renting_venue=False, subdomain="test-business"
        )
        temporal_info = BusinessTemporalInfo(
            created=datetime.datetime.now(),
            updated=datetime.datetime.now(),
            visible_from=datetime.datetime.now(),
            active_from=datetime.datetime.now(),
            active_till=None,
        )
        gbp_details = BusinessGbpDetails(
            synchronised_gbp=True,
            has_gbp=True,
            disable_google_reserve=False,
            verification="VERIFIED",
        )
        primary_contact = BusinessPrimaryContact(
            phone=PhoneNumber("+1234567890"),
            website=WebsiteUri("https://testbusiness.com"),
            email=Email("<EMAIL>"),
        )
        geo_details = BusinessGeoDetails(
            longitude=-122.4194,
            latitude=37.7749,
            address2="Suite 100",
            city_or_region_city="San Francisco",
            region_id=1,
        )
        address = LocationAddress(
            region_code=RegionCode('US'),
            language_code=LanguageCode('EN'),
            postal_code=PostalCode("94102", Country.US),
            administrative_area="CA",
            locality="San Francisco",
            address_lines=["123 Test Street"],
        )
        category = LocationCategory(
            name="Beauty Salon", category_id="gcid:beauty_salon", display_name="Beauty Salon"
        )
        booksy_category = BooksyCategory(id=1, name="Beauty", slug="beauty")

        return Business(
            business_id=BusinessId(12345),
            country_code=CountryCode('US'),
            owner_id=UserId(67890),
            identity=identity,
            supplementary_details=supplementary_details,
            temporal_info=temporal_info,
            gbp_details=gbp_details,
            primary_contact=primary_contact,
            geo_details=geo_details,
            address=address,
            category=category,
            booksy_category=booksy_category,
            booking_mode=BookingMode("Online"),
            status=BusinessStatus("Active"),
            active=True,
            visible=True,
        )

    def _create_raw_firestore_data(self) -> dict:
        """Create raw Firestore document data for get() method (WORKING METHOD)"""
        return {
            'business_id': '12345',
            'country_code': 'US',
            'owner_id': 67890,
            'name': 'Test Business',
            'official_name': 'Test Business LLC',
            'description': 'A test business',
            'renting_venue': False,
            'subdomain': 'test-business',
            'created': datetime.datetime.now(),
            'updated': datetime.datetime.now(),
            'visible_from': datetime.datetime.now(),
            'active_from': datetime.datetime.now(),
            'active_till': None,
            'synchronised_gbp': True,
            'has_gbp': True,
            'disable_google_reserve': False,
            'verification': 'VERIFIED',
            'phone': '+1234567890',
            'website': 'https://testbusiness.com',
            'email': '<EMAIL>',
            'longitude': -122.4194,
            'latitude': 37.7749,
            'address2': 'Suite 100',
            'city_or_region_city': 'San Francisco',
            'region_id': 1,
            'address': {
                'region_code': 'US',
                'language_code': 'EN',
                'postal_code': '94102',
                'administrative_area': 'CA',
                'locality': 'San Francisco',
                'address_lines': ['123 Test Street'],
            },
            'category': {
                'name': 'Beauty Salon',
                'category_id': 'gcid:beauty_salon',
                'display_name': 'Beauty Salon',
            },
            'booksy_category': {'id': 1, 'name': 'Beauty', 'slug': 'beauty'},
            'booking_mode': 'Online',
            'status': 'Active',
            'active': True,
            'visible': True,
        }

    def test_get_collection_name(self):
        """Test that the collection name is correct"""
        self.assertEqual(self.repository._get_collection_name(), "business")

    def test_get_existing_business_success(self):
        """Test successful retrieval of an existing business"""
        entity_id = "12345"
        mock_data = self._create_raw_firestore_data()

        self.mock_doc.exists = True
        self.mock_doc.to_dict.return_value = mock_data
        self.mock_doc_ref.get.return_value = self.mock_doc

        result = self.repository.get(entity_id)

        self.assertIsInstance(result, Business)
        self.assertEqual(str(result.id), entity_id)
        self.assertEqual(result.identity.name, "Test Business")
        self.assertEqual(result.country_code, CountryCode('US'))

        self.mock_collection.document.assert_called_once_with(entity_id)
        self.mock_doc_ref.get.assert_called_once()

    def test_get_nonexistent_business_returns_none(self):
        """Test that get returns None for non-existent business"""
        entity_id = "99999"
        self.mock_doc.exists = False
        self.mock_doc_ref.get.return_value = self.mock_doc

        result = self.repository.get(entity_id)

        self.assertIsNone(result)
        self.mock_collection.document.assert_called_once_with(entity_id)
        self.mock_doc_ref.get.assert_called_once()

    def test_get_business_with_empty_data_returns_none(self):
        """Test that get returns None when document has no data"""
        entity_id = "12345"
        self.mock_doc.exists = True
        self.mock_doc.to_dict.return_value = {}
        self.mock_doc_ref.get.return_value = self.mock_doc

        result = self.repository.get(entity_id)

        self.assertIsNone(result)

    def test_save_business_success(self):
        """Test successful saving of a business"""

        business = self._create_test_business_domain_object()

        result = self.repository.save(business)

        self.assertEqual(result, 12345)
        self.mock_collection.document.assert_called_once_with("12345")
        self.mock_doc_ref.set.assert_called_once()

        call_args = self.mock_doc_ref.set.call_args[0][0]
        self.assertEqual(call_args['business_id'], '12345')
        self.assertEqual(call_args['country_code'], 'US')
        self.assertEqual(call_args['name'], 'Test Business')

    def test_update_business_success(self):
        """Test successful updating of a business"""

        business = self._create_test_business_domain_object()
        self.repository.update(business)

        self.mock_collection.document.assert_called_once_with("12345")
        self.mock_doc_ref.set.assert_called_once()

        call_args = self.mock_doc_ref.set.call_args
        self.assertTrue(call_args[1]['merge'])

    def test_update_business_with_changed_id(self):
        """Test that updating a business with a changed ID uses the new ID"""
        business = self._create_test_business_domain_object()

        business._business_id = BusinessId(999)

        self.repository.update(business)
        self.mock_collection.document.assert_called_once_with("999")

    def test_delete_business_success(self):
        """Test successful deletion of a business"""

        entity_id = "12345"
        self.repository.delete(entity_id)

        self.mock_collection.document.assert_called_once_with(entity_id)
        self.mock_collection.document().delete.assert_called_once()

        self.mock_collection.reset_mock()
        self.mock_doc_ref.reset_mock()
        self.mock_doc.reset_mock()

        self.mock_doc.exists = False
        self.mock_doc_ref.get.return_value = self.mock_doc

        result = self.repository.get(entity_id)

        self.assertIsNone(result)
        self.mock_collection.document.assert_called_once_with(entity_id)
        self.mock_doc_ref.get.assert_called_once()

    def test_business_data_serialization_completeness(self):
        """Test that all business fields are properly serialized when saving"""

        business = self._create_test_business_domain_object()

        self.repository.save(business)

        call_args = self.mock_doc_ref.set.call_args[0][0]

        # Check all major field categories are present
        self.assertIn('business_id', call_args)
        self.assertIn('country_code', call_args)
        self.assertIn('owner_id', call_args)

        # Identity fields
        self.assertIn('name', call_args)
        self.assertIn('official_name', call_args)

        # Supplementary details
        self.assertIn('description', call_args)
        self.assertIn('subdomain', call_args)

        # Contact info
        self.assertIn('phone', call_args)
        self.assertIn('website', call_args)
        self.assertIn('email', call_args)

        # Address
        self.assertIn('address', call_args)
        self.assertIsInstance(call_args['address'], dict)

        # Category info
        self.assertIn('category', call_args)
        self.assertIsInstance(call_args['category'], dict)

        self.assertIn('booksy_category', call_args)
        self.assertIsInstance(call_args['booksy_category'], dict)

    def test_business_data_deserialization_completeness(self):
        """Test that all business fields are properly deserialized when retrieving"""
        entity_id = "12345"
        mock_data = self._create_raw_firestore_data()

        self.mock_doc.exists = True
        self.mock_doc.to_dict.return_value = mock_data
        self.mock_doc_ref.get.return_value = self.mock_doc

        result = self.repository.get(entity_id)

        self.assertIsNotNone(result)

        # Core identifiers
        self.assertEqual(result.id, str(BusinessId(12345)))
        self.assertEqual(result.country_code, CountryCode('US'))
        self.assertEqual(result.owner_id, UserId(67890))

        # Identity fields
        self.assertEqual(result.identity.name, "Test Business")
        self.assertEqual(result.identity.official_name, "Test Business LLC")

        # Supplementary details
        self.assertEqual(result.supplementary_details.description, "A test business")
        self.assertEqual(result.supplementary_details.renting_venue, False)
        self.assertEqual(result.supplementary_details.subdomain, "test-business")

        # Temporal info
        self.assertIsNotNone(result.temporal_info.created)
        self.assertIsNotNone(result.temporal_info.updated)
        self.assertIsNotNone(result.temporal_info.visible_from)
        self.assertIsNotNone(result.temporal_info.active_from)
        self.assertIsNone(result.temporal_info.active_till)

        # GBP details
        self.assertEqual(result.gbp_details.synchronised_gbp, True)
        self.assertEqual(result.gbp_details.has_gbp, True)
        self.assertEqual(result.gbp_details.disable_google_reserve, False)
        self.assertEqual(result.gbp_details.verification, "VERIFIED")

        # Primary contact
        self.assertEqual(result.primary_contact.phone, PhoneNumber("+1234567890"))
        self.assertEqual(result.primary_contact.website, WebsiteUri("https://testbusiness.com"))
        self.assertEqual(result.primary_contact.email, Email("<EMAIL>"))

        # Geo details
        self.assertEqual(result.geo_details.longitude, -122.4194)
        self.assertEqual(result.geo_details.latitude, 37.7749)
        self.assertEqual(result.geo_details.address2, "Suite 100")
        self.assertEqual(result.geo_details.city_or_region_city, "San Francisco")
        self.assertEqual(result.geo_details.region_id, 1)

        # Address
        self.assertEqual(result.address.region_code, RegionCode('US'))
        self.assertEqual(result.address.language_code, LanguageCode('EN'))
        self.assertEqual(result.address.postal_code, PostalCode("94102", Country.US))
        self.assertEqual(result.address.administrative_area, "CA")
        self.assertEqual(result.address.locality, "San Francisco")
        self.assertEqual(result.address.address_lines, ["123 Test Street"])

        # Category
        self.assertEqual(result.category.name, "Beauty Salon")
        self.assertEqual(result.category.category_id, "gcid:beauty_salon")
        self.assertEqual(result.category.display_name, "Beauty Salon")

        # Booksy category
        self.assertEqual(result.booksy_category.id, 1)
        self.assertEqual(result.booksy_category.name, "Beauty")
        self.assertEqual(result.booksy_category.slug, "beauty")

        # Business status and mode
        self.assertEqual(result.booking_mode, BookingMode("Online"))
        self.assertEqual(result.status, BusinessStatus("Active"))
        self.assertEqual(result.active, True)
        self.assertEqual(result.visible, True)

    def test_list_businesses_without_filters(self):
        """Test listing all businesses without filters"""
        mock_business_instance = Mock()
        mock_entity_class = Mock(return_value=mock_business_instance)
        self.repository.entity_class = mock_entity_class

        mock_docs = [Mock(), Mock()]
        mock_docs[0].id = "12345"
        mock_docs[0].to_dict.return_value = self._create_raw_firestore_data()
        mock_docs[1].id = "67890"
        mock_docs[1].to_dict.return_value = self._create_raw_firestore_data()

        self.mock_collection.stream.return_value = mock_docs

        results = self.repository.list()

        self.assertEqual(len(results), 2)
        self.assertEqual(results[0], mock_business_instance)
        self.assertEqual(results[1], mock_business_instance)
        self.mock_collection.stream.assert_called_once()

        self.assertEqual(mock_entity_class.call_count, 2)

        for call_args in mock_entity_class.call_args_list:
            args, kwargs = call_args
            self.assertIn('id', kwargs)

    def test_list_businesses_with_filters(self):
        """Test listing businesses with filters"""
        mock_business_instance = Mock()
        mock_entity_class = Mock(return_value=mock_business_instance)
        self.repository.entity_class = mock_entity_class

        filters = {"country_code": "US", "active": True}
        mock_query = Mock()
        mock_query.stream.return_value = []

        self.mock_collection.where.return_value = mock_query
        mock_query.where.return_value = mock_query

        results = self.repository.list(filters)

        self.assertEqual(len(results), 0)

        expected_calls = [call("country_code", "==", "US"), call("active", "==", True)]
        actual_calls = self.mock_collection.where.call_args_list + mock_query.where.call_args_list
        for expected_call in expected_calls:
            self.assertIn(expected_call, actual_calls)

    def test_exception_handling_behavior(self):
        """Test that exceptions are properly handled by the repository methods"""

        @contextmanager
        def failing_collection_context(collection_name=None):
            raise Exception("Firestore connection failed")

        self.mock_firestore_client.collection = failing_collection_context

        with self.assertRaises(BusinessSaveError):
            self.repository.get("12345")

        with self.assertRaises(BusinessSaveError):
            business = self._create_test_business_domain_object()
            self.repository.save(business)

        with self.assertRaises(BusinessSaveError):
            business = self._create_test_business_domain_object()
            self.repository.update(business)

        with self.assertRaises(BusinessSaveError):
            self.repository.delete("12345")

        with self.assertRaises(BusinessSaveError):
            self.repository.list()

    def test_repository_uses_correct_doc_id_attribute(self):
        """Test that the repository uses the correct document ID"""
        self.assertEqual(self.repository.doc_id, "business")

    def test_repository_uses_correct_entity_class_attribute(self):
        """Test that the repository uses the correct entity class"""
        self.assertEqual(self.repository.entity_class, Business)

    def test_firestore_data_type_compatibility(self):
        """Test that all data types are Firestore-compatible"""
        business = self._create_test_business_domain_object()

        self.repository.save(business)

        call_args = self.mock_doc_ref.set.call_args[0][0]

        def is_firestore_compatible(value):
            if value is None:
                return True
            if isinstance(value, (str, int, float, bool)):
                return True
            if isinstance(value, list):
                return all(is_firestore_compatible(item) for item in value)
            if isinstance(value, dict):
                return all(
                    isinstance(k, str) and is_firestore_compatible(v) for k, v in value.items()
                )
            if hasattr(value, 'isoformat'):
                return True
            return False

        for field_name, field_value in call_args.items():
            self.assertTrue(
                is_firestore_compatible(field_value),
                f"Field '{field_name}' with value '{field_value}' is not Firestore-compatible",
            )

    def test_firestore_field_names_are_valid(self):
        """Test that all field names are valid for Firestore"""
        business = self._create_test_business_domain_object()

        self.repository.save(business)

        call_args = self.mock_doc_ref.set.call_args[0][0]

        for field_name in call_args.keys():
            self.assertFalse(field_name.startswith('_'))
            self.assertIsInstance(field_name, str)

    def test_business_roundtrip_data_integrity(self):
        """Test that data survives a complete save/get roundtrip without corruption"""
        original_business = self._create_test_business_domain_object()

        self.repository.save(original_business)
        saved_data = self.mock_doc_ref.set.call_args[0][0]

        entity_id = str(original_business.id)
        self.mock_doc.exists = True
        self.mock_doc.to_dict.return_value = saved_data
        self.mock_doc_ref.get.return_value = self.mock_doc

        retrieved_business = self.repository.get(entity_id)

        self.assertIsNotNone(retrieved_business)

        self.assertEqual(str(original_business.id), retrieved_business.id)
        self.assertEqual(original_business.country_code, retrieved_business.country_code)
        self.assertEqual(original_business.owner_id, retrieved_business.owner_id)

        self.assertEqual(original_business.identity.name, retrieved_business.identity.name)
        self.assertEqual(
            original_business.identity.official_name, retrieved_business.identity.official_name
        )

        self.assertEqual(
            original_business.supplementary_details.description,
            retrieved_business.supplementary_details.description,
        )
        self.assertEqual(
            original_business.supplementary_details.renting_venue,
            retrieved_business.supplementary_details.renting_venue,
        )
        self.assertEqual(
            original_business.supplementary_details.subdomain,
            retrieved_business.supplementary_details.subdomain,
        )

        self.assertEqual(
            original_business.temporal_info.created, retrieved_business.temporal_info.created
        )
        self.assertEqual(
            original_business.temporal_info.updated, retrieved_business.temporal_info.updated
        )
        self.assertEqual(
            original_business.temporal_info.visible_from,
            retrieved_business.temporal_info.visible_from,
        )
        self.assertEqual(
            original_business.temporal_info.active_from,
            retrieved_business.temporal_info.active_from,
        )
        self.assertEqual(
            original_business.temporal_info.active_till,
            retrieved_business.temporal_info.active_till,
        )

        self.assertEqual(
            original_business.gbp_details.synchronised_gbp,
            retrieved_business.gbp_details.synchronised_gbp,
        )
        self.assertEqual(
            original_business.gbp_details.has_gbp, retrieved_business.gbp_details.has_gbp
        )
        self.assertEqual(
            original_business.gbp_details.disable_google_reserve,
            retrieved_business.gbp_details.disable_google_reserve,
        )
        self.assertEqual(
            original_business.gbp_details.verification, retrieved_business.gbp_details.verification
        )

        self.assertEqual(
            original_business.primary_contact.phone, retrieved_business.primary_contact.phone
        )
        self.assertEqual(
            original_business.primary_contact.website, retrieved_business.primary_contact.website
        )
        self.assertEqual(
            original_business.primary_contact.email, retrieved_business.primary_contact.email
        )

        self.assertEqual(
            original_business.geo_details.longitude, retrieved_business.geo_details.longitude
        )
        self.assertEqual(
            original_business.geo_details.latitude, retrieved_business.geo_details.latitude
        )
        self.assertEqual(
            original_business.geo_details.address2, retrieved_business.geo_details.address2
        )
        self.assertEqual(
            original_business.geo_details.city_or_region_city,
            retrieved_business.geo_details.city_or_region_city,
        )
        self.assertEqual(
            original_business.geo_details.region_id, retrieved_business.geo_details.region_id
        )

        self.assertEqual(
            original_business.address.region_code, retrieved_business.address.region_code
        )
        self.assertEqual(
            original_business.address.language_code, retrieved_business.address.language_code
        )
        self.assertEqual(
            original_business.address.postal_code, retrieved_business.address.postal_code
        )
        self.assertEqual(
            original_business.address.administrative_area,
            retrieved_business.address.administrative_area,
        )
        self.assertEqual(original_business.address.locality, retrieved_business.address.locality)
        self.assertEqual(
            original_business.address.address_lines, retrieved_business.address.address_lines
        )

        self.assertEqual(original_business.category.name, retrieved_business.category.name)
        self.assertEqual(
            original_business.category.category_id, retrieved_business.category.category_id
        )
        self.assertEqual(
            original_business.category.display_name, retrieved_business.category.display_name
        )

        self.assertEqual(
            original_business.booksy_category.id, retrieved_business.booksy_category.id
        )
        self.assertEqual(
            original_business.booksy_category.name, retrieved_business.booksy_category.name
        )
        self.assertEqual(
            original_business.booksy_category.slug, retrieved_business.booksy_category.slug
        )

        self.assertEqual(original_business.booking_mode, retrieved_business.booking_mode)
        self.assertEqual(original_business.status, retrieved_business.status)
        self.assertEqual(original_business.active, retrieved_business.active)
        self.assertEqual(original_business.visible, retrieved_business.visible)
