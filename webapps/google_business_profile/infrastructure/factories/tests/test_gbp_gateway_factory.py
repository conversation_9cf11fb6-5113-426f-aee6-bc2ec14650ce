# pylint: disable=protected-access
from unittest import TestCase

from unittest.mock import Mock

from lib.feature_flag.feature.gbp import ConcurrentRequestGetLocationStatusFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.google_business_profile.application.dtos.auth_dto import AuthContext
from webapps.google_business_profile.infrastructure.dtos.gateway_config_dto import GatewayConfig
from webapps.google_business_profile.infrastructure.factories.gbp_gateway_factory import (
    GoogleBusinessProfileGatewayFactory,
)
from webapps.google_business_profile.infrastructure.gateways.gbp_gateway import (
    GoogleBusinessProfileApiGateway,
)


def create_test_auth_context(
    oauth_token: str = "test_oauth_token",
    account_id: str = "test_account_id",
    location_id: str = "test_location_id",
) -> AuthContext:
    """Helper function to create AuthContext for testing."""
    return AuthContext(
        oauth_token=oauth_token,
        account_id=account_id,
        location_id=location_id,
    )


class TestGoogleBusinessProfileGatewayFactory(TestCase):

    @override_eppo_feature_flag({ConcurrentRequestGetLocationStatusFlag.flag_name: True})
    def test_create_gateway_with_feature_flag_enabled(self):
        """Test that gateway is created with concurrent calls enabled when feature flag is on."""
        auth_context = create_test_auth_context()
        gateway = GoogleBusinessProfileGatewayFactory.create_gateway(auth_context)
        self.assertIsInstance(gateway, GoogleBusinessProfileApiGateway)
        self.assertTrue(gateway._config.enable_concurrent_calls)

    @override_eppo_feature_flag({ConcurrentRequestGetLocationStatusFlag.flag_name: False})
    def test_create_gateway_with_feature_flag_disabled(self):
        """Test that gateway is created with concurrent calls disabled when feature flag is off."""
        auth_context = create_test_auth_context()
        gateway = GoogleBusinessProfileGatewayFactory.create_gateway(auth_context)
        self.assertIsInstance(gateway, GoogleBusinessProfileApiGateway)
        self.assertFalse(gateway._config.enable_concurrent_calls)

    def test_create_gateway_passes_correct_client_data(self):
        """Test that gateway is created with correct client data from auth context."""
        auth_context = create_test_auth_context()
        gateway = GoogleBusinessProfileGatewayFactory.create_gateway(auth_context)
        self.assertEqual("test_oauth_token", gateway._api_client._oauth_token)
        self.assertEqual("test_account_id", gateway._api_client.account_id)
        self.assertEqual("test_location_id", gateway._api_client.location_id)
        self.assertEqual(GatewayConfig().request_timeout, gateway._api_client.timeout)

    def test_gateway_configuration_matches_feature_flag(self):
        """Test that the created gateway calls the correct method based on the feature flag
        configuration."""

        auth_context = create_test_auth_context()

        with override_eppo_feature_flag({ConcurrentRequestGetLocationStatusFlag.flag_name: True}):
            gateway_con = GoogleBusinessProfileGatewayFactory.create_gateway(auth_context)
            self.assertTrue(gateway_con._config.enable_concurrent_calls)
            mock_status_response = Mock(data={})
            mock_verifications_response = Mock(data={})
            gateway_con._location_status_service.get_location_status_concurrent = Mock(
                return_value=(mock_status_response, mock_verifications_response)
            )
            gateway_con._location_status_service.validate_responses = Mock()
            gateway_con._location_status_mapper.to_domain_entity = Mock(return_value=Mock())

            gateway_con.get_location_status()
            gateway_con._location_status_service.get_location_status_concurrent.assert_called_once()

        with override_eppo_feature_flag({ConcurrentRequestGetLocationStatusFlag.flag_name: False}):
            gateway_sync = GoogleBusinessProfileGatewayFactory.create_gateway(auth_context)
            self.assertFalse(gateway_sync._config.enable_concurrent_calls)
            mock_status_response = Mock(data={})
            mock_verifications_response = Mock(data={})
            gateway_sync._location_status_service.get_location_status = Mock(
                return_value=(mock_status_response, mock_verifications_response)
            )
            gateway_sync._location_status_service.validate_responses = Mock()
            gateway_sync._location_status_mapper.to_domain_entity = Mock(return_value=Mock())

            gateway_sync.get_location_status()
            gateway_sync._location_status_service.get_location_status.assert_called_once()
