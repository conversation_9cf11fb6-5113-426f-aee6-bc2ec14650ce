import asyncio
import logging
from http import HTTPStatus

from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
)
from webapps.google_business_profile.domain.value_objects import LocationStatus
from webapps.google_business_profile.infrastructure.gateways.api_error_handler import (
    ApiErrorHandler,
)
from webapps.google_business_profile.infrastructure.converters.location_status_converter import (
    LocationStatusMapper,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.client import (
    GoogleBusinessProfileClient,
)
from webapps.google_business_profile.shared import BusinessId

logger = logging.getLogger("booksy.google_business_profile")


class LocationStatusService:
    """
    Helper that fetches location status and verification data in parallel.

    Public synchronous interface:
        • get_location_status()
    """

    def __init__(self, api_client: GoogleBusinessProfileClient):
        self._api_client = api_client
        self._error_handler = ApiErrorHandler()
        self._location_status_mapper = LocationStatusMapper()

    def get_location_status(self) -> tuple[ApiResponse, ApiResponse]:
        return (
            self._api_client.get_gbp_location_status(),
            self._api_client.get_gbp_location_verifications(),
        )

    def get_location_status_concurrent(self) -> tuple[ApiResponse, ApiResponse]:
        """
        Synchronous wrapper around the asynchronous concurrent implementation.
        """
        return asyncio.run(self._get_location_status_concurrent())

    async def _get_location_status_concurrent(self) -> tuple[ApiResponse, ApiResponse]:
        """Fetch status & verifications concurrently using aiohttp for true async IO.
        Returns tuple containing status and verification response"""

        async with self._api_client.create_aiohttp_session() as session:
            status_task = asyncio.create_task(
                self._api_client.get_gbp_location_status_async(session)
            )
            verifications_task = asyncio.create_task(
                self._api_client.get_gbp_location_verifications_async(session)
            )

            return await asyncio.gather(status_task, verifications_task)

    def validate_responses(self, responses: list[ApiResponse], business_id: BusinessId) -> None:
        for response in responses:
            if response.status_code != HTTPStatus.OK:
                self._error_handler.handle_error(response, business_id=business_id)

    def to_domain_entity(
        self, status_response: ApiResponse, verifications_response: ApiResponse
    ) -> LocationStatus:
        return self._location_status_mapper.to_domain_entity(
            status_response.data, verifications_response.data
        )
