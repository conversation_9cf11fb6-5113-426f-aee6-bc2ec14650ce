# pylint: disable=protected-access
import asyncio
import time
from http import HTTPStatus
from unittest.mock import AsyncMock, Mo<PERSON>, patch

import pytest

from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
)
from webapps.google_business_profile.infrastructure.services.location_status import (
    LocationStatusService,
)


class DummyApiClient:
    def __init__(self):
        self.location_id = "dummy-location-id"
        self.get_gbp_location_status = Mock()
        self.get_gbp_location_verifications = Mock()
        self._get_auth_headers = Mock(return_value={"Authorization": "Bearer token"})
        self.create_aiohttp_session = AsyncMock()
        self.get_gbp_location_status_async = AsyncMock()
        self.get_gbp_location_verifications_async = AsyncMock()


class TestLocationStatusServiceSync:
    """Test synchronous location status functionality."""

    def test_get_location_status_calls_api_client_methods(self):
        """Test that sync method calls both API client methods and returns their results."""
        api_client = DummyApiClient()
        service = LocationStatusService(api_client)
        status_resp = ApiResponse(status_code=HTTPStatus.OK, data={"status": "ok"})
        verif_resp = ApiResponse(status_code=HTTPStatus.OK, data={"verifications": []})

        api_client.get_gbp_location_status.return_value = status_resp
        api_client.get_gbp_location_verifications.return_value = verif_resp

        result = service.get_location_status()

        assert result == (status_resp, verif_resp)
        api_client.get_gbp_location_status.assert_called_once()
        api_client.get_gbp_location_verifications.assert_called_once()

    def test_get_location_status_sequential_execution(self):
        """Test that sync method executes calls sequentially."""
        api_client = DummyApiClient()
        service = LocationStatusService(api_client)
        call_order = []

        def status_call():
            call_order.append("status")
            time.sleep(0.1)  # Simulate network delay
            return ApiResponse(status_code=HTTPStatus.OK, data={"status": "ok"})

        def verif_call():
            call_order.append("verifications")
            time.sleep(0.1)  # Simulate network delay
            return ApiResponse(status_code=HTTPStatus.OK, data={"verifications": []})

        api_client.get_gbp_location_status.side_effect = status_call
        api_client.get_gbp_location_verifications.side_effect = verif_call

        start_time = time.time()
        service.get_location_status()
        end_time = time.time()

        # Should take at least 0.2s (sequential)
        assert end_time - start_time >= 0.2
        assert call_order == ["status", "verifications"]


class TestLocationStatusServiceConcurrent:
    """Test concurrent/async location status functionality."""

    @patch('asyncio.run')
    def test_get_location_status_concurrent_uses_asyncio_run(self, mock_asyncio_run):
        """Test that concurrent method uses asyncio.run."""
        service = LocationStatusService(DummyApiClient())
        expected_result = (Mock(), Mock())
        mock_asyncio_run.return_value = expected_result

        result = service.get_location_status_concurrent()

        mock_asyncio_run.assert_called_once()
        assert result == expected_result

    def test_concurrent_is_faster_than_sequential(self, monkeypatch):
        """Demonstrate that the concurrent approach is faster than sequential for two slow calls."""
        api_client = DummyApiClient()
        service = LocationStatusService(api_client)

        def slow_status():
            time.sleep(0.3)
            return ApiResponse(status_code=HTTPStatus.OK, data={"status": "ok"})

        def slow_verif():
            time.sleep(0.3)
            return ApiResponse(status_code=HTTPStatus.OK, data={"verifications": []})

        api_client.get_gbp_location_status.side_effect = slow_status
        api_client.get_gbp_location_verifications.side_effect = slow_verif

        async def slow_status_async(session):
            await asyncio.sleep(0.3)
            return ApiResponse(status_code=HTTPStatus.OK, data={"status": "ok"})

        async def slow_verif_async(session):
            await asyncio.sleep(0.3)
            return ApiResponse(status_code=HTTPStatus.OK, data={"verifications": []})

        api_client.get_gbp_location_status_async.side_effect = slow_status_async
        api_client.get_gbp_location_verifications_async.side_effect = slow_verif_async

        session_mock = AsyncMock()
        session_mock.__aenter__ = AsyncMock(return_value=session_mock)
        session_mock.__aexit__ = AsyncMock(return_value=None)

        api_client.create_aiohttp_session = Mock(return_value=session_mock)

        monkeypatch.setattr(
            asyncio, "run", lambda coro: asyncio.get_event_loop().run_until_complete(coro)
        )

        # Sequential: should take ~0.6s
        t0 = time.time()
        service.get_location_status()
        t1 = time.time()

        # Concurrent: should take just over 0.3s
        t2 = time.time()
        service.get_location_status_concurrent()
        t3 = time.time()

        seq_time = t1 - t0
        conc_time = t3 - t2
        assert conc_time < seq_time


class TestLocationStatusServiceValidation:
    """Test validation and error handling functionality."""

    def test_validate_responses_success_with_ok_responses(self):
        """Test that validate_responses passes with OK status codes."""
        service = LocationStatusService(DummyApiClient())
        ok_resp1 = ApiResponse(status_code=HTTPStatus.OK, data={})
        ok_resp2 = ApiResponse(status_code=HTTPStatus.OK, data={})

        # Should not raise
        service.validate_responses([ok_resp1, ok_resp2], 123)

    def test_validate_responses_raises_on_error_status(self):
        """Test that validate_responses raises when encountering error status codes."""
        service = LocationStatusService(DummyApiClient())
        bad_resp = ApiResponse(status_code=HTTPStatus.BAD_REQUEST, data={"error": "fail"})
        ok_resp = ApiResponse(status_code=HTTPStatus.OK, data={})

        # Patch error handler to raise
        service._error_handler.handle_error = Mock(side_effect=Exception("API Error"))

        with pytest.raises(Exception, match="API Error"):
            service.validate_responses([ok_resp, bad_resp], 123)

    def test_validate_responses_checks_all_responses(self):
        """Test that validate_responses checks all responses in the list."""
        service = LocationStatusService(DummyApiClient())
        responses = [
            ApiResponse(status_code=HTTPStatus.OK, data={}),
            ApiResponse(status_code=HTTPStatus.BAD_REQUEST, data={}),
            ApiResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, data={}),
        ]

        service._error_handler.handle_error = Mock()
        service.validate_responses(responses, 123)

        # Should call handle_error for the two error responses
        assert service._error_handler.handle_error.call_count == 2


class TestLocationStatusServiceMapping:
    """Test domain entity mapping functionality."""

    def test_to_domain_entity_calls_mapper(self):
        """Test that to_domain_entity delegates to the mapper correctly."""
        service = LocationStatusService(DummyApiClient())
        service._location_status_mapper.to_domain_entity = Mock(return_value="domain_obj")

        status_resp = ApiResponse(status_code=HTTPStatus.OK, data={"status": "ok"})
        verif_resp = ApiResponse(status_code=HTTPStatus.OK, data={"verifications": []})

        result = service.to_domain_entity(status_resp, verif_resp)

        service._location_status_mapper.to_domain_entity.assert_called_once_with(
            {"status": "ok"}, {"verifications": []}
        )
        assert result == "domain_obj"
