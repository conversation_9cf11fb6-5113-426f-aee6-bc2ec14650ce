# pylint: disable=line-too-long, redefined-outer-name
from unittest.mock import Mock, patch

import pytest

from webapps.google_business_profile.domain.const import Verification<PERSON>ethod
from webapps.google_business_profile.domain.interfaces.location_verification_client import (
    VerifyLocationParams,
    VerifyLocationBaseParams,
    CompleteLocationVerificationParams,
    LocationVerificationToken,
)
from webapps.google_business_profile.domain.value_types import LanguageCode
from webapps.google_business_profile.infrastructure.dtos.location_verification_client_dto import (
    LocationVerificationClientDTO,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.location_verification_client import (
    GoogleLocationVerificationClient,
    LOCATION_VERIFICATION_API,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.http.http_client import (
    HttpClient,
)
from webapps.google_business_profile.shared import (
    OAuthToken,
    LocationId,
    VerificationId,
)


@pytest.fixture
def mock_http_client():
    return Mock(spec=HttpClient)


@pytest.fixture
def client_data():
    return LocationVerificationClientDTO(
        oauth_token=OAuthToken("test_token"),
        location_id=LocationId("test_location_id"),
        verification_id=VerificationId("test_verification_id"),
    )


@pytest.fixture
def client(client_data, mock_http_client):
    return GoogleLocationVerificationClient(client_data=client_data, http_client=mock_http_client)


@pytest.fixture
def language_code():
    return LanguageCode("en-US")


@pytest.fixture
def verification_base_params(language_code):
    return VerifyLocationBaseParams(
        method=VerificationMethod.EMAIL,
        language_code=language_code,
    )


@pytest.fixture
def verify_location_params_email(verification_base_params):
    return VerifyLocationParams(
        base_params=verification_base_params,
        email_address="<EMAIL>",
    )


@pytest.fixture
def verify_location_params_sms(language_code):
    return VerifyLocationParams(
        base_params=VerifyLocationBaseParams(
            method=VerificationMethod.SMS,
            language_code=language_code,
        ),
        phone_number="+1234567890",
    )


@pytest.fixture
def verify_location_params_phone_call(language_code):
    return VerifyLocationParams(
        base_params=VerifyLocationBaseParams(
            method=VerificationMethod.PHONE_CALL,
            language_code=language_code,
        ),
        phone_number="+1234567890",
    )


@pytest.fixture
def verify_location_params_auto(language_code):
    return VerifyLocationParams(
        base_params=VerifyLocationBaseParams(
            method=VerificationMethod.AUTO,
            language_code=language_code,
        ),
    )


@pytest.fixture
def verify_location_params_vetted_partner(language_code):
    return VerifyLocationParams(
        base_params=VerifyLocationBaseParams(
            method=VerificationMethod.VETTED_PARTNER,
            language_code=language_code,
        ),
        token=LocationVerificationToken(token_string="test_token_string"),
    )


@pytest.fixture
def complete_location_verification_params():
    return CompleteLocationVerificationParams(verification_code="123456")


@patch.object(GoogleLocationVerificationClient, '_get_auth_headers')
class TestGoogleLocationVerificationClient:
    def test_fetch_verification_options(
        self, mock_get_auth_headers, client, mock_http_client, language_code
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {
                'options': [
                    {'verificationMethod': 'EMAIL'},
                    {'verificationMethod': 'SMS'},
                ]
            },
        }

        response = client.fetch_verification_options(language_code)

        mock_http_client.post.assert_called_once_with(
            f'{LOCATION_VERIFICATION_API}/locations/test_location_id:fetchVerificationOptions',
            headers=mock_get_auth_headers.return_value,
            data={'languageCode': 'en-US'},
        )

        assert response.status_code == 200
        assert response.data == {
            'options': [
                {'verificationMethod': 'EMAIL'},
                {'verificationMethod': 'SMS'},
            ]
        }

    def test_verify_location_email(
        self, mock_get_auth_headers, client, mock_http_client, verify_location_params_email
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {
                'name': 'locations/test_location_id/verifications/test_verification_id',
                'state': 'PENDING',
            },
        }

        response = client.verify_location(verify_location_params_email)

        expected_data = {
            'method': 'EMAIL',
            'languageCode': 'en-US',
            'emailAddress': '<EMAIL>',
        }

        mock_http_client.post.assert_called_once_with(
            f'{LOCATION_VERIFICATION_API}/locations/test_location_id:verify',
            headers=mock_get_auth_headers.return_value,
            data=expected_data,
        )

        assert response.status_code == 200
        assert response.data == {
            'name': 'locations/test_location_id/verifications/test_verification_id',
            'state': 'PENDING',
        }

    def test_verify_location_sms(
        self, mock_get_auth_headers, client, mock_http_client, verify_location_params_sms
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {
                'name': 'locations/test_location_id/verifications/test_verification_id',
                'state': 'PENDING',
            },
        }

        response = client.verify_location(verify_location_params_sms)

        expected_data = {
            'method': 'SMS',
            'languageCode': 'en-US',
            'phoneNumber': '+1234567890',
        }

        mock_http_client.post.assert_called_once_with(
            f'{LOCATION_VERIFICATION_API}/locations/test_location_id:verify',
            headers=mock_get_auth_headers.return_value,
            data=expected_data,
        )

        assert response.status_code == 200
        assert response.data == {
            'name': 'locations/test_location_id/verifications/test_verification_id',
            'state': 'PENDING',
        }

    def test_verify_location_phone_call(
        self, mock_get_auth_headers, client, mock_http_client, verify_location_params_phone_call
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {
                'name': 'locations/test_location_id/verifications/test_verification_id',
                'state': 'PENDING',
            },
        }

        response = client.verify_location(verify_location_params_phone_call)

        expected_data = {
            'method': 'PHONE_CALL',
            'languageCode': 'en-US',
            'phoneNumber': '+1234567890',
        }

        mock_http_client.post.assert_called_once_with(
            f'{LOCATION_VERIFICATION_API}/locations/test_location_id:verify',
            headers=mock_get_auth_headers.return_value,
            data=expected_data,
        )

        assert response.status_code == 200
        assert response.data == {
            'name': 'locations/test_location_id/verifications/test_verification_id',
            'state': 'PENDING',
        }

    def test_verify_location_auto(
        self, mock_get_auth_headers, client, mock_http_client, verify_location_params_auto
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {
                'name': 'locations/test_location_id/verifications/test_verification_id',
                'state': 'COMPLETED',
            },
        }

        response = client.verify_location(verify_location_params_auto)

        expected_data = {
            'method': 'AUTO',
            'languageCode': 'en-US',
        }

        mock_http_client.post.assert_called_once_with(
            f'{LOCATION_VERIFICATION_API}/locations/test_location_id:verify',
            headers=mock_get_auth_headers.return_value,
            data=expected_data,
        )

        assert response.status_code == 200
        assert response.data == {
            'name': 'locations/test_location_id/verifications/test_verification_id',
            'state': 'COMPLETED',
        }

    def test_verify_location_vetted_partner(
        self, mock_get_auth_headers, client, mock_http_client, verify_location_params_vetted_partner
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {
                'name': 'locations/test_location_id/verifications/test_verification_id',
                'state': 'COMPLETED',
            },
        }

        response = client.verify_location(verify_location_params_vetted_partner)

        expected_data = {
            'method': 'VETTED_PARTNER',
            'languageCode': 'en-US',
            'token': 'test_token_string',
        }

        mock_http_client.post.assert_called_once_with(
            f'{LOCATION_VERIFICATION_API}/locations/test_location_id:verify',
            headers=mock_get_auth_headers.return_value,
            data=expected_data,
        )

        assert response.status_code == 200
        assert response.data == {
            'name': 'locations/test_location_id/verifications/test_verification_id',
            'state': 'COMPLETED',
        }

    def test_complete_location_verification(
        self, mock_get_auth_headers, client, mock_http_client, complete_location_verification_params
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {
                'name': 'locations/test_location_id/verifications/test_verification_id',
                'state': 'COMPLETED',
            },
        }

        response = client.complete_location_verification(complete_location_verification_params)

        mock_http_client.post.assert_called_once_with(
            f'{LOCATION_VERIFICATION_API}/locations/test_location_id/verifications/test_verification_id:complete',
            headers=mock_get_auth_headers.return_value,
            data={'pin': '123456'},
        )

        assert response.status_code == 200
        assert response.data == {
            'name': 'locations/test_location_id/verifications/test_verification_id',
            'state': 'COMPLETED',
        }

    def test_fetch_verification_options_error_response(
        self, mock_get_auth_headers, client, mock_http_client, language_code
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 400,
            'data': {
                'error': {
                    'message': 'Location not found',
                    'code': 400,
                    'status': 'NOT_FOUND',
                }
            },
        }

        response = client.fetch_verification_options(language_code)

        assert response.status_code == 400
        assert response.data == {
            'error': {
                'message': 'Location not found',
                'code': 400,
                'status': 'NOT_FOUND',
            }
        }

    def test_verify_location_error_response(
        self, mock_get_auth_headers, client, mock_http_client, verify_location_params_email
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 400,
            'data': {
                'error': {
                    'message': 'Location is not eligible for the EMAIL method.',
                    'code': 400,
                    'status': 'FAILED_PRECONDITION',
                }
            },
        }

        response = client.verify_location(verify_location_params_email)

        assert response.status_code == 400
        assert response.data == {
            'error': {
                'message': 'Location is not eligible for the EMAIL method.',
                'code': 400,
                'status': 'FAILED_PRECONDITION',
            }
        }

    def test_complete_location_verification_error_response(
        self, mock_get_auth_headers, client, mock_http_client, complete_location_verification_params
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.post.return_value = {
            'status_code': 400,
            'data': {
                'error': {
                    'message': 'Invalid PIN.',
                    'code': 400,
                    'status': 'FAILED_PRECONDITION',
                }
            },
        }

        response = client.complete_location_verification(complete_location_verification_params)

        assert response.status_code == 400
        assert response.data == {
            'error': {
                'message': 'Invalid PIN.',
                'code': 400,
                'status': 'FAILED_PRECONDITION',
            }
        }

    def test_build_fetch_verification_options_request_data(  # pylint: disable=protected-access
        self, mock_get_auth_headers, language_code
    ):
        result = GoogleLocationVerificationClient._build_fetch_verification_options_request_data(
            language_code
        )

        assert result == {'languageCode': 'en-US'}

    def test_build_complete_location_verification_request_data(  # pylint: disable=protected-access
        self, mock_get_auth_headers, complete_location_verification_params
    ):
        result = (
            GoogleLocationVerificationClient._build_complete_location_verification_request_data(
                complete_location_verification_params
            )
        )

        assert result == {'pin': '123456'}

    def test_verify_location_params_to_api_request_data_email(
        self, mock_get_auth_headers, verify_location_params_email
    ):
        result = verify_location_params_email.to_api_request_data()

        expected_data = {
            'method': 'EMAIL',
            'languageCode': 'en-US',
            'emailAddress': '<EMAIL>',
        }
        assert result == expected_data

    def test_verify_location_params_to_api_request_data_sms(
        self, mock_get_auth_headers, verify_location_params_sms
    ):
        result = verify_location_params_sms.to_api_request_data()

        expected_data = {
            'method': 'SMS',
            'languageCode': 'en-US',
            'phoneNumber': '+1234567890',
        }
        assert result == expected_data

    def test_verify_location_params_to_api_request_data_phone_call(
        self, mock_get_auth_headers, verify_location_params_phone_call
    ):
        result = verify_location_params_phone_call.to_api_request_data()

        expected_data = {
            'method': 'PHONE_CALL',
            'languageCode': 'en-US',
            'phoneNumber': '+1234567890',
        }
        assert result == expected_data

    def test_verify_location_params_to_api_request_data_auto(
        self, mock_get_auth_headers, verify_location_params_auto
    ):
        result = verify_location_params_auto.to_api_request_data()

        expected_data = {
            'method': 'AUTO',
            'languageCode': 'en-US',
        }
        assert result == expected_data

    def test_verify_location_params_to_api_request_data_vetted_partner(
        self, mock_get_auth_headers, verify_location_params_vetted_partner
    ):
        result = verify_location_params_vetted_partner.to_api_request_data()

        expected_data = {
            'method': 'VETTED_PARTNER',
            'languageCode': 'en-US',
            'token': 'test_token_string',
        }
        assert result == expected_data

    def test_verify_location_params_to_api_request_data_vetted_partner_no_token(
        self, mock_get_auth_headers, language_code
    ):
        params = VerifyLocationParams(
            base_params=VerifyLocationBaseParams(
                method=VerificationMethod.VETTED_PARTNER,
                language_code=language_code,
            ),
            token=None,
        )
        result = params.to_api_request_data()

        expected_data = {
            'method': 'VETTED_PARTNER',
            'languageCode': 'en-US',
            'token': None,
        }
        assert result == expected_data

    def test_client_initialization(  # pylint: disable=protected-access
        self, mock_get_auth_headers, client_data, mock_http_client
    ):
        client = GoogleLocationVerificationClient(
            client_data=client_data, http_client=mock_http_client
        )

        assert client._oauth_token == "test_token"
        assert client.location_id == "test_location_id"
        assert client.verification_id == "test_verification_id"
        assert client._http_client == mock_http_client

    def test_client_initialization_default_http_client(  # pylint: disable=protected-access
        self, mock_get_auth_headers, client_data
    ):
        client = GoogleLocationVerificationClient(client_data=client_data)

        assert client._oauth_token == "test_token"
        assert client.location_id == "test_location_id"
        assert client.verification_id == "test_verification_id"
        assert client._http_client is not None
        assert isinstance(client._http_client, HttpClient)


class TestGoogleLocationVerificationClientUnpatched:
    """Tests that need access to the actual unpatched methods."""

    def test_get_auth_headers(
        self, client_data, mock_http_client
    ):  # pylint: disable=protected-access
        client = GoogleLocationVerificationClient(
            client_data=client_data, http_client=mock_http_client
        )
        headers = client._get_auth_headers()

        expected_headers = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        assert headers == expected_headers
