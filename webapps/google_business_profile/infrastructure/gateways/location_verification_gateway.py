# pylint: disable=line-too-long
from http import HTTPStatus

from settings import logging
from webapps.google_business_profile.domain.dtos import (
    VerificationOptionsDTO,
    LocationVerificationDTO,
)
from webapps.google_business_profile.domain.interfaces.location_verification_client import (
    VerifyLocationParams,
    CompleteLocationVerificationParams,
)

from webapps.google_business_profile.domain.interfaces.location_verification_gateway import (
    GoogleLocationVerificationGateway,
)
from webapps.google_business_profile.domain.value_types import LanguageCode
from webapps.google_business_profile.infrastructure.converters.location_verification_options_converter import (
    LocationVerificationOptionsMapper,
)
from webapps.google_business_profile.infrastructure.converters.location_verififcatoion_converter import (
    LocationVerificationMapper,
)
from webapps.google_business_profile.infrastructure.gateways.api_error_handler import (
    ApiErrorHandler,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.location_verification_client import (
    GoogleLocationVerificationClient,
)

logger = logging.getLogger('booksy.google_business_profile')


class GoogleLocationVerificationApiGateway(GoogleLocationVerificationGateway):
    """Gateway implementation for Location Verification API"""

    def __init__(self, api_client: GoogleLocationVerificationClient):
        self._error_handler = ApiErrorHandler()
        self._api_client = api_client
        self._location_verification_options_mapper = LocationVerificationOptionsMapper()
        self._location_verification_mapper = LocationVerificationMapper()

    def fetch_verification_options(self, language_code: LanguageCode) -> VerificationOptionsDTO:
        verification_options_response = self._api_client.fetch_verification_options(language_code)
        if verification_options_response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(verification_options_response)

        return self._location_verification_options_mapper.to_domain_dto(
            verification_options_response.data
        )

    def verify_location(
        self, verifi_location_params: VerifyLocationParams
    ) -> LocationVerificationDTO:
        verify_location_response = self._api_client.verify_location(verifi_location_params)
        if verify_location_response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(verify_location_response, verifi_location_params)

        return self._location_verification_mapper.to_domain_dto(verify_location_response.data)

    def complete_location_verification(
        self, complete_params: CompleteLocationVerificationParams
    ) -> LocationVerificationDTO:
        complete_verification_response = self._api_client.complete_location_verification(
            complete_params
        )
        if complete_verification_response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(complete_verification_response, complete_params)

        return self._location_verification_mapper.to_domain_dto(complete_verification_response.data)
