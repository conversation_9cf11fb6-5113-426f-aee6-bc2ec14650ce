from typing import Any

from webapps.google_business_profile.domain.dtos import LocationVerificationDTO


class LocationVerificationMapper:
    @staticmethod
    def to_domain_dto(location_verification_data: dict[str, Any]) -> LocationVerificationDTO:
        verification_name = location_verification_data.get('verification').get('name')
        verification_id = (
            verification_name.split('/')[-1] if '/' in verification_name else verification_name
        )
        return LocationVerificationDTO(
            verification_id=verification_id,
            method=location_verification_data.get('verification').get('method'),
            status_of_verification_attempt=location_verification_data.get('verification').get(
                'state'
            ),
            create_time=location_verification_data.get('verification').get('createTime'),
            announcement=location_verification_data.get('verification').get('announcement'),
        )
