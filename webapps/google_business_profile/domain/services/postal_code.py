import logging

from webapps.google_business_profile.domain.const import Country
from webapps.google_business_profile.domain.value_types import PostalCode

logger = logging.getLogger('booksy.google_business_profile')


class PostalCodeService:
    """Domain service for creating PostalCode value objects with proper country validation."""

    @staticmethod
    def create_postal_code(postal_code_value: str, country_code_str: str) -> PostalCode:
        """
        Create a PostalCode value object with proper country enum conversion.

        Args:
            postal_code_value: The postal code string
            country_code_str: Country code as string (e.g., 'us', 'pl', 'fr')

        Returns:
            PostalCode: A valid PostalCode value object
        """
        country_enum = PostalCodeService._convert_country_code_to_enum(country_code_str)
        return PostalCode(postal_code_value, country_enum)

    @staticmethod
    def _convert_country_code_to_enum(country_code: str) -> Country:
        """Convert country code string to Country enum if country_code is empty or there is no
        enum for given country returns Country.UNSUPPORTED value"""
        if not country_code:
            logger.warning(
                "Received empty Country code in _convert_country_code_to_enum, "
                "Using Country.UNSUPPORTED"
            )
            return Country.UNSUPPORTED

        try:
            return Country(country_code.lower())
        except ValueError:
            logger.warning(
                "Country code (%s) not supported for postal code validation. "
                "Using Country.UNSUPPORTED",
                country_code,
            )
            return Country.UNSUPPORTED
