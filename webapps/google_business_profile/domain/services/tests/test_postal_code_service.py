from unittest import TestCase
from unittest.mock import patch

from webapps.google_business_profile.domain.const import Country
from webapps.google_business_profile.domain.services.postal_code import PostalCodeService
from webapps.google_business_profile.domain.value_types import PostalCode


class TestPostalCodeService(TestCase):
    def setUp(self):
        self.service = PostalCodeService()

    def test_create_postal_code_valid_data_capital(self):
        postal_code_obj = self.service.create_postal_code('12345', 'US')
        self.assertIsInstance(postal_code_obj, PostalCode)

    def test_create_postal_code_valid_data_small(self):
        postal_code_obj = self.service.create_postal_code('12345', 'us')
        self.assertIsInstance(postal_code_obj, PostalCode)

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_create_postal_code_invalid_postal_code(self, mock_value_types_logger):
        self.service.create_postal_code('dav-321', 'US')
        mock_value_types_logger.error.assert_called_once_with(
            "Invalid postal code format %s", 'dav-321'
        )

    @patch('webapps.google_business_profile.domain.services.postal_code.logger')
    def test_create_postal_code_empty_postal_code(self, mock_service_logger):
        postal_code_obj = self.service.create_postal_code('12345', None)
        mock_service_logger.warning.assert_called_once_with(
            "Received empty Country code in _convert_country_code_to_enum, "
            "Using Country.UNSUPPORTED",
        )
        self.assertIsInstance(postal_code_obj, PostalCode)
        self.assertEqual(postal_code_obj.country_code, Country.UNSUPPORTED)

    @patch('webapps.google_business_profile.domain.services.postal_code.logger')
    def test_create_postal_code_invalid_country_code(self, mock_service_logger):
        postal_code_obj = self.service.create_postal_code('12345', 'xyz')
        mock_service_logger.warning.assert_called_once_with(
            "Country code (%s) not supported for postal code validation. Using Country.UNSUPPORTED",
            'xyz',
        )
        self.assertIsInstance(postal_code_obj, PostalCode)
        self.assertEqual(postal_code_obj.country_code, Country.UNSUPPORTED)
        self.assertEqual(postal_code_obj.value, '12345')
