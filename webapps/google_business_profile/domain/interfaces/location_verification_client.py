import abc
from dataclasses import dataclass
from typing import Union, Any

from webapps.google_business_profile.domain.const import VerificationMethod
from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
)
from webapps.google_business_profile.domain.value_types import LanguageCode


@dataclass(frozen=True)
class VerifyLocationBaseParams:
    method: Union[
        VerificationMethod.EMAIL,
        VerificationMethod.SMS,
        VerificationMethod.PHONE_CALL,
        VerificationMethod.AUTO,
        VerificationMethod.VETTED_PARTNER,
    ]
    language_code: LanguageCode


@dataclass(frozen=True)
class LocationVerificationToken:
    token_string: str


@dataclass(frozen=True)
class VerifyLocationParams:
    base_params: VerifyLocationBaseParams
    email_address: str | None = None
    phone_number: str | None = None
    token: LocationVerificationToken | None = None

    def to_api_request_data(self) -> dict[str, Any]:
        """Convert domain object to API request data based on verification method."""
        method = self.base_params.method
        language_code = self.base_params.language_code

        request_data = {
            'method': method,
            'languageCode': str(language_code),
        }

        if method == VerificationMethod.EMAIL:
            request_data['emailAddress'] = self.email_address
        elif method in (VerificationMethod.SMS, VerificationMethod.PHONE_CALL):
            request_data['phoneNumber'] = self.phone_number
        elif method == VerificationMethod.VETTED_PARTNER:
            request_data['token'] = self.token.token_string if self.token else None

        return request_data


@dataclass(frozen=True)
class CompleteLocationVerificationParams:
    verification_code: str


class GoogleLocationVerificationAbstractClient(abc.ABC):
    @abc.abstractmethod
    def fetch_verification_options(self, language_code: LanguageCode) -> ApiResponse: ...

    @abc.abstractmethod
    def verify_location(self, verify_location_params: VerifyLocationParams) -> ApiResponse: ...

    @abc.abstractmethod
    def complete_location_verification(
        self, complete_params: CompleteLocationVerificationParams
    ) -> ApiResponse: ...
