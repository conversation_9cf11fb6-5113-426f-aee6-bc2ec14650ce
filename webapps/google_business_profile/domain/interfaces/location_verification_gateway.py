import abc

from webapps.google_business_profile.domain.dtos import (
    VerificationOptionsDTO,
    LocationVerificationDTO,
)
from webapps.google_business_profile.domain.interfaces.location_verification_client import (
    CompleteLocationVerificationParams,
)
from webapps.google_business_profile.domain.interfaces.location_verification_client import (
    VerifyLocationParams,
)
from webapps.google_business_profile.domain.value_types import LanguageCode


class GoogleLocationVerificationGateway(abc.ABC):
    @abc.abstractmethod
    def fetch_verification_options(self, language_code: LanguageCode) -> VerificationOptionsDTO: ...

    @abc.abstractmethod
    def verify_location(
        self, verifi_location_params: VerifyLocationParams
    ) -> LocationVerificationDTO: ...

    @abc.abstractmethod
    def complete_location_verification(
        self, complete_params: CompleteLocationVerificationParams
    ) -> LocationVerificationDTO: ...
