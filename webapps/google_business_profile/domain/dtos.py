from dataclasses import dataclass, field

from webapps.google_business_profile.domain.const import (
    GoogleVerificationStatus,
    VerificationMethod,
)
from webapps.google_business_profile.shared import PhoneNumber, VerificationId


@dataclass(frozen=True)
class EmailData:
    domain: str
    user: str  # Username in the email address. E.g. "foo" in <EMAIL>
    is_user_name_editable: bool


@dataclass(frozen=True)
class BaseVerificationOptionDTO:
    verification_method: str


@dataclass(frozen=True)
class EmailVerificationOptionDTO(BaseVerificationOptionDTO):
    email_data: EmailData
    verification_method: str = field(default='EMAIL', init=False)


@dataclass(frozen=True)
class PhoneCallVerificationOptionDTO(BaseVerificationOptionDTO):
    phone_number: PhoneNumber
    verification_method: str = field(default='PHONE_CALL', init=False)


@dataclass(frozen=True)
class SMSVerificationOptionDTO(BaseVerificationOptionDTO):
    phone_number: PhoneNumber
    verification_method: str = field(default='SMS', init=False)


@dataclass(frozen=True)
class AutoVerificationOptionDTO(BaseVerificationOptionDTO):
    verification_method: str = field(default='AUTO', init=False)


@dataclass(frozen=True)
class VettedPartnerVerificationOptionDTO(BaseVerificationOptionDTO):
    announcement: str
    verification_method: str = field(default='VETTED_PARTNER', init=False)


@dataclass(frozen=True)
class VerificationOptionsDTO:
    options: list[BaseVerificationOptionDTO]


@dataclass(frozen=True)
class LocationVerificationDTO:
    verification_id: VerificationId
    method: VerificationMethod
    status_of_verification_attempt: GoogleVerificationStatus
    create_time: str
    announcement: str | None = None
