import unittest
from unittest.mock import patch, call

from webapps.google_business_profile.domain.const import Country
from webapps.google_business_profile.domain.value_types import PostalCode


class TestPostalCode(unittest.TestCase):
    def test_valid_postal_code(self):
        self.assertIsNotNone(PostalCode(value='12345', country_code=Country.US))

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_invalid_postal_code(self, mock_value_types_logger):
        PostalCode(value='abc-wrong-123', country_code=Country.US)
        mock_value_types_logger.error.assert_called_once_with(
            "Invalid postal code format %s", 'abc-wrong-123'
        )

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_unsupported_country(self, mock_value_types_logger):
        postal_code = PostalCode(value='abc-wrong-123', country_code=Country.UNSUPPORTED)
        mock_value_types_logger.error.assert_called_once_with(
            "Country code: %s not supported for PostalCode validation", Country.UNSUPPORTED
        )
        self.assertEqual(Country.UNSUPPORTED, postal_code.country_code)
        self.assertEqual('abc-wrong-123', postal_code.value)

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_unsupported_country_as_string(self, mock_value_types_logger):
        postal_code = PostalCode(value='abc-wrong-123', country_code="xyz")  # type: ignore
        mock_value_types_logger.error.assert_called_once_with(
            "Country code: %s not supported for PostalCode validation", "xyz"
        )
        self.assertEqual("xyz", postal_code.country_code)
        self.assertEqual("abc-wrong-123", postal_code.value)

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_empty_postal_code(self, mock_value_types_logger):
        PostalCode(value='', country_code=Country.US)
        mock_value_types_logger.error.assert_called_once_with("PostalCode cannot be empty")

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_empty_postal_code_unsupported_country(self, mock_value_types_logger):
        PostalCode(value='', country_code=Country.UNSUPPORTED)
        expected_calls = [
            call.error(
                'Country code: %s not supported for PostalCode validation', Country.UNSUPPORTED
            ),
            call.error('PostalCode cannot be empty'),
        ]
        mock_value_types_logger.assert_has_calls(expected_calls)
