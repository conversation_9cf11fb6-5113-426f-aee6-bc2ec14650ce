from rest_framework import serializers


class SimpleVerificationOptionDTOSerializer(serializers.Serializer):
    method = serializers.Char<PERSON>ield(read_only=True)
    confirmation_data = serializers.CharField()


class VerificationOptionsPresentationDTOSerializer(serializers.Serializer):
    options = SimpleVerificationOptionDTOSerializer(many=True)


class VerifyLocationRequestSerializer(serializers.Serializer):
    oauth_token = serializers.CharField(required=True)
    location_id = serializers.CharField(required=True)
    method = serializers.CharField(required=True)
    confirmation_data = serializers.CharField(required=True)


class CompleteLocationVerificationRequestSerializer(serializers.Serializer):
    oauth_token = serializers.Char<PERSON>ield(required=True)
    location_id = serializers.CharField(required=True)
    verification_id = serializers.CharField(required=True)
    verification_code = serializers.CharField(required=True)


class LocationVerificationPresentationDTOSerializer(serializers.Serializer):
    verification_id = serializers.Char<PERSON><PERSON>(read_only=True)
    method = serializers.Cha<PERSON><PERSON><PERSON>(read_only=True)
