from webapps.google_business_profile.domain.const import Veri<PERSON><PERSON>ethod
from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    UpdateLocationParams,
    CategoryQueryParams,
    CreateLocationParams,
)
from webapps.google_business_profile.domain.interfaces.location_verification_client import (
    VerifyLocationParams,
    LocationVerificationToken,
    VerifyLocationBaseParams,
    CompleteLocationVerificationParams,
)
from webapps.google_business_profile.domain.value_objects import (
    LocationCategory,
    LocationCategories,
    LocationAddress,
    LocationPhoneNumbers,
)
from webapps.google_business_profile.domain.value_types import RegionCode, LanguageCode


class DtoMapper:
    @staticmethod
    def to_create_location_params(serializer_data: dict) -> CreateLocationParams:
        primary_category = LocationCategory(
            name=serializer_data['categories']['primary_category']['name'],
            display_name=serializer_data['categories']['primary_category'].get('display_name'),
        )
        categories = LocationCategories(primary_category=primary_category)

        address_data = serializer_data['storefront_address']
        storefront_address = LocationAddress(
            region_code=RegionCode(address_data['region_code']),
            language_code=LanguageCode(address_data['language_code']),
            postal_code=address_data['postal_code'],
            administrative_area=address_data['administrative_area'],
            locality=address_data['locality'],
            address_lines=address_data['address_lines'],
        )

        phone_data = serializer_data['phone_numbers']
        phone_numbers = LocationPhoneNumbers(
            primary_phone=phone_data['primary_phone'],
            additional_phones=phone_data.get('additional_phones', []),
        )

        return CreateLocationParams(
            title=serializer_data['title'],
            language_code=LanguageCode(serializer_data['language_code']),
            storefront_address=storefront_address,
            categories=categories,
            phone_numbers=phone_numbers,
            website_uri=serializer_data.get('website_uri'),
            validate_only=serializer_data.get('validate_only', False),
        )

    @staticmethod
    def to_update_location_params(serializer_data: dict) -> UpdateLocationParams:
        primary_category = LocationCategory(
            name=serializer_data['categories']['primary_category']['name'],
            display_name=serializer_data['categories']['primary_category'].get('display_name'),
        )

        categories = LocationCategories(primary_category=primary_category)

        storefront_address = None
        if 'storefront_address' in serializer_data:
            address_data = serializer_data['storefront_address']
            storefront_address = LocationAddress(
                region_code=RegionCode(address_data['region_code']),
                language_code=LanguageCode(address_data['language_code']),
                postal_code=address_data['postal_code'],
                administrative_area=address_data['administrative_area'],
                locality=address_data['locality'],
                address_lines=address_data['address_lines'],
            )

        return UpdateLocationParams(
            title=serializer_data['title'],
            categories=categories,
            storefront_address=storefront_address,
            validate_only=serializer_data.get('validate_only', False),
        )

    @staticmethod
    def to_category_query_params(serializer_data: dict) -> CategoryQueryParams:
        return CategoryQueryParams(
            region_code=RegionCode(serializer_data['region_code']),
            language_code=LanguageCode(serializer_data['language_code']),
            filter=serializer_data['filter'],
            page_size=serializer_data.get('page_size'),
            view=serializer_data.get('view'),
        )

    @staticmethod
    def to_verify_location_query_params(
        serializer_data: dict, language_code: str
    ) -> VerifyLocationParams:

        method = serializer_data.get('method')
        confirmation_data = serializer_data.get('confirmation_data')

        email_address = None
        phone_number = None
        token = None

        if method == VerificationMethod.EMAIL:
            email_address = confirmation_data
        if method == VerificationMethod.SMS or VerificationMethod.PHONE_CALL:
            phone_number = confirmation_data
        if method == VerificationMethod.VETTED_PARTNER:
            token = LocationVerificationToken(token_string=confirmation_data)

        return VerifyLocationParams(
            base_params=VerifyLocationBaseParams(
                method=method,
                language_code=LanguageCode(language_code),
            ),
            email_address=email_address,
            phone_number=phone_number,
            token=token,
        )

    @staticmethod
    def to_complete_location_verification_params(
        serializer_data: dict,
    ) -> CompleteLocationVerificationParams:
        return CompleteLocationVerificationParams(
            verification_code=serializer_data.get('verification_code')
        )
