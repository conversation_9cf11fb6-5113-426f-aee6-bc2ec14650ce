from django.contrib.admin.models import LogEntry, CHANGE
from django.contrib.auth.models import Group
from django.test import RequestFactory
from django.urls import reverse
from model_bakery import baker

from webapps.admin_extra.tests import DjangoTestCase
from webapps.user.admin import GroupAdmin, GroupForm
from webapps.user.baker_recipes import user_recipe


class TestGroupAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.group = baker.make(Group, name='Test Group')
        self.user1 = user_recipe.make(email='<EMAIL>', is_staff=True)
        self.user2 = user_recipe.make(email='<EMAIL>', is_staff=True)
        self.user3 = user_recipe.make(email='<EMAIL>', is_staff=True)

        self.group.user_set.add(self.user1, self.user2)

        self.admin = GroupAdmin(Group, None)
        self.request = RequestFactory().get('/')
        self.request.user = self.login_admin()

    def create_form(self, user_ids):
        form_data = {'name': self.group.name, 'user_set': user_ids}
        form = GroupForm(data=form_data, instance=self.group)
        self.assertTrue(form.is_valid())
        return form

    def test_add_users_to_group(self):
        form = self.create_form([self.user1.id, self.user2.id, self.user3.id])

        self.admin.save_model(self.request, self.group, form, change=True)

        logs = LogEntry.objects.filter(object_id=self.group.id).order_by('action_time')
        self.assertEqual(logs.count(), 1)

        log = logs.first()
        self.assertIn('ADDED: <EMAIL>', log.change_message)
        self.assertEqual(log.action_flag, CHANGE)

    def test_remove_users_from_group(self):
        form = self.create_form([self.user1.id])  # Only user1, user2 removed

        self.admin.save_model(self.request, self.group, form, change=True)

        logs = LogEntry.objects.filter(object_id=self.group.id).order_by('action_time')
        self.assertEqual(logs.count(), 1)

        log = logs.first()
        self.assertIn('REMOVED: <EMAIL>', log.change_message)
        self.assertEqual(log.action_flag, CHANGE)

    def test_add_and_remove_users_simultaneously(self):
        form = self.create_form([self.user2.id, self.user3.id])  # user1 removed, user3 added

        self.admin.save_model(self.request, self.group, form, change=True)

        logs = LogEntry.objects.filter(object_id=self.group.id).order_by('action_time')
        self.assertEqual(logs.count(), 2)

        log_messages = [log.change_message for log in logs]
        self.assertTrue(any('ADDED: <EMAIL>' in msg for msg in log_messages))
        self.assertTrue(any('REMOVED: <EMAIL>' in msg for msg in log_messages))

    def test_no_changes_creates_no_logs(self):
        form = self.create_form([self.user1.id, self.user2.id])  # Same as initial state

        self.admin.save_model(self.request, self.group, form, change=True)

        logs = LogEntry.objects.filter(object_id=self.group.id)
        self.assertEqual(logs.count(), 0)

    def test_actual_admin_form_submission(self):
        url = reverse('admin:auth_group_change', args=[self.group.id])

        response = self.client.post(
            url,
            {
                'name': self.group.name,
                'user_set': [self.user1.id, self.user3.id],  # Remove user2, add user3
            },
            follow=True,
        )

        self.assertEqual(response.status_code, 200)

        logs = LogEntry.objects.filter(object_id=self.group.id).order_by('action_time')
        self.assertEqual(logs.count(), 2)

        log_messages = [log.change_message for log in logs]
        self.assertTrue(
            any('ADDED: <EMAIL>' in msg for msg in log_messages)
            or any('REMOVED: <EMAIL>' in msg for msg in log_messages)
        )
