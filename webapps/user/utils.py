import hashlib
import os
import logging
from abc import ABC, abstractmethod
from datetime import timed<PERSON>ta

from django.conf import settings
from django.contrib.admin.models import LogEntry
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.utils.encoding import smart_bytes
from django.utils.translation import gettext_lazy as _

from lib.elasticsearch.tools import delete_documents_with_retries
from lib.feature_flag.feature import AutomaticCxAccountDeletion
from lib.tools import tznow, chunker
from service.siwa.tools import revoke_apple_token
from service.tools import AuthenticateUsingAccessTokenMixin
from webapps import consts
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentTypeSM as AT, BookingAction, WhoMakesChange
from webapps.booking.events import appointment_status_changed_by_customer_event
from webapps.booking.models import Appointment, AppointmentTraveling, BookingSources, BookingChange
from webapps.booking.tasks import (
    create_late_cancellation_notifications_task,
    update_any_mobile_customer_appointments_task,
)
from webapps.business.models.bci import (
    BCIAttachedFile,
    BusinessCustomerInfoTag,
    BCIPatientFile,
    BusinessCustomerInfo,
    BCIVersumAgreement,
    BusinessCustomerInfoHistory,
)
from webapps.business.models import Resource
from webapps.business.service_promotions import CustomerAppointmentServicePromotionMixin
from webapps.consents.models import Consent
from webapps.family_and_friends.events import delete_family_and_friends_data_for_user
from webapps.notification.scenarios import BookingChangedScenario, start_scenario
from webapps.photo.models import Photo
from webapps.pos.models import POS, Transaction
from webapps.pos.tasks import (
    CheckoutBooksyPayTransaction,
    CheckoutPrepaidTransaction,
    ReleaseDepositOnCancel,
)
from webapps.segment.tasks import forget_email_gdpr
from webapps.user.enums import AccountDeletionExecutionMethod
from webapps.user.models import User, UserInternalData, UserPolicyAgreement, UserProfile
from webapps.user.tasks.user_deletion import (
    remove_photos_from_buckets,
    send_email_with_deletion_cancellation_task,
    send_emails_about_user_deletion_task,
)


log_action = logging.getLogger('booksy.delete_user')


def hash_field(value, max_length=None, prefix=''):
    if value is None:
        return None
    hashed = hashlib.sha256(smart_bytes(value)).hexdigest()
    if prefix:
        hashed = prefix + '_' + hashed
    return hashed if not max_length else hashed[:max_length]


# max length is 75 because BusinessCustomerInfo.email has this limit
def hash_email(email, max_length=75, prefix=''):
    if email is None:
        return None
    if email == '':
        return ''
    email_suffix = f'@{User.DELETED_USER_DOMAIN}'
    max_length = max_length - len(email_suffix) if max_length is not None else None
    return hash_field(email, max_length=max_length, prefix=prefix) + email_suffix


def is_business_user(user: User) -> bool:
    return user.staffers.filter(deleted__isnull=True).exists() or user.businesses.exists()


class AbstractDeleteUserStrategy(ABC):
    @abstractmethod
    def before_cancellation_steps(self, user: User, context: dict) -> tuple[bool, str]:
        pass

    @abstractmethod
    def delete_user(self, user: User, context: dict) -> tuple[bool, str]:
        pass

    @abstractmethod
    def after_cancellation_steps(self, user: User, context: dict):
        pass

    @classmethod
    @abstractmethod
    def clear_bcis(cls, bci_ids: list[int], context: dict):
        pass

    @classmethod
    @abstractmethod
    def clear_consent(cls, consent: Consent, context: dict):
        pass

    @classmethod
    @abstractmethod
    def delete_photos(cls, photos_ids: list[int]):
        pass


class DeleteUser:
    def __init__(
        self,
        strategy: AbstractDeleteUserStrategy,
        user: User,
        delete_business_user: bool = True,
        method: AccountDeletionExecutionMethod = AccountDeletionExecutionMethod.ADMIN,
    ):
        self._strategy = strategy
        self._user = user
        self.context = {'delete_business_user': delete_business_user, 'method': method}

    def call(self) -> tuple[bool, str]:
        result = self._strategy.before_cancellation_steps(self._user, self.context)
        if not result[0]:
            return result
        with transaction.atomic():
            result = self._strategy.delete_user(self._user, self.context)
        if not result[0]:
            return result
        transaction.on_commit(
            lambda: self._strategy.after_cancellation_steps(self._user, self.context)
        )
        return result


# pylint: disable=too-many-public-methods
class SelectForUpdateDeleteUserStrategy(AbstractDeleteUserStrategy):
    def before_cancellation_steps(self, user: User, context: dict) -> tuple[bool, str]:
        if not user:
            return False, _("No user given")
        revoke_apple_token(user.id)
        return True, ''

    def delete_user(self, user: User, context: dict) -> tuple[bool, str]:
        user = self.get_user(user)
        if not context.get('delete_business_user') and is_business_user(user):
            return False, _(
                "User is a business user and delete_business_user option was set to False"
            )
        id_md5 = hashlib.md5(smart_bytes(user.id)).hexdigest()
        email_prefix = id_md5[: settings.ID_MD5_CHARS_PREFIX_LENGTH]
        context['old_email'] = self.clear_user_data(user, email_prefix)
        context['old_language'] = self.clear_user_profile(user)
        self.clear_user_resources(user)
        self.clear_user_bcis(user, email_prefix)
        self.clear_user_appointments(user, context['old_email'], email_prefix)
        self.clear_user_transactions(user)
        user.socialaccount_set.all().delete()
        self.update_user_internal_data(user, context.get('method', None))
        user.delete_all_user_sessions()
        return True, ''

    def after_cancellation_steps(self, user: User, context: dict):
        old_email = context.get('old_email', '')
        old_language = context.get('old_language', '')
        delete_family_and_friends_data_for_user.send(user.id)
        forget_email_gdpr.delay(email=old_language)
        send_email_with_deletion_cancellation_task.delay(
            user.id, old_email=old_email, old_language=old_language
        )

    @staticmethod
    def clean_admin_log_entries_for_user(user: User):
        user_content_type = ContentType.objects.get_for_model(User)
        log_entries = LogEntry.objects.filter(content_type=user_content_type, object_id=user.id)
        log_entries.delete()

    @classmethod
    def clear_user_profile(cls, user: User) -> str:
        old_language = settings.LANGUAGE_CODE[:2]
        profile_fields = [
            'about_me',
            'photo_id',
            'address_line_1',
            'address_line_2',
            'longitude',
            'region_id',
            'apartment_number',
            'city',
            'zipcode',
            'language',
            'profile_type',
        ]
        for profile in cls.get_user_profile(user, profile_fields):
            photo = profile.photo
            if profile.profile_type == UserProfile.Type.CUSTOMER:
                old_language = profile.language
            cls.update_user_profile(profile, profile_fields)

            if photo:
                cls.delete_photos([photo.id])

            for apple_identity in profile.appleidentity_set.all():
                apple_identity.delete()

        cls.clear_authentication_cache(user)
        # required for sending email with proper language
        return old_language

    @classmethod
    def clear_authentication_cache(cls, user: User):
        AuthenticateUsingAccessTokenMixin.get_user_profile.cache_clear(
            user.id,
            UserProfile.Type.CUSTOMER.value,
        )
        AuthenticateUsingAccessTokenMixin.get_user_profile.cache_clear(
            user.id,
            UserProfile.Type.BUSINESS.value,
        )

    @classmethod
    def clear_user_bcis(cls, user: User, email_prefix: str = ''):
        bci_ids = list(user.business_customer_infos.values_list('id', flat=True))
        cls.clear_bcis(bci_ids, {'email_prefix': email_prefix})

    @classmethod
    def delete_photos(cls, photos_ids: list[int]):
        photos_ids = list(Photo.objects.filter(id__in=photos_ids).values_list('id', flat=True))
        remove_photos_from_buckets.delay(photos_ids)

    @classmethod
    def clear_user_data(cls, user: User, email_prefix: str = '') -> str:
        user.set_password(None)
        old_email = user.email
        user.email = hash_email(old_email, prefix=email_prefix)
        user.username = (
            f'deleted_user_{hash_field(user.username, max_length=137, prefix=email_prefix)}'
        )
        user.is_active = False
        user.deleted = tznow()
        user.first_name = f'deleted_user_{hash_field(user.first_name)}'
        user.last_name = f'deleted_user_{hash_field(user.last_name)}'
        user.birthday = None
        user.facebook_id = None
        user.google_id = None
        user.cell_phone = None
        user.home_phone = None
        user.work_phone = None
        user.apple_user_uuid = None
        user.authenticator_code = None
        user.save(
            update_fields=[
                'email',
                'username',
                'is_active',
                'deleted',
                'first_name',
                'last_name',
                'facebook_id',
                'google_id',
                'birthday',
                'cell_phone',
                'home_phone',
                'work_phone',
                'apple_user_uuid',
                'authenticator_code',
            ]
        )

        cls.clear_user_agreement(user)
        cls.clean_admin_log_entries_for_user(user)

        # required for removal from other services
        return old_email

    @classmethod
    def clear_user_agreement(cls, user: User):
        cls.update_user_agreement(user)

    @classmethod
    def clear_bcis(cls, bci_ids: list[int], context: dict):
        if not bci_ids:
            return
        # save photo ids in order to force lazy query to evaluate now
        photo_ids = list(
            Photo.objects.filter(businesscustomerinfo__id__in=bci_ids).values_list('pk', flat=True)
        )
        unlink_user = context.get('unlink_user', False)
        email_prefix = context.get('email_prefix', '')
        bci_fields = [
            'visible_in_business',
            'first_name',
            'last_name',
            'email',
            'cell_phone',
            'birthday',
            'address_line_1',
            'address_line_2',
            'photo_id',
            'city',
            'zipcode',
            'region_id',
            'tax_id',
            'web_communication_agreement',
            'processing_consent',
            'trusted',
            'allergens',
            'discount',
            'business_secret_note',
            'service_questions',
            'user_id',
        ]
        for bci in cls.get_bcis(bci_ids, bci_fields):
            cls._update_bci(bci, bci_fields, unlink_user, email_prefix)
            cls.clear_bci_history(bci.id)

        BusinessCustomerInfoTag.objects.filter(customer__in=bci_ids).delete()
        cls.delete_photos(photo_ids)
        bci_more_photos_ids = Photo.objects.filter(business_customers__in=bci_ids).values_list(
            'pk', flat=True
        )
        cls.delete_photos(bci_more_photos_ids)
        cls.delete_bci_files_for_bci_ids(bci_ids)
        cls.clear_patient_files_for_bci_ids(bci_ids)
        cls.clear_consent_personal_data_for_bci_ids(bci_ids, email_prefix)
        cls.clear_admin_log_entries_for_bci_ids(list(bci_ids))
        delete_documents_with_retries(BusinessCustomerInfo.es_doc_type, list(bci_ids))
        cls.delete_versum_agreements_data_for_bci_ids(bci_ids)

    @classmethod
    def _update_bci(
        cls,
        bci: BusinessCustomerInfo,
        bci_fields: list[str],
        unlink_user: bool = False,
        email_prefix: str = '',
    ):
        bci.visible_in_business = False
        bci.first_name = f'{hash_field(bci.first_name)}_deleted_user'
        bci.last_name = f'{hash_field(bci.last_name)}_deleted_user'
        bci.email = hash_email(bci.email, prefix=email_prefix)
        bci.cell_phone = None
        bci.birthday = None
        bci.address_line_1 = hash_field(bci.address_line_1)
        bci.address_line_2 = hash_field(bci.address_line_2)
        bci.photo = None
        bci.city = None
        bci.zipcode = None
        bci.region = None
        bci.tax_id = None
        bci.web_communication_agreement = False
        bci.processing_consent = False
        bci.trusted = False
        bci.allergens = ''
        bci.discount = 0
        bci.business_secret_note = ''
        bci.service_questions = []
        if unlink_user:
            bci.user = None
        cls.update_bci(bci, bci_fields)

    @classmethod
    def clear_patient_files_for_bci_ids(cls, bci_ids):
        patient_file_fields = [
            'file_number',
            'national_identity_number',
            'document_type',
            'document_number',
            'branch',
            'special_permissions_code',
        ]

        cls.update_patient_files(bci_ids, patient_file_fields)

    @classmethod
    def clear_consent_personal_data_for_bci_ids(cls, bci_ids: list[int], email_prefix: str = ''):
        for consent in cls.get_consents(bci_ids):
            cls.clear_consent(consent, {'email_prefix': email_prefix})

    @classmethod
    def clear_user_resources(cls, user: User):
        staffer_fields = [
            'name',
            'staff_email',
            'staff_cell_phone',
            'photo_id',
            'visible',
            'description',
            'active',
        ]
        for staffer in cls.get_staffer(user, staffer_fields):
            cls.update_staffer(user, staffer, staffer_fields)

    @classmethod
    def delete_bci_files_for_bci_ids(cls, bci_ids: list[int]):
        bci_files = cls.get_attached_files(bci_ids)
        for attached_file in bci_files:
            if attached_file.uploaded_file and os.path.isfile(attached_file.uploaded_file.path):
                os.remove(attached_file.uploaded_file.path)
            if attached_file.attached_file:
                attached_file.attached_file.delete()
        bci_files.delete()

    @classmethod
    def clear_user_transactions(cls, user: User):
        cls.update_transactions(user)

    @classmethod
    def clear_admin_log_entries_for_bci_ids(cls, bci_ids: list[int]):
        bci_content_type = ContentType.objects.get_for_model(BusinessCustomerInfo)
        bci_log_entries = LogEntry.objects.filter(
            content_type=bci_content_type, object_id__in=bci_ids
        )
        bci_log_entries.delete()

    @classmethod
    def delete_versum_agreements_data_for_bci_ids(cls, bci_ids: list[int]):
        BCIVersumAgreement.objects.filter(bci__in=bci_ids).delete()

    @classmethod
    def clear_bci_history(cls, bci_id: int):
        BusinessCustomerInfoHistory.objects.filter(model_id=bci_id).delete()

    @classmethod
    def clear_consent(cls, consent: Consent, context: dict):
        consent.customer_full_name = hash_field(consent.customer_full_name)
        consent.customer_email = hash_email(
            consent.customer_email, prefix=context.get('email_prefix', '')
        )
        consent.customer_cell_phone = ''
        consent.customer_address = hash_field(consent.customer_address)
        consent.save(
            override=True,
            update_fields=[
                'customer_full_name',
                'customer_email',
                'customer_cell_phone',
                'customer_address',
            ],
        )

    @classmethod
    def update_user_internal_data(
        cls,
        user: User,
        method: AccountDeletionExecutionMethod = AccountDeletionExecutionMethod.ADMIN,
    ):
        '''We want to mark that user data are actually removed with full deletion procedure'''
        internal_data: UserInternalData = UserInternalData.objects.get_or_create(user_id=user.id)[0]
        internal_data.account_deletion_execution_datetime = tznow()
        internal_data.account_deletion_execution_method = method
        internal_data.save(
            update_fields=[
                'account_deletion_execution_datetime',
                'account_deletion_execution_method',
            ]
        )

    @staticmethod
    def get_user(user):
        return (
            User.objects.select_for_update()
            .only(
                'id',
                'password',
                'username',
                'first_name',
                'last_name',
                'email',
                'is_active',
                'deleted',
                'facebook_id',
                'google_id',
                'apple_user_uuid',
                'cell_phone',
                'home_phone',
                'work_phone',
                'birthday',
                'authenticator_code',
            )
            .get(id=user.id)
        )

    @staticmethod
    def get_user_profile(user: User, profile_fields: list[str]):
        return list(user.profiles.select_for_update().only(*profile_fields).all())

    @staticmethod
    def get_bcis(bci_ids: list[int], bci_fields: list[str]):
        return list(
            BusinessCustomerInfo.objects.filter(id__in=bci_ids)
            .select_for_update()
            .only(*bci_fields)
            .all()
        )

    @staticmethod
    def get_appointments(bci_ids: list[int]):
        return list(
            Appointment.objects.filter(booked_for__id__in=bci_ids).select_for_update().all()
        )

    @staticmethod
    def get_traveling_appointments(bci_ids: list[int]):
        return list(
            AppointmentTraveling.objects.filter(appointments__booked_for__id__in=bci_ids)
            .select_for_update()
            .all()
        )

    @staticmethod
    def get_consents(bci_ids: list[int]):
        return (
            Consent.objects.filter(customer__id__in=bci_ids)
            .select_for_update()
            .only(
                'customer_full_name',
                'customer_email',
                'customer_cell_phone',
                'customer_address',
            )
            .all()
        )

    @staticmethod
    def get_attached_files(bci_ids: list[int]):
        return BCIAttachedFile.objects.filter(bci__in=bci_ids).select_for_update().all()

    @staticmethod
    def get_staffer(user: User, staffer_fields: list[str]):
        return list(user.staffers.select_for_update().only(*staffer_fields).all())

    @staticmethod
    def update_user_profile(profile: UserProfile, profile_fields: list[str]):
        profile.about_me = None
        profile.photo_id = None
        profile.address_line_1 = hash_field(profile.address_line_1)
        profile.address_line_2 = hash_field(profile.address_line_2)
        profile.longitude = None
        profile.latitude = None
        profile.region_id = None
        profile.apartment_number = ''
        profile.city = None
        profile.zipcode = None
        profile.language = settings.LANGUAGE_CODE[:2]
        profile.save(update_fields=profile_fields)

    @staticmethod
    def update_bci(bci: BusinessCustomerInfo, bci_fields: list[str]):
        bci.save(update_fields=bci_fields)

    @staticmethod
    def update_patient_files(bci_ids: list[int], patient_file_fields: list[str]):
        files = (
            BCIPatientFile.objects.filter(bci__in=bci_ids)
            .select_for_update()
            .only(*patient_file_fields)
            .all()
        )
        for patient_file in files:
            patient_file.file_number = None
            patient_file.national_identity_number = None
            patient_file.document_type = None
            patient_file.document_number = None
            patient_file.branch = None
            patient_file.special_permissions_code = None
            patient_file.save(update_fields=patient_file_fields)

    @staticmethod
    def update_transactions(user: User):
        transactions = list(user.transactions.only('customer_data').select_for_update().all())
        for transaction_ in transactions:
            transaction_.customer_data = ''
            transaction_.save(update_fields=['customer_data'])

    @staticmethod
    def update_staffer(user: User, staffer: Resource, staffer_fields: list[str]):
        staffer.name = f'{"deleted_user"}_{user.id}'
        staffer.staff_email = hash_email(staffer.staff_email, max_length=75)
        staffer.staff_cell_phone = hash_field(staffer.staff_cell_phone, max_length=50)
        staffer.photo = None
        staffer.visible = False
        staffer.description = hash_field(staffer.description)
        staffer.active = False
        staffer.save(update_fields=staffer_fields)

    @staticmethod
    def update_user_agreement(user: User):
        if agreement := UserPolicyAgreement.objects.filter(user=user).select_for_update().first():
            agreement.privacy_policy_agreement = False
            agreement.marketing_agreement = False
            agreement.partner_marketing_agreement = False
            agreement.save(
                update_fields=[
                    'privacy_policy_agreement',
                    'marketing_agreement',
                    'partner_marketing_agreement',
                ]
            )

    def clear_user_appointments(
        self, user: User, user_email_not_hashed: str | None, email_prefix: str = ''
    ):
        bci_ids = user.business_customer_infos.values_list('id', flat=True)
        appointments = self.get_appointments(bci_ids)
        clear_user_booking_changes(bci_ids, user_email_not_hashed)
        for appointment_traveling in self.get_traveling_appointments(bci_ids):
            appointment_traveling.address_line_1 = hash_field(appointment_traveling.address_line_1)
            appointment_traveling.address_line_2 = hash_field(appointment_traveling.address_line_2)
            appointment_traveling.apartment_number = ""
            appointment_traveling.city = ""
            appointment_traveling.zipcode = ""
            appointment_traveling.latitude = None
            appointment_traveling.longitude = None
            appointment_traveling.save()

        for appointment in appointments:
            if (
                appointment.status != Appointment.STATUS.CANCELED
                and appointment.booked_from >= tznow()
            ):
                cancel_appointment(appointment, user)
            hashed_customer_name = hash_field(appointment.customer_name)
            Appointment.objects.filter(id=appointment.id).update(
                customer_name=(
                    f'{hashed_customer_name}_deleted_user' if hashed_customer_name else None
                ),
                customer_email=hash_email(
                    appointment.customer_email, max_length=75, prefix=email_prefix
                ),
                customer_phone=None,
            )


# pylint: enable=too-many-public-methods


def choose_delete_user_strategy() -> AbstractDeleteUserStrategy:
    return SelectForUpdateDeleteUserStrategy()


def delete_user(
    user: User,
    delete_business_user: bool = True,
    method: AccountDeletionExecutionMethod = AccountDeletionExecutionMethod.ADMIN,
) -> tuple[bool, str]:
    """Method will not remove object from db but anonymize sensitive
    data and reset password."""
    return DeleteUser(choose_delete_user_strategy(), user, delete_business_user, method).call()


# this function duplicates behavior of
# service.booking.booking_actions.OldCustomerAppointmentActionsHandler.post
# for action=cancel
def cancel_appointment(appointment, user):
    if not appointment.is_active() or any(
        subbooking.has_ended() for subbooking in appointment.subbookings
    ):
        log_action.warning(
            'Cannot cancel appointment %s, appointment not active', str(appointment.id)
        )
        return
    previous_status = appointment.status
    if appointment.repeating:
        appointment.repeating.update_repeating_booking(
            prototype=appointment,
            updated_by_id=user.id,
            status=Appointment.STATUS.CANCELED,
            who_makes_change=WhoMakesChange.CUSTOMER,
            update_future=True,
        )
    else:
        appointment.update_appointment(
            updated_by_id=user.id,
            status=Appointment.STATUS.CANCELED,
            who_makes_change=WhoMakesChange.CUSTOMER,
        )
    appointment = AppointmentWrapper.get_appointment(
        appointment_type=AT.MULTI,
        appointment_id=appointment.id,
        business_id=appointment.business_id,
    )
    if appointment.business.pos and (
        (appointment.booked_from - appointment.business.pos.deposit_cancel_time) > tznow()
    ):
        ReleaseDepositOnCancel.run(
            appointment_id=appointment.appointment_uid,
        )
    appointment.send_signal()
    log_action.info(
        'CustomerAppointment action. Type %(type)s. User %(user)s. Action: %(action)s.',
        {
            'type': AT.MULTI,
            'user': user.id,
            'action': BookingAction.CANCEL,
        },
    )
    BookingChange.add(
        appointment,
        changed_by=BookingChange.BY_CUSTOMER,
        changed_user=user,
        metadata={
            'action': BookingAction.CANCEL,
        },
    )
    if tznow() + timedelta(hours=1) > appointment.booked_from:
        create_late_cancellation_notifications_task.run(appointment)
    appointment_status_changed_by_customer_event.send(
        appointment.appointment,
        status=appointment.status,
        previous_status=previous_status,
    )
    # # disable till iOS app available https://pm.booksy.net/issues/52764
    CustomerAppointmentServicePromotionMixin.push_last_minute_incentive(
        appointment=appointment.appointment
    )

    start_scenario(
        BookingChangedScenario,
        appointment=appointment,
        action=BookingChangedScenario.CUSTOMER_CANCEL,
    )

    if appointment.is_paid_by_booksy_pay:
        CheckoutBooksyPayTransaction.delay(
            appointment_id=appointment.appointment_uid,
            customer_user_id=user.id,
        )
    else:
        CheckoutPrepaidTransaction.delay(
            appointment_id=appointment.appointment_uid,
            customer_user_id=user.id,
        )

    if appointment.type == Appointment.TYPE.CUSTOMER:
        update_any_mobile_customer_appointments_task.delay(
            customer_id=appointment.booked_for_id,
            skip_if_true=False,
        )


def clear_user_booking_changes(bci_ids, user_email: str):
    booking_changes = BookingChange.objects.filter(customer_id__in=bci_ids)
    for booking_change in booking_changes:
        if booking_change.changed_user_email in {booking_change.customer_email, user_email}:
            booking_change.changed_user_email = hash_email(booking_change.changed_user_email)
        booking_change.customer_email = hash_email(booking_change.customer_email)
        booking_change.customer_phone = ''
        name_prefix = 'deleted_user_'
        max_name_length = consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN - len(name_prefix)
        booking_change.customer_name = name_prefix + (
            hash_field(booking_change.customer_name, max_length=max_name_length)
        )
        booking_change.save(
            update_fields=[
                'customer_name',
                'customer_email',
                'customer_phone',
                'changed_user_email',
            ]
        )


def clear_business_data(business_id: int):
    """Remove personal data from BCIs and appointments for a selected business."""
    bci_ids = BusinessCustomerInfo.objects.filter(business_id=business_id).values_list(
        'id', flat=True
    )
    # user is not cleared, just the bci, we need to disconnect user from bci
    choose_delete_user_strategy().clear_bcis(bci_ids, {'unlink_user': True})

    appointment_ids = (
        Appointment.objects.filter(business_id=business_id).values_list('id', flat=True).iterator()
    )
    clear_appointments_data(appointment_ids)

    pos = POS.objects.filter(business_id=business_id).first()
    if pos:
        transaction_ids = Transaction.objects.filter(pos=pos).values_list('id', flat=True)
        clear_transactions_data(transaction_ids)


def clear_transactions_data(transaction_ids: list[int]):
    Transaction.objects.filter(id__in=transaction_ids).update(
        customer_data='',
    )


def clear_appointments_data(appointment_ids: list[int], chunk_size: int = 500):
    for chunk in chunker(appointment_ids, chunk_size):
        Appointment.objects.filter(id__in=chunk).update(
            customer_name=None,
            customer_email=None,
            customer_phone=None,
        )


def mark_user_with_deletion_request(user: User, deletion_request_source: BookingSources = None):
    internal_data: UserInternalData = UserInternalData.objects.get_or_create(user_id=user.id)[0]
    if not internal_data.account_deletion_requested:
        internal_data.account_deletion_requested = True
        internal_data.account_deletion_requested_datetime = tznow()
        internal_data.deletion_request_source = deletion_request_source
        internal_data.save(
            update_fields=[
                'account_deletion_requested',
                'account_deletion_requested_datetime',
                'deletion_request_source',
            ],
        )
        if AutomaticCxAccountDeletion():
            send_emails_about_user_deletion_task.delay(user.id)
