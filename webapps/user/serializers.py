#!/usr/bin/env python
import base64
import logging
import re
from io import BytesIO

from PIL import Image
from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.core.validators import <PERSON>V<PERSON>ueVali<PERSON><PERSON>, MinValueValidator
from django.db import transaction
from django.utils.translation import gettext as _, gettext_lazy
from phonenumbers import PhoneNumber
from rest_framework import serializers
from rest_framework.fields import empty

from lib.booksy_sms import parse_phone_number
from lib.feature_flag.feature.security import SecurityBlacklistFlag
from lib.fields.date_field import AdultAgeField
from lib.fields.name_field import NameField
from lib.fields.password import BooksyPasswordSerializerField
from lib.fields.phone_number import BooksyPhoneSerial<PERSON><PERSON>ield, to_python
from lib.gdpr_descriptions import (
    BUSINESS_CUSTOMER_AGREEMENTS,
    CUSTOMER_AGREEMENTS,
)
from lib.serializers import RequiredContextMixin
from lib.email_internal import generate_private_email
from lib.email_internal import is_private_email
from lib.feature_flag.feature import CustomerQuickSignInUpFlag
from lib.tools import tznow
from lib.utils import is_gdpr_country
from webapps.notification.models import NotificationSMSCodes
from webapps.photo.models import Photo
from webapps.photo.serializers import PhotoSerializer
from webapps.registrationcode.models import RegistrationCode
from webapps.security_blacklist.enums import BlacklistType
from webapps.security_blacklist.validators import BlacklistValidator
from webapps.session.utils import get_user
from webapps.structure.models import Region
from webapps.structure.serializers import RegionField
from webapps.user.const import FIXED_DEV_SMS_CODE
from webapps.user.const import FORCED_LAST_NAME
from webapps.user.models import AppleIdentity, GENDERS, User, UserProfile
from webapps.user.tasks.apple import authorize_apple_user_task

log = logging.getLogger('booksy.photos-s3')


def _generate_unusable_password():
    return make_password(None)


def _check_name_blank(value, attribute, instance=None) -> str:
    if not value and instance is None:
        return value or ''
    existing_value = getattr(instance, attribute, '')
    if existing_value and not value:
        # value wasn't blank raise
        raise serializers.ValidationError(
            code='blank',
            detail=_('This field may not be blank.'),
        )
    # always return str not None
    return value or ''


class ReviewResponseCustomerSerializer(serializers.ModelSerializer):
    first_name = serializers.CharField(source='get_first_name_if_available', read_only=True)
    last_name = serializers.CharField(source='get_truncated_last_name', read_only=True)

    class Meta:
        model = User
        fields = (
            'id',
            'first_name',
            'last_name',
            # 'email' - not sent, because it is security risk
        )

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        # this was also changing business reviews, so is disabled for now
        # if ret.get('last_name'):
        #     ret['last_name'] = ret['last_name'].strip()[:1]

        try:
            profile = instance.profiles.get(profile_type=UserProfile.Type.CUSTOMER)
            if profile.photo:
                ret['avatar'] = profile.photo.full_url
        except UserProfile.DoesNotExist:
            pass

        return ret


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = (
            'address_line_1',
            'address_line_2',
            'about_me',
            'first_name',
            'last_name',
            'full_name',
            'email',
            'cell_phone',
            'photo',
            'region',
            'birthday',
            'bithday_date_int',
        )

    first_name = serializers.ReadOnlyField(source='user.first_name')
    last_name = serializers.ReadOnlyField(source='user.last_name')
    full_name = serializers.ReadOnlyField(source='user.full_name')
    email = serializers.ReadOnlyField(source='user.email')
    cell_phone = serializers.ReadOnlyField(source='user.cell_phone')
    birthday = serializers.DateField(format=settings.DATE_FORMAT, source='user.birthday')

    photo = PhotoSerializer()
    region = RegionField()
    bithday_date_int = serializers.SerializerMethodField()

    @staticmethod
    def get_bithday_date_int(instance):
        from lib.elasticsearch.tools import date_to_int

        return date_to_int(instance.user.birthday) if instance.user else None


class CustomerUserProfileSerializer(UserProfileSerializer):
    class Meta:
        model = UserProfile
        fields = (
            'address_line_1',
            'address_line_2',
            'city',
            'zipcode',
            'about_me',
            'first_name',
            'last_name',
            'full_name',
            'email',
            'cell_phone',
            'photo',
            'region',
            'birthday',
            'bithday_date_int',
            'privacy_policy_agreement',
            'marketing_agreement',
        )

    # GDPR
    privacy_policy_agreement = serializers.SerializerMethodField()
    marketing_agreement = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()

    @staticmethod
    def get_privacy_policy_agreement(instance):
        if not instance.user.agreement_exist:
            return None
        return instance.user.agreement.privacy_policy_agreement

    @staticmethod
    def get_marketing_agreement(instance):
        if not instance.user.agreement_exist:
            return None
        return instance.user.agreement.marketing_agreement

    @staticmethod
    def get_email(instance):
        return '' if is_private_email(instance.user.email) else instance.user.email


class PhoneNumberField(serializers.CharField):
    def to_internal_value(self, data):
        super_result = super().to_internal_value(data)
        return to_python(super_result)

    def __init__(self, **kwargs):
        if not kwargs.get('max_length'):
            kwargs['max_length'] = 50
        super().__init__(**kwargs)


class GenderField(serializers.CharField):
    def to_internal_value(self, data):
        if data and isinstance(data, (bytes, str)):
            first_letter = data[0].upper()
            if first_letter in list(dict(GENDERS).keys()):
                data = first_letter
        return super().to_internal_value(data)


class FacebookIdField(serializers.CharField):
    def to_internal_value(self, data):
        if not data:  # for backward compatibility
            return None
        return super().to_internal_value(data)


class BirthdayField(serializers.DateField):
    def to_internal_value(self, value):
        if not value:  # for backward compatibility
            return None
        return super().to_internal_value(value)


class GDPRUserAgreementListSerializer(serializers.ListSerializer):
    def validate(self, attrs):
        validated_data = super().validate(attrs)
        # reformat to acceptable dict
        response = {
            agreement['name']: agreement.get('value', False) for agreement in validated_data
        }
        # check if all required is send
        required_agreements = settings.REQUIRED_CUSTOMER_AGREEMENTS
        for agreement_name, value in required_agreements.items():
            # not required agreement
            if not value:
                continue
            # client should send True value for this agreement
            if not response.get(agreement_name, False):
                raise serializers.ValidationError(f'Agreement {agreement_name} is required')

        return response


class GDPRUserAgreementSerializer(serializers.Serializer):
    class Meta:
        list_serializer_class = GDPRUserAgreementListSerializer

    name = serializers.ChoiceField(choices=list(CUSTOMER_AGREEMENTS.keys()))
    value = serializers.BooleanField()

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        agreement_key = validated_data['name']
        must_be_true = settings.REQUIRED_CUSTOMER_AGREEMENTS[agreement_key]
        if must_be_true and validated_data['value'] is False:
            # someone send Falsy value on agreement that required to be True
            raise serializers.ValidationError(f'Value {agreement_key} can not be false')

        return validated_data


class BaseUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            'first_name',
            'last_name',
            'email',
        )

    first_name = NameField(
        error_messages={'invalid': _('Invalid first name.')},
    )
    last_name = NameField(
        error_messages={'invalid': _('Invalid last name.')},
        allow_blank=True,
    )
    email = serializers.EmailField(required=True)
    should_validate_is_private_email = False

    def validate_email(self, email):
        if not email:
            return
        if SecurityBlacklistFlag():
            BlacklistValidator.validate(email, BlacklistType.EMAIL)

        email = email.lower()
        email_qs = User.objects.filter(email=email)
        if self.instance and self.instance.id:
            email_qs = email_qs.exclude(id=self.instance.id)
        if email_qs.exists():
            raise serializers.ValidationError(
                _('User with provided email already exists.'),
                code='entity_exists',
            )
        if CustomerQuickSignInUpFlag():
            self.validate_is_private_email(email)
        return email

    def validate_is_private_email(self, email):
        if self.should_validate_is_private_email and is_private_email(email):
            raise serializers.ValidationError(
                _('Not allowed email.'),
                code='not_allowed',
            )

    def _consume_sms_code(self) -> None:
        sms_code = self.initial_data.get('sms_code')
        if not sms_code:
            return
        cell_phone = parse_phone_number(self.validated_data.get('cell_phone'))
        NotificationSMSCodes.objects.filter(
            phone=cell_phone.db_format,
            sms_code=sms_code,
        ).update(consumed=tznow())

    def save(self, **kwargs):
        obj = super().save(**kwargs)
        self._consume_sms_code()
        return obj


class UserPasswordSerializer(serializers.ModelSerializer):
    password = BooksyPasswordSerializerField()
    password_change_required = serializers.BooleanField(required=False, default=False)

    class Meta:
        model = User
        fields = ('password', 'password_change_required')


class BaseCustomerCreateSerializer(BaseUserSerializer):
    password = BooksyPasswordSerializerField()
    work_phone = PhoneNumberField(default='', allow_blank=True, required=False)
    home_phone = PhoneNumberField(default='', allow_blank=True, required=False)
    cell_phone = PhoneNumberField(default='', allow_blank=True, required=False)
    gender = GenderField(allow_blank=True, allow_null=True, required=False)
    facebook_id = FacebookIdField(allow_blank=True, allow_null=True, required=False)
    birthday = BirthdayField(required=False, allow_null=True)
    registration_code = serializers.CharField(
        required=False,
        allow_blank=True,
        allow_null=True,
        max_length=30,
    )
    sms_code = serializers.CharField(
        required=False,
        allow_blank=True,
        allow_null=True,
        max_length=30,
    )

    photo = None
    profile_type = None
    language = None
    booking_source = None

    class Meta:
        model = User
        exclude = (
            'date_joined',
            'is_active',
            'is_staff',
            'is_superuser',
            'superuser',
            'last_login',
            'password_change_required',
            'username',
        )

    def validate_facebook_id(self, value):
        if not value:  # for backward compatibility
            return None
        qs_facebook_account = User.objects.filter(facebook_id=value)
        if self.instance:
            qs_facebook_account = qs_facebook_account.exclude(
                id=self.instance.id,
            )
        if qs_facebook_account.exists():
            raise serializers.ValidationError(
                _('Your Facebook account is already registered'),
            )
        return value

    @staticmethod
    def validate_gender(data):
        if not data:  # for backward compatibility
            return None
        return data

    @staticmethod
    def validate_photo(photo):
        """Validation for legacy way of photo upload.

        Currently a separate handler is used for photo upload.
        We support legacy apps by updating a photo, but it would
        silently fail if there is an error during this process.

        """
        if not photo:
            return ''
        try:
            img = Image.open(BytesIO(base64.b64decode(photo)))
            img.verify()
            return photo
        except Exception:  # pylint: disable=broad-except
            pass
        return ''

    @staticmethod
    def validate_registration_code(registration_code):
        if not registration_code:
            return registration_code
        reg_code = RegistrationCode.match_code(registration_code)
        if reg_code is None:
            raise serializers.ValidationError(
                _('Provided registration code does not exist.'),
            )
        return reg_code

    @staticmethod
    def validate_cell_phone(value):
        if SecurityBlacklistFlag():
            BlacklistValidator.validate(value, BlacklistType.PHONE)
        return value

    def validate(self, attrs):
        result = super().validate(attrs)
        self._extra_sms_code_validation(result)
        return result

    def _extra_sms_code_validation(self, attrs):
        sms_code = attrs.pop('sms_code', None)
        if self.should_skip_sms_code_validation:
            return

        if not settings.LIVE_DEPLOYMENT and sms_code == FIXED_DEV_SMS_CODE:
            return  # workaround for automatic tests

        cell_phone = parse_phone_number(attrs.get('cell_phone'))
        if (
            self.instance
            and parse_phone_number(self.instance.cell_phone).db_format == cell_phone.db_format
        ):
            return

        return self._validate_sms_code(sms_code, cell_phone)

    def _validate_sms_code(self, sms_code, cell_phone: PhoneNumber) -> None:
        if not sms_code:
            raise serializers.ValidationError(
                {
                    'sms_code': _("Incorrect verification code"),
                }
            )

        if not cell_phone.is_valid:
            raise serializers.ValidationError(
                {
                    'cell_phone': _("Invalid phone number"),
                }
            )

        if sms_code == settings.INTERNAL_SMS_CODE and (
            settings.INTEGRATION_TESTS or self.context['booking_source'].is_performance_test
        ):
            return None

        if not NotificationSMSCodes.is_valid(
            cell_phone.db_format,
            sms_code,
            settings.SMS_REGISTRATION_PERIOD,
        ):
            raise serializers.ValidationError(
                {
                    'sms_code': _("Incorrect verification code"),
                }
            )

    def run_validation(self, data=empty):
        if data != empty and self.instance:
            profile = self.instance.profiles.filter(profile_type=self.context.get('profile_type'))[
                0
            ]
            updated_data = profile.format_account()
            updated_data.update(data)
            data = updated_data
        return super().run_validation(data=data)

    def save(self, **kwargs):
        email = self.validated_data['email']
        self.validated_data['username'] = User.generate_username(email)
        self.validated_data.pop('registration_code', None)
        user = super().save(**kwargs)
        return user

    def update(self, instance, validated_data):

        profile = instance.profiles.filter(profile_type=self.context['profile_type'])[0]
        profile_data = profile.format_account()
        photo = self.validate_photo(self.context.get('photo'))
        if not photo and 'photo' in profile_data:
            photo = None
        if photo:
            # AVOID THIS: synchronous way of sending file to s3
            profile.photo = Photo.save_from_base64(photo)

        self.user_changes_email = False
        if CustomerQuickSignInUpFlag() and is_private_email(validated_data['email']):
            self.user_changes_email = False
        elif validated_data['email'] != instance.email:
            self.user_changes_email = True

            if User.objects.filter(email=validated_data['email']).exists():
                raise serializers.ValidationError(_("Email already registered"))

            # don't save user with this new email
            validated_data['email'] = instance.email

        for field in [
            'about_me',
            'address_line_1',
            'address_line_2',
            'city',
            'zipcode',
            'region',
            'latitude',
            'longitude',
            'apartment_number',
        ]:
            if field in validated_data:
                setattr(profile, field, validated_data[field])

        profile.save()

        if profile.photo_id:
            profile.migrate_photo_to_s3()
        return super().update(instance, validated_data)

    def create(self, validated_data):
        photo = self.validate_photo(self.context.get('photo'))
        language = self.context['language']
        user = super().create(validated_data)
        profile = UserProfile(
            user=user,
            profile_type=self.context['profile_type'],
            language=language,
            source=self.context.get('booking_source'),
        )
        # <editor-fold desc="TODO photo base64 delete when frontend migrated">
        if photo and self.context.get('process_photo'):
            # AVOID THIS: synchronous way of sending file to s3
            profile.photo = Photo.save_from_base64(photo)

        profile.save()
        if profile.photo_id:
            profile.migrate_photo_to_s3()
        # </editor-fold>
        return user

    @property
    # pylint: disable=too-many-return-statements
    def should_skip_sms_code_validation(self):
        if not settings.SMS_REGISTRATION_CODE_REQUIRED:
            return True
        if self.context.get('skip_sms_code_validation'):
            return True
        serializer_class_name = self.__class__.__name__.lower()
        # when android switches to v2 then whole if statement is to be removed
        if ('customerfacebook' in serializer_class_name) and (
            booking_source := self.context.get('booking_source')
        ):
            return 'Android' in booking_source.name

        return 'business' in serializer_class_name


class CustomerCreateSerializer(BaseCustomerCreateSerializer):
    class Meta(BaseCustomerCreateSerializer.Meta):
        exclude = BaseCustomerCreateSerializer.Meta.exclude

    should_validate_is_private_email = True

    user_agreements = GDPRUserAgreementSerializer(
        required=False,
        allow_null=True,
        many=True,
    )
    birthday = AdultAgeField(required=False, allow_null=True)

    def get_fields(self):
        fields = super().get_fields()
        gdpr_enabled = settings.GDPR_COUNTRIES.get(
            settings.API_COUNTRY, settings.GDPR_COUNTRIES.get('default', False)
        )
        if not gdpr_enabled:
            fields.pop('user_agreements', None)
        return fields

    def save(self, **kwargs):
        gdpr_agreements = self.validated_data.pop(
            'user_agreements',
            None,
        )
        instance = super().save(**kwargs)
        if gdpr_agreements:
            instance.create_or_update_gdpr_agreements(
                gdpr_agreements,
                created=True,
            )
        return instance

    def update(self, instance, validated_data):
        gdpr_agreements = validated_data.pop(
            'user_agreements',
            None,
        )
        instance = super().update(instance, validated_data)
        if gdpr_agreements:
            instance.create_or_update_gdpr_agreements(gdpr_agreements)
        return instance


class CustomerFacebookAccountCreateSerializer(BaseCustomerCreateSerializer):
    password = BooksyPasswordSerializerField(required=False)


class BusinessAccountCreateSerializer(BaseCustomerCreateSerializer):
    facebook_id = None
    gender = None
    birthday = None
    first_name = NameField(error_messages={'invalid': _('Invalid first name.')}, allow_blank=True)

    class Meta(BaseCustomerCreateSerializer.Meta):
        exclude = BaseCustomerCreateSerializer.Meta.exclude + (
            'gender',
            'facebook_id',
            'birthday',
        )


class BusinessFacebookAccountCreateSerializer(BaseCustomerCreateSerializer):
    gender = None
    password = BooksyPasswordSerializerField(required=False)

    class Meta(BaseCustomerCreateSerializer.Meta):
        exclude = BaseCustomerCreateSerializer.Meta.exclude + ('gender',)


class CustomerAccountSerializer(BaseCustomerCreateSerializer):

    default_error_messages = {
        'invalid_zipcode': gettext_lazy("Invalid zip code."),
    }

    # overwrite base class attribute

    first_name = NameField(
        error_messages={'invalid': _('Invalid first name.')},
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=30,
    )
    last_name = NameField(
        error_messages={'invalid': _('Invalid last name.')},
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=30,
    )
    cell_phone = PhoneNumberField(required=True)
    about_me = serializers.CharField(
        required=False,
        max_length=250,
        allow_blank=True,
        allow_null=True,
    )
    address_line_1 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    address_line_2 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    apartment_number = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    city = serializers.CharField(
        required=False,
        allow_blank=True,
        allow_null=True,
    )
    zipcode = serializers.CharField(
        required=False,
        allow_blank=True,
        allow_null=True,
    )
    latitude = serializers.FloatField(required=False, allow_null=True)
    longitude = serializers.FloatField(required=False, allow_null=True)
    birthday = AdultAgeField(required=False, allow_null=True)

    # exclude from parent class
    password = None

    class Meta(BaseCustomerCreateSerializer.Meta):
        exclude = BaseCustomerCreateSerializer.Meta.exclude + ('password',)

    def validate_first_name(self, value):
        return _check_name_blank(value, 'first_name', self.instance)

    def validate_last_name(self, value):
        return _check_name_blank(value, 'last_name', self.instance)

    @staticmethod
    def validate_city(value):
        return value or ''

    @staticmethod
    def validate_zipcode(value):
        return (value or '').upper()

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        zipcode = validated_data.get('zipcode')
        if zipcode:
            zipcode_region = Region.objects.find_zipcode(zipcode).first()
            zipcode_regexp = settings.COUNTRY_CONFIG.zipcode_regexp

            if zipcode_region is None and (
                not zipcode_regexp or not re.match(zipcode_regexp, zipcode, re.IGNORECASE)
            ):
                raise serializers.ValidationError(
                    {
                        'zipcode': self.error_messages['invalid_zipcode'],
                    },
                    code='invalid',
                )

            if zipcode_region is not None:
                zipcode = zipcode_region.name

            validated_data['zipcode'] = zipcode
            validated_data['region'] = zipcode_region
        else:
            validated_data['region'] = None

        if not CustomerQuickSignInUpFlag():
            return validated_data

        cell_phone = validated_data.get('cell_phone')
        email = validated_data.get('email')
        if self.instance and cell_phone != self.instance.cell_phone:
            if is_private_email(self.instance.email) and (not email or is_private_email(email)):
                # private email depends on cell_phone
                new_email = generate_private_email(cell_phone)
                if User.objects.filter(email=new_email).exists():
                    raise serializers.ValidationError(
                        _('User with provided phone already exists.'),
                        code='entity_exists',
                    )

                validated_data['email'] = new_email

        return validated_data


class BusinessAccountSerializer(BaseCustomerCreateSerializer):
    facebook_id = None
    password = None
    first_name = NameField(error_messages={'invalid': _('Invalid first name.')}, allow_blank=True)

    class Meta(BaseCustomerCreateSerializer.Meta):
        exclude = BaseCustomerCreateSerializer.Meta.exclude + (
            'facebook_id',
            'password',
        )

    def to_internal_value(self, data):
        self.skip_validation_of_forced_last_name(data)
        return super().to_internal_value(data)

    def skip_validation_of_forced_last_name(
        self,
        data: dict,
    ) -> None:
        if data.get('last_name') == FORCED_LAST_NAME:
            data.pop('last_name')
            self.fields['last_name'].required = False


class UserAgreementsGetSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('user_agreements',)

    user_agreements = serializers.SerializerMethodField()

    @staticmethod
    def get_user_agreements(instance):
        """Return list of agreement dicts of current user:
        Present agreements:
            - 'privacy_policy_agreement',
            - 'marketing_agreement',
        :param instance: User
        :return: list
        """
        result = []
        for key_agreement, agreement in CUSTOMER_AGREEMENTS.items():
            if agreement.is_blacklisted:
                continue
            value = False
            if instance and instance.agreement_exist and agreement.information is False:
                value = getattr(instance.agreement, key_agreement)

            result.append(
                {
                    'name': key_agreement,
                    'required': settings.REQUIRED_CUSTOMER_AGREEMENTS.get(key_agreement),
                    'title': agreement.get_title(),
                    'description': agreement.get_description(),
                    'value': value,
                    'information': agreement.information,
                }
            )
        return result


def get_default_business_customer_agreements(business_name=None, business_address=None):
    result = []
    for key_agreement, agreement in BUSINESS_CUSTOMER_AGREEMENTS.items():
        if not agreement.information:
            result.append(
                {
                    'name': key_agreement,
                    'required': settings.REQUIRED_BUSINESS_CUSTOMER_AGREEMENTS.get(key_agreement),
                    'title': agreement.get_title(business_name=business_name),
                    'description': agreement.get_description(
                        business_name=business_name,
                        business_address=business_address,
                    ),
                    'value': False,
                    'information': agreement.information,
                }
            )
    return result


class CustomerAgreementSerializer(serializers.Serializer):
    name = serializers.ChoiceField(list(BUSINESS_CUSTOMER_AGREEMENTS.keys()))
    required = serializers.BooleanField()
    title = serializers.CharField()
    description = serializers.CharField()
    value = serializers.BooleanField(default=False)
    information = serializers.BooleanField()


class CustomerAgreementsSerializer(serializers.Serializer):
    user_agreements = CustomerAgreementSerializer(many=True)
    doc_url = serializers.URLField()


class UserAgreementsUpdateSerializer(serializers.Serializer):
    user_agreements = GDPRUserAgreementSerializer(
        many=True,
        required=True,
        allow_empty=False,
    )

    def create(self, validated_data):
        user = User.objects.filter(id=self.context['user_id']).only('id').first()
        user.create_or_update_gdpr_agreements(validated_data['user_agreements'])
        return validated_data


##################################
### GDPR CUSTOMER DATA EXPORT ###
##################################


class CustomerDataExportSerializer(serializers.ModelSerializer):
    """Used to export customers' data for their request.
    Field names will become headings in the report sent to the customer,
    ex. "first_name" -> "First Name" """

    # auth_models.User data
    first_name = serializers.ReadOnlyField(source='user.first_name')
    last_name = serializers.ReadOnlyField(source='user.last_name')
    email = serializers.ReadOnlyField(source='user.email')
    date_joined = serializers.DateTimeField(source='user.date_joined')

    # webapps.user.User data
    facebook_id = serializers.ReadOnlyField(source='user.facebook_id')
    cell_phone = serializers.ReadOnlyField(source='user.cell_phone')
    home_phone = serializers.ReadOnlyField(source='user.home_phone')
    work_phone = serializers.ReadOnlyField(source='user.work_phone')
    gender = serializers.ReadOnlyField(source='user.get_gender_display')
    birthday = serializers.DateField(source='user.birthday')

    # webapps.user.UserProfile data
    photo_url = serializers.ReadOnlyField(source='photo.full_url')
    region = serializers.ReadOnlyField(source='region.name')
    language = serializers.ReadOnlyField(source='get_language_display')
    source = serializers.ReadOnlyField(source='get_source_display')

    # GDPR
    privacy_policy_agreement = serializers.SerializerMethodField()
    marketing_agreement = serializers.SerializerMethodField()

    @staticmethod
    def get_privacy_policy_agreement(instance):
        if not instance.user.agreement_exist:
            return None
        return instance.user.agreement.privacy_policy_agreement

    @staticmethod
    def get_marketing_agreement(instance):
        if not instance.user.agreement_exist:
            return None
        return instance.user.agreement.marketing_agreement

    class Meta:
        model = UserProfile
        fields = (
            'first_name',
            'last_name',
            'email',
            'date_joined',
            'facebook_id',
            'cell_phone',
            'home_phone',
            'work_phone',
            'gender',
            'birthday',
            'photo_url',
            'region',
            'language',
            'source',
            'about_me',
            'address_line_1',
            'address_line_2',
            'latitude',
            'longitude',
            'privacy_policy_agreement',
            'marketing_agreement',
        )


##################################
### GDPR BUSINESS DATA EXPORT ###
##################################


class BusinessOwnerDataExportSerializer(CustomerDataExportSerializer):
    """Used to export businesses' data for their owners' request.
    Field names will become headings in the report sent to the business owner,
    ex. "first_name" -> "First Name" """


class BooksyAuthUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            'country_user_id',
            'birthday',
            'booking_score',
            'cell_phone',
            'email',
            'facebook_id',
            'google_id',
            'apple_user_uuid',
            'first_name',
            'home_phone',
            'include_in_analysis',
            'is_superuser',
            'last_name',
            'password_change_required',
            'password_hash',
            'payment_auto_accept',
            'work_phone',
            'authenticator_code',
        )

    is_superuser = serializers.BooleanField(source='superuser')
    cell_phone = serializers.SerializerMethodField()
    home_phone = serializers.SerializerMethodField()
    work_phone = serializers.SerializerMethodField()
    password_hash = serializers.CharField(source='password')
    country_user_id = serializers.IntegerField(source='id')

    @staticmethod
    def get_cell_phone(instance):
        parsed_phone = parse_phone_number(instance.cell_phone)
        if parsed_phone.is_valid:
            return parsed_phone.db_format

    @staticmethod
    def get_home_phone(instance):
        parsed_phone = parse_phone_number(instance.home_phone)
        if parsed_phone.is_valid:
            return parsed_phone.db_format

    @staticmethod
    def get_work_phone(instance):
        parsed_phone = parse_phone_number(instance.work_phone)
        if parsed_phone.is_valid:
            return parsed_phone.db_format

    @staticmethod
    def _user_profiles_to_representation(instance, data):
        profiles = list(instance.active_profiles)
        if profiles:
            # Customer profile have priority
            selected_profile = (
                list(
                    filter(
                        # if more than one profile customer profile has priority
                        lambda p: p.profile_type == UserProfile.Type.CUSTOMER,
                        profiles,
                    )
                )
                if len(profiles) > 1
                else profiles
            )[0]
            keys = (
                'about_me',
                'address_line_1',
                'address_line_2',
                'city',
                'language',
                'latitude',
                'longitude',
                'profile_type',
                'zipcode',
            )
            for key in keys:
                value = getattr(selected_profile, key)
                # allow falsy values except None and empty string
                if value is not None and value != '':
                    data[key] = value
            if selected_profile.photo and selected_profile.photo.full_url:
                data['photo_url'] = selected_profile.photo.full_url
            return data

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['country_user_id'] = instance.id
        data['country_code'] = settings.API_COUNTRY
        self._user_profiles_to_representation(instance, data)
        return data


class BooksyAuthUserWithSessionsSerializer(BooksyAuthUserSerializer):

    def to_representation(self, instance):
        data = super().to_representation(instance)
        self._user_profiles_to_representation(instance, data)
        return data


class UserProfileActualizationSerializer(serializers.Serializer):
    address_line_1 = serializers.CharField(
        required=False,
        default='',
        allow_null=True,
    )
    address_line_2 = serializers.CharField(
        required=False,
        default='',
        allow_null=True,
    )
    photo = PhotoSerializer(
        required=False,
        allow_null=True,
    )
    language = serializers.CharField(
        required=False,
        default=settings.LANGUAGE_CODE[:2],
        allow_null=True,
    )
    # override required from GeoDataBasicSerializer
    latitude = serializers.FloatField(
        required=False,
        allow_null=True,
        validators=[
            MinValueValidator(-90),
            MaxValueValidator(90),
        ],
    )
    longitude = serializers.FloatField(
        required=False,
        allow_null=True,
        validators=[
            MinValueValidator(-180),
            MaxValueValidator(180),
        ],
    )


class ActualizationBooksyAuthSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            'first_name',
            'last_name',
            'cell_phone',
            'email',
            'password',
            'password_change_required',
            'is_superuser',
            'facebook_id',
            'profile',
        )

    first_name = serializers.CharField(
        required=True,
        allow_null=True,
        max_length=30,
    )
    last_name = serializers.CharField(
        required=True,
        allow_null=True,
        max_length=30,
    )
    cell_phone = BooksyPhoneSerializerField(
        write_format='db_format',
        read_format='db_format',
        default='',
        allow_blank=True,
        required=False,
    )
    email = serializers.EmailField(required=True)
    password = BooksyPasswordSerializerField()
    facebook_id = FacebookIdField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    is_superuser = serializers.BooleanField(source='superuser')
    # UserProfileActualizationSerializer is not model Serializer because we
    # do update manually for every profile
    profile = UserProfileActualizationSerializer()

    def update(self, instance, validated_data):
        from service.tools import AuthenticateUsingAccessTokenMixin

        profile = validated_data.pop('profile', {})
        instance = super().update(
            instance=instance,
            validated_data=validated_data,
        )
        # Django update doesn't fire post_save signal.
        # It is important here because with save it will fire
        # sync_user_booksy_auth_task in
        #   1) webapps/user/models.py:384
        #   2) webapps/user/models.py:762
        # Causing infinite syncronization for this user
        user_id = instance.id
        with transaction.atomic():
            get_user.clear_from_cache(user_id=instance.id)
            UserProfile.objects.filter(user_id=user_id).update(**profile)
            AuthenticateUsingAccessTokenMixin.get_user_profile.cache_clear(
                user_id,
                UserProfile.Type.CUSTOMER.value,
            )
            AuthenticateUsingAccessTokenMixin.get_user_profile.cache_clear(
                user_id,
                UserProfile.Type.BUSINESS.value,
            )
        # if you will use user in handlers remember to refresh it
        return instance


class AppleBaseSerializer(RequiredContextMixin, BaseUserSerializer):
    required_context = ('booking_source', 'apple_token')

    class Meta:
        model = User
        fields = (
            'first_name',
            'last_name',
            'apple_user_uuid',
            'email',
        )

    first_name = NameField(
        error_messages={'invalid': _('Invalid first name.')},
        allow_blank=True,
    )
    last_name = NameField(
        error_messages={'invalid': _('Invalid last name.')},
        allow_blank=True,
    )
    apple_user_uuid = serializers.CharField(max_length=128, required=False)
    email = serializers.EmailField(required=False)

    @property
    def _profile_type(self):
        raise NotImplementedError

    def validate_apple_user_uuid(self, value):
        if not value:
            raise serializers.ValidationError(
                _('Your Apple account is already registered'),
            )
        qs_apple_account = User.objects.filter(apple_user_uuid=value)
        if self.instance:
            qs_apple_account = qs_apple_account.exclude(
                id=self.instance.id,
            )
        if qs_apple_account.exists():
            raise serializers.ValidationError(
                _('Your Apple account is already registered'),
            )
        return value

    def get_or_create_profile(self, user):
        booking_source = self.context.get('booking_source')

        profile_qs = user.profiles.filter(profile_type=self._profile_type)
        if not profile_qs.exists():
            profile = UserProfile(
                user=user,
                profile_type=self._profile_type,
                language=settings.LANGUAGE_CODE[:2].lower(),
                source=booking_source,
            )
            profile.save()
        else:
            profile = profile_qs.first()

        apple_service_id = booking_source.get_apple_service_id()
        apple_identity = AppleIdentity.objects.filter(
            profile=profile,
            service_id=apple_service_id,
        ).first()

        if not apple_identity:
            apple_identity = AppleIdentity(
                service_id=apple_service_id,
                profile=profile,
            )
        apple_identity.access_token = ''
        apple_identity.apple_data = {
            'apple_token': self.context['apple_token'],
        }
        apple_identity.save()

        authorize_apple_user_task.delay(apple_identity.id)

        return profile

    def create(self, validated_data):
        email = validated_data['email']
        validated_data['username'] = User.generate_username(email)
        validated_data['last_login'] = tznow()
        # set unusable password
        validated_data['password'] = _generate_unusable_password()
        with transaction.atomic():
            user = super().create(validated_data)
            # <editor-fold desc="create related">
            self.get_or_create_profile(user)
            # </editor-fold>
        return user

    def update(self, instance, validated_data):
        validated_data['last_login'] = tznow()
        if not validated_data['first_name']:
            # don't overwrite with empty name
            validated_data.pop('first_name')
        if not validated_data['last_name']:
            # don't overwrite with empty name
            validated_data.pop('last_name')
        if ('email' in validated_data) and (instance.email != validated_data['email']):
            # do not override email for user if exists
            validated_data.pop('email')
        user = super().update(instance, validated_data)
        self.get_or_create_profile(user)
        return user


class AppleCustomerSerializer(AppleBaseSerializer):
    """Create customer user from Sign in with Apple"""

    @property
    def _profile_type(self):
        return UserProfile.Type.CUSTOMER


class AppleBusinessSerializer(AppleBaseSerializer):
    """Create business user from Sign in with Apple"""

    @property
    def _profile_type(self):
        return UserProfile.Type.BUSINESS


class ColorPaletteSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('service_color_palette',)


class BaseBusinessSignInSerializer(BaseUserSerializer):
    def create(self, validated_data):
        validated_data['username'] = User.generate_username(validated_data['email'])
        validated_data['last_login'] = tznow()
        validated_data['password'] = _generate_unusable_password()
        return super().create(validated_data)


class BusinessGoogleSignInSerializer(BaseBusinessSignInSerializer):
    google_id = serializers.CharField(max_length=50)

    class Meta:
        model = User
        fields = (
            'email',
            'first_name',
            'last_name',
            'google_id',
        )


class BusinessFacebookSignInSerializer(BaseBusinessSignInSerializer):
    facebook_id = serializers.CharField(max_length=50)

    class Meta:
        model = User
        fields = (
            'email',
            'first_name',
            'last_name',
            'facebook_id',
        )


class BaseCustomerTokenSignInSerializer(RequiredContextMixin, BaseCustomerCreateSerializer):
    required_context = ('booking_source',)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.context['profile_type'] = UserProfile.Type.CUSTOMER
        self.context.setdefault('language', settings.LANGUAGE_CODE[:2].lower())

    def create(self, validated_data):
        validated_data['password'] = _generate_unusable_password()
        return super().create(validated_data)

    def update(self, instance, validated_data):
        raise NotImplementedError()


class GdprAgreementsSerializer(BaseCustomerTokenSignInSerializer):
    user_agreements = GDPRUserAgreementSerializer(required=False, allow_null=True, many=True)

    def validate_user_agreements(self, value):
        if not value and is_gdpr_country():
            raise serializers.ValidationError('This field is required.', code='required')

        return value

    def create(self, validated_data):
        gdpr_agreements = validated_data.pop('user_agreements', None)
        with transaction.atomic():
            user = super().create(validated_data)
            user.create_or_update_gdpr_agreements(gdpr_agreements)

        return user


class CustomerGoogleSignInSerializerOld(BaseCustomerTokenSignInSerializer):
    google_id = serializers.CharField(max_length=50)

    class Meta:
        model = User
        fields = (
            'cell_phone',
            'email',
            'first_name',
            'google_id',
            'last_name',
            'sms_code',
        )


class CustomerGoogleSignInSerializer(GdprAgreementsSerializer):
    google_id = serializers.CharField(max_length=50)

    class Meta:
        model = User
        fields = (
            'cell_phone',
            'email',
            'first_name',
            'google_id',
            'last_name',
            'sms_code',
            'user_agreements',
        )


class CustomerFacebookSignInSerializerOld(BaseCustomerTokenSignInSerializer):
    facebook_id = serializers.CharField(max_length=50)

    class Meta:
        model = User
        fields = (
            'cell_phone',
            'email',
            'facebook_id',
            'first_name',
            'last_name',
            'sms_code',
        )

    @property
    def should_skip_sms_code_validation(self):
        return not settings.SMS_REGISTRATION_CODE_REQUIRED


class CustomerFacebookSignInSerializer(GdprAgreementsSerializer):
    facebook_id = serializers.CharField(max_length=50)

    class Meta:
        model = User
        fields = (
            'cell_phone',
            'email',
            'facebook_id',
            'first_name',
            'last_name',
            'sms_code',
            'user_agreements',
        )

    @property
    def should_skip_sms_code_validation(self):
        return not settings.SMS_REGISTRATION_CODE_REQUIRED


class CustomerAppleSignInSerializer(GdprAgreementsSerializer):
    apple_user_uuid = serializers.CharField(max_length=128)
    first_name = NameField(
        error_messages={'invalid': _('Invalid first name.')},
        allow_blank=True,
    )
    last_name = NameField(
        error_messages={'invalid': _('Invalid last name.')},
        allow_blank=True,
    )

    class Meta:
        model = User
        fields = (
            'first_name',
            'last_name',
            'apple_user_uuid',
            'email',
            'cell_phone',
            'sms_code',
            'user_agreements',
        )


class CustomerAppleAccountCreateSerializer(AppleBaseSerializer, BaseCustomerCreateSerializer):
    class Meta:
        model = User
        fields = (
            'first_name',
            'last_name',
            'apple_user_uuid',
            'email',
            'cell_phone',
            'sms_code',
        )

    password = BooksyPasswordSerializerField(required=False)

    @property
    def _profile_type(self):
        return UserProfile.Type.CUSTOMER
