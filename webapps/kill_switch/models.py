import typing

from django.db import models

from lib.cache import lru_booksy_cache
from lib.enums import StrChoicesEnum
from webapps.segment.enums import AnalyticEventEnums

KILL_SWITCH_CACHE_TIMEOUT = 5 * 60


@lru_booksy_cache(timeout=KILL_SWITCH_CACHE_TIMEOUT, skip_in_pytest=True)
def _is_killed(name: str) -> bool:
    return KillSwitch.objects.filter(name=name, is_killed=True).exists()


@lru_booksy_cache(timeout=KILL_SWITCH_CACHE_TIMEOUT, skip_in_pytest=True)
def _exist_and_alive(name: str) -> bool:
    return KillSwitch.objects.filter(name=name, is_killed=False).exists()


class ReportsKillSwitchMixin:
    """This mixin allows to disable particular report from Stats and Reports module.
    Disabled report will not be returned to frontend. See: StatsDashboard.__init__()
    """

    PREFIX = 'reports'

    @classmethod
    def get_reports_killswitch_choices(cls) -> typing.List[typing.Tuple[str, str]]:
        from webapps.stats_and_reports.report_keys import ReportKeys

        return [
            (f'{cls.PREFIX}_{report_key}', f'Reports -> {report_name}')
            for report_key, report_name in ReportKeys.get_choices()
        ]

    @classmethod
    def is_report_alive(cls, report_key: str) -> bool:
        return cls.alive(f'{cls.PREFIX}_{report_key}')


class KillSwitch(ReportsKillSwitchMixin, models.Model):
    class System(StrChoicesEnum):
        AUTO_NOTIFY_ABOUT_RESCHEDULE = 'auto_notify_about_reschedule', (
            'Send reschedule notification automatically - killswitch ON = manually'
            ' (change state at night if needed to avoid errors)'
        )
        AVAILABLE_TODAY = 'available_today', 'Generates \'Available Today\' gallery (MyBooksy)'
        BILLING_FORCE_SWITCH_TO_STRIPE = (
            'billing_force_switch_to_stripe',
            'Turn on/off forcing all Billing Px to switch to Stripe when updating payment method',
        )
        BILLING_MIGRATION_VIA_API = (
            'billing_migration_via_api',
            'Auto-migration merchants into new Billing through the API (frontdesk/mobile)',
        )
        BILLING_NEW_ACCOUNTS_WITH_STRIPE = (
            'billing_new_accounts_with_stripe',
            'Turn on/off Stripe for a new Px in Billing',
        )
        BILLING_OFFLINE_MIGRATION = (
            'billing_offline_migration',
            'Trun on/off offline migrations to billing',
        )
        BILLING_SHOW_PAYPAL_WARNING = (
            'billing_show_paypal_warning',
            'Trun on/off Paypal not supported message',
        )
        BILLING_SUSPEND_ENABLED = 'billing_suspend_enabled', 'Billing Suspend Enabled'
        BOOKINGS_REACTIVATION = (
            'booking_reactivation',
            'Adds booking reactivation actions (gallery, sms)',
        )
        BOOKING_USER_SEARCH_DATA = (
            'booking_user_search_data',
            'Synchronous calculate search data for new booking',
        )
        BOOKSY_AUTH = 'booksy_auth', 'Booksy Auth layer is enabled'
        BUSINESS_VERSION_LOCKS = 'business_version_locks', 'Use BusinessVersion based locks'
        C2B_REFERRAL = 'c2b_referral', 'C2B Referral'
        CLAIM_APPOINTMENTS_MERGE_BCI = 'claim_appointments_merge_bci', (
            'Turn On/Off feature that let our customers claim/merge'
            ' appointments/bci by deeplink.'
        )
        DISABLE_MARKING_TEST_USERS_BUSINESSES_ON_NON_PROD = (
            'disable_marking_test_users_businesses_on_non_prod',
            'Disable marking for test users and business on non production environments',
        )
        DISCLOSURE_OBLIGATION_AGREEMENT = (
            'disclosure_obligation_agreement',
            'Turn on/off disclosure obligation agreement email',
        )
        DISCOUNT_AFTER_POB = (
            'discount_after_pob',
            '1 free month of subscription after Payment Overdue Blocked period.',
        )
        DONATIONS_PROMO = 'donations_promo', 'Donations Promo'
        DOUBLE_SUBSCRIPTIONS_REPORT = 'double_subscriptions_report', 'Double subscriptions report'
        ENABLE_SMS_BOOKING_CONFIRMATIONS_BY_DEFAULT = (
            'sms_booking_confirmation_default',
            'Enable customer SMS booking confirmations for new businesses',
        )
        EXPERIMENT_ACTIVITY_STATUS_CACHE = (
            'experiment_activity_status_cache',
            'Experiment activity status cache',
        )
        FAST_PAYOUTS_DAILY_LIMIT = (
            'fast_payouts_daily_limit',
            'Turn on/off daily limit of fast payouts',
        )
        GOOGLE_RESERVE_LIVE_UPDATES = 'google_reserve_live_updates', 'Google reserve live updates'
        GROUPON_RESERVE_LIVE_UPDATES = (
            'groupon_reserve_live_updates',
            'Groupon reserve live updates',
        )
        GTM_FOR_ADMIN = 'gtm_for_admin', 'Enable Google Tag Manager (GTM) for admin'
        MARKING_TEST_USERS_BUSINESSES_ON_NON_PROD = (
            'marking_test_users_businesses_on_non_prod',
            'Marking for test users and business on non production environments, killed == turn off',
        )
        MATCH_USERS_BY_PHONE_NUMBER = 'match_users_by_phone_number', 'Match users by phone number'
        NAVISION_USE_TAX_RATE_TABLE = (
            'navision_use_tax_rate_table',
            'Take tax rates from our internal table (when killed we assume 0% for each product)',
        )
        NEAR_AVAILABILITY = 'near_availability', 'Add inner hits to real availability (LA badge)'
        OLD_WELCOME_MAIL_ACCOUNT_ADDED = (
            'old_mail_account_added',
            'Turn on/off sending account added email',
        )
        OLD_WELCOME_MAIL_INACTIVITY_LOGIN = (
            'old_mail_inactivity_login',
            'Turn on/off sending inactivity login email',
        )
        PROMPTED_REVIEWS_ANDROID = (
            'prompted_reviews_android',
            'Send prompted reviews notifications on Android',
        )
        PROMPTED_REVIEWS_IOS = 'prompted_reviews_ios', 'Send prompted reviews notifications on iOS'
        RATE_LIMIT = 'rate_limit', 'Disable Rate limit check on every request'
        REPLACE_BANNED_TWILIO_NUMBER = (
            'replace_banned_twilio_number',
            'Replace banned Twilio number',
        )
        REPLICA_APPOINTMENT_DETAILS = (
            'replica_appointment_details',
            'Use Replica DB for appointment details',
        )
        REPLICA_APPOINTMENT_LISTING = (
            'replica_appointment_listing',
            'Use Replica DB for customer bookings list handler',
        )
        REPLICA_BOOKING_NOTIFICATIONS = (
            'replica_booking_notifications',
            'Use Replica DB for Booking Notifications',
        )
        REPLICA_CALENDAR = 'replica_calendar', 'Use Replica DB for business calendar'
        REPLICA_CALENDAR_CHANGES = (
            'replica_calendar_changes',
            'Use Replica DB for CalendarChangeHandler',
        )
        REPLICA_FEED_GENERATOR = (
            'replica_feed_generator',
            'Enable using replica db for RwG feed generator.',
        )
        REPLICA_LIVE_UPDATE_RWG_TASK = (
            'replica_live_update_rwg_task',
            'Enable using replica db for RwG RTUs.',
        )
        REPLICA_POS_TRANSACTIONS = (
            'replica_pos_transactions',
            'Use Replica DB for POS Transactions listing',
        )
        REPLICA_POS_TRANSACTION_DETAILS = (
            'replica_pos_transaction_details',
            'Use Replica DB for POS Transaction details ',
        )
        REPLICA_TIME_SLOTS = 'replica_time_slots', 'Use Replica DB for time slots handler'
        REPORTS_COMBO_SERVICES = (
            'reports_combo_services',
            'Reports -> SalesByComboServicesSection',
        )
        REPORTS_LOGICAL_REPLICATION = 'reports_logical_replication', (
            'Stats and reports will be calculated using logical replica db. '
            'When killed queries are routed to physical replica.'
        )
        RETRIEVE_STUCK_RECEIVED_NOTIFICATION = (
            'retrieve_stuck_received_notification',
            'Turn on/off retrieve_stuck_received_notification_schedules_task',
        )
        RETRIEVE_STUCK_STARTED_NOTIFICATION = (
            'retrieve_stuck_started_notification',
            'Turn on/off retrieve_stuck_started_notification_schedules_task',
        )
        SEND_SMS_BOOKING_CONFIRMATIONS = (
            'send_sms_booking_confirmations',
            'Send SMS booking confirmations to customers',
        )
        SKIP_HIDDEN_ON_WEB = 'skip_hidden_on_web', 'Turn on/off businesses on web search'
        SMS_WHITELIST_ENABLED = (
            'sms_whitelist_enabled',
            'Blocking SMSes to non-whitelisted countries',
        )
        SMS_WITH_DEEPLINKS_ENABLED = (
            'use_sms_with_deeplinks_enabled',
            'Enables customer booking sms\'s with deeplinks',
        )
        THREE_DS_ENABLED = '3d secure enabled', '3D Secure enabled'
        TRIAL_MESSAGES_PUSH_CHANNEL = (
            'trial_messages_push_channel',
            'Push for TRIAL related messages',
        )
        UTT2_BACKEND = 'utt2_backend', 'Enable operation included in UTT2 upgrade.'
        UTT2_EXPERIMENT = 'utt2_experiment', 'Enable search using UTT2 based fields.'
        VERSUM_MIGRATION_ENABLED = 'versum_migration_enabled', 'Enable versum migration handling'

    class MarTech(StrChoicesEnum):
        # global switches
        MARTECH_ANALYTICS = 'martech_analytics', 'Turn on/off MarTech analytics'
        OLD_MARTECH_ANALYTICS = 'old_martech_analytics', 'Turn on/off old MarTech analytics'
        # single event switches
        BB_NO_SHOW_FOR_BUSINESS = (
            'bb_no_show_for_business',
            'Turn on/off event bb_no_show_for_business',
        )
        BCR_ORDER_RECEIVED = (
            'bcr_order_received',
            'Turn on/off event bcr_order_received',
        )
        BCR_RESET_ACCOUNT_VERIFY = (
            'bcr_reset_account_verify',
            'Turn on/off event bcr_reset_account_verify',
        )
        BCR_STRIPE_KYC_NOT_VERIFIED_ACCOUNT = (
            'bcr_stripe_kyc_not_verified_account',
            'Turn on/off event bcr_stripe_kyc_not_verified_account',
        )
        BCR_STRIPE_KYC_PENDING_ACCOUNT = (
            'bcr_stripe_kyc_pending_account',
            'Turn on/off event bcr_stripe_kyc_pending_account',
        )
        BCR_STRIPE_KYC_VERIFIED_ACCOUNT = (
            'bcr_stripe_kyc_verified_account',
            'Turn on/off event bcr_stripe_kyc_verified_account',
        )
        BCR_TERMINAL_ORDERED = (
            'bcr_terminal_ordered',
            'Turn on/off event bcr_terminal_ordered',
        )
        BOOST_OFF = 'boost_off', 'Turn on/off event boost_off'
        BOOST_ON = 'boost_on', 'Turn on/off event boost_on'
        BOOST_ON_OFF = 'boost_on_off', 'Turn on/off event boost_on_off'
        BUSINESS_APP_OPENED = 'business_app_opened', 'Turn on/off event business_app_opened'
        BUSINESS_CATEGORIES_UPDATED = (
            'business_categories_updated',
            'Turn on/off event business_categories_updated',
        )
        BUSINESS_CONTACT_PREFERENCES_UPDATED = (
            'business_contact_pref_upd',
            'Turn on/off event business_contact_pref_upd',
        )

        BUSINESS_DELAY_SET = 'business_delay_set', 'Turn on/off event business_delay_set'
        BUSINESS_INFO_UPDATED = 'business_info_updated', 'Turn on/off event business_info_updated'
        BUSINESS_PBA_ENABLED = 'business_pba_enabled', 'Turn on/off event business_pba_enabled'
        BUSINESS_POS_UPDATED = 'business_pos_updated', 'Turn on/off event business_pos_updated'
        BUSINESS_REGISTRATION_COMPLETED = (
            'business_registration_completed',
            'Turn on/off event business_registration_completed',
        )
        BUSINESS_REGISTRATION_STARTED = (
            'business_registration_started',
            'Turn on/off event business_registration_started',
        )
        BUSINESS_RE_TRIAL_ELIGIBLE_SEGMENT = (
            AnalyticEventEnums.BUSINESS_RE_TRIAL_ELIGIBLE,
            f'Turn on/off event {AnalyticEventEnums.BUSINESS_RE_TRIAL_ELIGIBLE} for segment',
        )
        BUSINESS_STATUS_UPDATED = (
            'business_status_updated',
            'Turn on/off event business_status_updated',
        )
        BUSINESS_SUBSCRIPTION_UPDATED = (
            'business_subscription_updated',
            'Turn on/off event business_subscription_updated',
        )
        BUSINESS_USER_LANGUAGE_SET = (
            'business_user_language_set',
            'Turn on/off event business_user_language_set',
        )
        CB_CREATED_FOR_BUSINESS = (
            'cb_created_for_business',
            'Turn on/off event cb_created_for_business',
        )
        CB_CREATED_FOR_CUSTOMER = (
            'cb_created_for_customer',
            'Turn on/off event cb_created_for_customer',
        )
        CB_CUSTOMER_INFO_UPDATED = (
            'cb_customer_info_updated',
            'Turn on/off event cb_customer_info_updated',
        )
        CB_FINISHED_FOR_BUSINESS = (
            'cb_finished_for_business',
            'Turn on/off event cb_finished_for_business',
        )
        CB_FINISHED_FOR_CUSTOMER = (
            'cb_finished_for_customer',
            'Turn on/off event cb_finished_for_customer',
        )
        CB_NO_SHOW_FOR_BUSINESS = (
            'cb_no_show_for_business',
            'Turn on/off event cb_no_show_for_business',
        )
        CB_STARTED_FOR_CUSTOMER = 'CB_Started_For_Customer', 'Turn on/off CB_Started_For_Customer'
        CHECKOUT_TRANSACTION_COMPLETED = (
            'checkout_transaction_completed',
            'Turn on/off event checkout_transaction_completed',
        )
        CONTACT_PREFERENCES_UPDATED = (
            'contact_preferences_updated',
            'Turn on/off event contact_preferences_updated',
        )
        CUSTOMER_APP_OPENED = 'customer_app_opened', 'Turn on/off event Customer_App_Opened'
        CUSTOMER_REGISTRATION_COMPLETED = (
            'customer_registration_completed',
            'Turn on/off event customer_registration_completed',
        )
        CUSTOMER_SEARCH_QUERY = ('customer_search_query', 'Turn on/off event Customer_Search_Query')
        CUSTOMER_USER_LANGUAGE_SET = (
            'customer_user_language_set',
            'Turn on/off event customer_user_language_set',
        )
        FIRST_NO_SHOW_FOR_BUSINESS = (
            '1st_no_show_for_business',
            'Turn on/off event 1st_no_show_for_business',
        )
        INVITE_ALL_CLICKED = 'invite_all_clicked', 'Turn on/off event invite_all_clicked'
        INVITE_PROCESS_COMPLETED = (
            'invite_process_completed',
            'Turn on/off event invite_process_completed',
        )
        ITERABLE_NOTIFICATION_CENTER = (
            'iterable_notification_center',
            'Turn on/off showing iterable push history in Notification Center',
        )
        ITERABLE_USER_EMAIL_SYNC = (
            'iterable_user_email_sync',
            'Sync user\'s email in iterable when changed',
        )
        KYC_FAILURE = 'kyc_failure', 'Turn on/off event kyc_failure'
        KYC_SUCCESS = 'kyc_success', 'Turn on/off event kyc_success'
        ONBOARDING_BUSINESS_GO_LIVE = (
            'onboarding_business_go_live',
            'Turn on/off event onboarding_business_go_live',
        )
        PAID_STATUS_ACHIEVED = 'paid_status_achieved', 'Turn on/off event paid_status_achieved'
        PAYMENT_TRANSACTION_COMPLETED = (
            'payment_transaction_completed',
            'Turn on/off event payment_transaction_completed',
        )
        POSTPONED_INVITES_SENT = (
            'postponed_invites_sent',
            'Turn on/off event postponed_invites_sent',
        )
        PROTECTION_SERVICE_ENABLED = (
            'protection_service_enabled',
            'Turn on/off event protection_service_enabled',
        )
        REVIEW_COMPLETED = 'review_completed', 'Turn on/off event review_completed'
        STAFFER_CREATED = 'staffer_created', 'Turn on/off event staffer_created'
        VIEW_ITEM_LIST = 'view_item_list', 'Turn on/off event view_item_list'
        VIEW_ITEM_LIST_IN_QUERY_HINTS = (
            'view_item_list_in_query_hints',
            'Turn on/off view_item_list event in query hints',
        )

    NAME_CHOICES = (
        System.choices()
        + MarTech.choices()
        + ReportsKillSwitchMixin.get_reports_killswitch_choices()
    )

    class Type(StrChoicesEnum):
        SYSTEMS = 'S', 'Systems'
        MarTech = 'M', 'MarTech'  # pylint: disable=invalid-name
        Reports = 'R', 'Reports'  # pylint: disable=invalid-name

    name = models.CharField(
        primary_key=True,
        max_length=60,
        choices=NAME_CHOICES,
    )
    is_killed = models.BooleanField(default=False)
    type = models.CharField(
        max_length=1,
        choices=Type.choices(),
        default=Type.SYSTEMS,
    )

    class Meta:
        ordering = ('name',)
        verbose_name = 'Kill Switch'
        verbose_name_plural = 'Kill Switches'

    def __str__(self):
        return f'{self.__class__.__name__} "{self.name}" {self.get_name_display()}'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self._invalidate_cache()

    def delete(self, *args, **kwargs):
        delete_return = super().delete(*args, **kwargs)
        self._invalidate_cache()
        return delete_return

    def _invalidate_cache(self):
        _is_killed.cache_clear(self.name)
        _exist_and_alive.cache_clear(self.name)

    @classmethod
    def killed(cls, name: str) -> bool:
        """
        Check, if kill switch is killed.

        :param name: kill switch name
        :return: True if is killed, False if not or not exists.
        """
        return _is_killed(name)

    @classmethod
    def alive(cls, name: str) -> bool:
        """
        Check, if kill switch is alive.

        :param name: kill switch name
        :return: True if is alive or not exists, False otherwise.
        """
        return not cls.killed(name)

    @classmethod
    def exist_and_alive(cls, name: str) -> bool:
        """
        Check, if kill switch exist and is alive.

        :param name: kill switch name
        :return: True if is alive and exists, False otherwise.
        """
        return _exist_and_alive(name)

    @classmethod
    def get_all_names(cls) -> set[str]:
        result = {choice for choice, _ in cls.NAME_CHOICES}
        return result
