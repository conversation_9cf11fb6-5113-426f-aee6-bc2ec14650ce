from django.conf import settings
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from country_config import Country
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import SubjectType
from lib.feature_flag.feature.boost import BoostBanBackendFlag, BoostPreSuspensionWarningBackendFlag
from lib.tools import format_currency
from webapps.booking.models import Appointment
from webapps.boost.enums import BoostBanType, BoostWarningType
from webapps.boost.models import BoostBan, BoostFraudSuspicion
from webapps.business.enums import (
    BoostPaymentSource,
    FeatureLabel,
    FeatureStatus,
    FeatureStatusColor,
)
from webapps.business.models import Business, BusinessPromotion
from webapps.marketplace.models import (
    MarketplaceBusiness,
    MarketplaceCommission,
)


class BoostBanSerializer(serializers.ModelSerializer):
    class Meta:
        model = BoostBan
        fields = (
            'type',
            'active_from',
            'active_till',
        )

    _BASE_TYPES = {
        BoostBanType.SUSPENSION: 'suspension',
        BoostBanType.TERMINATION: 'termination',
    }

    type = serializers.SerializerMethodField()

    def get_type(self, instance: BoostBan) -> str:
        base_type = self._BASE_TYPES.get(instance.type)
        return f'{base_type}_with_invoice' if instance.invoice_required else base_type


def boost_eligible(business: Business) -> bool:
    # pylint: disable=too-many-boolean-expressions
    """
    https://redmine.booksy.pm/issues/84647
    """
    commission = MarketplaceCommission.get_commission(business)
    boost_business = MarketplaceBusiness.get_or_create_related(business)
    if (
        not settings.BOOST.ENABLED
        or business.payment_source in (Business.PaymentSource.PLAY, Business.PaymentSource.ITUNES)
        or not boost_business.is_verified
        or not commission
        or not commission.marketplace
        or (
            business.boost_payment_source == BoostPaymentSource.ONLINE
            and not business.has_braintree
        )
    ):
        return False
    return True


def _forced_disabled(business: Business):
    """
    https://redmine.booksy.pm/issues/84538
    """
    if (
        business.payment_source in (Business.PaymentSource.ITUNES, Business.PaymentSource.UNKNOWN)
        and settings.API_COUNTRY == Country.US
    ):
        cb_count = Appointment.objects.filter(
            business=business,
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.FINISHED,
        ).count()
        return cb_count < 5
    return False


def _get_status(business: Business, last_promo: BusinessPromotion) -> FeatureStatus:
    # pylint: disable=too-many-return-statements
    if _forced_disabled(business):
        return FeatureStatus.DISABLED

    if business.boost_status == Business.BoostStatus.ENABLED:
        return FeatureStatus.ACTIVE
    if business.boost_status == Business.BoostStatus.SUSPENSION_PENDING:
        return FeatureStatus.SUSPENSION_PENDING
    if not business.has_subscription:
        return FeatureStatus.DISABLED
    if last_promo and last_promo.disabled_by == BusinessPromotion.CARD:
        return FeatureStatus.BLOCKED
    if not boost_eligible(business):
        return FeatureStatus.NOT_ELIGIBLE
    if not last_promo:
        return FeatureStatus.INACTIVE
    return FeatureStatus.DEACTIVATED


def boost_status(business: Business) -> dict:
    labels = {
        FeatureStatus.NOT_ELIGIBLE: _(FeatureLabel.NOT_ELIGIBLE),
        FeatureStatus.DISABLED: _(FeatureLabel.DISABLED),
        FeatureStatus.SUSPENSION_PENDING: _(FeatureLabel.SUSPENSION_PENDING),
        FeatureStatus.ACTIVE: _(FeatureLabel.ACTIVE),
        FeatureStatus.INACTIVE: _(FeatureLabel.INACTIVE),
        FeatureStatus.BLOCKED: _(FeatureLabel.BLOCKED),
        FeatureStatus.DEACTIVATED: _(FeatureLabel.DEACTIVATED),
    }
    colors = {
        FeatureStatus.NOT_ELIGIBLE: FeatureStatusColor.GRAY,
        FeatureStatus.DISABLED: FeatureStatusColor.GRAY,
        FeatureStatus.SUSPENSION_PENDING: FeatureStatusColor.ORANGE,
        FeatureStatus.ACTIVE: FeatureStatusColor.GREEN,
        FeatureStatus.INACTIVE: FeatureStatusColor.GRAY,
        FeatureStatus.BLOCKED: FeatureStatusColor.RED,
        FeatureStatus.DEACTIVATED: FeatureStatusColor.GRAY,
    }

    last_promo = (
        BusinessPromotion.objects.filter(business=business).order_by('-promotion_start').first()
    )
    status = _get_status(business, last_promo)
    include_ban = BoostBanBackendFlag()
    include_warning = BoostPreSuspensionWarningBackendFlag(
        UserData(
            subject_key=business.id,
            subject_type=SubjectType.BUSINESS_ID.value,
        )
    )

    if settings.BOOST.BANS_ENABLED and include_ban:
        ban = BoostBan.get_current_ban(business.id)
        ban_data = BoostBanSerializer(instance=ban).data if ban else None
        # NOTE: when boost get blocked the status changes to NOT_ELIGIBLE
    else:
        ban_data = None

    warning_data = None
    if settings.BOOST.BANS_ENABLED and include_warning and not ban_data:
        if fraud_suspicion := (
            BoostFraudSuspicion.objects.filter(
                business_id=business.id,
                warning_visible=True,
            ).first()
        ):
            warning_texts = fraud_suspicion.get_warning_texts()
            warning_data = {
                'type': BoostWarningType.PRE_SUSPENSION.value,
                'alert_text_full': warning_texts.get('alert_text_full'),
                'alert_text_short': warning_texts.get('alert_text_short'),
            }

    color = colors[status]
    label = labels[status]
    auto_suspension = settings.BOOST.AUTO_SUSPENSION
    summary = {}

    if include_ban:
        summary['ban'] = ban_data

    if include_warning:
        summary['warning'] = warning_data

    if status == FeatureStatus.NOT_ELIGIBLE:
        return {
            'status': status,
            'status_color': color,
            'label': label,
            **summary,
        }

    tz = business.get_timezone()

    if last_promo:
        promo_summary = Appointment.objects.filter_all_finished_appointments_of_boost_clients(
            business=business
        ).aggregate_boost_summary()
        summary.update(
            {
                'last_action_date': last_promo.updated.astimezone(tz).strftime(
                    settings.DATE_FORMAT
                ),
                'appointments_count': promo_summary.get('appointments_count', 0),
                'estimated_revenue': format_currency(promo_summary.get('revenue', 0)),
            }
        )

    return {
        'status': status,
        'status_color': color,
        'label': label,
        'auto_suspension': auto_suspension,
        **summary,
    }
