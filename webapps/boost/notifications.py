from django.conf import settings
from django.utils.translation import gettext, gettext_lazy as _

from lib.deeplink.cache import Deep<PERSON>ink<PERSON>ache
from lib.feature_flag.feature.boost import BoostBanSMSChannelFlag
from webapps.boost.enums import BoostBanStatus, BoostBanNotificationStatus
from webapps.boost.models import <PERSON>ostBan, BoostFraudSuspicion
from webapps.boost.tools import get_boost_landing_page_url
from webapps.business.models import Business, Resource
from webapps.notification.base import (
    BaseNotification,
    Channel,
    ChannelSelector,
    PopupTemplate,
    PushTarget,
    Recipient,
)
from webapps.notification.channels import (
    EmailChannel,
    PopupChannel,
    PushChannel,
    SMSChannel,
)
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
)
from webapps.notification.recipients import (
    Managers,
    SystemSender,
)


class BoostBanChannelSelector(ChannelSelector):
    """
    Restrict sending emails to the business owner only.

    The invoice is sent only to the owner by the operations team, as hinted in the email content.
    """

    sms_priority = None  # pylint: disable=invalid-overridden-method

    def select(self, recipient: Recipient, channels: list[Channel]) -> set[Channel.Type]:
        possible_channels = super().select(recipient, channels)

        try:
            staff_access_level = recipient.staffer.staff_access_level
        except AttributeError:
            staff_access_level = None

        if staff_access_level != Resource.STAFF_ACCESS_LEVEL_OWNER:
            if Channel.Type.EMAIL in possible_channels:
                possible_channels.remove(Channel.Type.EMAIL)

            if Channel.Type.SMS in possible_channels:
                possible_channels.remove(Channel.Type.SMS)

        return possible_channels


class BaseBoostBanNotification(BaseNotification):
    category = NotificationCategory.BOOST
    recipients = (Managers,)
    sender = SystemSender
    channel_selector = BoostBanChannelSelector

    def __init__(self, ban):
        super().__init__(ban)
        self.ban = ban
        self.business = ban.business

    @property
    def identity(self):
        return f'{self.notif_type},{self.ban.id}'

    def get_context(self):
        return {
            'ban': self.ban,
            'support_email': settings.COUNTRY_CONFIG.cs_email_biz,
            'support_url': DeepLinkCache.get('support'),
            'boost_terms_url': (
                f'{settings.FRONTDESK_APP_URL}boost-terms/{settings.API_COUNTRY}.html'
            ),
            'boost_landing_page_url': get_boost_landing_page_url(),
        }


class BoostBanSMSChannel(SMSChannel):
    """
    Custom SMS channel that will track delivery in `BoostBan.notification_status`.
    """

    notification: BaseBoostBanNotification

    def __init__(self, notification):
        if not isinstance(notification, BaseBoostBanNotification):
            raise ValueError('This custom channel is intended for boost ban notifications only')

        if 'StartNotification' not in notification.notif_type:
            raise ValueError('We only care about tracking delivery when applying a ban')

        super().__init__(notification)

    def is_valid_recipient(self, recipient: Recipient) -> bool:
        return True

    def send(self, recipients):

        super().send(recipients)
        recipients_valid = [bool(recipient.phone) for recipient in recipients]

        ban = self.notification.ban
        if ban.notification_status is None:
            if not all(recipients_valid):
                ban.notification_status = BoostBanNotificationStatus.FAILED
                ban.notification_details = 'Recipient doesn\'t have cell phone number set'
                ban.save(update_fields=['notification_status', 'notification_details', 'updated'])
            else:
                ban.notification_status = BoostBanNotificationStatus.PENDING
                ban.save(update_fields=['notification_status', 'updated'])


class BoostSuspensionStartNotification(BaseBoostBanNotification):
    popup_template = PopupTemplate(
        icon=NotificationIcon.CONFIRM,
        group=NotificationGroup.BOOST,
        crucial=True,
        relevance=3,
        size=NotificationSize.NORMAL,
        messages=[
            _('Important notice'),
            _('Access to Boost suspended'),
            _('Your access was suspended due to a violation of the terms and conditions'),
        ],
    )
    push_template = _('Your access was suspended due to a violation of the terms and conditions')
    email_template_name = 'boost/boost_suspension_start'
    sms_template = _(
        "Your access to the Boost service has been suspended for 30 days due to a violation "
        "of Boost T&C's. Further information can be found in Booksy Biz."
    )

    @property
    def channels(self):
        channels = [
            EmailChannel,
            PushChannel,
            PopupChannel,
        ]
        if BoostBanSMSChannelFlag():
            channels.append(BoostBanSMSChannel)

        return channels

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.BOOST_BAN.value,
            title=gettext('Access to Boost suspended'),
        )

    def should_skip_with_plea(self):
        if self.ban.status != BoostBanStatus.ACTIVE:
            return True, 'Ban is no longer active'
        return super().should_skip_with_plea()


class BoostTerminationStartNotification(BaseBoostBanNotification):
    popup_template = PopupTemplate(
        icon=NotificationIcon.CONFIRM,
        group=NotificationGroup.BOOST,
        crucial=True,
        relevance=3,
        size=NotificationSize.NORMAL,
        messages=[
            _('Important notice'),
            _('Boost service terminated'),
            _('Your access was suspended due to a violation of the terms and conditions'),
        ],
    )
    push_template = _('Your access was suspended due to a violation of the terms and conditions')
    email_template_name = 'boost/boost_termination_start'
    sms_template = _(
        "Your Boost service has been terminated due to a violation of Boost T&C's. "
        "Further information can be found in Booksy Biz."
    )

    @property
    def channels(self):
        channels = [
            EmailChannel,
            PushChannel,
            PopupChannel,
        ]
        if BoostBanSMSChannelFlag():
            channels.append(BoostBanSMSChannel)

        return channels

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.BOOST_BAN.value,
            title=gettext('Access to Boost suspended'),
        )

    def should_skip_with_plea(self):
        if self.ban.status != BoostBanStatus.ACTIVE:
            return True, 'Ban is no longer active'
        return super().should_skip_with_plea()


class BoostSuspensionEndNotification(BaseBoostBanNotification):
    popup_template = PopupTemplate(
        icon=NotificationIcon.CONFIRM,
        group=NotificationGroup.BOOST,
        crucial=True,
        relevance=3,
        size=NotificationSize.NORMAL,
        messages=[
            _('Important notice'),
            _('Boost has been reactivated'),
            _(
                'The Boost service has been reactivated after a 30-day suspension resulting from '
                'a violation of the Boost Terms & Conditions.'
            ),
        ],
    )
    push_template = _(
        'The Boost service has been reactivated after a 30-day suspension resulting from '
        'a violation of the Boost Terms & Conditions.'
    )
    email_template_name = 'boost/boost_suspension_end'
    sms_template = _(
        'Your access to the Boost service has been reactivated. Further information can be found '
        'in Booksy Biz.'
    )

    @property
    def channels(self):
        channels = [
            EmailChannel,
            PushChannel,
            PopupChannel,
        ]
        if BoostBanSMSChannelFlag():
            channels.append(SMSChannel)

        return channels

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.BOOST_BAN.value,
            title=gettext('Boost has been reactivated'),
        )

    def should_skip_with_plea(self):
        if self.ban.status != BoostBanStatus.ENDED:
            return True, 'Ban has not ended'
        return super().should_skip_with_plea()


class BoostTerminationEndNotification(BaseBoostBanNotification):
    popup_template = PopupTemplate(
        icon=NotificationIcon.CONFIRM,
        group=NotificationGroup.BOOST,
        crucial=False,
        relevance=3,
        size=NotificationSize.NORMAL,
        messages=[
            _('Important notice'),
            _('You can now reactivate Boost'),
            _(
                'The suspension period for the Boost service, resulting from a violation of '
                'the Boost Terms and Conditions, has ended.'
            ),
        ],
    )
    push_template = _(
        'The suspension period for the Boost service, resulting from a violation of '
        'the Boost Terms and Conditions, has ended.'
    )
    email_template_name = 'boost/boost_termination_end'
    sms_template = _(
        'Your suspension from the Boost service has been lifted and you may now reactivate Boost. '
        'Further information can be found in Booksy Biz.'
    )

    @property
    def channels(self):
        channels = [
            EmailChannel,
            PushChannel,
            PopupChannel,
        ]
        if BoostBanSMSChannelFlag():
            channels.append(SMSChannel)

        return channels

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.BOOST_BAN.value,
            title=gettext('You can now reactivate Boost'),
        )

    def should_skip_with_plea(self):
        if self.ban.status != BoostBanStatus.ENDED:
            return True, 'Ban has not ended'
        return super().should_skip_with_plea()


class BoostPreSuspensionWarningNotification(BaseNotification):
    category = NotificationCategory.BOOST
    recipients = (Managers,)
    sender = SystemSender
    channels = (EmailChannel,)
    email_template_name = 'boost/boost_pre_suspension_warning'

    def __init__(self, fraud_suspicion: BoostFraudSuspicion, business: Business):
        if fraud_suspicion.business_id != business.id:
            raise ValueError
        super().__init__(fraud_suspicion)
        self.fraud_suspicion = fraud_suspicion
        self.business = business

    @property
    def identity(self):
        return f'{self.notif_type},{self.business.id}'

    def get_context(self):
        boost_terms_url = f'{settings.FRONTDESK_APP_URL}boost-terms/{settings.API_COUNTRY}.html'
        return {
            'support_email': settings.COUNTRY_CONFIG.cs_email_biz,
            'email_text_template': self.fraud_suspicion.get_warning_texts().get('email_text'),
            'boost_terms_url': boost_terms_url,
        }

    def should_skip_with_plea(self):
        if not self.fraud_suspicion.warning_visible:
            return True, 'Warning not visible'

        if self.business.boost_status not in Business.BoostStatus.active_statuses():
            return True, 'Boost disabled'

        ban = BoostBan.get_current_ban(self.business.id)
        if ban:
            return True, 'Ban already applied'

        return super().should_skip_with_plea()
