# Generated by Django 4.2.18 on 2025-06-10 14:12

from django.db import migrations, models
import webapps.boost.enums


class Migration(migrations.Migration):

    dependencies = [
        ('boost', '0008_boostfraudsuspicion'),
    ]

    operations = [
        migrations.AddField(
            model_name='boostfraudsuspicion',
            name='warning_type',
            field=models.CharField(
                choices=[('C', 'Unfinished Appointments Count'), ('P', 'Price Manipulations')],
                default=webapps.boost.enums.BoostFraudWarningType['UNFINISHED_APPOINTMENTS_COUNT'],
                max_length=1,
            ),
        ),
    ]
