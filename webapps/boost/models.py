from decimal import Decimal

import braintree
from django.conf import settings
from django.contrib.postgres.aggregates import ArrayAgg
from django.db import models, IntegrityError
from django.db.models import Exists, Sum, OuterRef, Subquery, Value, Q
from django.db.models.functions import Coalesce
from django.db.transaction import atomic as django_atomic_transaction

from country_config import BASE_INFO
from lib.capping import apply_capping
from lib.models import (
    ArchiveModel,
    ArchiveQuerySet,
    AutoAddHistoryModel,
    AutoAddHistoryQuerySet,
    AutoUpdateManager,
    AutoUpdateQuerySet,
    BaseArchiveManager,
    HistoryModel,
    ChangeArchivedModel,
)
from lib.tools import tznow
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import Appointment, SubBooking
from webapps.boost.consts import (
    FRAUD_UNFINISHED_APPOINTMENTS_COUNT_TEXTS,
    FRAUD_PRICE_MANIPULATIONS_TEXTS,
)
from webapps.boost.enums import (
    BoostAppointmentStatus,
    MarketplaceTransactionProgressStatus,
    MarketplaceTransactionType,
    TransactionPaymentSource,
    MarketplaceTransactionStage,
    MarketplaceTransactionStatusEnum,
    BoostClaimRequestStatus,
    BoostBanType,
    BoostBanLength,
    BoostBanStatus,
    BoostBanReason,
    BoostBanNotificationStatus,
    BoostFraudWarningType,
)
from webapps.boost.helpers import (
    is_boost_overdue,
    get_appointment_updated_gt_limit,
    get_booked_till_gt_limit,
)
from webapps.boost.tools import value_or_0
from webapps.business.models import PAID_PROMO
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.marketplace.models import (
    BoostClientCard,
    MarketplaceCommission,
    MarketplaceTransactionRow,
    MarketplaceTransactionStatus,
    MarketplaceTransaction,
)
from webapps.navision.models import TaxRate
from webapps.navision.ports.tax_rates import TaxForNetSummary
from webapps.structure.enums import RegionType
from webapps.structure.models import Region
from webapps.survey.models import Poll


class BoostAppointmentQuerySet(AutoUpdateQuerySet):
    def annotate_is_refunded(self):  # DEPRECATED
        return self.annotate(
            is_refunded=Exists(
                MarketplaceTransactionRow.objects.filter(
                    boost_appointment_id=OuterRef('id'),
                    refunded=True,
                ),
            ),
        )

    def annotate_transaction_stage(self):
        return self.annotate(
            transaction_stage=Coalesce(
                Subquery(
                    BoostAppointmentToStatus.objects.filter(
                        boost_appointment=OuterRef('pk'),
                        status__status__in=MarketplaceTransactionStatusEnum.revertable_statuses(),
                    )
                    .order_by('-status__charge_date')
                    .values_list('status__type', flat=True)[:1]
                ),
                Value(MarketplaceTransactionStage.NOT_CHARGED),
            )
        )

    def annotate_has_ever_been_charged(self):
        return self.annotate(
            has_ever_been_charged=Exists(
                MarketplaceTransactionStatus.objects.filter(
                    transaction_id=OuterRef('transaction_id'),
                    type=MarketplaceTransactionType.CHARGE,
                    status__in=(
                        braintree.Transaction.Status.Settling,
                        braintree.Transaction.Status.Settled,
                    ),
                )
            ),
        )

    def annotate_last_charge_status(self):
        return self.annotate(
            last_charge_status=Subquery(
                MarketplaceTransactionStatus.objects.filter(
                    boost_appointments=OuterRef('id'),
                    type=MarketplaceTransactionType.CHARGE,
                )
                .order_by('-created')
                .values_list('status', flat=True)[:1]
            )
        )

    def payable_boost_appointments(self):
        return self.filter(deleted__isnull=True)

    def filter_pending_refunds(self):
        """returns a q-set of BA that should be refunded, limited by default datetimes"""
        appointment_updated_gt_limit = get_appointment_updated_gt_limit()
        booked_till_gt_limit = get_booked_till_gt_limit()

        return (
            self.filter(
                appointment__payable=False,
                appointment__updated__gt=appointment_updated_gt_limit,
                appointment__booked_till__gt=booked_till_gt_limit,
            )
            .exclude(status=BoostAppointmentStatus.OFFLINE)
            .annotate_transaction_stage()
            .filter(transaction_stage=MarketplaceTransactionStage.CHARGED)
        )

    def aggregate_by_transaction(self):
        """returns objects like {'transaction': 123, 'ba_ids': [456, 789]}"""
        return self.values('transaction').annotate(ba_ids=ArrayAgg('id'))


class BoostAppointmentManager(AutoUpdateManager.from_queryset(BoostAppointmentQuerySet)):
    pass


class BoostAppointment(ArchiveModel):
    appointment = models.OneToOneField(
        related_name='boost_appointment',
        to='booking.Appointment',
        on_delete=models.PROTECT,
    )
    boost_promotion = models.ForeignKey(
        related_name='boost_appointments',
        to='business.BusinessPromotion',
        on_delete=models.PROTECT,
    )
    transaction = models.ForeignKey(
        related_name='boost_appointments',
        to='marketplace.MarketplaceTransaction',
        on_delete=models.PROTECT,
    )

    # pylint: disable=duplicate-code
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='Booksy commission')
    gross_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Booksy gross commission',
        null=True,
        default=None,
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Commission tax',
        null=True,
        blank=True,
    )
    tax_rate = models.ForeignKey(
        'navision.TaxRate',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    # pylint: enable=duplicate-code

    status = models.CharField(
        max_length=16,
        choices=BoostAppointmentStatus.choices(),
        default=BoostAppointmentStatus.PAYABLE,
    )
    appointment_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
    )

    claim = models.ForeignKey(
        'survey.PollChoice',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )  # replacement for marketplace_claim

    objects = BoostAppointmentManager()
    # do not use all_objects since it doesn't fill 'update' field

    @classmethod
    def _get_status_by_transaction(cls, transaction):
        return (
            BoostAppointmentStatus.PAYABLE
            if transaction.payment_source in TransactionPaymentSource.online_methods()
            else BoostAppointmentStatus.OFFLINE
        )

    @classmethod
    def get_safely(cls, appointment, defaults):
        try:
            boost_appointment, _created = cls.objects.get_or_create(
                appointment=appointment, defaults=defaults
            )
        except IntegrityError:
            boost_appointment = cls.objects.get(appointment=appointment)
        return boost_appointment

    @classmethod
    def get_or_create_by_appointment(cls, appointment):
        boost_appointment = BoostAppointment.objects.filter(appointment=appointment).first()
        if not boost_appointment:
            transaction = MarketplaceTransaction.get(appointment.business)

            originally_first_appointment = appointment.booked_for.originally_first_appointment
            boost_promotion = appointment.business.get_promotion(
                originally_first_appointment.created
            )

            defaults = {
                'boost_promotion': boost_promotion,
                'transaction': transaction,
                'amount': Decimal(0),
                'status': cls._get_status_by_transaction(transaction),
            }
            boost_appointment = cls.get_safely(appointment=appointment, defaults=defaults)
        elif (
            boost_appointment.deleted
            or boost_appointment.status not in BoostAppointmentStatus.payable_statuses()
        ):
            boost_appointment.transaction = MarketplaceTransaction.get(appointment.business)
            boost_appointment.status = cls._get_status_by_transaction(boost_appointment.transaction)
            boost_appointment.deleted = None
            boost_appointment.save()

        return boost_appointment

    @classmethod
    def disable_payable_objects(cls, appointment):
        """assuming 'payable' field is updated on appointment"""
        appointment.set_subbookings_chargeable_payable()

        boost_appointment_qset = BoostAppointment.objects.filter(appointment=appointment)
        if not boost_appointment_qset.exists():
            return

        all_rows = MarketplaceTransactionRow.all_objects.filter(appointment=appointment)
        changes = {}
        if any_claim_statuses := (
            set(BoostAppointmentStatus.claimed_accepted_statuses())
            & set(all_rows.values_list('status', flat=True))
        ):
            changes['status'] = any_claim_statuses.pop()
            # this flow actually doesn't happen - it is handled in `set_claim_status`
        elif appointment.status == AppointmentStatus.NOSHOW:
            changes['status'] = BoostAppointmentStatus.NOSHOW

        boost_appointment_qset.update(
            deleted=tznow(),
            **changes,
        )

    def update_mtrs(self):
        """update MTRs state according to their 'payable' information"""

        for booking in (
            self.appointment.bookings.all()
            .select_related('appointment')
            .prefetch_related('marketplace_transactions')
        ):
            mtr = getattr(booking, 'marketplace_transactions', None)
            mtr_found = bool(mtr)
            match mtr_found, booking.payable:
                case True, True:
                    MarketplaceTransactionRow.all_objects.filter(id=mtr.id).update(
                        deleted=None,
                        transaction=self.transaction,
                        refunded=False,
                    )
                case True, False:
                    mtr.soft_delete()
                case False, True:
                    MarketplaceTransactionRow.create_for_boost_appointment(
                        subbooking=booking,
                        boost_appointment=self,
                    )
                # case False, False - nothing to do

    @django_atomic_transaction
    def update_amount(self):
        if self.deleted:
            return  # update for deleted BA would lead to 0's in amounts fields, we do not wish that
            # because we wish to know what the price was before it was deleted

        if self.transaction.status == MarketplaceTransactionProgressStatus.CLOSED:
            return  # amount update of already paid transaction is not allowed

        self.appointment.set_subbookings_chargeable_payable()
        self.update_mtrs()

        commission = (
            MarketplaceCommission.objects.filter(business_promotions=self.boost_promotion_id)
            .minimal_computable_objects()
            .first()
        )
        amount_to_be_used = commission.max
        payable_rows = self.rows.filter(subbooking__payable=True)
        amount_missing_to_minimum = commission.minimum_commission
        for row in payable_rows.order_by('subbooking__booked_till'):
            used_amount = (
                row.update_amount_helper(amount_to_be_used, commission)
                if self.boost_promotion.type == PAID_PROMO
                else 0
            )
            amount_to_be_used = max(amount_to_be_used - used_amount, 0)
            amount_missing_to_minimum -= row.amount

        if (
            row := self.rows.filter(deleted__isnull=True, subbooking__payable=True).last()
        ) and amount_missing_to_minimum > 0:
            row.recalculate_amount_with_minimum_commission(row.amount + amount_missing_to_minimum)

        update_dict = payable_rows.aggregate(
            amount=Coalesce(Sum(value_or_0('amount')), Decimal(0)),
            gross_amount=Sum(value_or_0('gross_amount')),
            tax_amount=Sum(value_or_0('tax_amount')),
            appointment_price=Sum(value_or_0('booking_price')),
        )
        update_dict['amount'] = min(update_dict['amount'] or Decimal(0), commission.max)

        if tax_summary := TaxForNetSummary.for_business(
            business=self.boost_promotion.business,
            service=TaxRate.Service.BOOST,
            net_price=update_dict['amount'],
            round_results=True,
        ):
            update_dict['tax_rate'] = tax_summary.tax_rate

        for update_key, update_value in update_dict.items():
            setattr(self, update_key, update_value)
        self.save()

    @property
    def is_overdue(self):
        return is_boost_overdue(self.appointment.booked_till, self.transaction.business)

    @property
    def is_refunded(self):
        return (
            BoostAppointment.objects.filter(id=self.id)
            .annotate_transaction_stage()
            .filter(transaction_stage=MarketplaceTransactionStage.REFUNDED)
            .exists()
        )

    def get_final_claim_status(self):
        claim_allowed = self.boost_promotion.business.claim_allowed
        if self.is_overdue:
            return BoostAppointmentStatus.CLAIM_OVERDUE
        if not claim_allowed:
            return BoostAppointmentStatus.CLAIM_PENDING
        return BoostAppointmentStatus.CLAIMED

    def async_set_claim_status(self, selected_status, choice_id=None, poll_text=None):
        from webapps.boost.tasks import set_claim_status_task  # pylint: disable=cyclic-import

        set_claim_status_task.delay(self.id, selected_status, choice_id, poll_text)

    @django_atomic_transaction
    def set_claim_status(self, selected_status, choice_id=None, user_email=None, poll_text=None):
        """assuming that selected status is one of claim statuses"""
        # pylint: disable=too-many-branches
        payable = selected_status in BoostAppointmentStatus.payable_statuses()

        boost_appointment_update_dict = {'status': selected_status}
        if not payable:
            boost_appointment_update_dict['deleted'] = tznow()
        if choice_id:
            boost_appointment_update_dict['claim_id'] = choice_id
        BoostAppointment.objects.filter(appointment_id=self.appointment_id).update(
            **boost_appointment_update_dict
        )
        if not payable:
            Appointment.objects.filter(id=self.appointment_id).update(
                chargeable=False, payable=False
            )
            SubBooking.objects.filter(appointment_id=self.appointment_id).update(
                chargeable=False, payable=False
            )

        bci = self.appointment.booked_for
        rows_update_dict = {'status': selected_status}
        if choice_id:
            rows_update_dict['claim_id'] = choice_id
        for row in MarketplaceTransactionRow.all_objects.filter(appointment_id=self.appointment_id):
            for key, value in rows_update_dict.items():
                setattr(row, key, value)
            if payable and row.deleted:
                row.deleted = None
            row.save()  # save - not update - is to trigger creation of MarketplaceTransactionLog

            if choice_id:
                reply_data = {'transaction_row_id': row.id}
                if poll_text:
                    reply_data['text'] = poll_text

                Poll.reply(
                    business_id=self.appointment.business_id,
                    choice_id=choice_id,
                    data=reply_data,
                )
        # ^ no need to revert or delete - it is done by
        # mass_revert_or_delete_boost_transactions_task

        if not bci:
            return

        bci_update_fields = []

        if selected_status in BoostAppointmentStatus.claimed_accepted_statuses():
            bci.client_type = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_RECURRING
            bci.claimed = True
            BoostClientCard.bulk_update_by_related([bci.id], from_promo=False)
            bci_update_fields.extend(['client_type', 'claimed'])

        elif (
            selected_status
            in (BoostAppointmentStatus.CLAIM_DENIED, BoostAppointmentStatus.CLAIM_PENDING)
            and bci.client_type != BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        ):
            bci.client_type = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
            bci.claimed = False
            BoostClientCard.bulk_update_by_related([bci.id], from_promo=True)
            bci_update_fields.extend(['client_type', 'claimed'])

        if user_email:
            bci.marketplace_claim = f'Status changed to {selected_status} by {user_email}'
            bci_update_fields.append('marketplace_claim')

        if bci_update_fields:
            bci.save(update_fields=bci_update_fields)
            bci.refresh_from_db()
            bci.reindex()

    @apply_capping(delay=60, key=lambda *args, **kws: args[0].id)
    def claim_by_user(self, choice_id, poll_text=None):
        final_status = self.get_final_claim_status()
        claim_allowed = final_status != BoostAppointmentStatus.CLAIM_PENDING

        claim_result = BoostClaimRequestStatus.SENT
        if final_status == BoostAppointmentStatus.CLAIM_OVERDUE:
            claim_result = BoostClaimRequestStatus.OVERDUE
        elif self.boost_promotion.business.is_claim_ratio_over_account_notice_limit:
            claim_result = BoostClaimRequestStatus.ACCOUNT_NOTICE

        self.async_set_claim_status(
            selected_status=final_status,
            choice_id=choice_id,
            poll_text=poll_text,
        )

        return {
            'claim_allowed': claim_allowed,
            'claim_approved': final_status == BoostAppointmentStatus.CLAIMED,  # deprecated
            'client_status': final_status,  # deprecated with PXMAR-583
            'claim_result': claim_result,
        }

    def claim_by_admin(self, user_email):
        from webapps.marketplace.notifications import send_boost_claim

        final_status = self.get_final_claim_status()
        self.set_claim_status(selected_status=final_status, user_email=user_email)
        send_boost_claim(self.boost_promotion.business_id)

    def set_claim_pending(self, user_email):
        self.set_claim_status(
            selected_status=BoostAppointmentStatus.CLAIM_PENDING, user_email=user_email
        )

    def accept_claim(self, user_email):
        from webapps.marketplace.notifications import send_boost_claim

        self.set_claim_status(selected_status=BoostAppointmentStatus.CLAIMED, user_email=user_email)
        send_boost_claim(self.boost_promotion.business_id)

    def deny_claim(self, reason_id, user_email):
        from webapps.marketplace.notifications import send_boost_claim

        self.set_claim_status(
            selected_status=BoostAppointmentStatus.CLAIM_DENIED, user_email=user_email
        )
        bci = self.appointment.booked_for

        if bci:
            bci.deny_claim_reason_id = reason_id
            bci.save(update_fields=['deny_claim_reason_id'])

        send_boost_claim(self.appointment.business.id)


class BoostAppointmentToStatus(models.Model):
    status = models.ForeignKey(
        'marketplace.MarketplaceTransactionStatus',
        on_delete=models.CASCADE,
    )
    boost_appointment = models.ForeignKey(
        BoostAppointment,
        on_delete=models.CASCADE,
    )
    created = models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')

    @staticmethod
    def create_bats(boost_appointments, transaction_status):
        bats = [
            BoostAppointmentToStatus(
                status=transaction_status,
                boost_appointment=boost_appointment,
            )
            for boost_appointment in boost_appointments
        ]
        return BoostAppointmentToStatus.objects.bulk_create(bats)


class BoostSettingsQuerySet(
    ArchiveQuerySet,
    AutoAddHistoryQuerySet,
):
    pass


class BoostSettingsManager(BaseArchiveManager.from_queryset(BoostSettingsQuerySet)):
    pass


class BoostSettings(AutoAddHistoryModel, ArchiveModel):
    days_threshold = models.PositiveIntegerField(default=99)
    total_threshold = models.PositiveIntegerField(default=9999)
    days_threshold_enabled = models.BooleanField(default=False)
    total_threshold_enabled = models.BooleanField(default=False)

    region = models.OneToOneField(
        Region,
        on_delete=models.PROTECT,
        limit_choices_to=models.Q(type=Region.Type.COUNTRY),
    )

    objects = BoostSettingsManager()

    class Meta:
        verbose_name = 'Settings'
        verbose_name_plural = 'Settings'

    @staticmethod
    def get_current_settings():
        country_info = BASE_INFO[settings.API_COUNTRY]

        country_region_id = (
            Region.objects.filter(
                name=country_info['name'],
                type=RegionType.COUNTRY,
            )
            .values_list('id', flat=True)
            .get()
        )

        boost_settings, _created = BoostSettings.objects.get_or_create(
            region_id=country_region_id,
        )

        return boost_settings


class BoostSettingsHistory(HistoryModel):
    model = models.ForeignKey(
        BoostSettings,
        on_delete=models.CASCADE,
        related_name='history',
    )

    class Meta:
        verbose_name = 'Settings history'
        verbose_name_plural = 'Settings history'


class BoostBanError(BaseException):
    pass


class BoostBan(ArchiveModel):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                name='unique_active_boost_ban',
                fields=['business'],
                condition=Q(status=BoostBanStatus.ACTIVE),
            )
        ]

    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
        db_index=True,
    )  # override from superclass, to have it indexed

    business = models.ForeignKey(
        related_name='boost_bans',
        to='business.Business',
        on_delete=models.PROTECT,
    )
    type = models.CharField(
        max_length=1,
        choices=BoostBanType.choices(),
        db_index=True,
    )
    active_from = models.DateField(db_index=True)
    active_till = models.DateField(db_index=True)
    length = models.CharField(choices=BoostBanLength.choices(), db_index=True)
    status = models.CharField(
        max_length=1,
        choices=BoostBanStatus.choices(),
        default=BoostBanStatus.PENDING,
        db_index=True,
    )
    blocking_commission = models.ForeignKey(
        'marketplace.MarketplaceCommission',
        on_delete=models.PROTECT,
        null=True,
    )
    reason = models.CharField(choices=BoostBanReason.choices())
    reason_description = models.CharField(null=True, blank=True)
    created_by = models.ForeignKey('user.User', on_delete=models.PROTECT)
    invoice_required = models.BooleanField(default=False)
    invoice_paid = models.BooleanField(default=False)
    notification_status = models.CharField(
        max_length=1,
        choices=BoostBanNotificationStatus.choices(),
        null=True,
    )
    notification_details = models.CharField(null=True, blank=True)

    @classmethod
    def get_current_ban(cls, business_id):
        return cls.objects.filter(
            business_id=business_id,
            status=BoostBanStatus.ACTIVE,
        ).first()

    def __str__(self):
        return (
            f'BoostBan of business {self.business_id} from {self.active_from} till'
            f' {self.active_till}'
        )


class BoostFraudSuspicion(ChangeArchivedModel):
    business_id = models.IntegerField(db_index=True)
    warning_type = models.CharField(
        max_length=1,
        choices=BoostFraudWarningType.choices(),
        default=BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT,
    )
    warning_visible = models.BooleanField(default=False)

    def __str__(self):
        return (
            f'id:{self.id}, business_id:{self.business_id}, warning_visible:{self.warning_visible}'
        )

    def get_warning_texts(self):
        match self.warning_type:
            case BoostFraudWarningType.UNFINISHED_APPOINTMENTS_COUNT:
                return FRAUD_UNFINISHED_APPOINTMENTS_COUNT_TEXTS
            case BoostFraudWarningType.PRICE_MANIPULATIONS:
                return FRAUD_PRICE_MANIPULATIONS_TEXTS
        raise ValueError(f'Undefined warning type {self.warning_type}')
