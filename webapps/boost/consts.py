from django.utils.translation import gettext_lazy as _


COMMISSION_REQUIRED_FIELDS = (
    'commission',
    'bottom_cap',
    'max',
    'minimum_commission',
    'marketplace',
)


PROMOTED_LABELS = {
    'promoted_label': _("Promoted"),
    'promoted_hint': _(
        "This partner's profile ranks higher in search results because they have purchased an "
        "additional paid service from Booksy to assist in acquiring new clients."
    ),
    'recommended_label': _("Booksy Recommended"),
    'recommended_hint': _(
        "The Booksy Recommended badge is available to partners who have:\n"
        "- purchased an additional paid service from Booksy to assist in acquiring new clients.\n"
        "- met certain requirements such as having a high average rating."
    ),
}


PROMOTED_LABELS_DSA = PROMOTED_LABELS.copy()
PROMOTED_LABELS_DSA['promoted_hint'] = _(
    "This partner's profile ranks higher in search results in part due to your search terms and "
    "because they have purchased an additional paid service from Booksy to assist in acquiring "
    "new clients.\n"
    "This ad is displayed on behalf of partner."
)

BOOST_LANDING_PAGE_URLS = {
    'us-es-mx': 'https://cf.booksy.com/us-es-boost-explainer',
    'us-es-es': 'https://cf.booksy.com/us-es-boost-explainer',
    'us': 'https://cf.booksy.com/boost-explainer',
    'pl': 'https://booksy.com/blog/pl/wszystko-o-boost/',
    'gb': 'https://cf.booksy.com/uk-ie-boost-explainer',
    'za': 'https://cf.booksy.com/za-boost-explainer',
    'br': 'https://support.booksy.com/hc/pt-br',
    'es': 'https://cf.booksy.com/es-boost-explainer',
}


FRAUD_UNFINISHED_APPOINTMENTS_COUNT_TEXTS = {
    'splash_header': _("We've detected violations of the Boost Terms & Conditions"),
    'splash_paragraph': _(
        "Your account maintains a high number of appointments that were not completed,"
        " which may indicate non-compliance with the Boost Terms & Conditions."
        "<br><br>"
        "Please bear in mind that non-compliance with the Boost Terms & Conditions will result"
        " in Boost suspension."
    ),
    'splash_button_label': _('Done'),
    'boost_warning_type': 'unfinished_appointments_warning',
    'alert_text_full': _(
        "Your account maintains a <b>high number of appointments that were not completed</b>,"
        " which may indicate non-compliance with the Boost Terms & Conditions"
        " and result in Boost suspension."
    ),
    'alert_text_short': _("A high number of appointments that were not completed!"),
    'email_text': _(
        "Your account maintains a high number of appointments that were not completed,"
        " which may indicate non-compliance with the"
        " <a href='{boost_terms_url}' target='_blank'>Boost Terms &amp; Conditions</a>."
    ),
}

FRAUD_PRICE_MANIPULATIONS_TEXTS = {
    'splash_header': _("We've detected violations of the Boost Terms & Conditions"),
    'splash_paragraph': _(
        "Your account maintains a high number of appointments with modified service prices,"
        " which may indicate non-compliance with the Boost Terms & Conditions."
        "<br><br>"
        "Please bear in mind that non-compliance with the Boost Terms & Conditions will result"
        " in Boost suspension."
    ),
    'splash_button_label': _('Done'),
    'boost_warning_type': 'price_manipulations_warning',
    'alert_text_full': _(
        "Your account maintains a <b>high number of appointments with modified service prices</b>,"
        " which may indicate non-compliance with the Boost Terms & Conditions"
        " and result in Boost suspension."
    ),
    'alert_text_short': _("A high number of appointments with modified service prices."),
    'email_text': _(
        "Your account maintains a high number of appointments with modified service prices,"
        " which may indicate non-compliance with the"
        " <a href='{boost_terms_url}' target='_blank'>Boost Terms &amp; Conditions</a>."
    ),
}
