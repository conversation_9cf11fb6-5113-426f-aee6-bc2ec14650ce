import braintree
from dateutil.relativedelta import relativedelta
from django.utils.translation import gettext_lazy as _

from lib.enums import StrChoicesEnum, StrEnum


class BoostAppointmentStatus(StrChoicesEnum):
    FREE = 'free_trial', _('Free trial')
    NOSHOW = 'no_show', _('No show')
    NOSHOW_OVERDUE = 'no_show_overdue', _('No show overdue')
    CLAIMED = 'claimed', _('Claimed')
    CLAIM_PENDING = 'claim_pending', _('Claim pending')
    CLAIM_DENIED = 'claim_denied', _('Claim denied')
    CLAIM_OVERDUE = 'claim_overdue', _('Claim overdue')
    OFFLINE = 'offline', _('Offline')
    PAYABLE = 'payable', _('Payable')

    @classmethod
    def payable_statuses(cls):
        return (
            cls.PAYABLE,
            cls.CLAIM_PENDING,
            cls.CLAIM_DENIED,
            cls.CLAIM_OVERDUE,
            cls.NOSHOW_OVERDUE,
        )

    @classmethod
    def claimed_by_merchant_statuses(cls):
        return (
            cls.CLAIMED,
            cls.CLAIM_PENDING,
            cls.CLAIM_DENIED,
            cls.CLAIM_OVERDUE,
        )

    @classmethod
    def claimed_accepted_statuses(cls):
        return (
            cls.CLAIMED,
            cls.CLAIM_OVERDUE,
        )


class MarketplaceTransactionStatusEnum(StrChoicesEnum):
    # internal statuses
    FAILED = 'failed', _('Failed')
    SKIPPED = 'skipped', _('Skipped')
    OUTDATED = 'outdated', _('Outdated')

    # braintree statuses
    AUTHORIZATION_EXPIRED = braintree.Transaction.Status.AuthorizationExpired, _(
        'Authorization expired'
    )
    SETTLEMENT_DECLINED = braintree.Transaction.Status.SettlementDeclined, _('Settlement declined')
    GATEWAY_REJECTED = braintree.Transaction.Status.GatewayRejected, _('Gateway rejected')
    PROCESSOR_DECLINED = braintree.Transaction.Status.ProcessorDeclined, _('Processor declined')
    VOIDED = braintree.Transaction.Status.Voided, _('Voided')
    SETTLED = braintree.Transaction.Status.Settled, _('Settled')
    AUTHORIZED = braintree.Transaction.Status.Authorized, _('Authorized')
    AUTHORIZING = braintree.Transaction.Status.Authorizing, _('Authorizing')
    SETTLEMENT_PENDING = braintree.Transaction.Status.SettlementPending, _('Settlement Pending')
    SETTLING = braintree.Transaction.Status.Settling, _('Settling')
    SUBMITTED_FOR_SETTLEMENT = braintree.Transaction.Status.SubmittedForSettlement, _(
        'Submitted for settlement'
    )

    # stripe
    REQUIRES_PAYMENT_METHOD = (
        'requires_payment_method',
        'Intent created and requires a Payment Method to be attached.',
    )
    REQUIRES_CONFIRMATION = ('requires_confirmation', 'Intent is ready to be confirmed.')
    REQUIRES_ACTION = (
        'requires_action',
        'Payment Method require additional action, such as 3D secure.',
    )
    PROCESSING = ('processing', 'Required actions have been handled.')
    REQUIRES_CAPTURE = (
        'requires_capture',
        'Capture the funds on the cards which have been put on holds.',
    )
    CANCELED = (
        'canceled',
        'Cancellation invalidates the intent for future confirmation and cannot be undone.',
    )
    SUCCEEDED = ('succeeded', 'succeeded')

    @classmethod
    def succeeded_statuses(cls):
        return (
            cls.SETTLED,
            cls.SUCCEEDED,
        )

    @classmethod
    def failed_statuses(cls):
        return (
            # internal
            cls.FAILED,
            # braintree
            cls.AUTHORIZATION_EXPIRED,
            cls.SETTLEMENT_DECLINED,
            cls.GATEWAY_REJECTED,
            cls.PROCESSOR_DECLINED,
            cls.VOIDED,
            # stripe
            cls.REQUIRES_PAYMENT_METHOD,
        )

    @classmethod
    def finished_statuses(cls):
        return (
            # internal
            cls.SKIPPED,
            cls.OUTDATED,
            # braintree
            cls.SETTLED,
            # stripe
            cls.CANCELED,
            cls.SUCCEEDED,
        )

    @classmethod
    def processing_statuses(cls):
        return (
            # braintree
            cls.AUTHORIZED,
            cls.AUTHORIZING,
            cls.SETTLEMENT_PENDING,
            cls.SETTLING,
            cls.SUBMITTED_FOR_SETTLEMENT,
            # stripe
            cls.REQUIRES_CONFIRMATION,
            cls.REQUIRES_ACTION,
            cls.PROCESSING,
            cls.REQUIRES_CAPTURE,
        )

    @classmethod
    def revertable_statuses(cls):
        return [*cls.processing_statuses(), *cls.finished_statuses(), cls.VOIDED]

    @classmethod
    def refundable_statuses(cls):
        return (
            # braintree
            cls.SETTLING,
            cls.SETTLED,
            # stripe
            cls.CANCELED,
            cls.SUCCEEDED,
            cls.REQUIRES_CONFIRMATION,
            cls.REQUIRES_ACTION,
            cls.PROCESSING,
            cls.REQUIRES_CAPTURE,
        )


class MarketplaceTransactionType(StrChoicesEnum):
    CHARGE = 'C', _('Charge')
    REFUND = 'R', _('Refund')


class MarketplaceTransactionDeclineType(StrChoicesEnum):
    HARD_DECLINE = 'H', _('hard_declined')
    SOFT_DECLINE = 'S', _('soft_declined')


class TransactionPaymentSource(StrChoicesEnum):
    BRAINTREE = 'B', _('Braintree')
    OFFLINE = 'O', _('Offline')
    STRIPE = 'S', _('Stripe')

    @classmethod
    def online_methods(cls):
        return (
            cls.BRAINTREE,
            cls.STRIPE,
        )


class BoostBadge(StrChoicesEnum):
    FIRST_VISIT = 'first_visit', 'First visit'
    RETURNED = 'returned', 'Returned'
    FREE_TRIAL = 'free_trial', 'Free Trial'
    NO_SHOW = 'no_show', 'No show'
    NO_SHOW_OVERDUE = 'no_show_overdue', 'No show overdue'
    CLAIMED = 'claimed', 'Claimed'
    CLAIM_PENDING = 'claim_pending', 'Claim Pending'
    CLAIM_DENIED = 'claim_denied', 'Claim denied'
    CLAIM_OVERDUE = 'claim_overdue', 'Claim overdue'


class BoostPDFFIle(StrChoicesEnum):
    WALKIN = 'walkin', 'add_a_new_walk-in_client.pdf'
    INSTAGRAM = 'instagram', 'book_more_clients_with_the_instagram.pdf'
    FACEBOOK = 'facebook', 'set_up_booking_through_facebook.pdf'
    SHARE = 'share', 'share_your_booksy_profile_link.pdf'


class BoostPDFAppType(StrChoicesEnum):
    SOLO = 'solo', 'statics/pdf/boost/solo/'
    OLD_CUSTOMER = 'old_customer', 'statics/pdf/boost/'


class MarketplaceTransactionProgressStatus(StrChoicesEnum):
    OPEN = 'O', 'open'
    PROGRESS = 'P', 'in progress'
    CLOSED = 'C', 'closed'


class MarketplaceTransactionStatusResponseType(StrChoicesEnum):
    SOFT_DECLINED = 'soft_declined', _('Soft declined')
    HARD_DECLINED = 'hard_declined', _('Hard declined')
    APPROVED = 'approved', _('Approved')


class MarketplaceTransactionStage(StrChoicesEnum):
    REFUNDED = 'R', _('Refunded')
    CHARGED = 'C', _('Charged')
    NOT_CHARGED = 'N', _('Not Charged')


class MarketplaceCommissionFor(StrChoicesEnum):
    BUSINESS = 'B', 'Business'
    REGION = 'R', 'Region'


class BoostClientType(StrChoicesEnum):
    NEW = 'new'
    RETURNING = 'returning'
    CLAIMED = 'claimed'


class BoostClaimRequestStatus(StrChoicesEnum):
    SENT = 'sent'
    ACCOUNT_NOTICE = 'account_notice'
    OVERDUE = 'overdue'


class BoostWarningType(StrChoicesEnum):
    PRE_SUSPENSION = 'pre_suspension'


class BoostBanType(StrChoicesEnum):
    SUSPENSION = 'S', 'Suspension'
    TERMINATION = 'T', 'Termination'


class BoostBanLength(StrChoicesEnum):
    DAYS_30 = '30 days', '30 days'
    MONTHS_3 = '3 months', '3 months'
    MONTHS_6 = '6 months', '6 months'
    MONTHS_12 = '12 months', '12 months'

    @classmethod
    def get_relativedelta(cls, ban_length):
        match ban_length:
            case cls.DAYS_30:
                return relativedelta(days=30)
            case cls.MONTHS_3:
                return relativedelta(months=3)
            case cls.MONTHS_6:
                return relativedelta(months=6)
            case cls.MONTHS_12:
                return relativedelta(months=12)
            case _:
                raise ValueError(f'Missing timedelta for {ban_length}')


ALLOWED_BOOST_BAN_LENGTHS_FOR_TYPE = {
    BoostBanType.SUSPENSION: (BoostBanLength.DAYS_30,),
    BoostBanType.TERMINATION: (
        BoostBanLength.MONTHS_3,
        BoostBanLength.MONTHS_6,
        BoostBanLength.MONTHS_12,
    ),
}


class BoostBanStatus(StrChoicesEnum):
    PENDING = 'P', 'Pending'
    ACTIVE = 'A', 'Active'
    ENDED = 'E', 'Ended'
    REMOVED = 'R', 'Removed'


class BoostBanNotificationStatus(StrChoicesEnum):
    PENDING = 'P', 'Pending'
    SENT = 'S', 'Sent'
    DELIVERED = 'D', 'Delivered'
    FAILED = 'F', 'Failed'


class BoostBanReason(StrChoicesEnum):
    ADDING_BB_BEFORE_CB = 'adding_bb_before_cb', 'Adding BB before CB'
    CANCELING_CB_AND_ADDING_BB = 'canceling_cb_and_adding_bb', 'Canceling CB and adding BB'
    CANCELING_CB = 'canceling_cb', 'Canceling CB'
    SERVICE_MANIPULATION = 'service_manipulation', 'Service manipulation'
    PRICE_MANIPULATION = 'price_manipulation', 'Price manipulation'
    NO_SHOW = 'no_show', 'No show'
    OTHER = 'other', 'Other'


class BCIBoostEligibility(StrEnum):
    UNKNOWN = 'UNKNOWN'
    NOT_CHARGEABLE = 'NOT CHARGEABLE'
    CHARGEABLE_ONLY = 'CHARGEABLE ONLY'
    CHARGEABLE_AND_PAYABLE = 'CHARGEABLE AND PAYABLE'


class BoostClientCardStatus(StrChoicesEnum):
    EXEMPTED = 'X', 'Exempted'


class BoostFraudWarningType(StrChoicesEnum):
    UNFINISHED_APPOINTMENTS_COUNT = 'C', 'Unfinished Appointments Count'
    PRICE_MANIPULATIONS = 'P', 'Price Manipulations'
