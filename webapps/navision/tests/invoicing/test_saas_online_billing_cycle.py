import uuid
from datetime import datetime, timedelta, date
from decimal import Decimal
from unittest.mock import patch

import pytest
from django.db.models import Q
from django.test import TestCase, override_settings
from django.utils.timezone import make_aware
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC

from lib.feature_flag.feature.navision import (
    NavisionSaaSOnlineNewApproachEnabledFlag,
)
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from service.billing.tests import gen_func
from webapps.billing.enums import (
    TransactionStatus,
    ProductType,
    PaymentProcessorType,
)
from webapps.billing.models import (
    BillingTransaction,
    BillingCycleProductCharge,
    BillingSubscription,
    BillingCycle,
)
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_integration_enabled_recipe
from webapps.navision.enums import InvoicePaymentSource, InvoicePaymentSourceType, InvoiceService
from webapps.navision.models import (
    Invoice,
    InvoiceItem,
    InvoicingError,
    InvoicingSummary,
    Merchant,
    BusinessInvoiceSummary,
)
from webapps.navision.tasks import create_saas_online_invoices_based_on_billing_cycle_task
from webapps.purchase.models import SubscriptionBuyer
from webapps.user.models import User

baker.generators.add('lib.interval.fields.IntervalField', gen_func)


class TestNewSaaSMixin(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        navision_integration_enabled_recipe.make(
            auto_run_saas_online_invoicing=True,
            charge_to_invoice_delay=0,
        )

    def setUp(self) -> None:
        self.merchant = baker.make(Merchant)
        self.buyer = baker.make(
            SubscriptionBuyer,
            merchant=self.merchant,
        )
        self.business = baker.make(
            Business,
            payment_source=Business.PaymentSource.BRAINTREE_BILLING,
            has_new_billing=True,
            status=Business.Status.PAID,
            buyer=self.buyer,
            name='super business name',
        )

    def check_errors(self):
        if InvoicingError.objects.exists():
            self.fail('\n'.join(error.error for error in InvoicingError.objects.all()))

    def create_charge(
        self,
        billing_cycle=None,
        business=None,
        product_type=ProductType.SAAS,
        unit_price=60.0,
        gross_unit_price=None,
        discounted_price=None,
        gross_discounted_price=None,
        sms_amount=0,
        usage_from=None,
        usage_to=None,
        quantity=1,
    ):
        if not billing_cycle:
            billing_cycle = baker.make(
                BillingCycle,
                business=business or self.business,
                date_start=tznow() - timedelta(days=1),
                date_end=tznow() + timedelta(days=29),
                is_open=True,
            )

        usage_from = usage_from or billing_cycle.date_start
        usage_to = usage_to or billing_cycle.date_end

        if gross_unit_price is None:
            gross_unit_price = unit_price
        if discounted_price is None:
            discounted_price = unit_price
        if gross_discounted_price is None:
            gross_discounted_price = (
                discounted_price if gross_unit_price == unit_price else gross_unit_price
            )

        final_price = Decimal(discounted_price) * quantity
        gross_final_price = Decimal(gross_discounted_price) * quantity

        total_price = Decimal(unit_price) * quantity
        gross_total_price = Decimal(gross_unit_price) * quantity

        discount_granted = Decimal(total_price) - Decimal(final_price)
        gross_discount_granted = Decimal(gross_total_price) - Decimal(gross_final_price)

        charge = baker.make(
            BillingCycleProductCharge,
            product__product_type=product_type,
            product__sms_amount=sms_amount,
            usage_from=usage_from,
            usage_to=usage_to,
            billing_cycle=billing_cycle,
            unit_price=unit_price,
            gross_unit_price=gross_unit_price,
            discounted_price=discounted_price,
            gross_discounted_price=gross_discounted_price,
            discount_granted=discount_granted,
            gross_discount_granted=gross_discount_granted,
            final_price=final_price,
            gross_final_price=gross_final_price,
            total_price=total_price,
            gross_total_price=gross_total_price,
            quantity=quantity,
            currency='USD',
        )

        return billing_cycle, charge

    @staticmethod
    def create_transaction(business, billing_cycle, status=TransactionStatus.CHARGED, amount=60):
        return baker.make(
            BillingTransaction,
            amount=amount,
            business=business,
            status=status,
            external_id=str(uuid.uuid4()) if amount > 0 else None,
            billing_cycle=billing_cycle,
            currency='USD',
            payment_processor=PaymentProcessorType.STRIPE if amount > 0 else None,
        )


@override_settings(
    NAVISION_SPLIT_INVOICES_BASED_ON_BILLING_CYCLE=False,
    SUPPORTED_INVOICE_PAYMENT_METHODS={
        Business.PaymentSource.BRAINTREE: {
            'SaaS',
            'Boost',
        },
        Business.PaymentSource.BRAINTREE_BILLING: {
            'SaaS',
            'Boost',
        },
    },
    NAVISION_BANK_CODES_PER_SOURCE={
        'O': 'Offline Bank',
        'B': 'Online Bank',
    },
)
@override_eppo_feature_flag({NavisionSaaSOnlineNewApproachEnabledFlag.flag_name: True})
class TestSaaSOnlineInvoicing(TestNewSaaSMixin):
    @parameterized.expand(
        [
            ('𝐸𝑠𝑡𝑒𝑡𝑖𝑐 𝐿𝑖𝑝𝑠 𝐶𝑙𝑖𝑛𝑖𝑐 𝟡𝟡𝟳 💋', 'Estetic Lips Clinic 997'),
            ('𝓗𝓮𝓵𝓵𝓸 𝓾𝓼𝓮𝓻', 'Hello user'),
            ('   Łódka Masażysta 34   ', 'Łódka Masażysta 34'),
        ]
    )
    def test_invoice_business_name(self, business_name, clean_name):
        self.business.name = business_name
        self.business.save()

        self.create_charge()

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(1, Invoice.objects.count())
        invoice = Invoice.objects.first()
        self.assertEqual(clean_name, invoice.business_name)

    def test_invoice_header(self):
        billing_cycle, _ = self.create_charge()
        self.create_transaction(self.business, billing_cycle)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(1, Invoice.objects.count())
        invoice = Invoice.objects.first()
        self.assertEqual(Invoice.DocumentType.VAT, invoice.document_type)
        self.assertEqual(self.business.id, invoice.business_id)
        self.assertEqual(self.business.name, invoice.business_name)
        self.assertEqual(self.merchant.id, invoice.merchant_id)
        self.assertEqual(invoice.currency, 'USD')
        self.assertEqual(invoice.source, InvoicePaymentSource.HOF)
        self.assertIsNone(invoice.bank_code)

        billing_cycle_start = billing_cycle.date_start.date()
        self.assertEqual(invoice.invoice_date, billing_cycle_start)
        self.assertEqual(invoice.sales_date, billing_cycle_start)
        self.assertEqual(invoice.payment_due_date, billing_cycle_start)

    def test_saas_only_charge(self):
        billing_cycle, charge = self.create_charge()
        transaction = self.create_transaction(self.business, billing_cycle)

        tax_additional_data = {'id': 1, 'area': 'zip', 'tax_area_code': '90210'}
        charge.tax_additional_data = tax_additional_data
        charge.save()

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        invoice = Invoice.objects.first()
        item = invoice.items.first()
        summary = InvoicingSummary.objects.first()

        self.assertTrue(invoice.approved)
        self.assertEqual(invoice.service, InvoiceService.SAAS)
        self.assertEqual(item.product, 'Booksy Subscription')
        self.assertEqual(item.service, Invoice.Service.SAAS)
        self.assertEqual(item.base_gross_value, charge.gross_unit_price)
        self.assertEqual(item.quantity, charge.quantity)
        self.assertEqual(item.charging_dt, transaction.created)
        self.assertEqual(item.billing_cycle_start, charge.usage_from)
        self.assertEqual(item.billing_cycle_end, charge.usage_to - timedelta(days=1))
        self.assertEqual(item.transaction_id, transaction.external_id)
        self.assertEqual(item.object_id, charge.id)
        self.assertTrue(item.charge_completed)
        self.assertDictEqual(item.tax_additional_data, tax_additional_data)
        self.assertEqual(summary.service, InvoiceService.SAAS_PER_BC)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

    def test_no_transaction(self):
        _, charge = self.create_charge()

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        invoice = Invoice.objects.first()
        item = invoice.items.first()
        summary = InvoicingSummary.objects.first()

        self.assertTrue(invoice.approved)
        self.assertEqual(invoice.service, InvoiceService.SAAS)
        self.assertEqual(item.product, 'Booksy Subscription')
        self.assertEqual(item.service, Invoice.Service.SAAS)
        self.assertEqual(item.base_gross_value, charge.gross_unit_price)
        self.assertEqual(item.quantity, charge.quantity)
        self.assertIsNone(item.charging_dt)
        self.assertEqual(item.billing_cycle_start, charge.usage_from)
        self.assertEqual(item.billing_cycle_end, charge.usage_to - timedelta(days=1))
        self.assertIsNone(item.transaction_id)
        self.assertEqual(item.object_id, charge.id)
        self.assertFalse(item.charge_completed)
        self.assertEqual(summary.service, InvoiceService.SAAS_PER_BC)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

    def test_each_product_type_have_correct_caption(self):
        billing_cycle, _ = self.create_charge(
            unit_price=80,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=20,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.1,
            quantity=200,
        )

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)

        self.assertEqual(
            InvoiceItem.objects.filter(
                product='200 x SMS Delivery',
                base_gross_value=0.1 * 200,
            ).count(),
            1,
        )

        # from the create_saas_transaction method:
        self.assertEqual(
            InvoiceItem.objects.filter(
                product='Booksy Subscription',
                base_gross_value=80,
            ).count(),
            1,
        )

        self.assertEqual(
            InvoiceItem.objects.filter(
                product='Staffer',
                base_gross_value=20,
            ).count(),
            1,
        )

    def test_saas_sms_with_postpaid_charge(self):
        external_id = str(uuid.uuid4())
        billing_cycle = baker.make(
            BillingCycle,
            business=self.business,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
        )

        baker.make(
            BillingTransaction,
            amount=31,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=billing_cycle,
            payment_processor=PaymentProcessorType.STRIPE,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=30,
        )
        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=5,
            discounted_price=0,
            quantity=2,
        )

        _, sms_charge = self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.005,
            quantity=200,
            usage_from=make_aware(datetime(2021, 8, 28), timezone=UTC),
            usage_to=make_aware(datetime(2021, 9, 28), timezone=UTC),
        )

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 1)

        self.assertEqual(
            InvoiceItem.objects.count(),
            2,
            'Staffer row should be skipped, because this addon was free.'
            'If you send zero row to navision something weird is happening (would not recommend).',
        )
        self.assertEqual(InvoiceItem.objects.filter(service=Invoice.Service.SAAS).count(), 1)
        self.assertEqual(InvoiceItem.objects.filter(service=Invoice.Service.SMS).count(), 1)

        invoice = Invoice.objects.first()
        summary = InvoicingSummary.objects.first()

        billing_cycle_start = billing_cycle.date_start.date()
        self.assertEqual(invoice.invoice_date, billing_cycle_start)
        self.assertEqual(invoice.sales_date, billing_cycle_start)
        self.assertEqual(invoice.payment_due_date, billing_cycle_start)
        self.assertEqual(invoice.source, InvoicePaymentSource.HOF)

        self.assertEqual(summary.service, InvoiceService.SAAS_PER_BC)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        saas_item = invoice.items.filter(service=Invoice.Service.SAAS).first()

        self.assertEqual(saas_item.base_gross_value, Decimal('30.0'))
        self.assertEqual(saas_item.payment_source, InvoicePaymentSource.HOF)
        self.assertEqual(saas_item.quantity, 1)
        self.assertEqual(saas_item.transaction_id, external_id)
        self.assertTrue(saas_item.charge_completed)

        sms_item = invoice.items.filter(service=Invoice.Service.SMS).first()

        self.assertEqual(sms_item.base_gross_value, Decimal('0.005') * 200)
        self.assertEqual(sms_item.payment_source, InvoicePaymentSource.HOF)
        self.assertEqual(sms_item.quantity, 1)
        self.assertEqual(sms_item.transaction_id, external_id)
        self.assertEqual(sms_item.billing_cycle_start, sms_charge.usage_from)
        self.assertEqual(sms_item.billing_cycle_end, sms_charge.usage_to - timedelta(days=1))
        self.assertTrue(saas_item.charge_completed)

    @override_settings(
        NAVISION_SPLIT_INVOICES_BASED_ON_BILLING_CYCLE=True,
    )
    def test_sms_and_saas_are_split(self):
        external_id = str(uuid.uuid4())
        billing_cycle = baker.make(
            BillingCycle,
            business=self.business,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
        )

        baker.make(
            BillingTransaction,
            amount=30,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=billing_cycle,
            currency='USD',
        )
        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=25,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=5,
            quantity=2,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.006,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 7, 1), timezone=UTC),
            quantity=200,
        )

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 2)

        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertEqual(summary.service, InvoiceService.SAAS_PER_BC)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

        saas_item = InvoiceItem.objects.filter(
            service=InvoiceService.SAAS,
        ).first()

        staffer_item = InvoiceItem.objects.filter(
            service=InvoiceService.STAFFERS,
        ).first()

        self.assertEqual(saas_item.invoice_id, staffer_item.invoice_id)

        sms_item = InvoiceItem.objects.filter(
            service=InvoiceService.SMS,
        ).first()

        self.assertNotEqual(
            saas_item.invoice_id,
            sms_item.invoice_id,
        )

    def test_invoicing_with_offset_to_last_month(self):
        external_id = str(uuid.uuid4())

        previous_cycle = baker.make(
            BillingCycle,
            business=self.business,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
        )

        baker.make(
            BillingTransaction,
            amount=31,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=previous_cycle,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=previous_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.005,
            quantity=200,
        )

        expected_external_id = str(uuid.uuid4())

        yet_previous_cycle = baker.make(
            BillingCycle,
            business=self.business,
            date_start=tznow() - timedelta(days=31),
            date_end=tznow() - timedelta(days=1),
        )

        baker.make(
            BillingTransaction,
            amount=31,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=expected_external_id,
            billing_cycle=yet_previous_cycle,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=yet_previous_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.005,
            quantity=200,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 8, 1), timezone=UTC),
        )

        invoicing_target = (tznow() - timedelta(days=31)).date()
        invoicing_day = make_aware(datetime.combine(invoicing_target, datetime.min.time()))
        create_saas_online_invoices_based_on_billing_cycle_task(invoicing_day)

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertEqual(summary.service, InvoiceService.SAAS_PER_BC)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

        item = InvoiceItem.objects.first()

        self.assertEqual(item.transaction_id, expected_external_id)
        self.assertEqual(item.billing_cycle_start, make_aware(datetime(2021, 7, 1), timezone=UTC))
        self.assertEqual(item.billing_cycle_end, make_aware(datetime(2021, 7, 31), timezone=UTC))

    def test_product_is_not_double_invoiced(self):
        _, charge = self.create_charge(
            product_type=ProductType.SAAS,
            gross_discounted_price=14,
        )
        baker.make(
            InvoiceItem,
            product_identifier=f'BillingCycleProductCharge: {charge.id}, '
            f'Transaction identifier: {None}, '
            f'Provider: {None}',
            base_gross_value=66,
        )

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 1)
        self.assertIn('was already invoiced', InvoicingError.objects.first().message)

    def test_business_already_invoiced(self):
        billing_cycle, _ = self.create_charge()
        with freeze_time(billing_cycle.date_start):
            self.create_transaction(self.business, billing_cycle)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

    def test_two_billing_cycles_two_transactions(self):
        bc_date_start = tznow() - timedelta(days=1)
        bc_1 = baker.make(
            BillingCycle,
            business=self.business,
            date_start=bc_date_start,
            date_end=tznow() + timedelta(days=29),
            is_open=False,
        )
        bc_2 = baker.make(
            BillingCycle,
            business=self.business,
            date_start=bc_date_start,
            date_end=tznow() + timedelta(days=29),
            is_open=True,
        )
        self.create_charge(bc_1, unit_price=23)
        self.create_charge(bc_2, unit_price=67)
        with freeze_time(bc_date_start):
            _tr_1 = self.create_transaction(self.business, bc_1, amount=23)
            tr_2 = self.create_transaction(self.business, bc_2, amount=67)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        item = InvoiceItem.objects.first()
        self.assertEqual(tr_2.amount, item.base_gross_value)

    def test_one_failed_transaction(self):
        billing_cycle, _ = self.create_charge()
        with freeze_time(billing_cycle.date_start):
            tr = self.create_transaction(self.business, billing_cycle, TransactionStatus.FAILED)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        item = InvoiceItem.objects.first()
        self.assertEqual(tr.external_id, item.transaction_id)
        self.assertEqual(billing_cycle.date_start.date(), item.charging_dt.date())
        self.assertFalse(item.charge_completed)

    def test_two_failed_transactions(self):
        billing_cycle, _ = self.create_charge()
        with freeze_time(billing_cycle.date_start):
            tr_1 = self.create_transaction(self.business, billing_cycle, TransactionStatus.FAILED)

        with freeze_time(billing_cycle.date_start + timedelta(days=3)):
            self.create_transaction(self.business, billing_cycle, TransactionStatus.FAILED)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        item = InvoiceItem.objects.first()
        self.assertEqual(tr_1.external_id, item.transaction_id)
        self.assertEqual(billing_cycle.date_start.date(), item.charging_dt.date())
        self.assertFalse(item.charge_completed)

    def test_failed_and_charged_transactions(self):
        billing_cycle, _ = self.create_charge()
        with freeze_time(billing_cycle.date_start):
            self.create_transaction(self.business, billing_cycle, TransactionStatus.FAILED)

        three_days_later = billing_cycle.date_start + timedelta(days=3)
        with freeze_time(three_days_later):
            tr_2 = self.create_transaction(self.business, billing_cycle, TransactionStatus.CHARGED)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        item = InvoiceItem.objects.first()
        self.assertEqual(tr_2.external_id, item.transaction_id)
        self.assertEqual(three_days_later.date(), item.charging_dt.date())
        self.assertTrue(item.charge_completed)

    def test_gross_price_with_discount(self):
        subscription = baker.make(BillingSubscription, business=self.business)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
            subscription=subscription,
            business=self.business,
        )

        self.create_charge(billing_cycle=billing_cycle, unit_price=10, discounted_price=3)

        baker.make(
            BillingTransaction,
            amount=5,
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        create_saas_online_invoices_based_on_billing_cycle_task.delay()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 2)

        item = InvoiceItem.objects.filter(service=InvoiceService.SAAS).first()
        discount_item = InvoiceItem.objects.filter(service=InvoiceService.DISCOUNT).first()

        self.assertEqual(item.base_gross_value, Decimal('10.00'))
        self.assertEqual(item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(discount_item.base_gross_value, Decimal('7.00'))  # 10.00 - 3.00
        self.assertEqual(discount_item.discount_gross_value, Decimal('0.00'))

    def test_discount_and_base_gross_value_is_rounded_on_our_side(self):
        subscription = baker.make(BillingSubscription, business=self.business)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
            subscription=subscription,
            business=self.business,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price='350.00',
            gross_unit_price='437.50',
            discounted_price='262.50',
            gross_discounted_price='328.125',
        )
        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price='0.005',
            gross_unit_price='0.0056',
            quantity=100,
        )

        create_saas_online_invoices_based_on_billing_cycle_task.delay()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)

        saas_item = InvoiceItem.objects.filter(service=InvoiceService.SAAS).first()
        sms_item = InvoiceItem.objects.filter(service=InvoiceService.SMS).first()
        discount_saas_item = InvoiceItem.objects.filter(service=InvoiceService.DISCOUNT).first()

        self.assertEqual(saas_item.base_gross_value, Decimal('437.50'))
        self.assertEqual(saas_item.quantity, 1)
        self.assertEqual(saas_item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(sms_item.base_gross_value, Decimal('0.0056') * 100)
        self.assertEqual(sms_item.quantity, 1)
        self.assertEqual(sms_item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(discount_saas_item.base_gross_value, Decimal('109.38'))  # 437.50 - 328.125
        self.assertEqual(discount_saas_item.quantity, 1)
        self.assertEqual(discount_saas_item.discount_gross_value, Decimal('0.00'))

    def test_final_price_is_used_if_quantity_field_is_not_used(self):
        subscription = baker.make(BillingSubscription, business=self.business)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
            subscription=subscription,
            business=self.business,
        )

        _, saas_charge = self.create_charge(
            billing_cycle=billing_cycle,
            unit_price='350.00',
            gross_unit_price='437.50',
            discounted_price='262.50',
            gross_discounted_price='328.125',
            quantity=2,
        )
        # bogus price to show that this field will be taken into account no matter what
        saas_charge.gross_total_price = '2137.0'
        saas_charge.save()

        _, sms_charge = self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price='0.005',
            gross_unit_price='0.0056',
            quantity=100,
        )

        sms_charge.gross_final_price = '21.37'  # bogus price again
        sms_charge.save()

        create_saas_online_invoices_based_on_billing_cycle_task.delay()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)

        saas_item = InvoiceItem.objects.filter(service=InvoiceService.SAAS).first()
        sms_item = InvoiceItem.objects.filter(service=InvoiceService.SMS).first()
        discount_saas_item = InvoiceItem.objects.filter(service=InvoiceService.DISCOUNT).first()

        self.assertEqual(saas_item.product, '2 x Booksy Subscription')
        self.assertEqual(saas_item.base_gross_value, Decimal('2137.0'))
        self.assertEqual(saas_item.quantity, 1)
        self.assertEqual(saas_item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(sms_item.product, '100 x SMS Delivery')
        self.assertEqual(sms_item.base_gross_value, Decimal('21.37'))
        self.assertEqual(sms_item.quantity, 1)
        self.assertEqual(sms_item.discount_gross_value, Decimal('0.00'))

        self.assertIsNotNone(discount_saas_item)

    @parameterized.expand(
        [
            (
                3,
                make_aware(datetime(2021, 3, 30), timezone=UTC),
                make_aware(datetime(2021, 3, 29), timezone=UTC),
            ),
            (
                6,
                make_aware(datetime(2021, 6, 29), timezone=UTC),
                make_aware(datetime(2021, 6, 28), timezone=UTC),
            ),
            (
                12,
                make_aware(datetime(2021, 12, 30), timezone=UTC),
                make_aware(datetime(2021, 12, 29), timezone=UTC),
            ),
        ]
    )
    def test_saas_invoicing_sub_longer_than_one_month(
        self, sub_duration, sub_date_end, exp_cycle_end
    ):
        sub = baker.make(
            BillingSubscription,
            subscription_duration=sub_duration,
            date_end=sub_date_end,
            business=self.business,
        )
        external_id = str(uuid.uuid4())
        cycle = baker.make(
            BillingCycle,
            subscription=sub,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
            business=self.business,
        )
        transaction = baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
            subscription=sub,
        )

        _, charge = self.create_charge(cycle)

        tax_additional_data = {'id': 1, 'area': 'zip', 'tax_area_code': '90210'}
        charge.tax_additional_data = tax_additional_data
        charge.save()

        self.assertTrue(transaction.subscription.is_long_subscription)

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')
        self.assertEqual(summary.service, InvoiceService.SAAS_PER_BC)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        invoice = Invoice.objects.first()
        item = InvoiceItem.objects.first()

        self.assertEqual(invoice.currency, 'USD')
        self.assertIsNone(invoice.bank_code)
        self.assertEqual(invoice.source, InvoicePaymentSource.HOF)
        self.assertEqual(item.product, 'Booksy Subscription')
        self.assertEqual(item.service, Invoice.Service.SAAS)
        self.assertEqual(item.base_gross_value, charge.gross_unit_price)
        self.assertEqual(item.quantity, charge.quantity)
        self.assertEqual(item.charging_dt, transaction.created)
        self.assertEqual(item.billing_cycle_start, charge.usage_from)
        self.assertEqual(item.billing_cycle_end, exp_cycle_end)
        self.assertEqual(item.transaction_id, transaction.external_id)
        self.assertEqual(item.object_id, charge.id)
        self.assertTrue(item.charge_completed)
        self.assertDictEqual(item.tax_additional_data, tax_additional_data)

    def test_no_invoice_created_if_no_charge_sub_longer_than_one_month(self):
        sub = baker.make(
            BillingSubscription,
            subscription_duration=3,
            date_end=make_aware(datetime(2021, 4, 1), timezone=UTC),
            business=self.business,
        )
        external_id = str(uuid.uuid4())
        cycle = baker.make(
            BillingCycle,
            subscription=sub,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
            business=self.business,
        )
        baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
        )

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 0)

    def test_long_sub_invoiced_as_one_month_if_incorrect_duration(self):
        sub = baker.make(
            BillingSubscription,
            subscription_duration=5,
            date_end=make_aware(datetime(2021, 6, 1), timezone=UTC),
            business=self.business,
        )

        external_id = str(uuid.uuid4())
        cycle = baker.make(
            BillingCycle,
            subscription=sub,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
            business=self.business,
        )
        baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
        )

        _, charge = self.create_charge(cycle)

        tax_additional_data = {'id': 1, 'area': 'zip', 'tax_area_code': '90210'}
        charge.tax_additional_data = tax_additional_data
        charge.save()

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        item = InvoiceItem.objects.first()

        self.assertEqual(item.billing_cycle_end, charge.usage_to - timedelta(days=1))

    def test_discount_as_separate_item(self):
        subscription = baker.make(BillingSubscription, business=self.business)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=1),
            date_end=tznow() + timedelta(days=29),
            subscription=subscription,
            business=self.business,
        )
        _, charge = self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=10,
            discounted_price=7,
            quantity=1,
        )
        charge.refresh_tax_related_data()

        _, charge = self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=10,
            discounted_price=6,
            quantity=7,
        )
        charge.refresh_tax_related_data()

        _, charge = self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=0.001,
            discounted_price=0.001,
            quantity=547,
            business=self.business,
        )
        charge.refresh_tax_related_data()

        baker.make(
            BillingTransaction,
            amount=49.55,  # 49.547
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(5, InvoiceItem.objects.count())

        discount_items = InvoiceItem.objects.filter(
            service=InvoiceService.DISCOUNT,
            base_gross_value__in=[3, 28],
            discount_gross_value=Decimal('0.00'),
        )
        base_items = InvoiceItem.objects.filter(
            ~Q(service=InvoiceService.DISCOUNT),
            base_gross_value__in=[70, 10, 0.55],
            discount_gross_value=Decimal('0.00'),
        )

        self.assertEqual(2, discount_items.count())
        self.assertEqual(3, base_items.count())

    def test_invoicing_if_offline_invoice_exists(self):
        offline_invoice = baker.make(
            Invoice,
            service=InvoiceService.SAAS,
            source=InvoicePaymentSource.OFFLINE,
        )
        summary = baker.make(
            InvoicingSummary,
            invoicing_target=tznow(),
            service=InvoiceService.SAAS,
            source=InvoicePaymentSourceType.OFFLINE,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=summary,
            invoice=offline_invoice,
            business=self.business,
        )
        self.create_charge()

        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(2, Invoice.objects.count())
        self.assertEqual(1, InvoiceItem.objects.count())
        self.assertEqual(1, Invoice.objects.filter(source=InvoicePaymentSource.OFFLINE).count())
        self.assertEqual(InvoicingSummary.objects.count(), 2)
        self.assertEqual(InvoicingSummary.objects.filter(service=InvoiceService.SAAS).count(), 1)
        self.assertEqual(
            InvoicingSummary.objects.filter(service=InvoiceService.SAAS_PER_BC).count(), 1
        )
        self.assertEqual(
            InvoicingSummary.objects.filter(source=InvoicePaymentSourceType.ONLINE).count(), 1
        )
        self.assertEqual(
            InvoicingSummary.objects.filter(source=InvoicePaymentSourceType.OFFLINE).count(), 1
        )

    def test_same_transaction_id_in_two_items(self):
        # todo ?
        transaction_id = 'P05DR@W1@M'

        item = baker.make(
            InvoiceItem,
            service=Invoice.Service.SAAS,
            payment_source=InvoicePaymentSource.STRIPE,
            transaction_id=transaction_id,
        )
        baker.make(
            Invoice,
            service=InvoiceService.SAAS,
            source=InvoicePaymentSource.STRIPE,
            items=[item],
        )
        billing_cycle, _ = self.create_charge()
        transaction = self.create_transaction(self.business, billing_cycle)
        transaction.external_id = transaction_id
        transaction.save()

        create_saas_online_invoices_based_on_billing_cycle_task()
        self.assertEqual(2, InvoiceItem.objects.count())

    def test_no_transaction_and_empty_product_identifier(self):
        _, charge = self.create_charge()

        product_identifier = (
            f'BillingCycleProductCharge: {charge.id}, '
            f'Transaction identifier: {None}, '
            f'Provider: {None}'
        )

        item = baker.make(
            InvoiceItem,
            service=Invoice.Service.SAAS,
            payment_source=InvoicePaymentSource.STRIPE,
            product_identifier=product_identifier,
        )
        baker.make(
            Invoice,
            service=InvoiceService.SAAS,
            source=InvoicePaymentSource.STRIPE,
            items=[item],
        )

        create_saas_online_invoices_based_on_billing_cycle_task()
        self.assertEqual(1, InvoiceItem.objects.count())

    def test_operator(self):
        user = baker.make(User)
        create_saas_online_invoices_based_on_billing_cycle_task(user_id=user.id)
        self.assertEqual(user.id, InvoicingSummary.objects.first().operator.id)

    def test_system_operator_if_no_user_id(self):
        create_saas_online_invoices_based_on_billing_cycle_task(user_id=None)
        self.assertEqual(1, InvoicingSummary.objects.count())
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

    def test_system_operator_if_invalid_user_id(self):
        user = baker.make(User)
        create_saas_online_invoices_based_on_billing_cycle_task(user_id=user.id + 1)
        self.assertEqual(1, InvoicingSummary.objects.count())
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

    def test_one_summary_if_multiple_triggers_same_operator_and_target_date(self):
        user = baker.make(User)
        create_saas_online_invoices_based_on_billing_cycle_task(user_id=user.id)
        self.assertEqual(1, InvoicingSummary.objects.count())
        create_saas_online_invoices_based_on_billing_cycle_task(user_id=user.id)
        self.assertEqual(1, InvoicingSummary.objects.count())
        self.assertEqual(user.id, InvoicingSummary.objects.first().operator.id)

    @pytest.mark.random_failure
    @patch('webapps.navision.invoicing.base.logging.Logger.info')
    def test_no_invoice_if_100_percent_discount(self, log_msg):
        billing_cycle, _ = self.create_charge(
            unit_price=29,
            gross_unit_price=35.09,
            discounted_price=0,
            gross_discounted_price=0,
        )
        self.create_transaction(
            self.business, billing_cycle, status=TransactionStatus.SKIPPED, amount=0
        )
        create_saas_online_invoices_based_on_billing_cycle_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 0)
        self.assertEqual(InvoiceItem.objects.count(), 0)
        log_msg.assert_called_with('Nothing to do for %s', str(self.business))

    def test_manual_invoicing_with_custom_invoice_date(self):
        billing_cycle, _ = self.create_charge()
        billing_cycle.is_open = False
        billing_cycle.save()
        self.create_transaction(self.business, billing_cycle)

        create_saas_online_invoices_based_on_billing_cycle_task(
            invoice_date=datetime(2024, 10, 19),
            auto_invoicing=False,
        )

        self.assertFalse(billing_cycle.is_open)
        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(1, InvoiceItem.objects.count())
        invoice = Invoice.objects.first()
        self.assertEqual(date(2024, 10, 19), invoice.invoice_date)

    @freeze_time(datetime(2023, 9, 28))
    def test_auto_invoicing__ignore_param_invoice_date(self):
        billing_cycle, _ = self.create_charge()
        self.create_transaction(self.business, billing_cycle)

        create_saas_online_invoices_based_on_billing_cycle_task(
            invoice_date=datetime(2024, 10, 19),
        )

        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(1, InvoiceItem.objects.count())
        invoice = Invoice.objects.first()
        self.assertEqual(date(2023, 9, 27), invoice.invoice_date)

    @freeze_time(datetime(2023, 9, 28))
    def test_manual_invoicing_no_invoice_date(self):
        billing_cycle, _ = self.create_charge()
        billing_cycle.is_open = False
        billing_cycle.save()
        self.create_transaction(self.business, billing_cycle)

        create_saas_online_invoices_based_on_billing_cycle_task(auto_invoicing=False)

        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(1, InvoiceItem.objects.count())
        invoice = Invoice.objects.first()
        self.assertEqual(date(2023, 9, 27), invoice.invoice_date)
