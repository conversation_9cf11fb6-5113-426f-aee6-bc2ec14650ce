from typing import Iterable, Optional

from lib.elasticsearch.consts import ESIndex
from webapps.business.elasticsearch.account import BusinessAccountIndex
from webapps.business.elasticsearch.business import BusinessIndex
from webapps.business.elasticsearch.business_category import (
    BusinessCategoryIndex,
)
from webapps.business.elasticsearch.business_customer import (
    BusinessCustomerIndex,
)
from webapps.business.elasticsearch.history import BusinessHistoryIndex
from webapps.marketplace.cms.elasticsearch.content import SeoCmsContentDataIndex
from webapps.marketplace.cms.elasticsearch.feature_flag import SeoFeatureFlagIndex
from webapps.marketplace.cms.elasticsearch.region_category_listing import SeoRegionCategoryIndex
from webapps.marketplace.cms.elasticsearch.region_homepage import SeoRegionHomepageIndex
from webapps.marketplace.elasticsearch.elasticsearch import CmsContentIndex
from webapps.marketplace.elasticsearch.elasticsearch import SeoMetadataIndex
from webapps.notification.elasticsearch import NotificationHistoryIndex
from webapps.notification.elasticsearch import NotificationIndex
from webapps.pos.elasticsearch.business_item import BusinessItemIndex
from webapps.structure.elasticsearch import RegionIndex
from webapps.user.elasticsearch.user import UserIndex
from webapps.warehouse.elasticsearch.commodities import WarehouseItemIndex


class Elastic:
    def __init__(self, **kwargs):
        # instantiate indices
        self.indices = {k: v() for k, v in list(kwargs.items())}
        self.documents = {}
        for index in list(self.indices.values()):
            for name, document_cls in list(index.documents.items()):
                if name in self.documents:
                    raise Exception('Duplicated document name %s' % name)
                index.bind_document(document_cls)
            self.documents.update(index.documents)

    def filter_indices(self, names: Optional[Iterable[str]]) -> dict:
        if names is None:
            return self.indices
        return {k: v for k, v in list(self.indices.items()) if k in names}

    def filter_documents(self, names: Optional[Iterable[str]]) -> dict:
        if names is None:
            return self.documents
        return {k: v for k, v in list(self.documents.items()) if k in names}


_indices = {
    ESIndex.BUSINESS: BusinessIndex,
    ESIndex.BUSINESS_ACCOUNT: BusinessAccountIndex,
    ESIndex.BUSINESS_CATEGORY: BusinessCategoryIndex,
    ESIndex.BUSINESS_CUSTOMER: BusinessCustomerIndex,
    ESIndex.BUSINESS_HISTORY: BusinessHistoryIndex,
    ESIndex.BUSINESS_ITEM: BusinessItemIndex,
    ESIndex.CMS_CONTENT: CmsContentIndex,
    ESIndex.REGION: RegionIndex,
    ESIndex.SEO_METADATA: SeoMetadataIndex,
    ESIndex.USER: UserIndex,
    ESIndex.NOTIFICATION: NotificationIndex,
    ESIndex.NOTIFICATION_HISTORY: NotificationHistoryIndex,
    ESIndex.COMMODITY_CATEGORY: WarehouseItemIndex,
    ESIndex.SEO_FEATURE_FLAG: SeoFeatureFlagIndex,
    ESIndex.SEO_REGION_HOMEPAGE: SeoRegionHomepageIndex,
    ESIndex.SEO_CMS_CONTENT_DATA: SeoCmsContentDataIndex,
    ESIndex.SEO_REGION_CATEGORY: SeoRegionCategoryIndex,
}

ELASTIC = Elastic(**_indices)
