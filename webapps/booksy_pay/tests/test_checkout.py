import datetime
from decimal import Decimal

import pytest
import pytz
from dateutil.relativedelta import relativedelta
from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Resource,
    Service,
    ServiceVariant,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    PaymentRow,
    PaymentType,
    POS,
    Receipt,
    TaxRate,
    Tip,
    Transaction,
)
from webapps.user.models import User


@pytest.mark.django_db
class BusinessCheckoutBooksyPayTestCase(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()

        pos = baker.make(POS, business=self.business, active=True, tips_enabled=True)
        baker.make(Tip, pos=pos, default=True, rate=10)
        baker.make(Tip, pos=pos, rate=20)
        baker.make(TaxRate, pos=pos, default_for_service=True, rate=20)

        self.staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=baker.make(User),
        )

        baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=self.business.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=self.business.pos)
        payment_type = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.business.pos,
        )
        service = baker.make(Service, business=self.business)

        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=15),
            price=225,
            type=PriceType.FIXED,
        )
        booked_from = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        self.appointment = create_appointment(
            [
                {
                    'booked_from': booked_from,
                    'booked_till': booked_from + datetime.timedelta(minutes=15),
                    'service_variant': service_variant,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        )
        total_services = sum(x.service_price.value for x in self.appointment.subbookings)
        total_products = Decimal('0')
        total = total_services + total_products
        self.txn = baker.make(
            Transaction,
            appointment=self.appointment,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            business_name=self.business.name,
            customer_data='',
            subtotal_services=total_services,
            subtotal_products=total_products,
            taxed_subtotal_services=total_services,
            taxed_subtotal_products=total_products,
            total=total,
            service_fee=Decimal('0.00'),
            currency_symbol='USD',
            service_tax_mode=POS.POS_TAX_MODE__INCLUDED,
            product_tax_mode=POS.POS_TAX_MODE__INCLUDED,
            pos=self.business.pos,
        )
        receipt = baker.make(
            Receipt,
            transaction=self.txn,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            payment_type=payment_type,
            card_type=None,
            card_last_digits=None,
            already_paid=total,
            provider=None,
        )
        self.payment_row = baker.make(
            PaymentRow,
            amount=total,
            payment_type=payment_type,
            receipt=receipt,
            status=receipt_status.BOOKSY_PAY_SUCCESS,
            tip_amount=Decimal('0.00'),
        )
        self.txn.latest_receipt = receipt
        self.txn.save()
        self.body = {
            'id': self.txn.id,
            'transaction_type': 'P',
            'discount_rate': 0,
            'tip': {
                'already_paid': '$0.00',
                'already_paid_unformatted': '0.00',
                'amount_remaining': 0,
                'rate': '0.00',
                'type': 'P',
                'label': 'No Tip',
                'amount': '$0.00',
                'amount_unformatted': '0.00',
            },
            'booking': None,
            'multibooking': self.appointment.id,
            'payment_rows': [
                {
                    'id': self.payment_row.id,
                    'basket_payment_id': 'afaa8e2e-533d-4d63-9604-9503aa09f87d',
                    'created': '2024-03-04T10:41',
                    'amount': '225.00',
                    'payment_type_code': PaymentTypeEnum.BOOKSY_PAY,
                    'locked': True,
                    'label': 'Booksy Pay',
                    'amount_text': '$225.00',
                    'tip_amount': '0.00',
                    'tip_amount_text': '$0.00',
                    'service_amount': '225.00',
                    'service_amount_text': '$225.00',
                    'voucher_id': None,
                    'voucher_service_id': None,
                    'voucher_service_price': None,
                    'voucher_code': None,
                }
            ],
            'issuing_staffer': self.staffer.id,
            'confirming_staffer': None,
            'note': None,
            'commodity_usage': [],
            'bookings': [
                {
                    'booking_id': booking.id,
                }
                for booking in self.appointment.subbookings
            ],
            'travel_fee': None,
            'vouchers': [],
            'addons': [],
            'products': [],
            'force_customer': True,
            'dry_run': True,
            'selected_register_id': None,
            'compatibilities': {
                'stripe_terminal': True,
                'square': True,
                'split': True,
                'prepayment': True,
                'new_checkout': True,
                'membership': True,
                'egift_card': True,
                'package': True,
            },
        }

        self.url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{self.txn.id}'

    def test_booksy_pay_checkout_completed(self):
        self.body['dry_run'] = False
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 200)

        self.assertEqual(Transaction.objects.count(), 2)
        self.assertEqual(self.txn.latest_receipt.status_code, receipt_status.BOOKSY_PAY_SUCCESS)
        new_txn = Transaction.objects.filter(id=resp.json['transaction']['id']).first()
        self.assertEqual(new_txn.latest_receipt.status_code, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(new_txn.latest_receipt.total, self.txn.latest_receipt.total)

        self.assertEqual(PaymentRow.objects.count(), 2)
        self.assertEqual(self.payment_row.status, receipt_status.BOOKSY_PAY_SUCCESS)
        new_payment_row = PaymentRow.objects.filter(
            parent_payment_row=self.payment_row,
        ).first()
        self.assertEqual(new_payment_row.status, receipt_status.PAYMENT_SUCCESS)

    def test_booksy_pay_checkout_completed_with_tip_added(self):
        tip_amount = Decimal('22.50')
        self.body['tip'] = {
            'rate': '10.00',
            'type': 'P',
            'label': '10%',
            'amount': '$22.50',
            'amount_unformatted': '22.50',
            "selected": False,
            "default": True,
            "disabled": False,
            "main": True,
        }
        self.body['dry_run'] = True
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 200)

        self.body['payment_rows'].append(
            {
                'basket_payment_id': None,
                'amount': '22.50',
                'payment_type_code': PaymentTypeEnum.CASH,
                'mode': 'C',
                'label': PaymentTypeEnum.CASH.label,
                'amount_text': '$22.50',
                'tip_amount': None,
                'tip_amount_text': None,
                'service_amount': None,
                'service_amount_text': None,
                'voucher_id': None,
                'voucher_service_id': None,
                'voucher_service_price': None,
                'voucher_code': None,
            }
        )
        self.body['dry_run'] = False

        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 200)

        self.assertEqual(Transaction.objects.count(), 2)
        self.assertEqual(self.txn.latest_receipt.status_code, receipt_status.BOOKSY_PAY_SUCCESS)
        new_txn = Transaction.objects.filter(id=resp.json['transaction']['id']).first()
        self.assertEqual(new_txn.latest_receipt.status_code, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(new_txn.latest_receipt.total, self.txn.latest_receipt.total + tip_amount)
        self.assertEqual(
            new_txn.latest_receipt.already_paid,
            self.txn.latest_receipt.total + tip_amount,
        )

        self.assertEqual(PaymentRow.objects.count(), 3)
        self.payment_row.refresh_from_db()

        self.assertEqual(self.payment_row.status, receipt_status.BOOKSY_PAY_SUCCESS)
        new_payment_row = PaymentRow.objects.filter(
            parent_payment_row=self.payment_row,
        ).first()
        self.assertEqual(new_payment_row.status, receipt_status.PAYMENT_SUCCESS)

        tip_payment_row = new_txn.latest_receipt.payment_rows.exclude(
            id=new_payment_row.id,
        ).first()
        self.assertEqual(tip_payment_row.status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(tip_payment_row.amount, tip_payment_row.tip_amount)
        self.assertEqual(tip_payment_row.amount, tip_amount)
