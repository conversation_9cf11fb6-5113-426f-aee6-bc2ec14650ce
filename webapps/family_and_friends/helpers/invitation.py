from django.core.exceptions import ValidationError

from webapps.family_and_friends.enums import InvitationStatus
from webapps.family_and_friends.events import send_email_with_invitation, send_sms_with_invitation
from webapps.family_and_friends.helpers.notifications import (
    add_invite_notification,
    add_invitation_accepted_notification,
    add_invitation_rejected_notification,
)
from webapps.family_and_friends.models import (
    MemberBusinessCustomerInfo,
    MemberInvitation,
    MemberProfile,
    MemberRelations,
    MemberTransaction,
)
from webapps.user.models import User, UserProfile


def accept_invitation(key: str, user: User = None) -> None:
    invitation = MemberInvitation.objects.prefetch_related(
        'parent',
        'member',
    ).get(key=key)
    member_profile = (
        handle_member_profiles_for_booksy_user(user, invitation) if user else invitation.member
    )
    invitation.member = member_profile
    invitation.status = InvitationStatus.ACCEPTED
    invitation.save()
    add_invitation_accepted_notification(invitation)


def reject_invitation(key: str) -> None:
    invitation = MemberInvitation.objects.prefetch_related(
        'parent',
        'member',
    ).get(key=key)
    invitation.parent.unlink_child(invitation.member)
    invitation.status = InvitationStatus.REJECTED
    add_invitation_rejected_notification(invitation)
    invitation.save()


def expire_invitation(parent: MemberProfile, member: MemberProfile):
    if invitation := parent.sent_invitations.filter(member_id=member.id).not_expired().first():
        invitation.expire()


def handle_member_profiles_for_booksy_user(
    user: User, invitation: MemberInvitation
) -> MemberProfile:
    member_profile = MemberProfile.objects.filter(user_profile=user.customer_profile.id).first()
    if member_profile:
        merge_profiles(
            parent_profile=invitation.parent,
            new_profile=member_profile,
            profile_to_merge=invitation.member,
        )
        return member_profile
    invitation.member.update_data_from_user_profile(user.customer_profile)
    return invitation.member


def merge_profiles(
    parent_profile: MemberProfile,
    new_profile: MemberProfile,
    profile_to_merge: MemberProfile,
):
    merge_relations(parent_profile, new_profile, profile_to_merge)
    merge_appointments(new_profile, profile_to_merge)
    merge_transactions(new_profile, profile_to_merge)
    merge_bcis(new_profile, profile_to_merge)
    parent_profile.save()
    new_profile.save()
    profile_to_merge.delete()


def merge_bcis(new_profile: MemberProfile, profile_to_merge: MemberProfile):
    MemberBusinessCustomerInfo.objects.filter(member__id=profile_to_merge.id).update(
        member=new_profile
    )


def merge_transactions(new_profile: MemberProfile, profile_to_merge: MemberProfile):
    MemberTransaction.objects.filter(member__id=profile_to_merge.id).update(member=new_profile)


def merge_appointments(new_profile: MemberProfile, profile_to_merge: MemberProfile):
    for bci in profile_to_merge.bcis.all():
        if not bci.appointments_booked_for_me(manager='all_objects').exists():
            continue
        if not (new_bci := new_profile.bcis.filter(business=bci.business).first()):
            continue
        for member_appointment in bci.appointments_booked_for_me(manager='all_objects').all():
            appointment = member_appointment.appointment
            member_appointment.booked_for = new_bci
            member_appointment.save()
            appointment.booked_for = new_bci
            appointment.save()


def merge_relations(
    parent_profile: MemberProfile, new_profile: MemberProfile, profile_to_merge: MemberProfile
):
    MemberRelations.objects.filter(
        parent__id=parent_profile.id, member__id=profile_to_merge.id
    ).update(member=new_profile)


def send_invite_to_user(invitation: MemberInvitation, matched_customer: UserProfile = None):
    if not invitation:
        raise ValidationError(message='Invitation does not exists', code='invalid_key')

    member_profile = invitation.member

    if member_profile.email:
        send_email_with_invitation.send(invitation, matched_user_exists=bool(matched_customer))

    if member_profile.cell_phone and (
        not member_profile.email or (member_profile.email and not matched_customer)
    ):
        send_sms_with_invitation.send(invitation, matched_user_exists=bool(matched_customer))

    if matched_customer:
        add_invite_notification(matched_customer.user, invitation)
