FROM docker.elastic.co/elasticsearch/elasticsearch:7.17.27

# fix ES7 plugin install for arch64
# https://github.com/elastic/elasticsearch/issues/118583#issuecomment-2567270484 (ES7)
ENV _JAVA_OPTIONS -XX:UseSVE=0

# INSTALL ELASTICSEARCH
ADD .docker/elasticsearch/jvm.options /usr/share/elasticsearch/config/
COPY .docker/elasticsearch/elasticsearch.yml /usr/share/elasticsearch/config/
USER root
RUN chown elasticsearch:elasticsearch config/jvm.options config/elasticsearch.yml

# INSTALL PLUGINS
COPY ./settings/synonyms/ /usr/share/elasticsearch/config/
RUN mkdir /usr/share/elasticsearch/config/hunspell/
COPY .docker/elasticsearch/hunspell/ /usr/share/elasticsearch/config/hunspell/

RUN /usr/share/elasticsearch/bin/elasticsearch-plugin install --batch analysis-icu
RUN /usr/share/elasticsearch/bin/elasticsearch-plugin install --batch analysis-stempel
# RUN /usr/share/elasticsearch/bin/elasticsearch-plugin install --batch pl.allegro.tech.elasticsearch.plugin:elasticsearch-analysis-morfologik:7.5.2