from enum import Enum

from django.conf import settings
from lib.enums import StrEnum

from country_config.enums import Country


class ESIndex(StrEnum):
    BUSINESS = 'business'
    BUSINESS_CATEGORY = 'business_category'
    BUSINESS_CUSTOMER = 'business_customer'
    BUSINESS_ITEM = 'business_item'
    EXTERNAL_BUSINESS = 'external_business'
    REGION = 'region'
    CMS_CONTENT = 'cms_content'
    SEO_METADATA = 'seo_metadata'
    SEO_FEATURE_FLAG = 'seo_feature_flag'
    SEO_REGION_HOMEPAGE = 'seo_region_homepage'
    SEO_CMS_CONTENT_DATA = 'seo_cms_content_data'
    SEO_REGION_CATEGORY = 'seo_region_category'
    USER = 'user'
    NOTIFICATION = 'notification'
    NOTIFICATION_HISTORY = 'notification_history'
    COMMODITY_CATEGORY = 'commodity_category'
    BUSINESS_ACCOUNT = 'business_account'
    APPOINTMENT = 'appointment'
    BUSINESS_HISTORY = 'business_history'
    ADYEN_TO_STRIPE_MIGRATION_LOG = 'adyen_to_stripe_migration_log'


class ESDocType(StrEnum):
    # BusinessIndex
    BUSINESS = 'business'
    OPEN_HOURS = 'open_hours'
    RESOURCE = 'resource'
    REVIEW = 'review'
    IMAGE = 'image'
    IMAGE_COMMENT = 'image_comment'
    IMAGE_LIKE = 'image_like'
    AVAILABILITY = 'availability'
    # ExternalBusinessIndex
    EXTERNAL_BUSINESS = 'external_business'
    # BusinessCustomerIndex
    BUSINESS_CUSTOMER = 'business_customer'
    # BusinessCategoryIndex
    BUSINESS_CATEGORY = 'business_category'
    # BusinessAccountIndex
    BUSINESS_ACCOUNT = 'business_account'
    # BusinessHistoryIndex
    BUSINESS_HISTORY = 'business_history'
    # RegionIndex
    REGION = 'region'
    REGION_BUSINESS_SCORE_PARTIAL = 'region_business_score_partial'
    # CmsContentIndex
    CMS_CONTENT = 'cms_content'
    # SeoMetadataIndex
    SEO_METADATA = 'seo_metadata'
    SEO_FEATURE_FLAG = 'seo_feature_flag'
    SEO_REGION_HOMEPAGE = 'seo_region_homepage'
    SEO_CMS_CONTENT_DATA = 'seo_cms_content_data'
    SEO_REGION_CATEGORY = 'seo_region_category'
    # UserIndex
    USER = 'user'
    NOTIFICATION = 'notification'
    ARCHIVED_NOTIFICATION = 'archived_notification'
    NOTIFICATION_HISTORY = 'notification_history'
    # BusinessItemIndex
    BUSINESS_ITEM = 'business_item'
    COMMODITY = 'commodity'
    COMMODITY_CATEGORY = 'commodity_category'
    SERVICE = 'service'
    SERVICE_VARIANT = 'service_variant'
    ADDON = 'addon'
    # MarketPay / Adyen
    ADYEN_TO_STRIPE_MIGRATION_LOG = 'adyen_to_stripe_migration_log'


KILOMETER = 1000
MILE = 1609
ES_BASIC_DISTANCE = MILE if settings.API_COUNTRY in [Country.US, Country.BR] else KILOMETER
LOCATION_AREA_MIN_SIZE = 3000

# BOOST PARAMS
EACH_WORD_BOOST = 100.0
# EXACT_BUSINESS_NAME_MATCH_BOOST = 2100.0
# wil be multiplied by freq and doc count
EXACT_BUSINESS_NAME_MATCH_BOOST = 2100.0
EXACT_CATEGORY_TREATMENT_NAME_MATCH_BOOST = 250.0
PRIMARY_CATEGORY_NAME_MATCH_BOOST = 100.0

BUSINESS_LOCATION_MAIN_SCORE = 5000.0
BUSINESS_LOCATION_NEAR_ME_SCORE = BUSINESS_LOCATION_MAIN_SCORE * 2
MAIN_CATEGORY_SCORE = 2500.0
SECONDARY_CATEGORY_SCORE = 1500.0
REVIEWS_RANK_SCORE = 500.0
REGION_MATCH_SCORE = 100.0
AVAILABILITY_SCORE = 800.0
PROMOTION_PROFITABILITY_BOOST = 3500.0
# 8 hour convert to seconds
ONE_HOUR = 60 * 60
ES_FULL_DAY = float(8 * ONE_HOUR)

NEAR_AVAILABILITY_DOCS_NUM = 50

MAX_BUSINESS_SUGGESTION = 10


# NATURE OF AVAILABILITY DOC
class ESAvailabilityNature(Enum):
    FAKE = 'F'
    REAL = 'R'


SECONDARY_CATEGORY_SCRIPT = '''
double add_score = (double) params.min;
String category_key;
for (int i = 0; i != params.category_keys.length; ++i){
    category_key = params.category_keys[i];
    if(doc.containsKey(category_key) && doc[category_key].size() != 0) add_score += doc[category_key].value;
}
return add_score;
'''

UTT_SCORE_SCRIPT = """
int score = 0; 
for (treatment in params['_source'][params.field]) { 
    if ( params.utt_id.contains(treatment['id']) )
    { score += treatment['score'] }
} 
return score
"""

MARKET_PLACE_PROMOTION = '''
if (doc['mp_promotion'].empty) {
    return params.min_value;
} else if (doc['mp_promotion'].value <= params.min_value){
    return params.min_value;
} else {
    double base_score = (params.score_1 + params.weight * doc['mp_promotion'].value * params.score_2);
    return _score + base_score * doc['promotion_boost'].value;
}
'''

# params: s_1, s_2, weight
MARKET_PLACE_PROMOTION_VARIANT_A = '''
double conditions = doc['promotion_boost'].value;
return _score + conditions * (params.s_1 + params.weight * params.s_2);
'''

# params: s_1, s_2, seed, weight
MARKET_PLACE_PROMOTION_VARIANT_B = '''
double conditions = doc['promotion_boost'].value;
double x = randomScore(params.seed, '_id');
return _score + conditions * (params.s_1 + params.weight * x * params.s_2);
'''

# params: s_1, s_2, s_3, s_4, seed, weight
MARKET_PLACE_PROMOTION_VARIANT_C = '''
double conditions = doc['promotion_boost'].value;
double x = randomScore(params.seed, '_id');
return _score + conditions * (params.s_1 + params.weight * params.s_2 * x + Math.exp(params.s_3 * (x - params.s_4)));
'''
