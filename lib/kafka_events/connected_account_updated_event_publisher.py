from logging import getLogger

from lib.kafka_events.enums import BooksyKafkaTopics
from lib.kafka_events.kafka_event_publisher import BooksyKafkaAvroSerializedEventPublisher
from webapps.stripe_integration.kafka_events.connected_account_updated import (
    ConnectedAccountUpdatedEvent,
    ConnectedAccountUpdatedEventKey,
)


class ConnectedAccountUpdatedEventPublisher(BooksyKafkaAvroSerializedEventPublisher):
    topic = BooksyKafkaTopics.CONNECTED_ACCOUNT_UPDATED
    key_avro_model = ConnectedAccountUpdatedEventKey
    value_avro_model = ConnectedAccountUpdatedEvent

    @staticmethod
    def delivery_report(err, msg):
        logger = getLogger('booksy.redpanda_events')

        if err is not None:
            logger.error('❌ Delivery of message %s to %s failed: %s', msg.key(), msg.topic(), err)
            return

        logger.info('✅ Message %s Delivered to %s [%s]', msg.key(), msg.topic(), msg.partition())
