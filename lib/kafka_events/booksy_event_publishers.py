from lib.feature_flag.feature.data_streaming import EnablePublishingEventsToKafka
from lib.kafka_events.enums import KafkaSaslMechanism
from lib.kafka_events.kafka_event_publisher import KafkaProducerConfig
from lib.kafka_events.kafka_producer import build_kafka_producer
from lib.kafka_events.schema_registry import build_schema_registry_client
from lib.kafka_events.transaction_created_or_updated_event_publisher import (
    TransactionCreatedOrUpdatedEventPublisher,
)
from lib.kafka_events.connected_account_updated_event_publisher import (
    ConnectedAccountUpdatedEventPublisher,
)
from settings.kafka import KAFKA_SECURITY_PROTOCOL
from settings.kafka import (
    SCHEMA_REGISTRY_URL,
    KAFKA_AUTH_PASSWORD,
    KAFKA_CORE_PUBLISHER_USERNAME,
    KAFKA_BOOTSTRAP_SERVERS,
)


if EnablePublishingEventsToKafka():
    producer_config = KafkaProducerConfig(
        bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
        security_protocol=KAFKA_SECURITY_PROTOCOL,
        sasl_mechanisms=KafkaSaslMechanism.SCRAM_SHA_256,
        sasl_username=KAFKA_CORE_PUBLISHER_USERNAME,
        sasl_password=KAFKA_AUTH_PASSWORD,
    )
    producer = build_kafka_producer(producer_config)

    schema_registry_client = build_schema_registry_client(
        SCHEMA_REGISTRY_URL, username=KAFKA_CORE_PUBLISHER_USERNAME, password=KAFKA_AUTH_PASSWORD
    )

    transaction_created_or_updated_event_publisher = TransactionCreatedOrUpdatedEventPublisher(
        producer=producer,
        schema_registry_client=schema_registry_client,
    )

    connected_account_updated_event_publisher = ConnectedAccountUpdatedEventPublisher(
        producer=producer,
        schema_registry_client=schema_registry_client,
    )
