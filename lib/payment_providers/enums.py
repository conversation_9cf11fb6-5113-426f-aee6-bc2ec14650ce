from django.utils.translation import gettext_lazy as _

from lib.enums import StrC<PERSON>ices<PERSON>num, StrEnum


class PaymentOperationType(StrChoicesEnum):
    CANCEL_OR_REFUND = 'cancel_or_refund', _('Cancel or Refund')
    REFUND = 'refund', _('Refund')
    CHARGEBACK = 'chargeback', _('Chargeback')
    SECOND_CHARGEBACK = 'second_chargeback', _('Second chargeback')
    REVERSED_CHARGEBACK = 'reversed_chargeback', _('Reversed chargeback')


class ResponseEntityType(StrChoicesEnum):
    AUTH_RESULT = 'auth_result_entity', _('Account Holder')
    ACCOUNT_HOLDER_ENTITY = 'account_holder_entity', _('Account Holder')
    ACCOUNT_HOLDER_SETTING_ENTITY = 'account_holder_settings_entity', _(
        'Account Holder Settings Entity'
    )
    ACCOUNT_HOLDER_INFO_ENTITY = 'account_holder_info_entity', _('Account Holder Info')
    ACCOUNT_HOLDER_ACTIVITY_INFO_ENTITY = 'account_holder_activity_info_entity', _(
        'Account Holder Activity Info'
    )
    ACCOUNT_HOLDER_DETAILS_ENTITY = 'account_holder_details_entity', _('Account Holder Details')
    ACCOUNT_HOLDER_STATUS_ENTITY = 'account_holder_status_entity', _('Account Holder Status')
    ACCOUNT_HOLDER_ONLINE_BALANCE_ENTITY = 'account_holder_online_balance_entity', _(
        'Account Holder Online Balance'
    )
    AVAILABLE_PAYOUT_AMOUNTS = 'available_payout_amounts', _(
        'Map of payout types and available payout amounts'
    )
    CONNECTION_TOKEN_ENTITY = 'connection_token_entity', _('Connection Token')
    CUSTOMER_ENTITY = 'customer_entity', _('Customer')
    KYC_LINK = 'kyc_link', _('KYC link')
    PAYMENT_CLIENT_TOKEN = 'payment_client_token', _('Payment client token')
    PAYMENT_ENTITY = 'payment_entity', _('Payment')
    PAYMENT_OPERATION_ENTITY = 'payment_operation_entity', _('Payment Operation')
    PAYOUT_DETAILS_ENTITY = 'payout_details_entity', _(
        'List of transactions paid out in the payout'
    )
    PAYOUT_ENTITY = 'payout_entity', _('Payout')
    PAYOUT_METHOD_ENTITY = 'payout_method_entity', _('Payout Method Entity')
    SETUP_INTENT_ENTITY = 'setup_intent_entity', _('Setup Intent Entity')
    TRANSFER_FUND_ENTITY = 'transfer_fund_entity', _('Transfer Fund')
    TOKENIZED_PAYMENT_METHOD_ENTITY = 'tokenized_payment_method', _('Tokenized Payment Method')
    TOKENIZED_PAYMENT_METHOD_ENTITY_LIST = 'tokenized_payment_method_list', _(
        'Tokenized Payment Method List'
    )
    TOKENIZED_PAYMENT_METHOD_OK = 'tokenized_payment_method_ok', _('Tokenized Payment Method OK')
    PAYMENT_TOKEN_ENTITY = 'payment_token_entity', _('Payment Token Entity')
    TOKENIZED_PAYMENT_METHOD_VALIDATION = 'tokenized_payment_method_validation', _(
        'Tokenized Payment Method Validation'
    )


class PaymentMethodType(StrChoicesEnum):
    """
    Only online methods
    """

    TERMINAL = 'terminal', _('Terminal')
    CARD = 'card', _('Card (Mobile Payment)')  # special type for PBA
    GOOGLE_PAY = 'google_pay', _('Google Pay')
    APPLE_PAY = 'apple_pay', _('Apple Pay')
    TAP_TO_PAY = 'tap_to_pay', _('Tap To Pay')
    BOOKSY_GIFT_CARD = 'booksy_gift_card', _('Booksy Gift Card')
    BLIK = 'blik', _('Blik')
    KEYED_IN_PAYMENT = 'keyed_in_payment', _('Keyed In Payment')


class ExternalPaymentMethodType(StrEnum):
    GOOGLE_PAY = PaymentMethodType.GOOGLE_PAY.value
    APPLE_PAY = PaymentMethodType.APPLE_PAY.value


class TokenizedPaymentMethodType(StrChoicesEnum):
    TERMINAL = PaymentMethodType.TERMINAL.value, PaymentMethodType.TERMINAL.label
    CARD = PaymentMethodType.CARD.value, PaymentMethodType.CARD.label
    TAP_TO_PAY = PaymentMethodType.TAP_TO_PAY.value, PaymentMethodType.TAP_TO_PAY.label


class PaymentStatus(StrChoicesEnum):
    NEW = 'new', _('New')
    SENT_FOR_AUTHORIZATION = 'sent_for_authorization', _('Sent for authorization')
    ACTION_REQUIRED = 'action_required', _('Action required')
    AUTHORIZED = 'authorized', _('Authorized')
    AUTHORIZATION_FAILED = 'authorization_failed', _('Authorization failed')
    SENT_FOR_CAPTURE = 'sent_for_capture', _('Sent for capture')
    CAPTURED = 'captured', _('Captured')
    CAPTURE_FAILED = 'capture_failed', _('Capture failed')
    CANCELED = 'canceled', _('Canceled')


class SetupIntentStatus(StrChoicesEnum):
    NEW = 'new', _('New')
    SUCCESS = 'success', _('Success')
    FAILED = 'failed', _('Failed')


class TransferFundStatus(StrChoicesEnum):
    NEW = 'new', _('New')
    PROCESSING = 'processing', _('Processing')
    SUCCESS = 'success', _('Success')
    FAILED = 'failed', _('Failed')
    EXPIRED = 'expired', _('Expired')


class PaymentOperationStatus(StrChoicesEnum):
    NEW = 'new', _('New')
    PROCESSING = 'processing', _('Processing')
    SUCCESS = 'success', _('Success')
    FAILED = 'failed', _('Failed')
    CANCELED = 'canceled', _('Canceled')


class ProviderAccountHolderStatus(StrChoicesEnum):
    TURNED_OFF = 'TURNED_OFF', _('Turned Off')
    VERIFICATION_PENDING = 'VERIFICATION_PENDING', _('Verification Pending')
    NOT_VERIFIED = 'NOT_VERIFIED', _('Not Verified')
    VERIFIED = 'VERIFIED', _('Verified')


class StripeAccountHolderOnboardingInfo(StrChoicesEnum):
    DEFAULT = 'default', _('Default Onboarding')
    SKIP = 'skip', _('Skip Onboarding')


class StripeAccountType(StrChoicesEnum):
    STANDARD = 'standard', _('Standard')
    EXPRESS = 'express', _('Express')
    CUSTOM = 'custom', _('Custom')


class StripeAccountBusinessType(StrChoicesEnum):
    INDIVIDUAL = 'individual', _('Individual')
    COMPANY = 'company', _('Company')
    NON_PROFIT = 'non_profit', _('Non-profit')
    GOVERNMENT_ENTITY = 'government_entity', _('Government entity')


class PayoutMethodType(StrChoicesEnum):
    CARD = 'card', _('Card')
    BANK_ACCOUNT = 'bank_account', _('Bank account')


class PayoutMethodStatus(StrChoicesEnum):
    ACTIVE = 'active', _('Active')
    ERROR = 'error', _('Error')


class PayoutMethodErrorCode(StrChoicesEnum):
    ERROR = 'error'


class AccountHolderNotCreatedReason(StrChoicesEnum):
    WRONG_SIREN_NUMBER = 'wrong_siren_number', _('Wrong SIREN number')
    MORE_THAN_ONE_OWNER = 'more_than_one_owner', _('More than one owner')
    ALREADY_CREATED = 'already_created', _('Already created')
    RUNTIME_ERROR = 'runtime_error', _('Runtime error')
    INVALID_NIP = 'invalid_nip', _("Wrong NIP number")
    NO_SUBSCRIPTIONBUYER_DATA = 'no_subscriptionbuyer_data', _("No Subscription data")


class EntityType(StrChoicesEnum):
    # CEIDG
    JDG = 'JDG', 'Jednoosobowa działalność gospodarcza'
    SC = 'SC', 'Spółka cywilna'

    # KRS (included all from kyc_data_providers.rejestrio_pl.CompanyType)
    SPZOO = 'SPZOO', 'Spółka z ograniczoną odpowiedzialnością'
    SA = 'SA', 'Spółka akcyjna'
    SK = 'SK', 'Spółka komandytowa'
    SKA = 'SKA', 'Spółka komandytowo-akcyjna'
    SJ = 'SJ', 'Spółka jawna'
    SP = 'SP', 'Spółka partnerska'
    SA_SIMPLE = 'SA_SIMPLE', 'Prosta spółka akcyjna'
    FOREIGN_BRANCH = 'FOREIGN_BRANCH', 'Oddział zagranicznego przedsiębiorcy'

    # other
    INDIVIDUAL = 'INDIVIDUAL', 'Individual'
    INVALID = 'INVALID', 'Invalid'


class PayoutMethodAction(StrChoicesEnum):
    ADD = 'add', _('Add')
    REMOVE = 'remove', _('Remove')
    SET_AS_DEFAULT = 'set_as_default', _('Set as default')
