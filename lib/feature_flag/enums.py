from enum import Enum

from lib.enums import StrEnum


class CustomUserAttributes(StrEnum):
    PHONE_NO = 'phone_no'
    PREFIX_COUNTRY = 'prefix_country'
    BUSINESS_ID = 'business_id'
    SMS_SERVICE_PROFILE = 'sms_service_profile'
    PHONE_NO_COUNTRY = 'phone_no_country'
    EMAIL_SUBJECT = 'email_subject'


class FeatureFlagAdapter(StrEnum):
    LD = 'launch_darkly_adapter'
    EPPO = 'eppo_adapter'
    KILL_SWITCH = 'kill_switch_adapter'


class FlagType(Enum):
    BOOL = bool
    STR = str
    INT = int
    FLOAT = float
    DICT = dict


class ExperimentVariants(StrEnum):
    CONTROL = 'control'
    VARIANT_A = 'variant_A'
    VARIANT_B = 'variant_B'
    VARIANT_C = 'variant_C'


class AppDomains(StrEnum):
    PROVIDER = 'provider'
    CUSTOMER = 'customer'


class SubjectType(StrEnum):
    BUSINESS_ID = 'business_id'
    USER_ID = 'user_id'
    FINGERPRINT = 'fingerprint'
    APPOINTMENT_ID = 'appointment_id'
    BCI_ID = 'bci_id'
    WALLET_ID = 'wallet_id'


class ChurnSupportEnum(StrEnum):
    SCHEDULE = "schedule"
    CONTACT = "contact"


class ClientApp(StrEnum):
    ANDROID_BIZ = 'android-biz'
    ANDROID_CUST = 'android-cust'
    DEFAULT = 'core-api'
    FRONTDESK = 'frontdesk'
    IOS_BIZ = 'ios-biz'
    IOS_CUST = 'ios-cust'
    STATS_AND_REPORTS = 'stats-and-reports'
    WEB_CUSTOMER_2019 = 'web-customer-2019'
    WIDGET_2021 = 'widget-2021'


class FlagEvaluationStatus(StrEnum):
    VARIANT_ASSIGNED = 'variant_assigned'
    FLAG_NOT_FOUND = 'flag_not_found'
    CLIENT_NOT_CREATED = 'client_not_created'
    CLIENT_NOT_INITIALIZED = 'client_not_initialized'
