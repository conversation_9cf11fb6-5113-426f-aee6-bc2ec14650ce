import copy
from abc import abstractmethod
from dataclasses import dataclass, field
from logging import getLogger
from typing import Any, Dict, Union

import eppo_client
import ldclient
from django.conf import settings
from eppo_client import EppoClient  # pylint: disable=no-name-in-module
from eppo_client.config import (
    AssignmentLogger,
    Config as EppoConfig,
)
from ldclient import LDClient, Config as LDConfig
from ldclient.config import HTTPConfig

from lib.abc import ThreadSafeSingletonMeta
from lib.feature_flag.consts import USER_DATA_PRIVATE_ATTRS
from lib.feature_flag.enums import (
    AppDomains,
    ClientApp,
    FlagType,
    SubjectType,
    FlagEvaluationStatus,
)
from lib.feature_flag.exceptions import InvalidKillSwitchChoiceError
from lib.feature_flag.metrics import EppoFeatureFlagMetrics
from lib.feature_flag.typing import KillSwitchChoices, KillSwitchClass
from webapps.kill_switch.models import KillSwitch

logger = getLogger('booksy.feature_flag')
eppo_loger = getLogger('booksy.eppo')


class FeatureFlagAdapterABC(metaclass=ThreadSafeSingletonMeta):
    """Feature flag interface adapter."""

    @abstractmethod
    def evaluate(self, flag_name: str, **kwargs) -> bool: ...


@dataclass(slots=True)
class KillSwitchAdapter(FeatureFlagAdapterABC):
    """A Singleton Adapter for KillSwitch.

    PLS remember to add new attributes to __slots__ but do not redefine existing.
    It is recommended not to inherit from this class.
    """

    model: KillSwitchClass = KillSwitch
    _name_choices: set = field(init=False, default=None)

    @property
    def name_choices(self) -> set:
        if self._name_choices is None:
            self._name_choices = KillSwitch.get_all_names()
        return self._name_choices

    def is_valid(self, flag_name: KillSwitchChoices) -> bool:
        return flag_name in self.name_choices

    def evaluate(self, flag_name: KillSwitchChoices, **__) -> bool:
        """KillSwitch evaluates keys using alive method."""
        if not self.is_valid(flag_name):
            raise InvalidKillSwitchChoiceError(flag_name)

        return self.model.alive(flag_name)


def get_default_user() -> str:
    return f'{settings.DEPLOYMENT_LEVEL}-{settings.API_COUNTRY}'


@dataclass
class UserData:
    # pylint: disable=invalid-name,too-many-instance-attributes
    """The representation of default LD User object.
    This object can be used for targeting during flag resolution.

    key field is required and by default resolved as
        {settings.DEPLOYMENT_LEVEL}-{settings.API_COUNTRY}
        if any other value is passed as key, then its appended to default value
    country field is set to API_COUNTRY, but can be overwritten using custom field

    You can add custom fields to 'custom' attribute that by default is a dict
    with 'deployment_level' set to DEPLOYMENT_LEVEL

    Eppo: Eppo is unable to process nested structure, therefore `deployment_level` is
    also added as a separate field.
    - `subject_key` - business_id should be passed as int -> it's reformated to
        {country}-{id} format
    - `app_domain` should be filled accordingly to app domain, by default it is
      `provider`. - `customer` for customer domain experiments
    - `subject_type` should be filled accordingly to subject type, by default it is
      `business_id`.
    """

    anonymous: str = field(repr=False, init=False)
    avatar: str = field(repr=False, init=False)
    country: str = field(default_factory=lambda: settings.API_COUNTRY)
    device: str = field(repr=False, init=False)
    email: str = field(repr=False, init=False)
    firstName: str = field(repr=False, init=False)
    ip: str = field(repr=False, init=False)
    lastName: str = field(repr=False, init=False)
    name: str = field(repr=False, init=False)
    os: str = field(repr=False, init=False)
    secondary: str = field(repr=False, init=False)
    key: str = field(default_factory=get_default_user)
    custom: dict = field(default_factory=dict)
    privateAttrs: list = field(repr=False, init=False)
    deployment_level: str = field(default_factory=lambda: settings.DEPLOYMENT_LEVEL)
    subject_key: str = field(default_factory=get_default_user)
    subject_type: str = field(default=SubjectType.BUSINESS_ID.value)
    is_experiment: bool = field(default=False)
    app: str = field(default=ClientApp.DEFAULT.value)
    app_domain: str = field(default=AppDomains.PROVIDER.value)
    phone_no: str = field(repr=False, init=False)
    phone_no_country: str = field(repr=False, init=False)
    sms_service_profile: str = field(repr=False, init=False)

    def _populate_private_attrs(self):
        private_attrs = []
        for attr in self.custom:
            if attr in USER_DATA_PRIVATE_ATTRS:
                private_attrs.append(attr)

        if private_attrs:
            self.privateAttrs = private_attrs

    def __post_init__(self):
        self.custom['deployment_level'] = settings.DEPLOYMENT_LEVEL
        if self.key == get_default_user():
            self.anonymous = True
        else:
            self.key = f'{get_default_user()}-{self.key}'
        if isinstance(self.subject_key, int):
            self.subject_key = f'{self.country}-{self.subject_key}'
        self._populate_private_attrs()

    def as_dict(self) -> dict:
        return copy.deepcopy(getattr(self, '__dict__'))


@dataclass(slots=True)
class LaunchDarklyAdapter(FeatureFlagAdapterABC):
    """A Singleton Adapter for LaunchDarkly.

    PLS remember to add new attributes to __slots__ but do not redefine existing.
    It is recommended not to inherit from this class.
    """

    client: LDClient = None

    def __post_init__(self):
        if self.client is None:
            self.client = LaunchDarklyAdapter._get_launch_darkly_client()

    def evaluate(self, flag_name: str, user_data: UserData, default=False) -> Any:
        # pylint: disable=arguments-differ
        return self.client.variation(key=flag_name, user=user_data.as_dict(), default=default)

    @staticmethod
    def _get_launch_darkly_client() -> LDClient:
        sdk_key = settings.LAUNCH_DARKLY_SDK_KEY

        offline = not sdk_key
        if offline:
            logger.error('Missing Launchdarkly sdk_key. All flags are set to False')

        proxy_settings = HTTPConfig(
            connect_timeout=settings.LAUNCH_DARKLY_CLIENT_CONNECT_TIMEOUT,
            read_timeout=settings.LAUNCH_DARKLY_CLIENT_READ_TIMEOUT,
        )

        relay_proxy_uris = {
            'base_uri': settings.LAUNCH_DARKLY_RELAY_PROXY_BASE_URI,
            'events_uri': settings.LAUNCH_DARKLY_RELAY_PROXY_EVENTS_URI,
            'stream_uri': settings.LAUNCH_DARKLY_RELAY_PROXY_STREAM_URI,
        }
        if not all(relay_proxy_uris.values()):
            logger.error('LaunchDarkly Relay Proxy configuration is missing')
            relay_proxy_uris = {}

        ldconfig = LDConfig(
            sdk_key=sdk_key,
            offline=offline,
            http=proxy_settings,
            **relay_proxy_uris,
        )

        ldclient.set_config(ldconfig)
        return ldclient.get()


@dataclass
class EppoAdapter(FeatureFlagAdapterABC):
    client: EppoClient = None

    def __post_init__(self):
        if self.client is None:
            self.client = EppoAdapter._get_eppo_client()

    @staticmethod
    def _get_eppo_client() -> EppoClient:
        sdk_key = settings.EPPO_SDK_KEY
        if not sdk_key:
            logger.error('Missing Eppo sdk_key. All flags are set to False')
            return

        eppo_config = EppoConfig(
            api_key=sdk_key,
            assignment_logger=BooksyAssignmentLogger(),
            poll_interval_seconds=30,
            poll_jitter_seconds=10,
        )
        eppo_client.init(eppo_config)
        return eppo_client.get_instance()

    # pylint: disable=arguments-differ
    def evaluate(
        self,
        flag_name: str,
        user_data: UserData,
        flag_type: FlagType,
        default: Any | None = None,
    ) -> Any:
        if self.client is None:
            evaluation_status = FlagEvaluationStatus.CLIENT_NOT_CREATED
            EppoFeatureFlagMetrics.increment_eppo_flag_evaluations(flag_name, evaluation_status)
            return default

        subject_attributes = self._map_user_data_to_subject_attributes(flag_name, user_data)

        if flag_type.value == bool:
            assignment = self.client.get_boolean_assignment(
                flag_key=flag_name,
                subject_key=user_data.subject_key,
                subject_attributes=subject_attributes,
                default=default,
            )
        elif flag_type.value == str:
            assignment = self.client.get_string_assignment(
                flag_key=flag_name,
                subject_key=user_data.subject_key,
                subject_attributes=subject_attributes,
                default=default,
            )
        elif flag_type.value == float:
            assignment = self.client.get_numeric_assignment(
                flag_key=flag_name,
                subject_key=user_data.subject_key,
                subject_attributes=subject_attributes,
                default=default,
            )
        elif flag_type.value == dict:
            assignment = self.client.get_json_assignment(
                flag_key=flag_name,
                subject_key=user_data.subject_key,
                subject_attributes=subject_attributes,
                default={},
            )
        else:
            assignment = None

        evaluation_status = (
            FlagEvaluationStatus.CLIENT_NOT_INITIALIZED
            if not self.client.is_initialized()
            else (
                FlagEvaluationStatus.FLAG_NOT_FOUND
                if assignment is None
                else FlagEvaluationStatus.VARIANT_ASSIGNED
            )
        )
        EppoFeatureFlagMetrics.increment_eppo_flag_evaluations(flag_name, evaluation_status)

        return assignment if assignment is not None else default

    @staticmethod
    def _map_user_data_to_subject_attributes(flag_name: str, user_data: UserData):
        custom_subject_attributes = {
            f"custom_{key}": value for key, value in user_data.custom.items()
        }
        subject_attributes = user_data.as_dict() | custom_subject_attributes

        for key, value in subject_attributes.items():
            if key != 'custom' and not isinstance(value, Union[str, int, float, bool, None]):
                logger.warning(
                    'Flag %s evaluation: Unsupported type of %s (%s)',
                    flag_name,
                    key,
                    type(value).__name__,
                )

        return {
            key: value
            for key, value in subject_attributes.items()
            if key not in ['custom', 'privateAttrs', 'custom_deployment_level']
        }


class BooksyAssignmentLogger(AssignmentLogger):
    def log_assignment(self, assignment_event: Dict):
        if assignment_event.get("subjectAttributes", {}).get("is_experiment"):
            eppo_loger.info(
                msg=f'{assignment_event["featureFlag"]} for user {assignment_event["subject"]}\n'
                f'Experiment: {assignment_event["experiment"]}, '
                f'Timestamp: {assignment_event["timestamp"]},\n'
                f'Allocation: {assignment_event["allocation"]}, '
                f'Subject attributes: {assignment_event["subjectAttributes"]}\n'
                f'Variation: {assignment_event["variation"]}, '
                f'Subject type: {assignment_event["subjectAttributes"]["subject_type"]}, '
                f'App domain: {assignment_event["subjectAttributes"]["app_domain"]}',
                extra=assignment_event,
            )
