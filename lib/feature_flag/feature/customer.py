from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, DictFlag, StringFlag


class AppleDrfEndpointFlag(BooleanFlag):
    flag_name = 'Feature_AppleDrfEndpointFlag'
    adapter = FeatureFlagAdapter.EPPO


class AutomaticAccountLinkingFlag(BooleanFlag):
    flag_name = 'Feature_AutomaticAccountLinkingFlag'
    adapter = FeatureFlagAdapter.EPPO


class BooksyAuthTempExtraMetadataFlag(BooleanFlag):
    flag_name = 'Feature_BooksyAuthTempExtraMetadata'
    adapter = FeatureFlagAdapter.EPPO


class BooksyAuthTimeoutsFlag(DictFlag):
    flag_name = 'Feature_BooksyAuthTimeouts'
    adapter = FeatureFlagAdapter.EPPO


class PeopleAlsoBookedFlag(BooleanFlag):
    flag_name = 'Feature_PeopleAlsoBookedFlag'
    adapter = FeatureFlagAdapter.EPPO


class RefactorSaveRegisterAgreements(BooleanFlag):
    flag_name = 'Refactor_SaveRegisterAgreements'
    adapter = FeatureFlagAdapter.EPPO


class SessionAuthImprovementFlag(BooleanFlag):
    flag_name = 'Feature_SessionAuthImprovement'
    adapter = FeatureFlagAdapter.EPPO


class CustomerRecommendedNewFlag(DictFlag):
    flag_name = 'Feature_CustomerRecommendedNew'
    adapter = FeatureFlagAdapter.EPPO


class UseLastBookingLocationInSelectedForYouFlag(BooleanFlag):
    flag_name = 'Feature_UseLastBookingLocationInSelectedForYou'
    adapter = FeatureFlagAdapter.EPPO


class PhoneRegistrationNameFlag(BooleanFlag):
    flag_name = 'Feature_PhoneRegistrationNameFlag'
    adapter = FeatureFlagAdapter.EPPO


class CustomerMyBooksySugestedForYouGalleryFlag(DictFlag):
    flag_name = 'Feature_CustomerMyBooksySelectedForYouGallery'
    adapter = FeatureFlagAdapter.EPPO


class SimpleSerchableInRecommendedForYouFlag(BooleanFlag):
    flag_name = 'Feature_SimpleSerchableInRecommendedForYou'
    adapter = FeatureFlagAdapter.EPPO


class AutomaticGoogleAccountLinkingFlag(BooleanFlag):
    flag_name = 'Feature_AutomaticGoogleAccountLinkingFlag'
    adapter = FeatureFlagAdapter.EPPO


class SkipFavoriteCategoryUpdateFlag(BooleanFlag):
    flag_name = 'Feature_SkipFavoriteCategoryUpdate'
    adapter = FeatureFlagAdapter.EPPO


class IterableSyncUserByMergeFlag(BooleanFlag):
    flag_name = 'Feature_IterableSyncUserByMerge'
    adapter = FeatureFlagAdapter.EPPO


class FixAutomaticAccountLinkingCellPhoneFlag(BooleanFlag):
    flag_name = 'Fix_AutomaticAccountLinkingCellPhoneFlag'
    adapter = FeatureFlagAdapter.EPPO


class PortfolioImagesExperimentOnFlag(BooleanFlag):
    flag_name = 'Feature_PortfolioImagesExperimentOn'
    adapter = FeatureFlagAdapter.EPPO


class CustomerPhoneLimitsFlag(StringFlag):
    flag_name = 'Feature_CustomerPhoneLimits'
    adapter = FeatureFlagAdapter.EPPO


class PhoneLimitterFlag(BooleanFlag):
    flag_name = 'Feature_PhoneLimitter'
    adapter = FeatureFlagAdapter.EPPO


class NotificationBadgesFlag(DictFlag):
    flag_name = 'Fix_NotificationBadges'
    adapter = FeatureFlagAdapter.EPPO


class NotificationReceiversFlag(BooleanFlag):
    flag_name = 'Fix_NotificationReceivers'
    adapter = FeatureFlagAdapter.EPPO


class SetNotificationReceiversFlag(BooleanFlag):
    flag_name = 'Fix_SetNotificationReceivers'
    adapter = FeatureFlagAdapter.EPPO


class VistedLikedSelected4UFlag(DictFlag):
    """
    E.g.:
    {
        "Android": "3.28.1_634",
        "iPhone": "3.28.3"
    }
    """

    flag_name = 'Feature_VistedLikedSelected4'
    adapter = FeatureFlagAdapter.EPPO


class VistedLikedS4URandomizationFlag(DictFlag):
    flag_name = 'Feature_VistedLikedS4URandomization'
    adapter = FeatureFlagAdapter.EPPO


class CommaSeparatedListFieldFixFlag(BooleanFlag):
    flag_name = 'Fix_CommaSeparatedListField'
    adapter = FeatureFlagAdapter.EPPO


class S4UServiceVariantCountFixFlag(BooleanFlag):
    flag_name = 'Fix_S4UServiceVariantCount'
    adapter = FeatureFlagAdapter.EPPO
