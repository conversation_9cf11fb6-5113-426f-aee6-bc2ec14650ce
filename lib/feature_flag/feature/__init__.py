from lib.enums import StrEnum
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import (
    BooleanFlag,
    IntegerFlag,
    StringFlag,
)


class AdyenToStripeMaxNumberOfConsentsStageI(IntegerFlag):
    flag_name = 'Feature_AdyenToStripeMaxNumberOfConsentsStageI'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: int = 3) -> int:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class AdyenToStripeMaxNumberOfConsentsStageII(IntegerFlag):
    flag_name = 'Feature_AdyenToStripeMaxNumberOfConsentsStageII'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: int = 6) -> int:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class AdyenToStripeMigrationConsentDeadlineFlag(StringFlag):
    flag_name = 'Feature_AdyenToStripeMigrationConsentDeadlineFlag'
    adapter = FeatureFlagAdapter.LD


class AuthPasswordChangeRequiredFlag(BooleanFlag):
    flag_name = 'Feature_AuthPasswordChangeRequiredFlag'
    adapter = FeatureFlagAdapter.LD


class AutomaticCxAccountDeletion(BooleanFlag):
    flag_name = 'Feature_automatic_CX_account_deletion'
    adapter = FeatureFlagAdapter.LD


class BLIKCalendarBanner(BooleanFlag):
    flag_name = 'Feature_BLIKCalendarBanner'
    adapter = FeatureFlagAdapter.EPPO


class BlikForFreePromoFlag(BooleanFlag):
    flag_name = 'Feature_BlikForFreePromoFlag'
    adapter = FeatureFlagAdapter.EPPO


class BlikRedirectEfortlessKycFlag(BooleanFlag):
    flag_name = 'Feature_BlikRedirectEfortlessKyc'
    adapter = FeatureFlagAdapter.EPPO


class BooksyGiftcardsCheckoutTimeValidationFlag(BooleanFlag):
    flag_name = 'Feature_BooksyGiftcardsCheckoutTimeValidationFlag'
    adapter = FeatureFlagAdapter.EPPO


class BooksyGiftcardsEnabledFlag(BooleanFlag):
    flag_name = 'Feature_BooksyGiftcardsEnabled'
    adapter = FeatureFlagAdapter.LD


class BranchIOBusinessBCRTerminalOrderedTrackingFlag(BooleanFlag):
    flag_name = 'Feature_BranchIOBusinessBCRTerminalOrderedTracking'
    adapter = FeatureFlagAdapter.LD


class BranchIOBusinessKYCSuccessTrackingFlag(BooleanFlag):
    flag_name = 'Feature_BranchIOBusinessKYCSuccessTracking'
    adapter = FeatureFlagAdapter.LD


class BusinessListingImporterFlag(BooleanFlag):
    flag_name = 'Feature_BusinessListingImporterFlag'
    adapter = FeatureFlagAdapter.LD


class BusinessListingTransformInFrance(BooleanFlag):
    flag_name = 'Feature_BusinessListingTransformInFrance'
    adapter = FeatureFlagAdapter.LD


class CancellationFeeFullAmountAuthorizationFlag(BooleanFlag):
    flag_name = 'Feature_CancellationFeeFullAmountAuthorization'
    adapter = FeatureFlagAdapter.LD


class ConsentsTaskLookupDaysFlag(IntegerFlag):
    flag_name = 'Feature_ConsentsTaskLookupDaysFlag'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: int = 2) -> int:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class CriticalPY1902Flag(BooleanFlag):  # PY-1902
    flag_name = 'Feature_CriticalPY1902'
    adapter = FeatureFlagAdapter.LD


class CustomerQuickSignInUpFlag(BooleanFlag):
    flag_name = 'Feature_CustomerQuickSignInUp'
    adapter = FeatureFlagAdapter.LD


class CustomerSendPrivateEmail(BooleanFlag):
    flag_name = 'Feature_CustomerSendPrivateEmail'
    adapter = FeatureFlagAdapter.LD


class DisableBGCStatusChangeIfFailedPayment(BooleanFlag):
    flag_name = 'Feature_DisableBGCStatusChangeIfFailedPaymentFlag'
    adapter = FeatureFlagAdapter.EPPO


class DistinguishHereAPIUrlInHitMissMetricFlag(BooleanFlag):
    flag_name = 'Feature_DistinguishHereAPIUrlInHitMissMetric'
    adapter = FeatureFlagAdapter.LD


class DoNotDeleteEmailReceiversInGlobalLogoutFlag(BooleanFlag):
    flag_name = 'Feature_DoNotDeleteEmailReceiversInGlobalLogout'
    adapter = FeatureFlagAdapter.EPPO


class EnableCancellationFeeAuthChangelogServiceFlag(BooleanFlag):
    flag_name = 'Feature_EnableCancellationFeeAuthChangelogService'
    adapter = FeatureFlagAdapter.EPPO


class EnablePBAOnlyAfterAcceptingFeesFlag(BooleanFlag):
    flag_name = 'Feature_EnablePBAOnlyAfterAcceptingFees'
    adapter = FeatureFlagAdapter.LD


class ExtraAddressInCustomerConsentFlag(BooleanFlag):
    flag_name = 'Feature_ExtraAddressInCustomerConsent'
    adapter = FeatureFlagAdapter.LD


class ForceSmsReasonableHoursFlag(BooleanFlag):
    flag_name = 'Feature_ForceSmsReasonableHoursFlag'
    adapter = FeatureFlagAdapter.LD


class ForgetUserEmailGDPRFlag(BooleanFlag):
    flag_name = 'Feature_ForgetUserEmailGDPR'
    adapter = FeatureFlagAdapter.LD


class FrenchCertificationMajorSoftwareVersionFlag(IntegerFlag):
    flag_name = 'Feature_FrenchCertificationMajorSoftwareVersion'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: int = 3) -> int:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class FrenchCertificationVoucherSummaryReportFlag(BooleanFlag):
    flag_name = 'Feature_FrenchCertificationVoucherSummaryReport'
    adapter = FeatureFlagAdapter.EPPO


class HelpCenterFlag(BooleanFlag):
    flag_name = 'Feature_HelpCenter'
    adapter = FeatureFlagAdapter.LD


class HereAPICacheExpiryTimeFlag(StringFlag):
    flag_name = 'Feature_HereAPICacheExpiryTimeFlag'
    adapter = FeatureFlagAdapter.LD

    class Values(StrEnum):
        HOUR = 'hour'
        DAY = 'day'
        WEEK = 'week'


class HowOldDeleteOldNotRefreshedPushTokens(IntegerFlag):
    flag_name = 'Feature_HowOldDeleteOldNotRefreshedPushTokens'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: int = 180) -> int:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class IncludeInstagramInMyppFlowFlag(BooleanFlag):
    flag_name = 'Bug_IncludeInstagramInMyppFlow'
    adapter = FeatureFlagAdapter.EPPO


class LogDocumentNotFoundErrorFlag(BooleanFlag):
    flag_name = 'Feature_LogDocumentNotFoundError'
    adapter = FeatureFlagAdapter.EPPO


class LogHereApiCalls(BooleanFlag):
    flag_name = 'Feature_LogHereApiCalls'
    adapter = FeatureFlagAdapter.LD


class LoyaltyProgramFlag(BooleanFlag):
    flag_name = 'Feature_LoyaltyProgram'
    adapter = FeatureFlagAdapter.LD


class LRUBooksyCacheHitMissMetricsFlag(BooleanFlag):
    flag_name = 'Feature_LRUBooksyCacheHitMissMetrics'
    adapter = FeatureFlagAdapter.LD


class MarketPayB2BReferralSettingEnabledFlag(BooleanFlag):
    flag_name = 'Feature_MarketPayB2BReferralSettingEnabledFlag'
    adapter = FeatureFlagAdapter.LD


class MarketPayPayoutRefactorEnabledFlag(BooleanFlag):
    flag_name = 'Feature_MarketPayPayoutRefactorEnabledFlag'
    adapter = FeatureFlagAdapter.LD


class MessageBlastGDPREnabledUSCAFlag(BooleanFlag):
    flag_name = 'Feature_MessageBlastGDPREnabledUSCAFlag'
    adapter = FeatureFlagAdapter.LD


class OptimizeBListingsFlag(BooleanFlag):
    flag_name = 'Feature_OptimizeBListings'
    adapter = FeatureFlagAdapter.EPPO


class OptimizeStripeCallsForBannersFlag(BooleanFlag):
    flag_name = 'Feature_OptimizeStripeCallsForBannersFlag'
    adapter = FeatureFlagAdapter.EPPO


class OverrideTotalInVersumAppointmentImportFlag(BooleanFlag):
    flag_name = 'Feature_OverrideTotalInVersumAppointmentImport'
    adapter = FeatureFlagAdapter.LD


class ReindexBCIAfterBlastUnsubscribeFlag(BooleanFlag):
    flag_name = 'Feature_ReindexBCIAfterBlastUnsubscribe'
    adapter = FeatureFlagAdapter.LD


class ReindexImagesWithBusinessInAdminFlag(BooleanFlag):
    flag_name = 'Feature_ReindexImagesWithBusinessInAdmin'
    adapter = FeatureFlagAdapter.LD


class ServiceNameReplicationFlag(IntegerFlag):
    flag_name = 'Feature_ServiceNameReplicationFlag'
    adapter = FeatureFlagAdapter.LD


class ServiceTypeAlertGenerationNumberFlag(IntegerFlag):
    flag_name = 'Feature_ServiceTypeAlertGenerationNumber'
    adapter = FeatureFlagAdapter.LD


class ServiceTypeCampaignFlag(BooleanFlag):
    flag_name = 'Feature_ServiceTypeCampaign'
    adapter = FeatureFlagAdapter.LD


class ServiceTypeServicesAndCombosBanner(BooleanFlag):
    flag_name = 'Feature_ServiceTypeServicesAndCombosBanner'
    adapter = FeatureFlagAdapter.LD


class SetSMSLimitForActivePeriodFlag(BooleanFlag):
    flag_name = 'Feature_SetSMSLimitForActivePeriod'
    adapter = FeatureFlagAdapter.LD


class ShowBooksyGiftCardsForChosenProvidersFlag(BooleanFlag):
    flag_name = 'Feature_ShowBooksyGiftCardsForChosenProviders'
    adapter = FeatureFlagAdapter.EPPO


class ShowDroppedBooksyGiftCardNotificationOnIosFlag(BooleanFlag):
    flag_name = 'Feature_ShowDroppedBooksyGiftCardNotificationOnIos'
    adapter = FeatureFlagAdapter.EPPO


class SmsBlastMarketingTimeAdjustFlag(BooleanFlag):
    flag_name = 'Feature_SmsBlastMarketingTimeAdjustFlag'
    adapter = FeatureFlagAdapter.LD


class SMSMarketingRequiresConsentFlag(BooleanFlag):
    flag_name = 'Feature_SMSMarketingRequiresConsentFlag'
    adapter = FeatureFlagAdapter.LD


class SMSProfilesPerCountryFlag(BooleanFlag):
    flag_name = 'Feature_SMSProfilesPerCountry'
    adapter = FeatureFlagAdapter.LD


class SMSServiceNameFlag(StringFlag):
    flag_name = 'Feature_SMSServiceName'
    adapter = FeatureFlagAdapter.LD


class SMSVonageUseRotatingCredentialsFlag(BooleanFlag):
    flag_name = 'Feature_SMSVonageUseRotatingCredentials'
    adapter = FeatureFlagAdapter.LD


class SMSVonageWebhookUrlFlag(BooleanFlag):
    flag_name = 'Feature_SMSVonageWebhookUrl'
    adapter = FeatureFlagAdapter.LD


class StaffManagementRedesignFlag(BooleanFlag):
    flag_name = 'Feature_StaffManagementRedesign'
    adapter = FeatureFlagAdapter.LD


class ThrottleBusinessReviewsRateRecalculations(BooleanFlag):
    flag_name = "Feature_ThrottleBusinessReviewsRateRecalculations"
    adapter = FeatureFlagAdapter.EPPO


class TrustedClientsTaskAsyncFlag(BooleanFlag):
    flag_name = 'Feature_TrustedClientsTaskAsyncFlag'
    adapter = FeatureFlagAdapter.LD


class TrustedClientsTaskEnabledFlag(BooleanFlag):
    flag_name = 'Feature_TrustedClientsTaskEnabledFlag'
    adapter = FeatureFlagAdapter.LD


class TTPReminderFlag(BooleanFlag):
    flag_name = 'Feature_TTPReminder'
    adapter = FeatureFlagAdapter.EPPO


class TurnTrackerFlag(BooleanFlag):
    flag_name = 'Feature_TurnTracker'
    adapter = FeatureFlagAdapter.EPPO


class UseExplicitRoutingWhenDeletingDocumentsFlag(BooleanFlag):
    flag_name = 'Feature_UseExplicitRoutingWhenDeletingDocuments'
    adapter = FeatureFlagAdapter.EPPO


class WarningCirclesFlag(BooleanFlag):
    flag_name = 'Feature_WarningCirclesFlag'
    adapter = FeatureFlagAdapter.LD


class WhatsappDeeplinkFlag(BooleanFlag):
    flag_name = 'Feature_WhatsappDeeplinkGenerator'
    adapter = FeatureFlagAdapter.LD
