import os
from datadog import statsd

from lib.feature_flag.enums import FlagEvaluationStatus


class EppoFeatureFlagMetrics:

    @classmethod
    def increment_eppo_flag_evaluations(
        cls, flag_name: str, flag_evaluation_status: FlagEvaluationStatus
    ):
        if os.getenv("DD_AGENT_HOST"):
            statsd.increment(
                'booksy.eppo.flag_evaluations',
                tags=[
                    f'flag_name:{flag_name}',
                    f'flag_evaluation_status:{flag_evaluation_status}',
                ],
            )
