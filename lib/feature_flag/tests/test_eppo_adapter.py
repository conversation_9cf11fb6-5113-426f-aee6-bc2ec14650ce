from unittest.mock import call, MagicMock, patch
import pytest

from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import (
    FeatureFlagAdapter,
    CustomUserAttributes,
    FlagEvaluationStatus,
    FlagType,
)
from lib.feature_flag.factory import get_eppo_adapter
from lib.feature_flag.flag_base import BooleanFlag, StringFlag


class TestBoolFlag(BooleanFlag):
    flag_name = "Test_Bool_Flag"
    adapter = FeatureFlagAdapter.EPPO


class TestStringFlag(StringFlag):
    flag_name = "Test_String_Flag"
    adapter = FeatureFlagAdapter.EPPO


def test_eppo_adapter_is_singleton():
    adapter_1 = get_eppo_adapter()
    adapter_2 = get_eppo_adapter()

    assert adapter_1
    assert adapter_2
    assert adapter_1 is adapter_2
    assert adapter_1.client is adapter_2.client


@pytest.fixture(name="eppo_adapter_with_mocked_client")
def fixture_eppo_adapter_with_mocked_client():
    eppo_adapter = get_eppo_adapter()
    original_client = eppo_adapter.client
    eppo_adapter.client = MagicMock()
    yield eppo_adapter
    eppo_adapter.client = original_client


@pytest.mark.django_db
def test_eppo_adapter_evaluate_type_assignment(eppo_adapter_with_mocked_client):
    eppo_adapter_with_mocked_client.client.get_string_assignment.return_value = 'string_assignment'
    eppo_adapter_with_mocked_client.client.get_boolean_assignment.return_value = True

    assert eppo_adapter_with_mocked_client.client.get_string_assignment.call_count == 0
    assert eppo_adapter_with_mocked_client.client.get_boolean_assignment.call_count == 0

    assert TestStringFlag() == 'string_assignment'
    assert eppo_adapter_with_mocked_client.client.get_string_assignment.call_count == 1
    assert eppo_adapter_with_mocked_client.client.get_boolean_assignment.call_count == 0

    assert TestBoolFlag() is True
    assert eppo_adapter_with_mocked_client.client.get_string_assignment.call_count == 1
    assert eppo_adapter_with_mocked_client.client.get_boolean_assignment.call_count == 1


@pytest.mark.django_db
def test_eppo_adapter_send_subject_attributes(eppo_adapter_with_mocked_client):
    mocked_client = eppo_adapter_with_mocked_client.client
    mocked_client.get_boolean_assignment.return_value = True
    user_data = UserData(
        subject_key='subject_key_value',
        subject_type='subject_type_value',
        key='key_value',
        country='us',
        is_experiment=True,
        app='app-name',
        app_domain='app-domain-name',
    )

    assert TestBoolFlag(user_data) is True
    mocked_client.get_boolean_assignment.assert_called_once_with(
        flag_key='Test_Bool_Flag',
        subject_key='subject_key_value',
        subject_attributes={
            'country': 'us',
            'key': 'dev-us-key_value',
            'deployment_level': 'dev',
            'subject_key': 'subject_key_value',
            'subject_type': 'subject_type_value',
            'is_experiment': True,
            'app': 'app-name',
            'app_domain': 'app-domain-name',
        },
        default=False,
    )


@pytest.mark.django_db
def test_eppo_adapter_populate_custom_subject_attributes(eppo_adapter_with_mocked_client):
    mocked_client = eppo_adapter_with_mocked_client.client
    mocked_client.get_boolean_assignment.return_value = True
    user_data = UserData(
        subject_key='subject_key_value',
        key='key_value',
        custom={
            CustomUserAttributes.PHONE_NO: 'phone-no',
            CustomUserAttributes.BUSINESS_ID: 'business-id',
            'additional_attribute': 'additional_attribute_value',
        },
    )

    assert TestBoolFlag(user_data) is True
    mocked_client.get_boolean_assignment.assert_called_once_with(
        flag_key='Test_Bool_Flag',
        subject_key='subject_key_value',
        subject_attributes={
            'country': 'us',
            'key': 'dev-us-key_value',
            'deployment_level': 'dev',
            'subject_key': 'subject_key_value',
            'subject_type': 'business_id',
            'is_experiment': False,
            'app': 'core-api',
            'app_domain': 'provider',
            'custom_phone_no': 'phone-no',
            'custom_business_id': 'business-id',
            'custom_additional_attribute': 'additional_attribute_value',
        },
        default=False,
    )


@pytest.mark.django_db
@patch('datadog.dogstatsd.base.DogStatsd.increment')
def test_eppo_flag_evaluations_client_does_not_exist(
    increment_mock, eppo_adapter_with_mocked_client, monkeypatch
):
    """Test metric increment when Eppo client is not initialized."""
    monkeypatch.setenv('DD_AGENT_HOST', '127.0.0.111')
    eppo_adapter_with_mocked_client.client = None
    user_data = UserData()
    flag_name = 'test_flag'

    result = eppo_adapter_with_mocked_client.evaluate(
        flag_name, user_data, FlagType.BOOL, default=False
    )

    assert result is False
    assert increment_mock.call_count == 1
    assert increment_mock.call_args_list[0] == call(
        'booksy.eppo.flag_evaluations',
        tags=[
            f'flag_name:{flag_name}',
            f'flag_evaluation_status:{FlagEvaluationStatus.CLIENT_NOT_CREATED.value}',
        ],
    )


@pytest.mark.django_db
@patch('datadog.dogstatsd.base.DogStatsd.increment')
def test_eppo_flag_evaluations_client_exists_but_not_initialized(
    increment_mock, eppo_adapter_with_mocked_client, monkeypatch
):
    """Test metric increment when Eppo client exists but is not initialized."""
    monkeypatch.setenv('DD_AGENT_HOST', '127.0.0.111')
    mocked_client = eppo_adapter_with_mocked_client.client
    mocked_client.is_initialized.return_value = False
    mocked_client.get_boolean_assignment.return_value = None
    user_data = UserData()
    flag_name = 'test_flag'

    result = eppo_adapter_with_mocked_client.evaluate(
        flag_name, user_data, FlagType.BOOL, default=False
    )

    assert result is False
    assert increment_mock.call_count == 1
    assert increment_mock.call_args_list[0] == call(
        'booksy.eppo.flag_evaluations',
        tags=[
            f'flag_name:{flag_name}',
            f'flag_evaluation_status:{FlagEvaluationStatus.CLIENT_NOT_INITIALIZED.value}',
        ],
    )


@pytest.mark.django_db
@patch('datadog.dogstatsd.base.DogStatsd.increment')
def test_eppo_flag_evaluations_flag_not_found(
    increment_mock, eppo_adapter_with_mocked_client, monkeypatch
):
    """Test metric increment when flag is not found in Eppo."""
    monkeypatch.setenv('DD_AGENT_HOST', '127.0.0.111')
    mocked_client = eppo_adapter_with_mocked_client.client
    mocked_client.get_boolean_assignment.return_value = None
    user_data = UserData()
    flag_name = 'non_existent_flag'

    result = eppo_adapter_with_mocked_client.evaluate(
        flag_name, user_data, FlagType.BOOL, default=False
    )

    assert result is False
    assert increment_mock.call_count == 1
    assert increment_mock.call_args_list[0] == call(
        'booksy.eppo.flag_evaluations',
        tags=[
            f'flag_name:{flag_name}',
            f'flag_evaluation_status:{FlagEvaluationStatus.FLAG_NOT_FOUND.value}',
        ],
    )


@pytest.mark.django_db
@patch('datadog.dogstatsd.base.DogStatsd.increment')
def test_eppo_flag_evaluations_variant_assigned(
    increment_mock, eppo_adapter_with_mocked_client, monkeypatch
):
    """Test metric increment when Eppo successfully assigns a variant."""
    monkeypatch.setenv('DD_AGENT_HOST', '127.0.0.111')
    mocked_client = eppo_adapter_with_mocked_client.client
    mocked_client.get_boolean_assignment.return_value = True
    user_data = UserData()
    flag_name = 'test_flag'

    result = eppo_adapter_with_mocked_client.evaluate(
        flag_name, user_data, FlagType.BOOL, default=False
    )

    assert result is True
    assert increment_mock.call_count == 1
    assert increment_mock.call_args_list[0] == call(
        'booksy.eppo.flag_evaluations',
        tags=[
            f'flag_name:{flag_name}',
            f'flag_evaluation_status:{FlagEvaluationStatus.VARIANT_ASSIGNED.value}',
        ],
    )
