#!/usr/bin/env python
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext as _

import lib.tools
from service.tools import (
    HTTPError<PERSON>ithCode,
    RequestHandler,
    json_request,
    session,
)
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import WhoMakesChange
from webapps.booking.events import appointment_canceled_by_business_event
from webapps.booking.models import (
    Appointment,
    BookingChange,
    SubBooking,
)
from webapps.booking.serializers.booking import (
    BusinessBookingSerializer,
    ReservationSerializer,
)
from webapps.business import forms
from webapps.business.enums import ResourceType
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_booking import (
    BookingChangedScenario as BCS,
)


class BusinessBookingHandler(RequestHandler):

    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(login_required=True)
    def get(self, booking_id):
        """DEPRECATED Get booking details.

        Please use BusinessAppointmentHandler.

        swagger:
            summary: DEPRECATED Get booking details
            notes: Please use /appointments/ endpoints.
            parameters:
                - name: booking_id
                  type: integer
                  paramType: path
                  description: Booking id
            type: BusinessBookingResponse
        """
        booking = self.get_object_or_404(SubBooking, id=booking_id)
        self.business = self.business_with_staffer(booking.appointment.business)

        serializer = BusinessBookingSerializer(
            instance=booking,
            context={
                'business': self.business,
                'access_level': self.access_level,
                'single_category': self.business.is_single_category,
            },
        )
        ret = {'booking': serializer.data}
        self.finish_with_json(200, ret)


class BusinessReservationsMixin:
    def create_reservation(self, reservation=None, overbooking=False):
        """Similar to booking creation."""
        reservation = reservation or SubBooking(appointment=Appointment())
        params = self.get_diff_reservation_parameters(reservation)

        appointment = reservation.appointment
        for obj in (reservation, appointment):
            lib.tools.set_model_attributes(obj, params)

        for resource in self.cleaned_data['resources']:
            if resource.type == ResourceType.STAFF:
                reservation.staffer = resource
            else:
                reservation.appliance = resource
        appointment.make_appointment(
            [reservation],
            who_makes_change=WhoMakesChange.BUSINESS,
            overbooking=overbooking,
        )
        return reservation

    def get_diff_reservation_parameters(self, reservation):
        """Returns a dict of Booking fields which differ from the
        booking instance when compared to self.cleaned_data."""
        self.business_tz = self.business.get_timezone()
        now = lib.tools.tznow(tz=self.business_tz)

        self.cleaned_data['source'] = self.booking_source

        self.cleaned_data['booked_from'] = self.cleaned_data['reserved_from']
        self.cleaned_data['booked_till'] = self.cleaned_data['reserved_till']

        self.cleaned_data['type'] = Appointment.TYPE.RESERVATION
        if reservation is None or reservation.id is None:
            self.cleaned_data['status'] = Appointment.STATUS.ACCEPTED
        self.cleaned_data['autoassign'] = False
        self.cleaned_data['updated_by'] = self.user
        self.cleaned_data['booked_for'] = None
        self.cleaned_data['business_note'] = self.cleaned_data['reason']

        ret = {
            'business': self.business,
        }
        diff_subbooking = lib.tools.get_model_diff_parameters(
            reservation,
            self.cleaned_data,
        )
        diff_appointment = lib.tools.get_model_diff_parameters(
            reservation.appointment,
            self.cleaned_data,
        )
        ret.update(diff_appointment)
        ret.update(diff_subbooking)

        if reservation.created is None:
            ret['created'] = now
        if reservation.updated is None:
            ret['updated'] = now

        return ret

    def get_validated_reservation(self, reservation_id):
        reservation = self.get_object_or_404(
            SubBooking,
            id=reservation_id,
            # TODO: uncomment below after Android is fixed
            # type=Appointment.TYPE.RESERVATION,
            __select_related=['appointment__business'],
            __prefetch_related=['resources'],
        )
        business = self.business_with_staffer(reservation.appointment.business)
        return reservation, business


# WARNING: POST method (create reservation) should be separate
#          from PUT, GET, DELETE: in POST we require business_id,
#          while in PUT, GET, DELETE we need booking_id
#          If we put all these methods in the same handler
#          we could do a PUT with business_id (instead of booking_id)
#          and POST with booking_id (instead of business_id)
# WHY? ->  Separating those doesn't guard against putting business_id in PUT
#           or booking_id in POST. These ids should be checked at the view
#           level anyway.
class BusinessCreateReservationsHandler(BusinessReservationsMixin, RequestHandler):
    """
    swagger:
        parameters:
            - name: business_id
              type: integer
              paramType: path
              description: Business id
    """

    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id):
        self.data['business_id'] = business_id
        self.data['owner'] = self.user

        self.business = self.business_with_staffer(business_id)

        overbooking = self.data.get('overbooking', True)
        form = forms.BusinessCreateReservationForm(
            business=self.business,
            data=self.data,
        )

        self.raise_error_if_form_not_valid(form)

        self.cleaned_data = form.cleaned_data
        reservation = self.create_reservation(
            reservation=None,
            overbooking=overbooking,
        )
        # region early_finish
        BookingChange.add(
            reservation,
            changed_by=BookingChange.BY_BUSINESS,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'business_add',
            },
        )
        # endregion

        # FINISH
        # check that we have actual version
        appointment = reservation.appointment
        appointment.refresh_from_db()

        self.set_status(201)
        self.finish(
            {
                'reservation': ReservationSerializer(
                    instance=reservation,
                    context={'business': reservation.appointment.business},
                ).data,
            }
        )


class BusinessReservationsHandler(BusinessReservationsMixin, RequestHandler):
    """
    swagger:
        parameters:
            - name: booking_id
              type: integer
              paramType: path
              description: Booking id (Reservation)
    """

    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def put(self, reservation_id):
        reservation, self.business = self.get_validated_reservation(
            reservation_id,
        )

        if not reservation.check_access(self.user_staffer):
            raise HTTPErrorWithCode(404, reason=_('Access Denied'))

        data = {
            'reserved_from': self.data.get(
                'reserved_from', reservation.booked_from.strftime(settings.DATETIME_FORMAT)
            ),
            'reserved_till': self.data.get(
                'reserved_till', reservation.booked_till.strftime(settings.DATETIME_FORMAT)
            ),
            'reason': self.data.get('reason', reservation.appointment.business_note),
            'resources': self.data.get(
                'resources', [res.id for res in reservation.resources.only('id').all()]
            ),
        }
        overbooking = self.data.get('overbooking', True)

        form = forms.BusinessCreateReservationForm(
            business=self.business,
            data=data,
        )

        self.raise_error_if_form_not_valid(form)

        self.cleaned_data = form.cleaned_data
        reservation = self.create_reservation(
            reservation=reservation,
            overbooking=overbooking,
        )
        reservation.refresh_from_db()
        # for presentation of reservation we need to change dates from utc
        # to local business timezone

        # Ticket: #23443 - edition of finished reservation time doen't
        # update staffer availability
        now = self.business.tznow
        if (
            reservation.appointment.status == Appointment.STATUS.FINISHED
            and reservation.booked_till >= now
        ):
            reservation.appointment.update_appointment(
                updated_by=reservation.appointment.updated_by,
                who_makes_change=WhoMakesChange.BUSINESS,
                status=Appointment.STATUS.ACCEPTED,
            )

        BookingChange.add(
            reservation,
            changed_by=BookingChange.BY_BUSINESS,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'business_update',
            },
        )

        # FINISH
        self.set_status(200)
        self.finish(
            {
                'reservation': ReservationSerializer(
                    instance=reservation,
                    context={'business': reservation.appointment.business},
                ).data,
            }
        )

    @session(login_required=True)
    def get(self, reservation_id):
        reservation = self.get_validated_reservation(reservation_id)[0]
        self.set_status(200)

        self.finish(
            {
                'reservation': ReservationSerializer(
                    instance=reservation,
                    context={'business': reservation.appointment.business},
                ).data,
            }
        )

    @session(login_required=True)
    def delete(self, reservation_id):
        reservation, self.business = self.get_validated_reservation(
            reservation_id,
        )

        if not reservation.check_access(self.user_staffer):
            raise HTTPErrorWithCode(404, reason=_('Access Denied'))

        reservation.appointment.update_appointment(
            force_status_transition=True,
            updated_by=self.user,
            status=reservation.appointment.STATUS.CANCELED,
            who_makes_change=WhoMakesChange.BUSINESS,
        )

        BookingChange.add(
            reservation,
            changed_by=BookingChange.BY_BUSINESS,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'business_delete',
            },
            change_created_at=timezone.now(),
        )

        appointment = AppointmentWrapper([reservation])
        appointment_canceled_by_business_event.send(appointment.appointment)
        start_scenario(
            BCS,
            appointment_id=reservation.appointment_id,
            action=BCS.BUSINESS_CANCEL,
        )

        # FINISH
        self.finish_with_json(200, {})


class BusinessBookingArchiveHandler(RequestHandler):
    """
    swagger:
        parameters:
            - name: booking_id
              type: integer
              paramType: path
              description: Booking id (Reservation)
    """

    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(login_required=True, api_key_required=True)
    def post(self, booking_id):
        booking = self.get_object_or_404(
            SubBooking,
            id=booking_id,
            __select_related='appointment__business',
        )
        self.business_with_staffer(booking.appointment.business)

        if not booking.check_access(self.user_staffer):
            raise HTTPErrorWithCode(404, reason=_('Access Denied'))

        if booking.appointment.archived:
            self.return_error(
                [
                    {
                        'code': 'already_in',
                        'field': 'booking',
                        'description': _('Booking is already in the archive'),
                    }
                ]
            )
            return

        appointment = booking.appointment
        if appointment.repeating:
            appointment.repeating.update_repeating_booking(
                prototype=appointment,
                updated_by=self.user,
                archived=True,
            )
        else:
            appointment.update_appointment(
                updated_by=self.user,
                archived=True,
            )

        self.finish({})
