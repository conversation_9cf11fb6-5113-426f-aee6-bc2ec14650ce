import datetime
import json
import urllib.parse
from datetime import timed<PERSON><PERSON>

import pytest
from dateutil.relativedelta import relativedelta
from django.db import connection
from django.test import override_settings
from freezegun import freeze_time
from mock import patch
from model_bakery import baker
from pytz import UTC
from rest_framework import status as http_status

from lib.booksy_sms import parse_phone_number
from lib.tools import l_b, tznow
from service.booking.tests import get_before_and_after
from service.booking.tests.test_create_appointment._base import (
    BaseAppointmentWithPrepaymentTestCase,
    BaseCreateAppointmentTestCase,
)
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import Appointment, RepeatingBooking, SubBooking
from webapps.booking.tests.test_appointment_base import BaseTestAppointment
from webapps.business.baker_recipes import (
    bci_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    basic_staffer_recipe,
)
from webapps.business.enums import ComboType
from webapps.business.models import (
    ComboMembership,
    PriceType,
    Resource,
    Service,
    ServiceAddOn,
    ServiceAddOnUse,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.consents.models import Consent, ConsentForm
from webapps.consents.notifications import ConsentFormSMSRequestNotification
from webapps.notification.enums import DeeplinkFeature
from webapps.pop_up_notification.models import GenericPopUpNotificationModel
from webapps.pos.models import POS
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum

TEST_DATETIME = datetime.datetime(2018, 1, 1, tzinfo=UTC)


# pylint: disable=unused-argument


@pytest.mark.django_db
class CreateAppointmentTestCase(BaseCreateAppointmentTestCase):
    def run_test(self, appointment_status, booked_from):
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )

        booked_till = booked_from + timedelta(hours=1)

        body = {
            'customer': {'mode': ACMode.WALK_IN},
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
        }

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)

        json_body = json.loads(l_b(resp.body))
        booking = SubBooking.objects.get()

        assert resp.code == http_status.HTTP_201_CREATED
        assert len(json_body['appointment']['subbookings']) == 1
        assert json_body['appointment']['customer']['mode'] == ACMode.WALK_IN

        assert booking.booked_from == booked_from.replace(tzinfo=UTC)
        assert booking.booked_till == booked_till.replace(tzinfo=UTC)
        assert booking.appointment.status == appointment_status
        assert booking.appointment._version is not None  # pylint: disable=protected-access
        assert booking.appointment.business_id == self.business.id
        assert (
            json_body['appointment']['_version']
            == booking.appointment._version  # pylint: disable=protected-access
        ), 'Wrong booking version returned'
        assert (
            resp.json['appointment']['subbookings'][0]['actions']
            == resp.json['appointment']['actions']
        )

        diff = set(json_body['appointment'].keys()) ^ self.req_appt_fields_in_resp
        diff.difference_update({'repeating_series'})  # ignore
        assert not diff, f"Reponse doesn't have keys {diff} in appointment"

    def test_create_booking_in_future(self):
        self.run_test(Appointment.STATUS.ACCEPTED, get_before_and_after()[0])

    def test_create_booking_in_past(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            get_before_and_after(before_future=False)[0],
        )

    def run_test_gap_time(self, variant):
        booked_from = get_before_and_after()[0]

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        variant.service.add_staffers([staffer])

        booked_till = booked_from + timedelta(hours=1)

        body = {
            'customer': {'mode': ACMode.WALK_IN},
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
        }

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == http_status.HTTP_201_CREATED

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_booking_with_gap_time_both(self, get_segment_api_mock):
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(Service, business=self.business, gap_time=relativedelta(minutes=60)),
            gap_hole_duration=relativedelta(minutes=30),
            gap_hole_start_after=relativedelta(minutes=5),
        )
        self.run_test_gap_time(variant)

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_booking_with_gap_time_during(self, get_segment_api_mock):
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=60),
            service=baker.make(
                Service,
                business=self.business,
                gap_time=relativedelta(),
            ),
            gap_hole_duration=relativedelta(minutes=30),
            gap_hole_start_after=relativedelta(minutes=5),
        )

        self.run_test_gap_time(variant)

    @patch('webapps.segment.tasks.segment_api_appointment_booked_task.delay')
    @patch('service.booking.appointments.invite_customer')
    def test_create_booking_manual_detailed_walkin(
        self,
        invite_customer_mock,
        appt_booked_mock,
    ):
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )

        booked_from = get_before_and_after()[0]
        booked_till = booked_from + timedelta(hours=1)

        body = {
            'customer': {
                'mode': ACMode.MANUAL,
                'name': 'Krzysztof Jarzyna ze Szczecina',
                'phone': '+***********',
                'email': '<EMAIL>',
                'invite': True,
                'detailed_walkin': True,
            },
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
        }

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        json_body = json.loads(l_b(resp.body))
        booking = SubBooking.objects.get()

        assert resp.code == http_status.HTTP_201_CREATED
        assert len(json_body['appointment']['subbookings']) == 1
        self.assertEqual(json_body['appointment']['customer']['mode'], ACMode.CUSTOMER_CARD)

        assert booking.booked_from == booked_from.replace(tzinfo=UTC)
        assert booking.booked_till == booked_till.replace(tzinfo=UTC)
        assert booking.appointment.status == Appointment.STATUS.ACCEPTED
        assert booking.appointment._version is not None  # pylint: disable=protected-access
        assert booking.appointment.business_id == self.business.id

        bci = BusinessCustomerInfo.objects.get()
        assert bci.full_name == 'Krzysztof Jarzyna ze Szczecina'
        assert bci.email == '<EMAIL>'
        assert bci.cell_phone == '+48 12 345 67 89'

        call_args = appt_booked_mock.call_args_list
        assert call_args[0][0][0] == json_body['appointment']['subbookings'][0]['id']

        assert invite_customer_mock.call_count == 1

        call_args = invite_customer_mock.call_args_list[0][0]
        invite_customer_mock_params = call_args[0]
        assert invite_customer_mock_params.business == self.business
        assert invite_customer_mock_params.email == '<EMAIL>'
        assert invite_customer_mock_params.phone == '+***********'
        assert invite_customer_mock_params.bci_id == bci.id

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_consent_forms_walkin(self):
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(
                Service,
                business=self.business,
                name='a',
            ),
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        service_variant.resources.add(staffer)
        consent_form = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_variant.service],
        )

        # test with dry-run
        body = {
            'customer': {
                'mode': ACMode.WALK_IN,
            },
            'subbookings': [
                {
                    'booked_from': '2018-01-01T00:00:00Z',
                    'booked_till': '2018-01-01T01:00:00Z',
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': True,
            'overbooking': True,  # ignore working hours
        }
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)
        self.assertListEqual(
            resp.json['appointment']['consent_forms'],
            [
                {
                    'id': consent_form.id,
                    'consent_uuid': None,
                    'consent_is_signed': False,
                    'form_title': consent_form.title,
                    'form_updated': None,
                    'signed': None,
                    'photo': None,
                }
            ],
        )

        # test without dry-run
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)
        self.assertListEqual(
            resp.json['appointment']['consent_forms'],
            [
                {
                    'id': consent_form.id,
                    'consent_uuid': None,
                    'consent_is_signed': False,
                    'form_title': consent_form.title,
                    'form_updated': None,
                    'signed': None,
                    'photo': None,
                }
            ],
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_consent_forms_single_booking(self):
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(
                Service,
                business=self.business,
                name='a',
            ),
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        service_variant.resources.add(staffer)
        customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        consent_form_1 = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_variant.service],
        )
        consent_1 = baker.make(
            Consent,
            form=consent_form_1,
            customer=customer,
            signed=TEST_DATETIME,
        )
        consent_form_2 = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_variant.service],
        )

        # test with dry-run
        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': '2018-01-01T00:00:00Z',
                    'booked_till': '2018-01-01T01:00:00Z',
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': True,
            'overbooking': True,  # ignore working hours
        }
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)

        result_forms = resp.json['appointment']['consent_forms']
        result_forms.sort(key=lambda item: item['id'])

        self.assertListEqual(
            result_forms,
            [
                {
                    'id': consent_form_1.id,
                    'consent_uuid': str(consent_1.uuid),
                    'consent_is_signed': True,
                    'form_title': consent_1.form_title,
                    'form_updated': '2018-01-01T00:00:00Z',
                    'signed': '2018-01-01T00:00:00Z',
                    'photo': None,
                },
                {
                    'id': consent_form_2.id,
                    'consent_uuid': None,
                    'consent_is_signed': False,
                    'form_title': consent_form_2.title,
                    'form_updated': None,  # because there is no consent
                    'signed': None,
                    'photo': None,
                },
            ],
        )

        # test without dry-run
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)

        result_forms = resp.json['appointment']['consent_forms']
        result_forms.sort(key=lambda item: item['id'])

        consent_2 = Consent.objects.get(form=consent_form_2, customer=customer)

        self.assertListEqual(
            result_forms,
            [
                {
                    'id': consent_form_1.id,
                    'consent_uuid': str(consent_1.uuid),
                    'consent_is_signed': True,
                    'form_title': consent_1.form_title,
                    'form_updated': '2018-01-01T00:00:00Z',
                    'signed': '2018-01-01T00:00:00Z',
                    'photo': None,
                },
                {
                    'id': consent_form_2.id,
                    'consent_uuid': str(consent_2.uuid),
                    'consent_is_signed': False,
                    'form_title': consent_form_2.title,
                    'form_updated': '2018-01-01T00:00:00Z',
                    'signed': None,
                    'photo': None,
                },
            ],
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_consent_forms_multi_booking(self):
        now = TEST_DATETIME

        service_1 = service_recipe.make(business=self.business)
        service_2 = service_recipe.make(business=self.business)
        service_3 = service_recipe.make(business=self.business)
        service_variant_1 = service_variant_recipe.make(service=service_1)
        service_variant_2 = service_variant_recipe.make(service=service_2)
        service_variant_recipe.make(service=service_3)  # service_variant_3

        customer = bci_recipe.make(business=self.business)
        staffer = staffer_recipe.make(
            business=self.business,
            services=[service_1, service_2, service_3],
        )

        consent_form_1 = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_1],
        )
        consent_form_2 = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_1],
        )
        consent_form_3 = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_2],
        )
        consent_form_4 = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_2, service_3],
        )
        consent_form_5 = baker.make(
            ConsentForm,
            business=self.business,
            services=[service_3],
        )

        consent_3 = consent_form_3.create_consent(customer, notify=False)

        consent_4 = consent_form_4.create_consent(customer, notify=False)
        consent_4.signed = consent_4.updated
        consent_4.save(override=True)

        booked_from_1 = now + datetime.timedelta(minutes=15)
        booked_till_1 = now + datetime.timedelta(minutes=30)
        booked_from_2 = now + datetime.timedelta(minutes=30)
        booked_till_2 = now + datetime.timedelta(minutes=45)

        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': booked_from_1.isoformat(),
                    'booked_till': booked_till_1.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant_1.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                },
                {
                    'booked_from': booked_from_2.isoformat(),
                    'booked_till': booked_till_2.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant_2.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                },
            ],
            'dry_run': False,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        url = self.get_path(self.business.id)
        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, 201)

        result_forms = response.json['appointment']['consent_forms']
        result_forms.sort(key=lambda item: item['id'])

        # expect missing consents to be created
        consent_1 = Consent.objects.filter(
            form=consent_form_1,
            customer=customer,
        ).latest()
        self.assertIsNotNone(consent_1)

        consent_2 = Consent.objects.filter(
            form=consent_form_2,
            customer=customer,
        ).latest()
        self.assertIsNotNone(consent_2)

        self.assertListEqual(
            result_forms,
            [
                {
                    'id': consent_form_1.id,
                    'consent_uuid': str(consent_1.uuid),
                    'consent_is_signed': False,
                    'form_title': consent_form_1.title,
                    'form_updated': '2018-01-01T00:00:00Z',
                    'signed': None,
                    'photo': None,
                },
                {
                    'id': consent_form_2.id,
                    'consent_uuid': str(consent_2.uuid),
                    'consent_is_signed': False,
                    'form_title': consent_form_2.title,
                    'form_updated': '2018-01-01T00:00:00Z',
                    'signed': None,
                    'photo': None,
                },
                {
                    'id': consent_form_3.id,
                    'consent_uuid': str(consent_3.uuid),
                    'consent_is_signed': False,
                    'form_title': consent_form_3.title,
                    'form_updated': '2018-01-01T00:00:00Z',
                    'signed': None,
                    'photo': None,
                },
                {
                    'id': consent_form_4.id,
                    'consent_uuid': str(consent_4.uuid),
                    'consent_is_signed': True,
                    'form_title': consent_4.form_title,
                    'form_updated': '2018-01-01T00:00:00Z',
                    'signed': '2018-01-01T00:00:00Z',
                    'photo': None,
                },
            ],
        )

        # Consent forms that are required after the booking
        # should not be listed.
        with freeze_time(now + datetime.timedelta(minutes=60)):
            consent_form_5.services.add(service_1)

            appointment_id = response.json['appointment']['appointment_id']
            appointment_type = response.json['appointment']['appointment_type']

            response = self.fetch(url + f'{appointment_type}/{appointment_id}/')
            self.assertEqual(response.code, 200)

            result_forms = response.json['appointment']['consent_forms']
            self.assertSetEqual(
                {item['id'] for item in result_forms},
                {
                    consent_form_1.id,
                    consent_form_2.id,
                    consent_form_3.id,
                    consent_form_4.id,
                },
            )

    @patch('webapps.notification.tasks.push.send_push_notification')
    def test_notifications_order(self, send_push_mock, *mocks):
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(
                Service,
                business=self.business,
                name='a',
            ),
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
            services=[service_variant.service],
        )
        customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )
        baker.make(
            ConsentForm,
            business=self.business,
            services=[service_variant.service],
        )
        booked_from = tznow() + timedelta(days=1)
        booked_till = booked_from + timedelta(hours=1)

        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
            'boost_remind_later': False,
            'boost_contact_now': False,
        }
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)

        # check push notifications
        consent_notification = GenericPopUpNotificationModel.objects.get(
            user=customer.user,
            notification_type=GenericPopUpNotificationModel.TYPE_CONSENTS,
        )
        targets = [
            call_kwargs['target'] for call_args, call_kwargs in send_push_mock.call_args_list
        ]
        self.assertListEqual(targets, [('open_pop_up_notification', str(consent_notification.id))])

        # check pop-ups
        customer_notification_types = list(
            GenericPopUpNotificationModel.objects.filter(
                user=customer.user,
                notification_type__in=(GenericPopUpNotificationModel.CUSTOMER_NOTIFICATIONS),
            ).values_list('notification_type', flat=True)
        )
        self.assertListEqual(
            customer_notification_types,
            [
                GenericPopUpNotificationModel.TYPE_CONSENTS,
            ],
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    @override_settings(CONSENT_FORM_SMS_REQUEST=True)
    @patch(
        'webapps.consents.notifications.generate_deeplink',
        return_value='dl.booksy.com/F7UMKGW1aN',
    )
    def test_consent_forms_notification(self, generate_mock):
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(
                Service,
                business=self.business,
                name='a',
            ),
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
            services=[service_variant.service],
        )
        customer = baker.make(
            BusinessCustomerInfo, business=self.business, cell_phone='+***********'
        )
        baker.make(
            ConsentForm,
            business=self.business,
            services=[service_variant.service],
            required_per_appointment=True,
        )

        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': '2018-01-01T00:00:00Z',
                    'booked_till': '2018-01-01T01:00:00Z',
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
        }
        url = self.get_path(self.business.id)

        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)

        id_ = resp.json['appointment']['appointment_uid']

        consent = (
            Appointment.objects.filter(
                id=resp.json['appointment']['appointment_uid'],
            )
            .first()
            .consent_set.first()
        )

        assert ConsentFormSMSRequestNotification(
            Appointment.objects.get(pk=id_)
        ).schedule_record.exists()

        assert generate_mock.call_args[1]['feature'] == DeeplinkFeature.CONSENT_FORM_SMS_REQUEST
        mobile_deeplink = generate_mock.call_args[1]['data']['mobile_deeplink']
        assert 'custom_form_sign_up/' in mobile_deeplink
        link_cell_phone = consent.customer_cell_phone.replace(' ', '')

        raw_query = urllib.parse.urlparse(mobile_deeplink).query
        query = urllib.parse.parse_qs(raw_query, keep_blank_values=True)
        assert link_cell_phone in query.get('customer_phone', [])
        assert consent.customer_email in query.get('customer_email', [])
        assert str(consent.uuid) in query.get('uid', [])
        assert str(consent.secret) in query.get('secret', [])

    @pytest.mark.freeze_time(TEST_DATETIME)
    @override_settings(CONSENT_FORM_SMS_REQUEST=True)
    @patch(
        'webapps.consents.notifications.generate_deeplink',
        return_value='dl.booksy.com/F7UMKGW1aN',
    )
    def test_consent_forms_notification_email_with_alias(self, generate_mock):
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(
                Service,
                business=self.business,
                name='a',
            ),
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
            services=[service_variant.service],
        )
        customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+***********',
            email='<EMAIL>',
        )
        baker.make(
            ConsentForm,
            business=self.business,
            services=[service_variant.service],
            required_per_appointment=True,
        )

        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': '2018-01-01T00:00:00Z',
                    'booked_till': '2018-01-01T01:00:00Z',
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
        }
        url = self.get_path(self.business.id)

        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)

        id_ = resp.json['appointment']['appointment_uid']

        consent = (
            Appointment.objects.filter(
                id=resp.json['appointment']['appointment_uid'],
            )
            .first()
            .consent_set.first()
        )

        assert ConsentFormSMSRequestNotification(
            Appointment.objects.get(pk=id_)
        ).schedule_record.exists()

        mobile_deeplink = generate_mock.call_args[1]['data']['mobile_deeplink']
        assert 'custom_form_sign_up/' in mobile_deeplink
        link_cell_phone = consent.customer_cell_phone.replace(' ', '')
        assert (
            urllib.parse.urlencode(
                {
                    'customer_phone': link_cell_phone,
                }
            )
            in mobile_deeplink
        )
        assert (
            urllib.parse.urlencode(
                {
                    'customer_email': consent.customer_email,
                }
            )
            in mobile_deeplink
        )
        assert f'uid={consent.uuid}' in mobile_deeplink
        assert f'secret={consent.secret}' in mobile_deeplink
        assert ' ' not in mobile_deeplink

    @pytest.mark.freeze_time(TEST_DATETIME)
    @override_settings(CONSENT_FORM_SMS_REQUEST=True)
    def test_consent_forms_notification_with_appointment(self):
        service_variant = baker.make_recipe(
            'webapps.business.service_variant_recipe',
            service=baker.make_recipe(
                'webapps.business.service_recipe',
                business=self.business,
            ),
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
            services=[service_variant.service],
        )
        customer = baker.make(
            BusinessCustomerInfo, business=self.business, cell_phone='+***********'
        )
        baker.make(
            ConsentForm,
            business=self.business,
            services=[service_variant.service],
        )

        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': '2018-01-01T00:00:00Z',
                    'booked_till': '2018-01-01T01:00:00Z',
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': service_variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
        }
        url = self.get_path(self.business.id)

        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)

        id_ = resp.json['appointment']['appointment_uid']

        assert ConsentFormSMSRequestNotification(
            Appointment.objects.get(pk=id_)
        ).schedule_record.exists()

    def test_create_appointment_with_duplicated_bci_phones_but_one_has_user(self):
        customer = bci_recipe.make(
            business=self.business, user=None, cell_phone="+***********", email=""
        )
        bci_with_user = bci_recipe.make(
            business=self.business,
            cell_phone=parse_phone_number(customer.cell_phone).db_format,
            visible_in_business=False,
            email="",
        )
        self.assertIsNotNone(bci_with_user.user)

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': '2018-01-01T00:00:00Z',
                    'booked_till': '2018-01-01T01:00:00Z',
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': True,
            'overbooking': True,  # ignore working hours
        }
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_201_CREATED)

    def test_create_appointment_with_old_card_with_duplicated_phones(self):
        customer_old = bci_recipe.make(
            business=self.business, user=None, cell_phone="+***********", email=""
        )
        with connection.cursor() as cursor:
            cursor.execute(
                "update business_businesscustomerinfo set cell_phone='+***********' "
                "where business_customer_info_id = %s;",
                [customer_old.id],
            )
        bci_recipe.make(
            business=self.business,
            user=None,
            cell_phone=parse_phone_number(customer_old.cell_phone).db_format,
            email="",
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer_old.id,
            },
            'subbookings': [
                {
                    'booked_from': '2018-01-01T00:00:00Z',
                    'booked_till': '2018-01-01T01:00:00Z',
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': True,
            'overbooking': True,  # ignore working hours
        }
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, http_status.HTTP_400_BAD_REQUEST)
        self.assertEqual(resp.json['errors'][0]['field'], 'customer.id.cell_phone')
        self.assertEqual(resp.json['errors'][0]['code'], 'invalid')

    def test_create_booking_with_requested_staffer(self):
        customer = bci_recipe.make(
            business=self.business, user=None, cell_phone="+***********", email=""
        )
        staffer = basic_staffer_recipe.make(
            business=self.business,
        )

        booked_from = get_before_and_after()[0]
        booked_till = booked_from + timedelta(hours=1)

        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                    'is_staffer_requested_by_client': True,
                }
            ],
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        url = self.get_path(self.business.id, dry_run=True)

        response = self.fetch(url, method='POST', body=body)
        subbooking = response.json['appointment']['subbookings'][0]
        self.assertEqual(response.code, http_status.HTTP_201_CREATED)
        self.assertTrue(subbooking['is_staffer_requested_by_client'])

        url = self.get_path(self.business.id)
        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, http_status.HTTP_201_CREATED)

        subbooking = response.json['appointment']['subbookings'][0]
        booking = SubBooking.objects.get(id=subbooking['id'])
        self.assertTrue(booking.is_staffer_requested_by_client)

    def test_basic_staffer_creates_appointment_for_other_staffer(self):
        test_user = user_recipe.make()
        basic_staffer_recipe.make(
            business=self.business,
            staff_user=test_user,
        )

        other_staffer = basic_staffer_recipe.make(
            business=self.business,
        )

        self.session = test_user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        booked_from = get_before_and_after()[0]
        booked_till = booked_from + timedelta(hours=1)
        body = {
            'customer': {
                'mode': ACMode.WALK_IN,
            },
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': -1,
                    'staffer_id': other_staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': False,
        }

        url = self.get_path(self.business.id, dry_run=True)
        response = self.fetch(url, method='POST', body=body)

        self.assertEqual(response.code, http_status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json['errors'][0]['description'],
            'Staff member cannot assign bookings to other staff.',
        )


@pytest.mark.django_db
class CreateAppointmentWithComboAndAddonsTestCase(BaseCreateAppointmentTestCase):
    def setUp(self):
        super().setUp()
        now = TEST_DATETIME

        baker.make(
            POS,
            business=self.business,
            service_tax_mode=POS.POS_TAX_MODE__EXCLUDED,
        )

        service_1 = service_recipe.make(business=self.business)
        service_2 = service_recipe.make(business=self.business)
        service_3 = service_recipe.make(business=self.business)
        self.service_variant_1 = service_variant_recipe.make(service=service_1)
        self.service_variant_2 = service_variant_recipe.make(service=service_2)
        service_variant_recipe.make(service=service_3)  # service_variant_3

        self.staffer = staffer_recipe.make(
            business=self.business,
            services=[service_1, service_2, service_3],
        )

        self.extra_staffer = baker.make(
            Resource,
            business=self.business,
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        BaseTestAppointment._make_resource_calendar(  # pylint: disable=protected-access
            self.extra_staffer
        )

        self.combo_children = [self.service_variant_1, self.service_variant_2]
        self.combo = service_variant_recipe.make(
            service=service_recipe.make(business=self.business, combo_type=ComboType.SEQUENCE),
            price=None,
            type=None,
            duration=None,
        )
        for i, child in enumerate(self.combo_children):
            baker.make(
                ComboMembership, combo=self.combo, child=child, order=i, gap_time=relativedelta()
            )
            child.service.add_staffers([self.staffer, self.extra_staffer])

        self.addon_1 = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price=12.50,
            price_type=PriceType.FIXED,
            services=[service_1, self.combo_children[0].service],
            duration=relativedelta(minutes=30),
            max_allowed_quantity=10,
        )
        self.addon_2 = baker.make(
            ServiceAddOn,
            name='addon 2',
            business=self.business,
            price=14.50,
            price_type=PriceType.FIXED,
            services=[service_2, self.combo_children[1].service],
            duration=relativedelta(minutes=15),
            max_allowed_quantity=1,
        )

        customer = bci_recipe.make(business=self.business)

        booked_from_1 = now + datetime.timedelta(minutes=15)
        booked_till_1 = now + datetime.timedelta(minutes=30)
        booked_from_2 = now + datetime.timedelta(minutes=30)
        booked_till_2 = now + datetime.timedelta(minutes=45)

        self.body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': customer.id,
            },
            'subbookings': [
                {
                    'addons': [
                        {'id': self.addon_1.id, 'quantity': 1},
                        {'id': self.addon_2.id, 'quantity': 2},
                    ],
                    'booked_from': booked_from_1.isoformat(),
                    'booked_till': booked_till_1.isoformat(),
                    'appliance_id': None,
                    'staffer_id': self.staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': self.service_variant_1.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                },
                {
                    'booked_from': booked_from_2.isoformat(),
                    'booked_till': booked_till_2.isoformat(),
                    'appliance_id': None,
                    'staffer_id': self.staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': self.service_variant_2.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                },
            ],
            'dry_run': False,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_multi_booking_with_addons(self):
        url = self.get_path(self.business.id)
        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, 201)

        addons_use = ServiceAddOnUse.all_objects.all().values(
            'id',
            'price',
            'quantity',
            'service_addon_id',
            'subbooking_id',
        )
        addons_use = {addon['service_addon_id']: addon for addon in addons_use}
        addon_use_1 = addons_use[self.addon_1.id]
        addon_use_2 = addons_use[self.addon_2.id]

        self.assertEqual(len(addons_use), 2)
        self.assertEqual(self.addon_1.price, addon_use_1['price'])
        self.assertEqual(self.addon_2.price, addon_use_2['price'])
        self.assertEqual(3, addon_use_2['quantity'] + addon_use_1['quantity'])
        self.assertEqual(response.json['appointment']['total_value'], 71.5)

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_multi_booking_with_addons_dry_run(self):
        url = self.get_path(self.business.id, dry_run=True)
        self.body['subbookings'][0]['booked_till'] = None
        self.body['subbookings'][1]['booked_from'] = None
        self.body['subbookings'][1]['booked_till'] = None
        self.body['dry_run'] = True

        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, 201)

        booked_time = datetime.datetime.strptime(
            response.json['appointment']['booked_till'], '%Y-%m-%dT%H:%M'
        ) - datetime.datetime.strptime(
            response.json['appointment']['booked_from'], '%Y-%m-%dT%H:%M'
        )

        expected_time_delta = timedelta(hours=1, minutes=30)

        self.assertEqual(booked_time, expected_time_delta)
        self.assertEqual(response.json['appointment']['total_value'], 71.5)

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_multi_booking_with_addons_without_price_dry_run(self):
        addon = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price_type=PriceType.FREE,
            duration=relativedelta(minutes=30),
            max_allowed_quantity=10,
        )

        url = self.get_path(self.business.id, dry_run=True)
        self.body['subbookings'][0]['addons'] = [{'id': addon.id, 'quantity': 2}]
        self.body['subbookings'][0]['booked_till'] = None
        self.body['subbookings'][1]['booked_from'] = None
        self.body['subbookings'][1]['booked_till'] = None
        self.body['dry_run'] = True

        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, 201)

        booked_time = datetime.datetime.strptime(
            response.json['appointment']['booked_till'], '%Y-%m-%dT%H:%M'
        ) - datetime.datetime.strptime(
            response.json['appointment']['booked_from'], '%Y-%m-%dT%H:%M'
        )

        expected_time_delta = timedelta(hours=1, minutes=30)

        self.assertEqual(booked_time, expected_time_delta)
        self.assertEqual(response.json['appointment']['total_value'], 30.0)

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_repeating_multi_booking_with_addons(self):
        url = self.get_path(self.business.id)
        self.body['new_repeating'] = {
            'booking_dates': [],
            'end_type': 'A',
            'repeat': 'W',
            'repeat_number': 2,
            'repeat_till': TEST_DATETIME + datetime.timedelta(days=7, hours=23, minutes=59),
        }
        self.body['overbooking'] = True
        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, 201)
        self.assertEqual(ServiceAddOnUse.objects.count(), 4)
        self.assertEqual(Appointment.objects.count(), 2)
        self.assertEqual(RepeatingBooking.objects.count(), 1)
        self.assertEqual(len(response.json['appointment']['subbookings'][0]['addons']), 2)
        self.assertEqual(
            len(RepeatingBooking.objects.first().appointments.last().bookings.first().addons), 2
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_repeating_multi_booking_with_requested_staffer(self):
        url = self.get_path(self.business.id)
        self.body['new_repeating'] = {
            'booking_dates': [],
            'end_type': 'A',
            'repeat': 'W',
            'repeat_number': 2,
            'repeat_till': TEST_DATETIME + datetime.timedelta(days=7, hours=23, minutes=59),
        }
        self.body['overbooking'] = True
        self.body['subbookings'][0]['is_staffer_requested_by_client'] = True
        response = self.fetch(url, method='POST', body=self.body)

        self.assertEqual(response.code, http_status.HTTP_201_CREATED)
        self.assertEqual(Appointment.objects.count(), 2)
        self.assertEqual(RepeatingBooking.objects.count(), 1)
        next_appointment = RepeatingBooking.objects.first().appointments.last()
        self.assertEqual(next_appointment.subbookings[0].is_staffer_requested_by_client, True)

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_multi_booking_with_addons_dry_run_service_promotion_customer_discount(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
            user=self.user,
            discount=10,
        )
        url = self.get_path(self.business.id, dry_run=True)
        self.body['dry_run'] = True
        self.body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }

        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, 201)

        # total value without discount 71.5, but client has 10%
        self.assertEqual(response.json['appointment']['total_value'], 64.35)
        self.assertEqual(response.json['appointment']['total_discount_amount'], 7.15)

        self.assertEqual(
            response.json['appointment']['subbookings'][1]['service_promotion'][
                'price_unformatted'
            ],
            13.5,
        )
        self.assertEqual(
            response.json['appointment']['subbookings'][1]['service_promotion'][
                'price_before_discount'
            ],
            15.0,
        )
        self.assertEqual(
            response.json['appointment']['subbookings'][0]['service_promotion'][
                'price_unformatted'
            ],
            50.85,
        )
        self.assertEqual(
            response.json['appointment']['subbookings'][0]['service_promotion'][
                'price_before_discount'
            ],
            56.5,
        )

    def make_subbookings_dry_run_request(self):
        return [
            {
                'service_variant': {
                    'mode': SVMode.VARIANT,
                    'id': self.combo.id,
                },
                'booked_from': self.body['subbookings'][0]['booked_from'],
                'booked_till': None,
                'appliance_id': None,
                'staffer_id': self.extra_staffer.id,
                'combo_children': [
                    {
                        'booked_from': self.body['subbookings'][0]['booked_from'],
                        'booked_till': None,
                        'staffer_id': self.staffer.id,
                        'appliance_id': None,
                        'service_variant': {
                            'mode': SVMode.VARIANT,
                            'id': self.service_variant_1.id,
                        },
                    },
                    {
                        'booked_from': None,
                        'booked_till': None,
                        'staffer_id': self.staffer.id,
                        'appliance_id': None,
                        'service_variant': {
                            'mode': SVMode.VARIANT,
                            'id': self.service_variant_2.id,
                        },
                    },
                ],
            }
        ]

    def make_subbookings_with_any_staffer_dry_run_request(self):
        return [
            {
                'service_variant': {
                    'mode': SVMode.VARIANT,
                    'id': self.combo.id,
                },
                'booked_from': self.body['subbookings'][0]['booked_from'],
                'booked_till': None,
                'appliance_id': None,
                'staffer_id': None,
                'combo_children': [
                    {
                        'booked_from': self.body['subbookings'][0]['booked_from'],
                        'booked_till': None,
                        'staffer_id': -1,
                        'appliance_id': None,
                        'service_variant': {
                            'mode': SVMode.VARIANT,
                            'id': self.service_variant_1.id,
                        },
                    },
                    {
                        'booked_from': None,
                        'booked_till': None,
                        'staffer_id': -1,
                        'appliance_id': None,
                        'service_variant': {
                            'mode': SVMode.VARIANT,
                            'id': self.service_variant_1.id,
                        },
                    },
                ],
            }
        ]

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_combo_booking_with_addons__parallel(self):
        self.combo.service.combo_type = ComboType.PARALLEL
        self.combo.service.save()

        self.body['subbookings'] = self.make_subbookings_dry_run_request()
        self.body['subbookings'][0]['combo_children'][0]['addons'] = [
            {
                'id': self.addon_1.id,
                'quantity': 1,
            },
        ]
        self.body['subbookings'][0]['combo_children'][1]['addons'] = [
            {
                'id': self.addon_2.id,
                'quantity': 4,
            },
        ]
        url = self.get_path(self.business.id, dry_run=True)
        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, 201)

        subbookings = response.json['appointment']['subbookings']
        combo_children = subbookings[0]['combo_children']

        # check booked_from/till for the first child
        booked_from_1 = datetime.datetime.strptime(
            combo_children[0]['booked_from'],
            '%Y-%m-%dT%H:%M',
        )
        booked_till_1 = datetime.datetime.strptime(
            combo_children[0]['booked_till'],
            '%Y-%m-%dT%H:%M',
        )
        expected_booked_till_1 = (
            booked_from_1 + self.service_variant_1.duration + self.addon_1.duration
        )
        self.assertEqual(booked_till_1, expected_booked_till_1)

        # check booked_from/till for the second child
        booked_from_2 = datetime.datetime.strptime(
            combo_children[1]['booked_from'],
            '%Y-%m-%dT%H:%M',
        )
        # parallel combo, children have the same start datetime
        self.assertEqual(booked_from_2, booked_from_1)

        booked_till_2 = datetime.datetime.strptime(
            combo_children[1]['booked_till'],
            '%Y-%m-%dT%H:%M',
        )
        expected_booked_till_2 = (
            booked_from_2 + self.service_variant_2.duration + self.addon_2.duration * 4
        )
        self.assertEqual(booked_till_2, expected_booked_till_2)

        # check subbooking's booked_till is the datetime of the combo child that
        # is the last to end.
        booked_till = datetime.datetime.strptime(
            subbookings[0]['booked_till'],
            '%Y-%m-%dT%H:%M',
        )
        self.assertEqual(booked_till, booked_till_2)

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_combo_booking_with_addons(self):
        self.body['subbookings'] = self.make_subbookings_dry_run_request()
        self.body['subbookings'][0]['combo_children'][0]['addons'] = [
            {
                'id': self.addon_1.id,
                'quantity': 1,
            },
        ]
        self.body['subbookings'][0]['combo_children'][1]['addons'] = [
            {
                'id': self.addon_2.id,
                'quantity': 2,
            },
        ]

        url = self.get_path(self.business.id, dry_run=True)
        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, 201)

        body = response.json['appointment']
        combo_children = body['subbookings'][0]['combo_children']

        # Check booked_till for the first combo child
        booked_from_1 = datetime.datetime.strptime(
            combo_children[0]['booked_from'],
            '%Y-%m-%dT%H:%M',
        )
        booked_till_1 = datetime.datetime.strptime(
            combo_children[0]['booked_till'],
            '%Y-%m-%dT%H:%M',
        )
        expected_booked_till_1 = (
            booked_from_1 + self.service_variant_1.duration + self.addon_1.duration
        )
        self.assertEqual(booked_till_1, expected_booked_till_1)

        # Check booked_from and booked_till for the second combo child
        booked_from_2 = datetime.datetime.strptime(
            combo_children[1]['booked_from'],
            '%Y-%m-%dT%H:%M',
        )
        # booked_from of the second child is the same as booked_till of the first child
        self.assertEqual(booked_from_2, booked_till_1)

        booked_till_2 = datetime.datetime.strptime(
            combo_children[1]['booked_till'],
            '%Y-%m-%dT%H:%M',
        )
        expected_booked_till_2 = (
            booked_from_2 + self.service_variant_2.duration + self.addon_2.duration * 2
        )
        self.assertEqual(booked_till_2, expected_booked_till_2)

        # check subbooking's booked_till is the same as the second child's booked_till
        booked_till = datetime.datetime.strptime(
            body['subbookings'][0]['booked_till'],
            '%Y-%m-%dT%H:%M',
        )
        self.assertEqual(booked_till, booked_till_2)

        body['overbooking'] = False
        body['_notify_about_reschedule'] = False

        url = self.get_path(self.business.id)
        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, 201)

        addons_use = ServiceAddOnUse.all_objects.all().values(
            'id',
            'price',
            'quantity',
            'service_addon_id',
            'subbooking_id',
        )
        addons_use = {addon['service_addon_id']: addon for addon in addons_use}
        addon_use_1 = addons_use[self.addon_1.id]
        addon_use_2 = addons_use[self.addon_2.id]

        self.assertEqual(len(addons_use), 2)
        self.assertEqual(self.addon_1.price, addon_use_1['price'])
        self.assertEqual(self.addon_2.price, addon_use_2['price'])
        self.assertEqual(3, addon_use_2['quantity'] + addon_use_1['quantity'])
        self.assertEqual(response.json['appointment']['total_value'], 71.5)

    def test_combo_booking_with_requested_staffer(self):
        self.body['subbookings'] = self.make_subbookings_dry_run_request()
        self.body['subbookings'][0]['combo_children'][0]['is_staffer_requested_by_client'] = True
        self.body['subbookings'][0]['combo_children'][1]['is_staffer_requested_by_client'] = False
        url = self.get_path(self.business.id, dry_run=True)
        response = self.fetch(url, method='POST', body=self.body)
        self.assertEqual(response.code, http_status.HTTP_201_CREATED)
        body = response.json['appointment']
        combo_children = body['subbookings'][0]['combo_children']
        self.assertTrue(combo_children[0]['is_staffer_requested_by_client'])
        self.assertFalse(combo_children[1]['is_staffer_requested_by_client'])

        body['overbooking'] = False
        body['_notify_about_reschedule'] = False

        url = self.get_path(self.business.id)
        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, http_status.HTTP_201_CREATED)

        subbooking = response.json['appointment']['subbookings'][0]
        combo_children = SubBooking.objects.get(id=subbooking['id']).combo_children
        self.assertTrue(combo_children[0].is_staffer_requested_by_client)
        self.assertFalse(combo_children[1].is_staffer_requested_by_client)

    def test_combo_booking_with_requested_any_staffer(self):
        self.body['subbookings'] = self.make_subbookings_with_any_staffer_dry_run_request()
        self.body['subbookings'][0]['combo_children'][0]['is_staffer_requested_by_client'] = True
        self.body['subbookings'][0]['combo_children'][1]['is_staffer_requested_by_client'] = False
        url = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url, method='POST', body=self.body)

        self.assertEqual(resp.code, http_status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            resp.json['errors'][0]['field'],
            'subbookings.0.combo_children.0.is_staffer_requested_by_client',
        )
        self.assertEqual(resp.json['errors'][0]['code'], 'invalid')

    def test_basic_staffer_creates_combo_appointment_for_other_staffer(self):
        test_user = user_recipe.make()
        staffer = basic_staffer_recipe.make(
            business=self.business,
            staff_user=test_user,
        )

        self.session = test_user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.body['subbookings'] = self.make_subbookings_dry_run_request()
        self.body['subbookings'][0]['combo_children'][0]['staffer_id'] = staffer.id
        self.body['subbookings'][0]['combo_children'][1]['staffer_id'] = self.extra_staffer.id

        url = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url, method='POST', body=self.body)

        self.assertEqual(resp.code, http_status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            resp.json['errors'][0]['description'],
            'Staff member cannot assign bookings to other staff.',
        )


@pytest.mark.django_db
class CreateBusinessAppointmentWithPrepaymentTestCase(BaseAppointmentWithPrepaymentTestCase):
    def test_create_booking_in_future_with_combo_service(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment(
            is_combo=True,
        )
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == http_status.HTTP_201_CREATED

        self._add_combo_children_fields_to_body(body, booked_from, variant)

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == http_status.HTTP_201_CREATED
        json_body = resp.json

        appointment = Appointment.objects.filter(
            id=json_body['appointment']['appointment_uid'],
        ).first()
        assert appointment
        assert len(appointment.subbookings) == 1
        assert len(appointment.subbookings[0].combo_children) == 3
