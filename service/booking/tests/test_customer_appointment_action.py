from datetime import time

import pytest
from dateutil.relativedelta import relativedelta
from django.test.utils import override_settings
from freezegun import freeze_time
from model_bakery import baker

from lib.test_utils import get_in_memory_img
from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import AppointmentTypeSM
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_custappt_data,
)
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    POS,
    PaymentMethod,
    PaymentType,
    Transaction,
)
from webapps.pos.provider.fake import _CARDS
from webapps.user.enums import AuthOriginEnum


@pytest.mark.django_db
class CancelCustomerAppointment(BaseAsyncHTTPTest, BaseTestAppointment):

    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

    @staticmethod
    def generate_url(appointment_dct, booking_type):
        if booking_type == AppointmentTypeSM.SINGLE:
            appointment_id = appointment_dct['subbookings'][0]['id']
        else:
            appointment_id = appointment_dct['appointment_uid']

        url = f'/customer_api/me/appointments/{booking_type}' f'/{appointment_id}/action'
        return url

    def test_cancel_appointment_with_both(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        pos = baker.make(
            POS, business=self.business, active=True, deposit_cancel_time=relativedelta(years=10)
        )
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=9.99,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])
        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=80.17,
        )

        url = f'/customer_api/me/appointments/business/{self.business.id}/'
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        # dry run for deposit needed
        body['dry_run'] = True

        # test no_thumbs
        in_memory_img = get_in_memory_img()
        image = baker.make(
            Image,
            image=in_memory_img,
            business=self.business,
            category=ImageTypeEnum.BIZ_PHOTO,
            is_cover_photo=True,
        )
        image.reindex(refresh_index=True)
        self.business.reindex(refresh_index=True)

        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 201
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '80.17'

        # test no_thumbs
        assert 'thumbnails' in resp.json['appointment']['business']['images']['cover'][0]
        body['no_thumbs'] = True
        resp = self.fetch(url, body=body, method='POST')
        assert 'thumbnails' not in resp.json['appointment']['business']['images']['cover'][0]

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == 201
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        # Cancel appointment
        cancel_body = {
            'action': 'cancel',
            'id': resp.json['appointment']['appointment_id'],
            '_version': resp.json['appointment']['_version'],
        }

        cancel_url = self.generate_url(
            resp.json['appointment'],
            AppointmentTypeSM.MULTI,
        )

        cancel_resp = self.fetch(cancel_url, method='POST', body=cancel_body)

        assert cancel_resp.code == 200
        assert set(cancel_resp.json['appointment']['meta'].keys()).issuperset(
            {
                'booking_score',
                'energy_booking',
            }
        )

        appointment_id = resp.json['appointment']['appointment_id']
        txn = Transaction.objects.get(
            appointment_id=appointment_id, transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT
        )
        deposit = Transaction.objects.get(
            appointment_id=appointment_id,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        self.assertEqual(
            deposit.latest_receipt.status_code, receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

        assert self.business.events.late_cancellation_count == 0

    def test_cancel_appointment_clear_single(self):
        self._test_cancel_appointment_clear(AppointmentTypeSM.SINGLE)

    def test_cancel_appointment_clear_multi(self):
        self._test_cancel_appointment_clear(AppointmentTypeSM.MULTI)

    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    def _test_cancel_appointment_clear(self, appointment_type):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        pos = baker.make(
            POS, business=self.business, active=True, deposit_cancel_time=relativedelta(years=10)
        )
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        sv = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        sv.add_staffers([self.staffer])

        url = f'/customer_api/me/appointments/business/{self.business.id}/'
        body = build_custappt_data(
            variant=sv,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == 201
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # Cancel appointment
        cancel_body = {
            'action': 'cancel',
            'id': resp.json['appointment']['appointment_id'],
            '_version': resp.json['appointment']['_version'],
        }

        cancel_url = self.generate_url(
            resp.json['appointment'],
            appointment_type,
        )

        with freeze_time(self._dt_from_hour(time(9, 30))):
            self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
            cancel_resp = self.fetch(cancel_url, method='POST', body=cancel_body)

            assert cancel_resp.code == 200

            # refresh
            self.business.events.refresh_from_db()
            assert self.business.events.late_cancellation_count == 1


class CancelCustomerAppointmentUrlUid(CancelCustomerAppointment):

    @staticmethod
    def generate_url(appointment_dct, booking_type):
        appointment_id = appointment_dct['appointment_uid']
        return f'/customer_api/me/appointments/{appointment_id}/action'


# tworzenie apointemntu dry run wyciągnąć do urla
