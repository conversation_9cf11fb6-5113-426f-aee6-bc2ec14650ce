import json
from datetime import datetime, timedelta
from decimal import Decimal
from freezegun import freeze_time

import pytest
import responses
from dateutil import tz
from dateutil.relativedelta import relativedelta
from django.utils.translation import gettext as _
from mock import (
    MagicMock,
    patch,
)
from model_bakery import baker
from pytz import UTC
from rest_framework import status

from lib.test_utils import (
    create_resource,
    create_subbooking,
    increase_appointment_next_id,
)
from lib.tools import (
    l_b,
    tznow,
)
from service.booking.tests import get_before_and_after
from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps.booking.enums import AppointmentCustomerMode as ACMode
from webapps.booking.enums import AppointmentTypeSM as AT
from webapps.booking.enums import SubbookingServiceVariantMode as SVMode
from webapps.booking.exceptions import BookingConflict
from webapps.booking.models import (
    Appointment,
    BookingResource,
    SubBooking,
)

from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import (
    bci_recipe,
    service_variant_recipe,
)
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.payment_gateway.models import Wallet, WalletOwnerType
from webapps.pos.booksy_gift_cards.ports import BGCValidationResponse
from webapps.pos.enums import (
    PaymentProviderEnum,
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    POS,
    PaymentMethod,
    PaymentRow,
    PaymentType,
    Receipt,
    Transaction,
    TransactionRow,
)
from webapps.pos.provider.fake import _CARDS
from webapps.pos.tests.pos_refactor.helpers_adyen import AdyenMixin
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


@pytest.mark.django_db
class EditBookingTestCase(BaseAsyncHTTPTest, AdyenMixin):

    def setUp(self):
        increase_appointment_next_id(40)
        return super().setUp()

    @staticmethod
    def generate_url(booking, business_id):
        url = f'/business_api/me/businesses/{business_id}/appointments/single/{booking.id}/'
        return url

    def run_test(self, status_, from_before, from_after, dry_run=False):
        bcis, variants, appliances, staffers = self._standard_test_initialize()
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_for=bcis[0],
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=from_before,
                booked_till=from_before + timedelta(hours=1),
                type=Appointment.TYPE.BUSINESS,
                status=status_,
                autoassign=True,
                service_variant=variants[0],
            ),
        )

        BookingResource(subbooking=booking, resource=staffers[0]).save()
        BookingResource(subbooking=booking, resource=appliances[0]).save()

        # just in case
        assert booking.staffer == staffers[0]
        assert booking.appliance == appliances[0]
        assert booking.appointment.booked_for == bcis[0]
        assert booking.service_variant == variants[0]

        booked_from = from_after
        booked_till = from_after + timedelta(hours=1)
        old_version = booking.appointment._version  # pylint: disable=protected-access
        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': bcis[1].id,
            },
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': appliances[1].id,
                    'staffer_id': staffers[1].id,
                    'is_staffer_requested_by_client': True,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variants[1].id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            '_version': old_version,
            'dry_run': dry_run,
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
        }

        url = self.generate_url(booking, self.business.id)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body

        biz_tz = self.business.get_timezone()
        json_body = json.loads(l_b(resp.body))
        resp_booking = json_body['appointment']['subbookings'][0]
        if not dry_run:
            assert json_body['appointment']['_version'] != old_version, 'Booking was not updated'

        resp_from = datetime.strptime(resp_booking['booked_from'], '%Y-%m-%dT%H:%M').replace(
            tzinfo=biz_tz
        )

        resp_till = datetime.strptime(resp_booking['booked_till'], '%Y-%m-%dT%H:%M').replace(
            tzinfo=biz_tz
        )

        assert resp_from == booked_from
        assert resp_till == booked_till

        if dry_run is False:
            booking = SubBooking.objects.get()
            assert booking.booked_from == booked_from
            assert booking.booked_till == booked_till
            assert booking.appointment.booked_from == booking.booked_from
            assert booking.appointment.booked_till == booking.booked_till
            assert booking.appointment.status == status_
            assert booking.staffer == staffers[1]
            assert booking.is_staffer_requested_by_client
            assert booking.appliance == appliances[1]
            assert booking.appointment.booked_for == bcis[1]
            assert booking.service_variant == variants[1]

    def _standard_test_initialize(self):
        service = baker.make(Service, business=self.business)
        variants = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            _quantity=2,
        )

        staffers = baker.make(Resource, type=Resource.STAFF, business=self.business, _quantity=2)
        service.add_staffers([staffers[0], staffers[1]])

        appliances = baker.make(
            Resource, type=Resource.APPLIANCE, business=self.business, _quantity=2
        )
        service.add_appliances([appliances[0], appliances[1]])

        bcis = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            _quantity=2,
        )
        return bcis, variants, appliances, staffers

    def test_edit_booking_in_future(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            *get_before_and_after(tz=UTC),
        )

    def test_edit_booking_in_past(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            *get_before_and_after(
                before_future=False,
                after_future=False,
                tz=UTC,
            ),
        )

    def test_edit_booking_without_requested_staffer_same_staffer(self):
        bcis, variants, appliances, staffers = self._standard_test_initialize()
        from_before, from_after = get_before_and_after(tz=UTC)
        booking = create_appointment(
            [
                {
                    "booked_from": from_before,
                    "staffer": staffers[0],
                    "appliance": appliances[0],
                    "is_staffer_requested_by_client": True,
                }
            ],
            business=self.business,
        ).subbookings[0]

        booked_from = from_after + timedelta(hours=1)
        booked_till = from_after + timedelta(hours=2)
        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': bcis[1].id,
            },
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': appliances[0].id,
                    'staffer_id': staffers[0].id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variants[0].id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            'dry_run': False,
            'overbooking': True,
            '_notify_about_reschedule': False,
        }

        url = self.generate_url(booking, self.business.id)
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK, resp.body

        booking = SubBooking.objects.get()
        assert booking.staffer == staffers[0]
        assert booking.is_staffer_requested_by_client

    @pytest.mark.freeze_time(datetime(2022, 8, 5, 10, 30, tzinfo=UTC))
    def test_customer_should_change_appointment(self):
        service = baker.make(Service, business=self.business)
        variant = service_variant_recipe.make(service=service)
        staffer = create_resource(
            business=self.business,
            service=service,
            calendar=True,
        )

        bci = bci_recipe.make(business=self.business)
        appointment = create_appointment(
            [
                dict(
                    service_variant=variant,
                    booked_from=datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    booked_till=datetime(2022, 8, 8, 11, tzinfo=UTC),
                )
            ],
            business=self.business,
            booked_for=bci,
        )
        assert appointment.can_customer_change() is True

        data = {
            'recurring': False,
            'subbookings': [
                {
                    'booked_from': datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    'service_variant': {'id': variant.id, 'mode': 'variant'},
                    'staffer_id': staffer.id,
                }
            ],
        }

        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = f'/customer_api/me/appointments/{appointment.id}'
        resp_dry_run = self.fetch(f"{url}/dry_run", body=data, method='PUT')
        resp = self.fetch(url, body=data, method='PUT')

        assert resp.code == 200
        assert resp_dry_run.code == 200
        assert 'payment_summary' in resp_dry_run.json['appointment_payment']

    @pytest.mark.freeze_time(datetime(2022, 8, 5, 10, 30, tzinfo=UTC))
    def test_customer_should_not_change_finished_booking(self):
        service = baker.make(Service, business=self.business)
        variant = service_variant_recipe.make(service=service)
        staffer = create_resource(
            business=self.business,
            service=service,
            calendar=True,
        )

        bci = bci_recipe.make(business=self.business)
        appointment = create_appointment(
            [
                dict(
                    service_variant=variant,
                    staffer=staffer,
                    booked_from=datetime(2022, 8, 4, 10, 30, tzinfo=UTC),
                    booked_till=datetime(2022, 8, 4, 11, tzinfo=UTC),
                )
            ],
            business=self.business,
            booked_for=bci,
            status=Appointment.STATUS.FINISHED,
        )
        assert appointment.status == Appointment.STATUS.FINISHED
        assert appointment.booked_for == bci
        assert appointment.can_customer_change() is False

        data = {
            'recurring': False,
            'subbookings': [
                {
                    'booked_from': datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    'service_variant': {'id': variant.id, 'mode': 'variant'},
                    'staffer_id': staffer.id,
                }
            ],
        }

        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        resp = self.fetch(
            f'/customer_api/me/appointments/{appointment.id}', body=data, method='PUT'
        )
        resp_dry_run = self.fetch(
            f'/customer_api/me/appointments/{appointment.id}/dry_run', body=data, method='PUT'
        )

        for r in (resp, resp_dry_run):
            assert r.code == 400
            assert len(r.json['errors']) == 1
            assert r.json['errors'][0] == {
                'code': 'invalid',
                'type': 'validation',
                'description': _('Appointment change is not allowed'),
            }

    def test_edit_booking_in_future_dry_run(self):
        before, after = get_before_and_after(tz=UTC)
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            before,
            after,
            dry_run=True,
        )

    def test_edit_booking_in_past_dry_run(self):
        before, after = get_before_and_after(
            before_future=False,
            after_future=False,
            tz=UTC,
        )
        self.run_test(
            Appointment.STATUS.FINISHED,
            before,
            after,
            dry_run=True,
        )

    def test_staff_access_levels(self):
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                updated_by=self.business.owner,
                source=self.biz_booking_src,
                status=Appointment.STATUS.ACCEPTED,
                booked_from=tznow() + relativedelta(hours=1),
                booked_till=tznow() + relativedelta(hours=2),
            ),
        )
        BookingResource.objects.create(subbooking=booking, resource=staffer)

        other_staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=baker.make(User),
        )

        def test_access_level(user, denied_levels):
            self.user = user
            self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
            url = self.generate_url(booking, self.business.id)
            response_get = self.fetch(url, method='GET')
            self.assertEqual(
                response_get.code,
                200,
                f'GET responded with {response_get.code} status for access_level "{access_level}"',
            )

            response_put = self.fetch(
                url,
                method='PUT',
                body={},
            )
            self.assertEqual(
                response_put.code,
                404 if access_level in denied_levels else 400,
                f'PUT responded with {response_put.code} status for access_level "{access_level}"',
            )

        # Note: it's not clear whether we should check access_level from
        # staffer.staff_access_level or session

        for access_level in Resource.STAFF_ACCESS_LEVELS_ALL:
            staffer.staff_access_level = access_level
            staffer.save()

            test_access_level(staffer.staff_user, denied_levels=[])

        for access_level in Resource.STAFF_ACCESS_LEVELS_ALL:
            other_staffer.staff_access_level = access_level
            other_staffer.save()

            test_access_level(
                other_staffer.staff_user, denied_levels=Resource.STAFF_ACCESS_LEVELS_LIMIT_CALENDAR
            )

    @responses.activate
    @pytest.mark.django_db
    @pytest.mark.freeze_time(datetime(2016, 12, 30))
    @patch('webapps.pos.provider.adyen_ee.AdyenEEPaymentProvider.make_payment')
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    def test_multi_from_single_prepaid_charge(
        self,
        get_timezone_mock,
        get_location_name_mock,
        charge_prepayment_mock,
    ):
        self.mock_auth_success()
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        get_location_name_mock.return_value = 'adasdas, 00000'

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            price=100,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=100,
        )

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        staffer.add_services([service.id])

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_for=bci,
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=datetime(2017, 1, 1, 12, tzinfo=biz_tz),
                booked_till=datetime(2017, 1, 1, 13, tzinfo=biz_tz),
                type=Appointment.TYPE.CUSTOMER,
                status=Appointment.STATUS.UNCONFIRMED,
                autoassign=True,
                service_variant=service_variant,
            ),
        )

        BookingResource(subbooking=booking, resource=staffer).save()
        baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
            default=True,
        )

        pos = baker.make(POS, business=self.business, active=True)
        txn = baker.make(
            Transaction,
            appointment=booking.appointment,
            pos=pos,
            total=100,
            customer=self.user,
            customer_card=bci,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        baker.make(
            TransactionRow,
            transaction=txn,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            subbooking=booking,
        )
        prepayment = baker.make(
            PaymentType,
            pos=pos,
            code=PaymentTypeEnum.PREPAYMENT,
        )
        receipt = baker.make(
            Receipt,
            status_code=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            transaction=txn,
            already_paid=100,
        )

        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prepayment,
            receipt=receipt,
            status=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
        )

        url = f'/business_api/me/businesses/{self.business.id}/appointments/single/{booking.id}/'

        # # add service to booking (create multibooking from single)
        body = {
            "subbookings": [
                {
                    "booked_from": "2017-01-01T12:00",
                    "booked_till": "2017-01-01T13:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {
                        "id": service_variant.id,
                        "mode": SVMode.VARIANT,
                    },
                    "id": booking.id,
                    "service": {
                        "id": service.id,
                        "variant": {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 100,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    '_version': booking.appointment._version,  # pylint: disable=protected-access
                },
                {
                    "booked_from": "2017-01-01T13:00",
                    "booked_till": "2017-01-01T14:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {
                        "id": service_variant.id,
                        "mode": SVMode.VARIANT,
                    },
                    "id": None,
                    "service": {
                        "id": service.id,
                        "variant": {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 100,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                },
            ],
            "customer": {
                'id': bci.id,
                'mode': ACMode.CUSTOMER_CARD,
            },
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            'dry_run': False,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        assert charge_prepayment_mock.call_count == 1

    def test_null_booking(self):
        from_before, from_after = get_before_and_after(tz=UTC)
        bcis, variants, appliances, staffers = self._standard_test_initialize()

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_for=bcis[0],
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=from_before,
                booked_till=from_before + timedelta(hours=1),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.ACCEPTED,
                autoassign=True,
                service_variant=variants[0],
            ),
        )

        booked_till = from_after + timedelta(hours=1)
        old_version = booking.appointment._version  # pylint: disable=protected-access
        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': bcis[1].id,
            },
            'subbookings': [
                {
                    'booked_from': from_after.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': appliances[1].id,
                    'staffer_id': staffers[1].id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variants[1].id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                },
                None,
            ],
            '_version': old_version,
            'dry_run': True,
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
        }

        url = self.generate_url(booking, self.business.id)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 400
        dict_assert(
            resp.json['errors'][0],
            {
                'field': 'subbookings.1.0',
                'description': 'This field may not be null.',
                'code': 'null',
            },
        )

    @freeze_time(datetime(2022, 6, 3, 11, tzinfo=UTC))
    @patch('webapps.pos.serializers.BooksyGiftCardFieldMixin.validate_if_bgc_available')
    @patch('webapps.pos.services.TransactionService.check_prepayment_auth_success')
    @patch(
        'webapps.booksy_gift_cards.payment_provider.BooksyGiftCardsPaymentsProvider.make_payment'
    )
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    @pytest.mark.django_db
    def test_update_appointment_bgc_without_prepayment(  # pylint: disable=too-many-arguments, too-many-positional-arguments, line-too-long
        self,
        use_booksy_gift_card_mock,
        validate_booksy_gift_card_mock,
        make_payment_mock,
        check_prepayment_mock,
        bgc_available_mock,
    ):
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
            pos_refactor_stage2_enabled=True,
        )
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)

        assert self.business.pos_pay_by_app_enabled
        deposit_amount = Decimal('50.00')
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            True, deposit_amount, None, ['9a488bbd-bc72-4673-8850-ec6ef2a8e8de']
        )
        service = baker.make(Service, business=self.business)
        variant = service_variant_recipe.make(service=service)
        staffer = create_resource(
            business=self.business,
            service=service,
            calendar=True,
        )
        variant.add_staffers([staffer])
        bci = bci_recipe.make(business=self.business)
        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        appointment = create_appointment(
            [
                {
                    "service_variant": variant,
                    "booked_from": datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    "booked_till": datetime(2022, 8, 8, 11, tzinfo=UTC),
                }
            ],
            business=self.business,
            booked_for=bci,
        )

        assert appointment.can_customer_change() is True
        data = {
            'recurring': False,
            'subbookings': [
                {
                    'booked_from': datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    'service_variant': {'id': variant.id, 'mode': 'variant'},
                    'staffer_id': staffer.id,
                }
            ],
            'gift_card_code': '123-abc-123',
        }
        baker.make(Wallet, owner_type=WalletOwnerType.CUSTOMER, user_id=bci.user.id)
        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = f'/customer_api/me/appointments/{appointment.id}'

        resp_dry_run = self.fetch(f"{url}/dry_run", body=data, method='PUT')
        self.assertEqual(resp_dry_run.code, 200)
        self.assertEqual(
            len(resp_dry_run.json['appointment_payment']['payment_summary']['rows']), 2
        )
        self.assertEqual(resp_dry_run.json['appointment_payment']['gift_card_code'], '123ABC123')
        appointment.refresh_from_db()
        assert appointment.is_booksy_gift_card_appointment is False

        resp = self.fetch(url, body=data, method='PUT')
        self.assertEqual(resp.code, 200)
        self.assertEqual(len(resp.json['appointment_payment']['payment_summary']['rows']), 2)
        self.assertEqual(resp.json['appointment_payment']['gift_card_code'], '123ABC123')
        appointment.refresh_from_db()
        assert appointment.is_booksy_gift_card_appointment is True

    @freeze_time(datetime(2022, 6, 3, 11, tzinfo=UTC))
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    @pytest.mark.django_db
    def test_update_appointment_bgc_with_prepayment(
        self, use_booksy_gift_card_mock, validate_booksy_gift_card_mock
    ):
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
            pos_refactor_stage2_enabled=True,
        )
        prepayment = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)

        deposit_amount = Decimal('50.00')
        assert self.business.pos_pay_by_app_enabled
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            True, deposit_amount, None, ['9a488bbd-bc72-4673-8850-ec6ef2a8e8de']
        )
        service = baker.make(Service, business=self.business)
        variant = service_variant_recipe.make(service=service)
        staffer = create_resource(
            business=self.business,
            service=service,
            calendar=True,
        )
        variant.add_staffers([staffer])
        bci = bci_recipe.make(business=self.business)
        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        appointment = create_appointment(
            [
                {
                    "service_variant": variant,
                    "booked_from": datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    "booked_till": datetime(2022, 8, 8, 11, tzinfo=UTC),
                }
            ],
            business=self.business,
            booked_for=bci,
        )
        txn = baker.make(
            Transaction,
            appointment=appointment,
            pos=pos,
            total=100,
            customer=self.user,
            customer_card=bci,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        baker.make(
            TransactionRow,
            transaction=txn,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
        )
        baker.make(
            Receipt,
            status_code=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            payment_type=prepayment,
            transaction=txn,
            already_paid=100,
        )
        assert appointment.can_customer_change() is True
        data = {
            'recurring': False,
            'subbookings': [
                {
                    'booked_from': datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    'service_variant': {'id': variant.id, 'mode': 'variant'},
                    'staffer_id': staffer.id,
                }
            ],
            'gift_card_code': '123-abc-123',
        }

        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = f'/customer_api/me/appointments/{appointment.id}'
        resp_dry_run = self.fetch(f"{url}/dry_run", body=data, method='PUT')
        resp = self.fetch(url, body=data, method='PUT')
        self.assertEqual(resp_dry_run.code, 400)
        self.assertEqual(
            resp_dry_run.json['errors'][0]['description'],
            'Only time and note can be edited for prepaid appointments',
        )
        self.assertEqual(resp.code, 400)
        self.assertEqual(
            resp.json['errors'][0]['description'],
            'Only time and note can be edited for prepaid appointments',
        )
        data = {
            'compatibilities': {
                'prepayment': True,
            },
            'subbookings': [
                {
                    'booked_from': datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    'service_variant': {'id': variant.id, 'mode': 'variant'},
                    'staffer_id': staffer.id,
                }
            ],
            'gift_cards_ids': ['123-abc-123'],
        }
        resp_dry_run = self.fetch(f"{url}/dry_run", body=data, method='PUT')
        self.assertEqual(
            resp_dry_run.json['errors'][0]['description'],
            'Only time and note can be edited for prepaid appointments',
        )

    @freeze_time(datetime(2022, 6, 3, 11, tzinfo=UTC))
    @patch('webapps.pos.serializers.validate_booksy_gift_card')
    @patch(
        'webapps.payment_providers.services.booksy_gift_cards.payment_services.use_booksy_gift_card',  # pylint: disable=line-too-long
        return_value=True,
    )
    @pytest.mark.django_db
    def test_update_appointment_bgc_with_deposit(
        self, use_booksy_gift_card_mock, validate_booksy_gift_card_mock
    ):
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
            pos_refactor_stage2_enabled=True,
        )
        pay_type = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        deposit_amount = Decimal('50.00')
        assert self.business.pos_pay_by_app_enabled
        validate_booksy_gift_card_mock.return_value = BGCValidationResponse(
            True, deposit_amount, None, ['9a488bbd-bc72-4673-8850-ec6ef2a8e8de']
        )
        service = baker.make(Service, business=self.business)
        variant = service_variant_recipe.make(service=service)
        staffer = create_resource(
            business=self.business,
            service=service,
            calendar=True,
        )
        variant.add_staffers([staffer])
        bci = bci_recipe.make(business=self.business)
        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        appointment = create_appointment(
            [
                {
                    "service_variant": variant,
                    "booked_from": datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    "booked_till": datetime(2022, 8, 8, 11, tzinfo=UTC),
                }
            ],
            business=self.business,
            booked_for=bci,
        )
        txn = baker.make(
            Transaction,
            appointment=appointment,
            pos=pos,
            total=100,
            customer=self.user,
            customer_card=bci,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        baker.make(
            TransactionRow,
            transaction=txn,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
        )
        receipt = baker.make(
            Receipt,
            status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            payment_type=pay_type,
            transaction=txn,
            card_last_digits=card.card_last_digits,
            already_paid=100,
        )
        txn.latest_receipt = receipt
        txn.save(update_fields=['latest_receipt'])

        pr = PaymentRow.create_with_status(
            receipt=receipt,
            amount=txn.total,
            is_deposit=True,
            payment_type=pay_type,
            status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            provider=card.provider,
            card_last_digits=card.card_last_digits,
        )
        pr.save()
        assert appointment.can_customer_change() is True

        self.session = bci.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = f'/customer_api/me/appointments/{appointment.id}'
        data = {
            'compatibilities': {
                'prepayment': True,
            },
            'subbookings': [
                {
                    'booked_from': datetime(2022, 8, 8, 10, 30, tzinfo=UTC),
                    'service_variant': {'id': variant.id, 'mode': 'variant'},
                    'staffer_id': staffer.id,
                }
            ],
            'gift_cards_ids': ['123-abc-123'],
        }
        resp_dry_run = self.fetch(f"{url}/dry_run", body=data, method='PUT')
        self.assertEqual(
            resp_dry_run.json['errors'][0]['description'],
            'Only time and note can be edited for prepaid appointments',
        )


class EditBookingTestCaseUrlUid(EditBookingTestCase):

    @staticmethod
    def generate_url(booking, business_id):
        url = f'/business_api/me/businesses/{business_id}/appointments/{booking.appointment_id}/'
        return url


@pytest.mark.django_db
class MultiFromChargedSingleTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id(40)
        return super().setUp()

    @staticmethod
    def generate_url(booking, business_id):
        url = f'/business_api/me/businesses/{business_id}/appointments/single/{booking.id}/'
        return url

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(Business, 'patient_file_enabled', MagicMock(return_value=True))
    def test_make_multi_from_charged_booking(
        self,
        get_timezone_mock,
        get_location_name_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        get_location_name_mock.return_value = 'adasdas, 00000'

        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=pos)

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
        )

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        staffer.add_services([service.id])

        bci = baker.make(BusinessCustomerInfo, business=self.business)

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_for=bci,
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=datetime(2017, 1, 1, 12, tzinfo=biz_tz),
                booked_till=datetime(2017, 1, 1, 13, tzinfo=biz_tz),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.FINISHED,
                autoassign=True,
                service_variant=service_variant,
            ),
        )

        BookingResource(subbooking=booking, resource=staffer).save()

        # charge booking
        url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'

        body = {
            'bookings': [
                {
                    'booking_id': booking.id,
                    'quantity': 1,
                    'item_price': 100,
                    'discount_rate': 0,
                }
            ],
            'dry_run': False,
            'products': [],
            'transaction_type': 'P',
            'payment_type_code': PaymentTypeEnum.CASH,
            'customer_card_id': bci.id,
            'discount_rate': 0,
            'tip_rate': 0,
        }

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201, resp.json

        # get booking
        url = self.generate_url(booking, self.business.id)

        resp = self.fetch(url)
        assert resp.code == 200

        redirect_url = resp.json['physio_visit_redirect']
        assert f'/visit/{booking.id}/?' in redirect_url
        assert 'x-access-token' in redirect_url
        assert 'business_id' in redirect_url
        assert '/us/2/visit/' in redirect_url
        assert f'customer_id={booking.appointment.booked_for_id}' in redirect_url

        pmnt_info = resp.json['appointment']['payment_info']
        txn_info = pmnt_info['transaction_info']
        assert txn_info['status_code'] == receipt_status.PAYMENT_SUCCESS
        assert pmnt_info['payable'] is False

        # add service to booking (create multibooking from single
        body = {
            "subbookings": [
                {
                    "booked_from": "2017-01-01T12:00",
                    "booked_till": "2017-01-01T13:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant.id, "mode": SVMode.VARIANT},
                    "id": booking.id,
                    "service": {
                        "id": service.id,
                        "variant": {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 10,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    '_version': booking.appointment._version,  # pylint: disable=protected-access
                },
                {
                    "booked_from": "2017-01-01T13:00",
                    "booked_till": "2017-01-01T14:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant.id, "mode": SVMode.VARIANT},
                    "id": None,
                    "service": {
                        "id": service.id,
                        "variant": {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 10,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                },
            ],
            "customer": {
                "mode": ACMode.WALK_IN,
            },
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            'dry_run': False,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200

        pmnt_info = resp.json['appointment']['payment_info']
        txn_info = pmnt_info['transaction_info']
        assert pmnt_info['payable'] is False
        assert txn_info['status_code'] == receipt_status.PAYMENT_SUCCESS

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    def test_get_no_physio_visit_redirect(
        self,
        get_timezone_mock,
        get_location_name_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        get_location_name_mock.return_value = 'adasdas, 00000'

        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=pos)

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
        )

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        staffer.add_services([service.id])

        bci = baker.make(BusinessCustomerInfo, business=self.business)

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_for=bci,
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=datetime(2017, 1, 1, 12, tzinfo=biz_tz),
                booked_till=datetime(2017, 1, 1, 13, tzinfo=biz_tz),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.FINISHED,
                autoassign=True,
                service_variant=service_variant,
            ),
        )

        BookingResource(subbooking=booking, resource=staffer).save()

        url = self.generate_url(booking, self.business.id)

        resp = self.fetch(url)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertEqual(resp.json['physio_visit_redirect'], '')


class MultiFromChargedSingleUrlUidTestCase(MultiFromChargedSingleTestCase):

    @staticmethod
    def generate_url(booking, business_id):
        url = f'/business_api/me/businesses/{business_id}/appointments/{booking.appointment_id}/'
        return url


@pytest.mark.django_db
class AddSubBookingTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id(40)
        return super().setUp()

    def test_add_new_subbooking_with_dry_run(self):
        return self.run_test(dry_run=True)

    def test_add_new_subbooking_without_dry_run(self):
        return self.run_test(dry_run=False)

    def run_test(self, dry_run):
        service = baker.make(Service, business=self.business)
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
        )

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        staffer.add_services([service.id])

        appliance = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            business=self.business,
        )
        appliance.add_services([service.id])

        start = datetime(2050, 1, 1, 10, tzinfo=tz.gettz('UTC'))
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=start,
                booked_till=start + timedelta(hours=2),
                type=Appointment.TYPE.BUSINESS,
                autoassign=True,
                service_variant=variant,
            ),
        )

        BookingResource.objects.bulk_create(
            [
                BookingResource(subbooking=booking, resource=staffer),
                BookingResource(subbooking=booking, resource=appliance),
            ]
        )

        booked_from_0 = booking.booked_from
        booked_till_0 = booking.booked_till
        booked_from_1 = booked_from_0 + timedelta(hours=2)
        booked_till_1 = booked_from_1 + timedelta(hours=1.5)
        old_version = booking.appointment._version  # pylint: disable=protected-access
        body = {
            'customer': {
                'mode': ACMode.WALK_IN,
            },
            'subbookings': [
                {
                    'booked_from': booked_from_0.isoformat(),
                    'booked_till': booked_till_0.isoformat(),
                    'appliance_id': appliance.id,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                },
                {
                    'booked_from': booked_from_1.isoformat(),
                    'booked_till': booked_till_1.isoformat(),
                    'appliance_id': appliance.id,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                },
            ],
            '_version': old_version,
            'dry_run': dry_run,
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
        }

        url = f'/business_api/me/businesses/{self.business.id}/appointments/single/{booking.id}/'

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200
        resp_json = resp.json
        qset = SubBooking.objects.values_list('booked_from', flat=True)
        if dry_run:
            assert resp_json['appointment']['appointment_type'] == AT.SINGLE
            assert resp_json['appointment']['_version'] == old_version
            assert list(qset) == [booked_from_0]
        else:
            assert resp_json['appointment']['appointment_type'] == AT.MULTI
            assert resp_json['appointment']['_version'] != old_version
            assert sorted(qset) == [booked_from_0, booked_from_1]


@pytest.mark.django_db
class RemoveSubBookingTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id(40)
        return super().setUp()

    def test_remove_subbooking_with_dry_run(self):
        return self.run_test(dry_run=True)

    def test_remove_subbooking_without_dry_run(self):
        return self.run_test(dry_run=False)

    def run_test(self, dry_run):
        service = baker.make(Service, business=self.business)
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
        )

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        staffer.add_services([service.id])

        appliance = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            business=self.business,
        )
        appliance.add_services([service.id])

        start = datetime(2050, 1, 1, 11, tzinfo=tz.gettz('UTC'))

        booking_0, booking_1 = create_appointment(
            [
                dict(
                    booked_from=start,
                    booked_till=start + timedelta(hours=2),
                    staffer=staffer,
                    appliance=appliance,
                    autoassign=True,
                    service_variant=variant,
                ),
                dict(
                    booked_from=start + timedelta(minutes=130),
                    booked_till=start + timedelta(minutes=160),
                    autoassign=True,
                    service_variant=variant,
                ),
            ],
            business=self.business,
            updated_by=self.user,
            source=self.customer_booking_src,
            type=Appointment.TYPE.BUSINESS,
        ).subbookings

        booked_from_0 = booking_0.booked_from
        booked_till_0 = booking_0.booked_till
        booked_from_1 = booking_1.booked_from
        old_version = booking_0.appointment._version  # pylint: disable=protected-access
        body = {
            'customer': {
                'mode': ACMode.WALK_IN,
            },
            'subbookings': [
                {
                    'booked_from': booked_from_0.isoformat(),
                    'booked_till': booked_till_0.isoformat(),
                    'appliance_id': appliance.id,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variant.id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            '_version': old_version,
            'dry_run': dry_run,
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
        }

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/appointments/multi/{booking_0.appointment_id}/'
        )

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200
        resp_json = resp.json
        qset = (
            SubBooking.objects.filter(deleted__isnull=True)
            .values_list('booked_from', flat=True)
            .order_by('booked_from')
        )
        if dry_run:
            assert resp_json['appointment']['appointment_type'] == AT.MULTI
            assert resp_json['appointment']['_version'] == old_version
            assert list(qset) == [booked_from_0, booked_from_1]
        else:
            assert resp_json['appointment']['appointment_type'] == AT.MULTI
            assert resp_json['appointment']['_version'] != old_version
            assert list(qset) == [booked_from_0]


@pytest.mark.django_db
def test_update_appointment():
    subbooking = create_subbooking()[0]
    appointment = subbooking.appointment
    new_booked_till = subbooking.booked_till + timedelta(minutes=1)

    # TODO: this is totally wrong: no reschedule in this method!
    with pytest.raises(BookingConflict) as e:
        appointment.update_appointment(
            booked_till=new_booked_till,
        )
        assert e.value.message == 'Specify subbooking'

    subbooking.refresh_from_db()
    assert subbooking.booked_from != new_booked_till
    assert subbooking.appointment.booked_from != new_booked_till

    appointment.update_appointment(
        subbooking=subbooking,
        booked_till=new_booked_till,
        updated_by=appointment.business.owner,
    )
    subbooking.refresh_from_db()
    assert subbooking.booked_till == new_booked_till
    assert subbooking.appointment.booked_till == new_booked_till
