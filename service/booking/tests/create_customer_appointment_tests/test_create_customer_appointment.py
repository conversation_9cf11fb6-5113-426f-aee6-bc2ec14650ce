# pylint: disable=too-many-lines,too-many-positional-arguments
import datetime
import uuid
from datetime import time
from decimal import Decimal

import pytest
import responses
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.cache import cache
from django.test import TestCase
from django.test.utils import override_settings
from django.utils.translation import gettext as _
from facebook_business.adobjects.serverside.gender import Gender as FBGender
from mock import patch
from mock.mock import MagicMock
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC
from rest_framework import status
from segment.analytics import Client

from lib.baker_utils import get_or_create_booking_source
from lib.deeplink import BranchIOAppTypes, generate_deeplink
from lib.deeplink.branchio.client import BranchIOClient
from lib.deeplink.deeplinks_ms.ports import generate_deeplink_ms
from lib.facebook.enums import EventName
from lib.facebook.service import FacebookEventService
from lib.feature_flag.feature import CriticalPY1902Flag
from lib.feature_flag.feature.analytics import (
    BranchIOCustomerBookingTrackingFlag,
    FacebookAnalyticsCustomerBookingTrackingFlag,
)
from lib.feature_flag.feature.booking import TrackAppointmentCreatedAnalytics
from lib.feature_flag.feature.notification import NotifyOwnerThroughEmailAboutNewBookingFlag
from lib.feature_flag.feature.payment import PromoteTTPAndBCRPaymentsToCustomersFlag
from lib.payment_gateway.enums import (
    BalanceTransactionType,
    PaymentMethodType,
    WalletOwnerType,
)
from lib.payment_providers.enums import PaymentStatus
from lib.payment_providers.mocks import (
    get_payment_object_mock,
    get_invalid_tokenized_payment_method_mock,
)
from lib.payments.enums import TokenizedPaymentMethodInternalStatus
from lib.test_utils import (
    get_in_memory_img,
    increase_appointment_next_id,
    is_dict_subset_of_dict,
    spy_mock,
)
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from lib.tools import id_to_external_api
from service.booking.tests.create_customer_appointment_tests import (
    AppointmentTestCaseMixin,
    make_staffer_notifications,
)
from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps import consts
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import (
    Appointment,
    AppointmentAnalytics,
    BookingChange,
    BookingSources,
    SubBooking,
)
from webapps.booking.notifications.appointment_created import (
    AppointmentCreatedNotification,
    SubBookingCreatedNotification,
)
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_custappt_data,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business._admin.forms import BusinessAdminForm
from webapps.business.baker_recipes import service_variant_recipe, staffer_recipe
from webapps.business.enums import (
    ComboType,
    CustomData,
    PriceType,
)
from webapps.business.models import (
    Business,
    Resource,
    SalonNetwork,
    Service,
    ServiceAddOn,
    ServiceVariant,
    ServiceVariantPayment,
    TravelingToClients,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.service import ComboMembership
from webapps.business.resources import AnyResource
from webapps.consents.models import (
    Consent,
    ConsentForm,
)
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.kill_switch.models import KillSwitch
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.enums import NotificationSendStatus, ScheduleState
from webapps.notification.models import (
    NotificationHistory,
    NotificationSMSStatistics,
    NotificationSchedule,
    UserNotification,
    PMAvailabilityNotificationRecord,
)
from webapps.notification.recipients import Recipient
from webapps.notification.tasks import execute_task
from webapps.pop_up_notification.models import GenericPopUpNotificationModel
from webapps.pos.baker_recipes import pos_recipe, posplan_recipe
from webapps.pos.enums import (
    POSPlanPaymentTypeEnum,
    PaymentProviderEnum,
    PaymentTypeEnum,
    compatibilities,
    receipt_status,
    TapToPayStatus,
)
from webapps.pos.models import (
    PaymentMethod,
    PaymentType,
    POS,
    POSPlan,
    Tip,
    Transaction,
)
from webapps.pos.provider.fake import _CARDS
from webapps.premium_services.public import (
    DayOfWeek as PremiumServicesDayOfWeek,
    PeakHour,
    PeakHourServiceFactory,
    Service as PremiumServicesService,
)
from webapps.schedule.enums import DayOfWeek
from webapps.schedule.models import (
    BusinessHours,
    ResourceHours,
)
from webapps.search_engine_tuning.models import BusinessCustomerTuning
from webapps.segment.enums import DeviceTypeName
from webapps.segment.feature_flags import EnergyCBCreatedForCustomerBranchEventFlag
from webapps.segment.tasks import segment_api_appointment_booked_task
from webapps.stripe_integration.models import StripeAccount
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum

TEST_DATETIME = datetime.datetime(2018, 1, 1, tzinfo=UTC)


@pytest.mark.django_db
class LegacyCreateCustomerAppointmentTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        self.pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        increase_appointment_next_id()

    @staticmethod
    def _assert_notification_delivered__sender_system(appt_id):
        assert not NotificationHistory.objects.filter(appointment_id=appt_id).exists()
        assert not NotificationSMSStatistics.objects.exists()

        notification_history_record = NotificationHistoryDocument.task_get(
            task_id=f'booking_changed:customer_booking_confirmation:appointment_id={appt_id}',
            type=UserNotification.SMS_NOTIFICATION,
        )

        assert len(notification_history_record.title) < 300
        assert notification_history_record.title.count('booksy.com') == 1
        assert notification_history_record.sender == NotificationHistory.SENDER_SYSTEM
        assert notification_history_record.status == NotificationSendStatus.GATEWAY_SUCCESS

    def test_customer_unauthorized_403(self):
        url = self.appointments_url(self.business.id)
        self.session._session_key = None  # pylint: disable=protected-access
        headers = self.get_headers(url)
        headers.pop('X-ACCESS-TOKEN')

        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['dry_run'] = False  # will be set by url

        resp = self.fetch(url, method='POST', body=body, headers=headers)
        assert resp.code == 403
        assert resp.json == {
            'errors': [
                {
                    'code': 'unauthorized',
                    'field': 'access_token',
                    'description': _('Unauthorized access attempt.'),
                }
            ],
        }

        url += 'dry_run/'
        resp = self.fetch(url, method='POST', body=body, headers=headers)
        assert resp.code == status.HTTP_201_CREATED

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @patch.object(BranchIOClient, 'track_event')
    @override_feature_flag({EnergyCBCreatedForCustomerBranchEventFlag: True})
    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
    )
    @override_eppo_feature_flag({TrackAppointmentCreatedAnalytics.flag_name: True})
    def test_handler_custappt_create_single(
        self,
        analytics_branchio_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
            invited=True,
        )
        self.user.cell_phone = '+***********'
        self.user.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        # test no_thumbs
        in_memory_img = get_in_memory_img()
        image = baker.make(
            Image,
            image=in_memory_img,
            business=self.business,
            category=ImageTypeEnum.BIZ_PHOTO,
            is_cover_photo=True,
        )
        image.reindex(refresh_index=True)
        self.business.reindex(refresh_index=True)

        self.customer_booking_src.chargeable = True
        self.customer_booking_src.name = 'Android'
        self.customer_booking_src.save()

        resp = self.fetch(
            url,
            method='POST',
            body=body,
            extra_headers={'X-Analytics-Tokens': 'idfv;someidfvman'},
        )
        assert resp.code == status.HTTP_201_CREATED
        self._test_martech_events(
            analytics_track_mock,
            analytics_identify_mock,
            analytics_branchio_mock,
            resp,
        )
        self._test_tokenized_payments(
            resp_json=resp.json,
            appointment_status=Appointment.STATUS.ACCEPTED,
            google_pay_available=True,
            apple_pay_available=True,
        )
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        # test no_thumbs
        assert 'thumbnails' in resp.json['appointment']['business']['images']['cover'][0]

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # BookingChanges and NotificationSchedules
        appt_id = resp.json['appointment']['appointment_uid']
        assert (
            BookingChange.objects.filter(
                appointment_id=appt_id,
            ).count()
            == 1
        )
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_confirmation:appointment_id={appt_id}',
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:business_booking_confirmation:appointment_id={appt_id}',
        ).exists()

        # test no_thumbs
        last = SubBooking.objects.last()
        last.delete()
        last.appointment.delete()
        body['no_thumbs'] = True
        resp = self.fetch(url, body=body, method='POST')
        assert 'thumbnails' not in resp.json['appointment']['business']['images']['cover'][0]
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_reminder:appointment_id={appt_id}',
        ).exists()

    @patch.object(BranchIOClient, 'track_event')
    @override_eppo_feature_flag(
        {
            BranchIOCustomerBookingTrackingFlag.flag_name: True,
        }
    )
    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
    )
    def test_handler_custappt_create_single_with_branchio(self, analytics_branchio_mock):
        self.assertEqual(Appointment.objects.filter(business=self.business).count(), 0)
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(
            url,
            method='POST',
            body=body,
            extra_headers={'X-Analytics-Tokens': 'idfv;someidfvman'},
        )
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(
            1,
            Appointment.objects.filter(
                business=self.business,
                type=Appointment.TYPE.CUSTOMER,
            ).count(),
        )
        self.assertEqual(analytics_branchio_mock.call_count, 1)
        dict_assert(
            analytics_branchio_mock.call_args_list[0][1],
            {'event_name': '1st_CB_Created_For_Business'},
        )

    @patch.object(BranchIOClient, 'track_event')
    @override_eppo_feature_flag({BranchIOCustomerBookingTrackingFlag.flag_name: True})
    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
    )
    def test_handler_customer_appointment_create_single_as_5th_booking_with_branchio(
        self,
        analytics_branchio_mock,
    ):
        create_appointment(
            [],
            business=self.business,
            source=self.booking_source,
            type=Appointment.TYPE.BUSINESS,
        )
        for _ in range(4):
            create_appointment(
                [],
                business=self.business,
                source=self.booking_source,
                type=Appointment.TYPE.CUSTOMER,
            )
        self.assertEqual(Appointment.objects.filter(business=self.business).count(), 5)
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(
            url,
            method='POST',
            body=body,
            extra_headers={'X-Analytics-Tokens': 'idfv;someidfvman'},
        )
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(
            5,
            Appointment.objects.filter(
                business=self.business,
                type=Appointment.TYPE.CUSTOMER,
            ).count(),
        )
        self.assertEqual(6, Appointment.objects.filter(business=self.business).count())
        self.assertEqual(analytics_branchio_mock.call_count, 1)
        dict_assert(
            analytics_branchio_mock.call_args_list[0][1],
            {'event_name': '5th_CB_Achieved'},
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    @patch.object(FacebookEventService, 'send_event_with_custom_data')
    @override_eppo_feature_flag(
        {
            FacebookAnalyticsCustomerBookingTrackingFlag.flag_name: True,
        }
    )
    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
    )
    def test_handler_custappt_create_single_with_facebook(self, facebook_send_event_mock):
        self.assertEqual(Appointment.objects.filter(business=self.business).count(), 0)
        self.user.gender = 'M'
        self.user.save()

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(
            url,
            method='POST',
            body=body,
            extra_headers={'X-Analytics-Tokens': 'idfv;someidfvman'},
        )
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(
            1,
            Appointment.objects.filter(
                business=self.business,
                type=Appointment.TYPE.CUSTOMER,
            ).count(),
        )

        facebook_send_event_mock.assert_called_once()

        self.assertEqual(
            facebook_send_event_mock.call_args.kwargs['event_name'],
            EventName.FIRST_CB_CREATED_FOR_BUSINESS,
        )
        self.assertEqual(facebook_send_event_mock.call_args.kwargs['event_time'], 1514764800)

        property_keys = [
            'business_postal_code',
            'booked_from',
            'appointment_type',
            'business_id',
            'appointment_id',
            'customer_id',
            'is_xCB',
            'is_xCB_xCategory',
            'energy_booking',
            'business_name',
            'business_primary_category',
            'family_and_friends_role',
            'source',
            'category_id',
            'category_name',
            'service_id',
            'service_name',
            'service_price',
            'staff_id',
            'booking_score',
            'urban_area',
            'urban_subarea',
            'focus_area',
            'email',
        ]
        self.assertTrue(
            all(p in facebook_send_event_mock.call_args.kwargs['data'] for p in property_keys)
        )
        self.assertTrue(
            all(
                p in facebook_send_event_mock.call_args.kwargs['user_data']
                for p in {
                    'email': '<EMAIL>',
                    'first_name': 'Extendable',
                    'last_name': 'Kamal',
                    'external_id': 22,
                    'phone': '123456789',
                    'gender': FBGender.MALE,
                }
            )
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_handler_custappt_create_single_same_day(self):
        self.user.cell_phone = '+***********'
        self.user.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(
                hour=time(10, 0),
                specific_date=TEST_DATETIME.date(),
            ),
            staffer=self.staffer,
            recurring=False,
        )
        self.business.reindex(refresh_index=True)

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # BookingChanges and NotificationSchedules
        appt_id = resp.json['appointment']['appointment_uid']
        assert (
            BookingChange.objects.filter(
                appointment_id=appt_id,
            ).count()
            == 1
        )
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_confirmation:appointment_id={appt_id}',
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:business_booking_confirmation:appointment_id={appt_id}',
        ).exists()
        assert not NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_reminder:appointment_id={appt_id}',
        ).exists()

    def _test_martech_events(
        self,
        analytics_track_mock,
        analytics_identify_mock,
        analytics_branchio_mock,
        response,
    ):
        assert analytics_branchio_mock.call_count == 1
        dict_assert(
            analytics_branchio_mock.call_args_list[0][1],
            {
                'event_name': 'Energy_CB_Created_For_Customer',
                'event_data': {
                    'appointment_id': id_to_external_api(
                        response.json['appointment']['appointment_uid']
                    ),
                    'appointment_type': 'Single',
                    'booking_id': id_to_external_api(SubBooking.objects.last().id),
                    'business_id': id_to_external_api(self.business.id),
                    'country': settings.API_COUNTRY,
                    'device_type': DeviceTypeName.ANDROID.value,
                    'email': '<EMAIL>',
                    'user_id': id_to_external_api(self.user.id),
                },
                'user_data': {
                    'os': 'iOS',
                    'idfv': 'someidfvman',
                },
                'app_type': BranchIOAppTypes.CUSTOMER.value,
            },
        )
        assert analytics_track_mock.call_count == 5
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {'event': 'CB_Created_For_Customer'},
        )
        cb_created_for_customer_properties_keys = analytics_track_mock.call_args_list[0][1][
            'properties'
        ].keys()
        assert set(cb_created_for_customer_properties_keys).issuperset(
            {
                'booked_from',
                'business_id',
                'business_name',
                'business_postal_code',
                'business_primary_category',
                'category_id',
                'category_name',
                'country',
                'device_type',
                'email',
                'focus_area',
                'booking_score',
                'energy_booking',
                'service_id',
                'service_name',
                'service_price',
                'source',
                'staff_id',
                'urban_area',
                'urban_subarea',
                'control_group',
                'user_role',
                'user_id',
                'phone',
                'currency',
                'is_xCB_xCategory',
                'is_xCB',
                'appointment_type',
                'appointment_id',
                'family_and_friends_role',
            }
        )
        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': '1st_CB_Created_For_Customer',
            },
        )
        first_cb_created_for_customer_properties_keys = set(
            analytics_track_mock.call_args_list[1][1]['properties'].keys()
        )
        assert first_cb_created_for_customer_properties_keys.issuperset(
            {
                'booked_from',
                'business_id',
                'business_name',
                'business_postal_code',
                'business_primary_category',
                'category_id',
                'category_name',
                'country',
                'device_type',
                'email',
                'service_id',
                'service_name',
                'service_price',
                'source',
                'staff_id',
                'control_group',
                'booking_score',
                'energy_booking',
                'appointment_id',
                'last_booking_longitude',
                'last_booking_latitude',
                'family_and_friends_role',
            }
        )
        cb_created = analytics_track_mock.call_args_list[2][1]
        cb_created_1st = analytics_track_mock.call_args_list[3][1]
        assert cb_created['event'] == 'CB_Created_For_Business'
        assert cb_created['properties']['client_from_invite']
        assert not cb_created['properties']['family_and_friends_role']
        assert cb_created_1st['event'] == '1st_CB_Created_For_Business'
        assert not cb_created_1st['properties']['family_and_friends_role']
        assert analytics_identify_mock.call_count == 4

    def test_handler_custappt_create_single_by_instagram(self):
        self.user.cell_phone = '+***********'
        self.user.save()
        self.customer_api_key = 'kjsafbjfad'
        self.customer_booking_src = get_or_create_booking_source(
            name=consts.INSTAGRAM,
            app_type=BookingSources.CUSTOMER_APP,
            api_key=self.customer_api_key,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # BookingChanges and NotificationSchedules
        appt_id = resp.json['appointment']['appointment_uid']
        self._assert_notification_delivered__sender_system(appt_id)

    def test_handler_booking_reminder_time_change(self):
        self.user.cell_phone = '+***********'
        self.user.save()

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(
                hour=time(10, 0),
                specific_date=datetime.datetime.now() + datetime.timedelta(days=2),
            ),
            staffer=self.staffer,
            recurring=False,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id

        appointment_uid = resp.json['appointment']['appointment_uid']
        reminder_task_id = (
            f'booking_changed:customer_booking_reminder:appointment_id={appointment_uid}'
        )

        old_custom_data = dict(self.business.custom_data)
        self.business.custom_data[CustomData.BOOKING_REMIND_BEFORE] = 72
        self.business.save()
        BusinessAdminForm._handle_change_custom_data_booking_remind_before(  # pylint: disable=protected-access
            self.business,
            old_custom_data,
        )

        NotificationHistoryDocument.refresh_index()
        reminder_history_document = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=reminder_task_id,
            )
            .execute()
        )
        assert len(reminder_history_document) == 0

        reminder_schedule = NotificationSchedule.objects.filter(
            task_id=reminder_task_id,
        ).first()
        execute_task(reminder_schedule)

        NotificationHistoryDocument.refresh_index()
        reminder_history_document = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=reminder_task_id,
            )
            .execute()
        )
        assert len(reminder_history_document)

    @override_feature_flag(
        {
            CriticalPY1902Flag: True,
        }
    )
    def test_handler_booking_reminder_time_change_py_1902(self):
        self.user.cell_phone = '+***********'
        self.user.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(
                hour=time(10, 0),
                specific_date=datetime.datetime.now() + datetime.timedelta(days=2),
            ),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        appointment_uid = resp.json['appointment']['appointment_uid']
        reminder_task_id = (
            f'booking_changed:customer_booking_reminder:appointment_id={appointment_uid}'
        )
        old_custom_data = dict(self.business.custom_data)
        self.business.custom_data[CustomData.BOOKING_REMIND_BEFORE] = 72
        self.business.save()
        BusinessAdminForm._handle_change_custom_data_booking_remind_before(  # pylint: disable=protected-access
            self.business,
            old_custom_data,
        )
        NotificationHistoryDocument.refresh_index()
        reminder_history_document = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=reminder_task_id,
            )
            .execute()
        )
        assert len(reminder_history_document) == 0
        reminder_schedule = NotificationSchedule.objects.filter(
            task_id=reminder_task_id,
        ).first()
        execute_task(reminder_schedule)

        NotificationHistoryDocument.refresh_index()
        reminder_history_document = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=reminder_task_id,
            )
            .execute()
        )
        assert len(reminder_history_document)

    @override_feature_flag(
        {
            CriticalPY1902Flag: True,
        }
    )
    def test_handler_booking_reminder_time_change_pxmar_155(self):
        spy__lib_generate_deeplink = spy_mock(generate_deeplink_ms)
        spy__cache_generate_deeplink = spy_mock(generate_deeplink)
        cache.delete('deeplink:dev:us:my_bookings')
        with (
            patch('lib.deeplink.adapters.generate_deeplink_ms', spy__lib_generate_deeplink),
            patch('lib.deeplink.cache.generate_deeplink', spy__cache_generate_deeplink),
        ):
            self.user.cell_phone = '+***********'
            self.user.save()

            url = self.appointments_url(self.business.id)
            body = build_custappt_data(
                self.variant,
                booked_from=self._dt_from_hour(
                    hour=time(10, 0),
                    specific_date=datetime.datetime.now() + datetime.timedelta(days=2),
                ),
                staffer=self.staffer,
                recurring=False,
            )

            resp = self.fetch(url, method='POST', body=body)
            assert resp.code == status.HTTP_201_CREATED
            assert resp.json['appointment']['business']['id'] == self.business.id

            appointment_uid = resp.json['appointment']['appointment_uid']
            reminder_task_id = (
                f'booking_changed:customer_booking_reminder:appointment_id={appointment_uid}'
            )

            old_custom_data = dict(self.business.custom_data)
            self.business.custom_data[CustomData.BOOKING_REMIND_BEFORE] = 72
            self.business.save()
            BusinessAdminForm._handle_change_custom_data_booking_remind_before(  # pylint: disable=protected-access
                self.business,
                old_custom_data,
            )

            NotificationHistoryDocument.refresh_index()
            reminder_history_document = (
                NotificationHistoryDocument.search()
                .query(
                    'term',
                    task_id=reminder_task_id,
                )
                .execute()
            )
            assert len(reminder_history_document) == 0

            reminder_schedule = NotificationSchedule.objects.filter(
                task_id=reminder_task_id,
            ).first()
            execute_task(reminder_schedule)

        NotificationHistoryDocument.refresh_index()
        reminder_history_document = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=reminder_task_id,
            )
            .execute()
        )
        assert len(reminder_history_document)
        assert spy__lib_generate_deeplink.mock.call_count == 2
        assert spy__cache_generate_deeplink.mock.call_count == 1
        dict_assert(
            spy__cache_generate_deeplink.mock.call_args_list[0].kwargs,
            {
                'app_type': BranchIOAppTypes.CUSTOMER,
                'data': {
                    '$deeplink_path': 'my_bookings',
                    '$desktop_url': 'http://localhost:8600/en-us/dl/my-bookings',
                    '$ios_deeplink_path': 'my_bookings',
                    'mobile_deeplink': 'my_bookings',
                },
            },
        )

    def test_handler_custappt_create_single_by_instagram_killswitch(self):
        from lib.jinja_renderer import ScenariosJinjaRenderer

        baker.make(
            KillSwitch,
            name=KillSwitch.System.SMS_WITH_DEEPLINKS_ENABLED,
            is_killed=True,
        )
        ScenariosJinjaRenderer.clear_renderer()
        self.user.cell_phone = '+***********'
        self.user.save()
        self.customer_api_key = 'kjsafbjfad'
        self.customer_booking_src = get_or_create_booking_source(
            name=consts.INSTAGRAM,
            app_type=BookingSources.CUSTOMER_APP,
            api_key=self.customer_api_key,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # BookingChanges and NotificationSchedules
        appt_id = resp.json['appointment']['appointment_uid']
        self._assert_notification_delivered__sender_system(appt_id)

    def test_handler_custappt_create_multi(self):
        self.create_extra_staffer()  # for autoassign to work
        self.customer_booking_src.name = consts.ANDROID
        self.customer_booking_src.save()
        notifications_count = NotificationSchedule.objects.count()

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=None,  # no needed in dry_run
        )
        # add second booking
        body['subbookings'].append(
            {
                'booked_from': None,  # multimatcher should assign it
                'service_variant': (
                    {
                        'mode': SVMode.VARIANT,
                        'id': self.gap_hole_variant.id,
                    }
                ),
                'staffer_id': -1,  # autoassign
            }
        )
        body['dry_run'] = True

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert (
            SubBooking.objects.filter(
                appointment__business=self.business,
            ).count()
            == 0
        )
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        assert resp.json['meta'] is None

        assert BookingChange.objects.count() == 0
        assert NotificationSchedule.objects.count() == notifications_count

        # this time make
        body = resp.json['appointment']
        body['dry_run'] = False
        body['recurring'] = False
        body['bci_agreements'] = {
            agreement['name']: agreement['value']
            for agreement in resp.json['appointment']['bci_agreements']
        }
        body['bci_agreements']['web_communication_agreement'] = True

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert (
            SubBooking.objects.filter(
                appointment__business=self.business,
            ).count()
            == 2
        )
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2
        assert not resp.json['appointment']['subbookings'][0]['autoassign']
        assert resp.json['appointment']['subbookings'][1]['autoassign']

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # BookingChanges and NotificationSchedules
        assert (
            BookingChange.objects.filter(
                appointment_id=resp.json['appointment']['appointment_id'],
            ).count()
            == 2
        )
        appointment_id = resp.json['appointment']['appointment_uid']
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:customer_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:business_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()

        appointment = Appointment.objects.filter(
            id=resp.json['appointment']['appointment_uid'],
        ).first()
        tuning = BusinessCustomerTuning.objects.filter(
            customer=appointment.booked_for,
        ).first()
        assert tuning and tuning.any_mobile_customer_appointments is True
        assert appointment.booked_for.web_communication_agreement is True

    def test_handler_custappt_create_with_deposit(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        addon_1 = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price=10.00,
            price_type=PriceType.FIXED,
            services=[
                with_deposit.service,
            ],
            duration=relativedelta(minutes=5),
            max_allowed_quantity=10,
        )
        body['subbookings'][0]['addons'] = [
            {
                'id': addon_1.id,
                'quantity': 1,
            },
        ]
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '15.78'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

    def test_handler_custappt_create_with_deposit_discount(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('17.30'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='**********',
            user=self.user,
            discount=20,
        )

        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )
        assert deposit_info['already_paid'] == '$13.84'
        assert deposit_info['total'] == '$13.84'

    def test_handler_custappt_create_with_deposit_discount_stardust(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('17.30'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id) + '?stardust=True'

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='**********',
            user=self.user,
            discount=20,
        )

        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }

        body[compatibilities.COMPATIBILITIES] = {
            compatibilities.STARDUST: True,
        }

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )
        assert deposit_info['already_paid'] == '$0.00'
        assert deposit_info['total'] == '$13.84'

    def test_handler_custappt_create_with_prepayment(self):
        assert self.business.pos_pay_by_app_enabled
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '9.99'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_handler_custappt_create_with_prepayment_wrong_card(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)

        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(resp.json['errors']), 1)
        self.assertEqual(resp.json['errors'][0]['code'], 'wrong_payment_provider')

    def test_handler_custappt_create_with_prepayment_wrong_card_business_stripe(self):
        self.business.pos.pos_refactor_stage2_enabled = True
        self.business.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.business.pos.save()

        baker.make(
            StripeAccount,
            pos=self.business.pos,
            kyc_verified_at_least_once=True,
            external_id='123',
        )

        self.assertTrue(self.business.pos_pay_by_app_enabled)
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)

        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(resp.json['errors']), 1)
        self.assertEqual(resp.json['errors'][0]['code'], 'wrong_payment_provider')

    @patch('webapps.pos.services.BalanceTransaction.objects.get', MagicMock())
    @patch(
        'webapps.payment_providers.services.common.CommonPaymentServices.save_payment_to_history',
        MagicMock(),
    )
    @patch(
        'webapps.payment_providers.services.common.payment_providers_payment_updated_event',
        MagicMock(),
    )
    @patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_customer_wallet', MagicMock())
    @patch('webapps.payment_providers.ports.payment_ports.TokenizedPaymentMethod.objects.filter')
    @patch('webapps.payment_providers.ports.payment_ports.TokenizedPaymentMethod.objects.get')
    @patch('webapps.payment_providers.ports.payment_ports.Payment.objects.get')
    @patch('webapps.payment_gateway.ports.BalanceTransactionService.get_balance_transaction')
    @patch(
        'webapps.stripe_integration.services.SynchronizeService.synchronize_stripe_payment_intent',
        MagicMock(),
    )
    @patch('webapps.point_of_sale.services.basket_payment.initialize_payment_adapter')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.point_of_sale.services.basket_payment.get_customer_wallet_id_adapter')
    @patch('service.booking.appointments.CustomerAppointmentTransactionSerializer.save')
    def test_handler_custappt_create_with_prepayment_stripe_invalid_card_business_stripe(
        self,
        customer_appointment_transaction_save_mock,
        basket_payment_get_customer_wallet_mock,
        proxy_get_customer_wallet_id_adapter_mock,
        payment_adapter_mock,
        get_balance_transaction_mock,
        payment_objects_get_mock,
        tokenized_objects_get_mock,
        tokenized_objects_filter_mock,
    ):
        payment_object_mock = get_payment_object_mock()
        payment_objects_get_mock.return_value = payment_object_mock

        tokenized_object_mock = get_invalid_tokenized_payment_method_mock()
        tokenized_objects_get_mock.return_value = tokenized_object_mock
        tokenized_objects_filter_mock.return_value.first.return_value = tokenized_object_mock

        get_balance_transaction_mock.return_value = MagicMock(
            id=uuid.uuid4(),
            payment_method=PaymentMethodType.CARD,
            transaction_type=BalanceTransactionType.PAYMENT,
            external_id=123,
        )
        payment_adapter_mock.return_value = uuid.uuid4()
        basket_payment_get_customer_wallet_mock.return_value = 1
        proxy_get_customer_wallet_id_adapter_mock.return_value = 1
        self.business.pos.pos_refactor_stage2_enabled = True
        self.business.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.business.pos.save()

        posplan = posplan_recipe.make(
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.business.pos.pos_plans.add(posplan)

        baker.make(
            StripeAccount,
            pos=self.business.pos,
            kyc_verified_at_least_once=True,
            external_id='123',
        )

        self.assertTrue(self.business.pos_pay_by_app_enabled)
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)

        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
            tokenized_pm_id=uuid.uuid4(),
            expiry_year=2057,
            expiry_month=12,
        )

        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        assert customer_appointment_transaction_save_mock.call_count == 0
        self.assertEqual(len(resp.json['errors']), 1)
        dict_assert(
            resp.json['errors'][0],
            {
                'code': 'generic_error',
                'description': 'Declined. Try again or try another payment method.',
                'field': 'non_field_errors',
            },
        )
        assert payment_object_mock.status == PaymentStatus.NEW  # not changed

        url = f'/api/{settings.API_COUNTRY}/2/customer_api/me/payment_methods/'
        resp = self.fetch(url, method='GET')
        assert (
            resp.json['payment_methods'][0]['internal_status']
            == TokenizedPaymentMethodInternalStatus.INVALID
        )

    def test_handler_custappt_create_with_both(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])
        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('80.17'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '80.17'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )

    def test_handler_multibooking_with_deposit(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(12, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body3 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(13, 30)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings'] + body3['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 3
        subbookings = resp.json['appointment']['subbookings']
        # checking wait time and long wait time warnings
        assert subbookings[0]['wait_time'] == {}
        assert 'wait_type' not in subbookings[0]
        assert 'long_wait_time' not in subbookings[0]
        assert subbookings[1]['wait_time'] == {'minutes': 5, 'hours': 1}
        assert subbookings[1]['wait_type'] == 'wait'
        assert subbookings[1]['long_wait_time']
        assert subbookings[2]['wait_time'] == {'minutes': 30}
        assert subbookings[2]['wait_type'] == 'wait'
        assert not subbookings[2]['long_wait_time']

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None

    def test_handler_custappt_create_with_none_deposit(self):
        assert self.business.pos_pay_by_app_enabled
        sv1 = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        sv1.add_staffers([self.staffer])

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=sv1,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info is None

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None

    def test_handler_custappt_create_with_prepayment_old_app(self):
        assert self.business.pos_pay_by_app_enabled
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        del body['compatibilities']
        resp = self.fetch(url, method='POST', body=body)

        # There is no prepayment compatibilities required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['field'] == 'prepayment_needed'
        assert resp.json['errors'][0]['code'] == 'prepayment_not_supported'

    def test_handler_custappt_create_with_deposit_old_app(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        del body['compatibilities']
        resp = self.fetch(url, method='POST', body=body)

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # There is no deposit compatibilities required
        assert resp.code == status.HTTP_201_CREATED

    def test_handler_custappt_create_single_with_live_update(self):
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.address = 'asdasdadaddaad'
        self.business.latitude = 2.3456
        self.business.longitude = 2.3456
        self.business.save()

        self.date = datetime.datetime.now() + datetime.timedelta(days=7)

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        # test no_thumbs
        in_memory_img = get_in_memory_img()
        image = baker.make(
            Image,
            image=in_memory_img,
            business=self.business,
            category=ImageTypeEnum.BIZ_PHOTO,
            is_cover_photo=True,
        )
        image.reindex(refresh_index=True)
        self.business.reindex(refresh_index=True)

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        # test no_thumbs
        last = SubBooking.objects.last()
        last.delete()
        last.appointment.delete()
        body['no_thumbs'] = True
        resp = self.fetch(url, body=body, method='POST')
        assert 'thumbnails' not in resp.json['appointment']['business']['images']['cover'][0]


@pytest.mark.django_db
class CreateCustomerAppointmentTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):  # pylint: disable=too-many-public-methods
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
        )

        baker.make(Tip, pos=self.pos, rate=0, default=True)
        baker.make(Tip, pos=self.pos, rate=10)
        baker.make(Tip, pos=self.pos, rate=30)

        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        self.adyen_pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            # not random just to force non-negative value of a fee (same for param below)
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(self.adyen_pos_plan)

    def test_handler_custapp_create_appointment_after_consent_revoke(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
            web_communication_agreement=False,
            ask_for_consent=True,
        )
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['ask_for_consent'] = False
        body['bci_agreements'] = {'web_communication_agreement': True}
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        bci.refresh_from_db()
        self.assertFalse(bci.ask_for_consent)

    def test_handler_custappt_create_single_wrong_agreements_format(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
            web_communication_agreement=False,
            ask_for_consent=True,
        )
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['ask_for_consent'] = False
        body['bci_agreements'] = {'web_communication_agreement': 'true'}
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        bci.refresh_from_db()
        self.assertFalse(bci.ask_for_consent)

    @parameterized.expand([(True,), (False,)])
    @patch(
        'webapps.business.notifications.marketing_consent.SMSMarketingDoubleOptInNotification.send'
    )
    def test_handler_custapp_create_first_appointment_double_opt_in_sms(
        self, communication_agreed, send_mock
    ):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['bci_agreements'] = {'web_communication_agreement': communication_agreed}
        with TestCase.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        if communication_agreed:
            send_mock.assert_called_once()
        else:
            send_mock.assert_not_called()

    def test_handler_custappt_create_single(self):
        NotificationHistoryDocument.tasks_clear()
        make_staffer_notifications(business=self.business, staffer=self.staffer)
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # GTM Energy booking mixin
        assert set(resp.json['appointment']['meta'].keys()).issuperset(
            {'energy_booking', 'booking_score', 'first', 'cross', 'first_cross'}
        )

        # BookingChanges and NotificationSchedules
        assert (
            BookingChange.objects.filter(
                appointment_id=resp.json['appointment']['appointment_uid']
            ).count()
            == 1
        )
        appointment_id = resp.json['appointment']['appointment_uid']
        appointment = Appointment.objects.get(pk=appointment_id)
        appt_notif = AppointmentCreatedNotification(appointment)
        appt_notif.schedule_record.assert_skipped()
        staffer_recipient = Recipient.from_staffer(self.staffer)
        assert staffer_recipient not in appt_notif.resolved_recipients

        booking_notif = SubBookingCreatedNotification(appointment.subbookings[0])
        booking_notif.schedule_record.assert_success()

        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:customer_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:business_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()

        notification = NotificationHistoryDocument.task_get(
            task_id=f'booking_changed:business_booking_confirmation:'
            f'appointment_id={appointment_id}',
            type=UserNotification.PUSH_NOTIFICATION,
        )
        assert notification is not None

    @override_eppo_feature_flag({NotifyOwnerThroughEmailAboutNewBookingFlag.flag_name: True})
    def test_handler_custappt_create_single_owner_should_receive_email_notification(self):
        NotificationHistoryDocument.tasks_clear()

        user = user_recipe.make(cell_phone='123456789')
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        staffer = staffer_recipe.make(
            business=self.business,
            staff_email='<EMAIL>',
        )
        self._make_resource_calendar(staffer)
        staffer.add_services([self.service.id])
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=staffer,
            recurring=False,
        )

        resp = self.fetch(self.appointments_url(self.business.id), method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        staffer_email_notification_count = NotificationHistoryDocument.task_count(
            type=UserNotification.EMAIL_NOTIFICATION,
            sender=NotificationHistory.SENDER_CUSTOMER,
            recipient_email=staffer.staff_email,
        )
        self.assertEqual(staffer_email_notification_count, 1)
        owner_email_notification_count = NotificationHistoryDocument.task_count(
            type=UserNotification.EMAIL_NOTIFICATION,
            sender=NotificationHistory.SENDER_CUSTOMER,
            recipient_email=self.user.email,
        )
        self.assertEqual(owner_email_notification_count, 1)
        appt_id = resp.json['appointment']['appointment_uid']
        self.assertTrue(
            NotificationSchedule.objects.filter(
                task_id=f'booking_changed:business_booking_confirmation:appointment_id={appt_id}',
            ).exists()
        )

    def test_handler_custappt_create_single_with_requested_staffer(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        appointment_id = resp.json['appointment']['appointment_uid']
        appointment = Appointment.objects.get(pk=appointment_id)
        assert appointment.subbookings[0].is_staffer_requested_by_client

    def test_handler_custappt_create_single_with_any_staffer(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=AnyResource,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        appointment_id = resp.json['appointment']['appointment_uid']
        appointment = Appointment.objects.get(pk=appointment_id)
        assert appointment.subbookings[0].is_staffer_requested_by_client is False

    def test_handler_custappt_create_single_without_push_2_0(self):
        NotificationHistoryDocument.tasks_clear()
        make_staffer_notifications(business=self.business, staffer=self.staffer)
        self.business.custom_data[CustomData.CAN_USE_FRONTDESK] = True
        self.business.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # GTM Energy booking mixin
        assert set(resp.json['appointment']['meta'].keys()).issuperset(
            {'energy_booking', 'booking_score'}
        )

        # BookingChanges and NotificationSchedules
        assert (
            BookingChange.objects.filter(
                appointment_id=resp.json['appointment']['appointment_uid']
            ).count()
            == 1
        )
        appointment_id = resp.json['appointment']['appointment_uid']
        appointment = Appointment.objects.get(pk=appointment_id)
        appt_notif = AppointmentCreatedNotification(appointment)
        appt_notif.schedule_record.assert_skipped()
        staffer_recipient = Recipient.from_staffer(self.staffer)
        assert staffer_recipient not in appt_notif.resolved_recipients

        booking_notif = SubBookingCreatedNotification(appointment.subbookings[0])
        booking_notif.schedule_record.assert_success()
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:customer_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:business_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()
        notification = NotificationHistoryDocument.task_get(
            task_id=f'booking_changed:business_booking_confirmation:'
            f'appointment_id={appointment_id}',
            type=UserNotification.PUSH_NOTIFICATION,
        )
        assert notification is None

    def test_handler_custappt_create_multi(self):
        # autoassign doesn't make sense in single staffer business
        self.create_extra_staffer()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=None,  # no needed in dry_run
        )
        # add second booking
        body['subbookings'].append(
            {
                'booked_from': None,  # multimatcher should assign it
                'service_variant': (
                    {
                        'mode': SVMode.VARIANT,
                        'id': self.gap_hole_variant.id,
                    }
                ),
                'staffer_id': -1,  # autoassign
            }
        )
        body['dry_run'] = True

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert SubBooking.objects.filter(appointment__business=self.business).count() == 0
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        assert resp.json['meta'] is None

        assert BookingChange.objects.count() == 0
        # assert NotificationSchedule.objects.count() == 0
        assert (
            NotificationSchedule.objects.filter(
                task_id__contains='booking_changed:business_booking_confirmation'
            ).count()
            == 0
        )

        # this time make
        body = resp.json['appointment']
        body['dry_run'] = False
        body['recurring'] = False
        body['bci_agreements'] = {
            agreement['name']: agreement['value']
            for agreement in resp.json['appointment']['bci_agreements']
        }

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert (
            SubBooking.objects.filter(
                appointment__business=self.business,
            ).count()
            == 2
        )
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2
        assert not resp.json['appointment']['subbookings'][0]['autoassign']
        assert resp.json['appointment']['subbookings'][1]['autoassign']

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # BookingChanges and NotificationSchedules
        assert (
            BookingChange.objects.filter(
                appointment_id=resp.json['appointment']['appointment_id'],
            ).count()
            == 2
        )
        appointment_id = resp.json['appointment']['appointment_uid']
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:customer_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:business_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()

    def test_handler_custappt_create_multi_with_combo(self):
        combo_variant = service_variant_recipe.make(
            service__business=self.business,
            service__combo_type=ComboType.SEQUENCE,
        )
        baker.make(
            ComboMembership,
            combo=combo_variant,
            child=self.variant,
            _quantity=2,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(16, 00)),
            staffer=self.staffer,
        )
        body['subbookings'].append(
            {
                'addons': [],
                'combo_children': [],
                'booked_from': None,  # multimatcher should assign it
                'booked_till': None,  # multimatcher should assign it
                'duration': None,
                'id': None,
                'service_variant': {
                    'id': combo_variant.id,
                    'mode': SVMode.VARIANT,
                    'service_name': None,
                },
                'staffer_id': -1,
                'appliance_id': -1,
            }
        )
        body['force_incomplete'] = True
        body['dry_run'] = True

        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertEqual(len(response.json['appointment']['subbookings']), 2)
        self.assertEqual(len(response.json['appointment']['subbookings'][1]['combo_children']), 2)
        self.assertIsNotNone(response.json['appointment']['subbookings'][0]['booked_from'])
        self.assertIsNotNone(response.json['appointment']['subbookings'][0]['booked_till'])
        self.assertIsNotNone(response.json['appointment']['subbookings'][1]['booked_from'])
        self.assertIsNotNone(response.json['appointment']['subbookings'][1]['booked_till'])

        # Check against second booking exceeding working hours.
        # Expect booked_till to be None for combo when using force_incomplete.
        body['subbookings'][0]['booked_from'] = self._dt_from_hour(time(17, 30))
        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertIsNotNone(response.json['appointment']['subbookings'][0]['booked_from'])
        self.assertIsNotNone(response.json['appointment']['subbookings'][0]['booked_till'])
        self.assertIsNone(response.json['appointment']['subbookings'][1]['booked_from'])
        self.assertIsNone(response.json['appointment']['subbookings'][1]['booked_till'])

    def test_handler_custappt_create_combo_with_requested_staffer(self):
        combo_variant = service_variant_recipe.make(
            service__business=self.business,
            service__combo_type=ComboType.SEQUENCE,
        )
        baker.make(
            ComboMembership,
            combo=combo_variant,
            child=self.variant,
            _quantity=2,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            combo_variant,
            booked_from=self._dt_from_hour(time(16, 00)),
            staffer=self.staffer,
            recurring=False,
        )

        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertEqual(len(response.json['appointment']['subbookings'][0]['combo_children']), 2)

        appointment_id = response.json['appointment']['appointment_uid']
        appointment = Appointment.objects.get(pk=appointment_id)
        self.assertTrue(appointment.subbookings[0].combo_children[0].is_staffer_requested_by_client)
        self.assertTrue(appointment.subbookings[0].combo_children[1].is_staffer_requested_by_client)

    def test_handler_custappt_create_with_deposit(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '10.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

    def test_handler_custappt_create_with_deposit_tax_excluded(self):
        self.pos.service_tax_mode = self.pos.POS_TAX_MODE__EXCLUDED
        self.pos.save()

        self.service.tax_rate = Decimal(5)
        self.service.save()

        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '10.50'
        assert appointment_payment['prepayment_total'] == '0.00'

    def test_handler_custappt_create_with_deposit_discount(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('17.30'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='**********',
            user=self.user,
            discount=20,
        )

        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )
        assert deposit_info['already_paid'] == '$13.84'
        assert deposit_info['total'] == '$13.84'

    def test_handler_custappt_create_with_deposit_discount_stardust(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('17.30'),
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        url = self.appointments_url(self.business.id) + '?stardust=True'

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='**********',
            user=self.user,
            discount=20,
        )

        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }

        body[compatibilities.COMPATIBILITIES] = {
            compatibilities.STARDUST: True,
        }

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

        addon_1 = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price=10.00,
            price_type=PriceType.FIXED,
            services=[
                with_deposit.service,
            ],
            duration=relativedelta(minutes=5),
            max_allowed_quantity=10,
        )
        body['subbookings'][0]['addons'] = [
            {
                'id': addon_1.id,
                'quantity': 1,
            },
        ]
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )
        assert deposit_info['already_paid'] == '$0.00'
        assert deposit_info['total'] == '$21.84'  # ($17.30 + $10.00) * 0.8

        # update appointment with addons (with cancellation fee)
        body = resp.json['appointment']
        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }
        body['subbookings'][0]['appliance_id'] = -1
        body['overbooking'] = True
        body['_notify_about_reschedule'] = False

        url = (
            f"/business_api/me/businesses/{self.business.id}/appointments/"
            f"{resp.json['appointment']['appointment_uid']}/"
        )
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        assert resp.json['appointment']['total_value'] == 21.84  # ($17.30 + $10.00) * 0.8
        assert resp.json['appointment']['total_discount_amount'] == 5.46  # ($17.30 + $10.00) * 0.2

    def test_handler_custappt_create_with_prepayment(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        self.assertEqual(resp.json['appointment_payment']['prepayment_total'], '9.99')

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_handler_custappt_create_with_prepayment_tax_excluded(self):
        self.pos.service_tax_mode = self.pos.POS_TAX_MODE__EXCLUDED
        self.pos.save()

        self.service.tax_rate = Decimal(5)
        self.service.save()

        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        self.assertEqual(resp.json['appointment_payment']['prepayment_total'], '10.49')

    def test_handler_custappt_create_with_both(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('80.17'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '9.99')
        assert resp.json['appointment_payment']['prepayment_total'] == '80.17'
        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )

    def test_handler_multibooking_with_deposit(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '9.99')
        assert resp.json['appointment_payment']['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None

    def test_handler_multibooking_with_deposit_tax_excluded(self):
        self.pos.service_tax_mode = self.pos.POS_TAX_MODE__EXCLUDED
        self.pos.save()

        self.service.tax_rate = Decimal(12)
        self.service.save()

        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '11.19')
        assert resp.json['appointment_payment']['prepayment_total'] == '0.00'

    def test_handler_custappt_create_with_none_deposit(self):
        sv1 = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        sv1.add_staffers([self.staffer])

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=sv1,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info is None

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None

    def test_handler_custappt_create_with_prepayment_old_app(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        del body['compatibilities']
        resp = self.fetch(url, method='POST', body=body)

        # There is no prepayment compatibilities required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['field'] == 'prepayment_needed'
        assert resp.json['errors'][0]['code'] == 'prepayment_not_supported'

    def test_handler_custappt_create_with_deposit_old_app(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        del body['compatibilities']
        resp = self.fetch(url, method='POST', body=body)

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # There is no deposit compatibilities required
        assert resp.code == status.HTTP_201_CREATED

    def test_create_custapp_simple_with_tip_percent_fixed(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '9.99'

        body['tip'] = {'type': 'P', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment'] == '9.99'
        assert resp.json['appointment_payment']['prepayment_total'] == '11.72'
        assert resp.json['appointment_payment']['tip_choices']

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_create_custapp_simple_with_tip_percent_noprice(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.DONT_SHOW,
            price=0,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '9.99'
        assert resp.json['appointment_payment']['tip_choices']

        body['tip'] = {'type': 'P', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment'] == '9.99'
        assert resp.json['appointment_payment']['prepayment_total'] == '10.99'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_create_custapp_simple_with_custom_tip_percent_fixed(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '9.99'
        assert resp.json['appointment_payment']['tip_choices']

        body['tip'] = {'type': 'H', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment'] == '9.99'
        assert resp.json['appointment_payment']['prepayment_total'] == '19.99'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_create_custapp_simple_with_custom_tip_percent_noprice(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FREE,
            price=0,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '9.99'
        assert resp.json['appointment_payment']['tip_choices']

        body['tip'] = {'type': 'H', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment'] == '9.99'
        assert resp.json['appointment_payment']['prepayment_total'] == '19.99'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_create_custapp_simple_with_tip_cancellation_fee(self):
        with_cancellation_fee = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FREE,
            price=0,
        )
        with_cancellation_fee.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_cancellation_fee,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_cancellation_fee,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '9.99')
        assert resp.json['appointment_payment']['prepayment_total'] == '0.00'
        assert 'tip_choices' not in resp.json['appointment_payment']

        body['tip'] = {'type': 'H', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '9.99')
        assert resp.json['appointment_payment']['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

    def test_multi_tip_percent_price_price(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=60,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('30.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100,
        )
        with_prepayment.add_staffers([self.staffer])
        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '10.00'

        body['tip'] = {'type': 'P', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment'] == '10.00'
        assert resp.json['appointment_payment']['prepayment_total'] == '26.00'
        assert resp.json['appointment_payment']['tip_choices']

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )

    def test_multi_tip_percent_price_noprice(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FREE,
            price=0,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('30.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100,
        )
        with_prepayment.add_staffers([self.staffer])
        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '10.00'

        body['tip'] = {'type': 'P', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment'] == '10.00'
        assert resp.json['appointment_payment']['prepayment_total'] == '20.00'
        assert resp.json['appointment_payment']['tip_choices']

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )

    def test_multi_tip_percent_noprice_price(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=60,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('30.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FREE,
            time_slot_interval='0300',
            price=0,
        )
        with_prepayment.add_staffers([self.staffer])

        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '10.00'

        body['tip'] = {'type': 'P', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment'] == '10.00'
        assert resp.json['appointment_payment']['prepayment_total'] == '17.00'
        assert resp.json['appointment_payment']['tip_choices']

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )

    def test_multi_tip_percent_noprice_noprice(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FREE,
            price=0,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('30.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FREE,
            time_slot_interval='0300',
            price=0,
        )
        with_prepayment.add_staffers([self.staffer])

        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '10.00'

        body['tip'] = {'type': 'P', 'rate': 10}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '30.00')
        assert resp.json['appointment_payment']['prepayment'] == '10.00'
        assert resp.json['appointment_payment']['prepayment_total'] == '11.00'
        assert resp.json['appointment_payment']['tip_choices']

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )

    def test_handler_custappt_create_tips_disabled_extra_tip_data(self):
        self.pos.tips_enabled = False
        self.pos.save()

        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['dry_run'] = True
        body['tip'] = {'type': 'P', 'rate': 0}
        resp = self.fetch(url, method='POST', body=body)

        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        self.assertEqual(resp.json['appointment_payment']['prepayment'], '9.99')
        self.assertEqual(resp.json['appointment_payment']['prepayment_total'], '9.99')

    def test_handler_custappt_create_with_deposit_service_fee(self):
        """Simple deposit, but with Service Fee."""
        self.pos.service_fee = 5
        self.pos.save()

        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '10.00')
        self.assertEqual(resp.json['appointment_payment']['prepayment_total'], '0.00')

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        assert deposit_info['total'] == '$10.00'

    def test_handler_custappt_create_with_prepayment_service_fee(self):
        """Simple prepayment, but with Service Fee."""
        self.pos.service_fee = 5
        self.pos.save()

        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        self.assertEqual(resp.json['appointment_payment']['prepayment_total'], '14.99')

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        transaction_info = self._check_appointment_and_transaction_info(resp, self.business.id)

        assert transaction_info['total'] == '$22.30'
        assert transaction_info['already_paid'] == '$14.99'

    def test_create_user_known_by_salon_network(self):
        recommending_business = baker.make(Business)
        salon_network = baker.make(
            SalonNetwork,
            business=recommending_business,
        )
        salon_network.members.set([self.business])
        baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=recommending_business,
        )

        url = f'/customer_api/me/appointments/business/{self.business.id}/'
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        bci = BusinessCustomerInfo.objects.filter(
            business_id=self.business.id,
            user_id=self.user.id,
        ).first()

        assert bci
        assert bci.client_type == (BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_RECURRING)

    def test_create_user_known_by_salon_network_and_marked_recurring(self):
        recommending_business = baker.make(Business)
        salon_network = baker.make(
            SalonNetwork,
            business=recommending_business,
        )
        salon_network.members.set([self.business])
        baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=recommending_business,
        )

        url = f'/customer_api/me/appointments/business/{self.business.id}/'
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=True,
        )

        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        bci = BusinessCustomerInfo.objects.filter(
            business_id=self.business.id,
            user_id=self.user.id,
        ).first()
        assert bci
        assert bci.client_type == (BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_RECURRING)

    def test_create_user_unknown_by_salon_network_but_marked_recurring(self):
        recommending_business = baker.make(Business)
        salon_network = baker.make(
            SalonNetwork,
            business=recommending_business,
        )
        salon_network.members.set([self.business])

        url = f'/customer_api/me/appointments/business/{self.business.id}/'
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=True,
        )

        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        bci = BusinessCustomerInfo.objects.filter(
            business_id=self.business.id,
            user_id=self.user.id,
        ).first()
        assert bci
        assert bci.client_type == (BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_RECURRING)

    def test_create_user_unknown_by_salon_network(self):
        recommending_business = baker.make(Business)
        salon_network = baker.make(
            SalonNetwork,
            business=recommending_business,
        )
        salon_network.members.set([self.business])

        url = f'/customer_api/me/appointments/business/{self.business.id}/'
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        bci = BusinessCustomerInfo.objects.filter(
            business_id=self.business.id,
            user_id=self.user.id,
        ).first()
        assert bci
        assert bci.client_type == (BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW)

    def test_traveling_dry_run(self):
        NotificationHistoryDocument.tasks_clear()
        make_staffer_notifications(business=self.business, staffer=self.staffer)

        baker.make(
            TravelingToClients,
            business=self.business,
            price='10.00',
            price_type=PriceType.FIXED,
            traveling_only=False,
            distance=15,
            distance_unit='km',
            policy='abra kadabra',
        )
        assert self.business.is_traveling
        self.variant.service.is_traveling_service = True
        self.variant.service.save()

        assert self.business.is_traveling

        url = self.appointments_url(self.business.id)
        body = self._get_traveling_body()
        resp = self.fetch(url + 'dry_run/', method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

    def test_traveling_missing_traveling_info(self):
        NotificationHistoryDocument.tasks_clear()
        make_staffer_notifications(business=self.business, staffer=self.staffer)

        baker.make(
            TravelingToClients,
            business=self.business,
            price='10.00',
            price_type=PriceType.FIXED,
            traveling_only=False,
            distance=15,
            distance_unit='km',
            policy='abra kadabra',
        )
        assert self.business.is_traveling
        self.variant.service.is_traveling_service = True
        self.variant.service.save()

        url = self.appointments_url(self.business.id)
        body = self._get_traveling_body()
        body['traveling'] = None
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def _get_traveling_body(self):
        return {
            'bci_agreements': None,
            'compatibilities': {'prepayment': True},
            'customer_note': None,
            'external_payment_method': None,
            'force_incomplete': True,
            'payment_method': None,
            '_preserve_order': False,
            'recurring': None,
            'service_questions': [],
            'subbookings': [
                {
                    'booked_from': self._dt_from_hour(time(16, 0)),
                    'booked_till': None,
                    'duration': None,
                    'id': None,
                    'service_variant': {
                        'id': self.variant.id,
                        'mode': 'variant',
                        'service_name': None,
                    },
                    'staffer_id': None,
                }
            ],
            'tip': None,
            '_version': 0,
            'no_thumbs': True,
            'traveling': {
                'address_line_1': 'as',
                'address_line_2': '',
                'apartment_number': '',
                'city': '',
                'zipcode': "",
                'latitude': None,
                'longitude': None,
            },
        }

    @pytest.mark.freeze_time(TEST_DATETIME)
    def test_handler_custappt_after_min_lead_time(self):
        url = self.appointments_url(self.business.id)
        self.business.booking_min_lead_time = relativedelta(days=2)
        self.business.save()
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0), specific_date=TEST_DATETIME),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_409_CONFLICT)
        self.assertDictEqual(
            resp.json,
            {
                'errors': [
                    {
                        'code': 'conflict',
                        'type': 'validation',
                        'description': 'This time slot is no longer available.',
                        'notices': {'lead_time': ['min_lead_time']},
                    }
                ]
            },
        )

    def test_handler_custappt_appointment_analytics_doesnt_break_booking(self):
        analytics_test_cases = [
            None,
            {},
            {'booking_source': 'source1'},
            {
                'booking_source': 'Deeplink',
                'traffic_source': 'abc',
                'traffic_channel': 'abc',
                'traffic_initial_referrer': 'abc',
                'traffic_referrer': 'abc',
                'task_id': 'abc',
            },
            {'booking_source': 123},
        ]
        hour = 10
        for case in analytics_test_cases:
            with self.subTest(case=case):
                url = self.appointments_url(self.business.id)
                body = build_custappt_data(
                    self.variant,
                    booked_from=self._dt_from_hour(time(hour, 0)),
                    staffer=self.staffer,
                    recurring=False,
                )
                body['analytics'] = case
                resp = self.fetch(url, method='POST', body=body)
                assert resp.code == status.HTTP_201_CREATED
                assert resp.json['appointment']['business']['id'] == self.business.id
                assert len(resp.json['appointment']['subbookings']) == 1

                appointment_id = resp.json['appointment']['appointment_uid']
                Appointment.objects.get(pk=appointment_id)
                hour += 1

    def test_handler_creates_appointment_analytics_in_db(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['analytics'] = {
            'booking_source': 'Deeplink',
            'traffic_source': 'abc',
            'traffic_channel': 'abc',
            'traffic_initial_referrer': 'abc',
            'traffic_referrer': '',
        }
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        appointment_id = resp.json['appointment']['appointment_uid']
        Appointment.objects.get(pk=appointment_id)
        assert AppointmentAnalytics.objects.count() == 1
        analytics = AppointmentAnalytics.objects.get(appointment_id=appointment_id)
        assert analytics.booking_source == 'Deeplink'
        assert analytics.traffic_referrer == ''
        assert analytics.task_id is None

    def test_handler_creates_appointment__tap_to_pay_reminder__happy_path(self):
        self.pos.tap_to_pay_enabled = True
        self.pos.tap_to_pay_status = TapToPayStatus.ENABLED
        self.pos.promote_payment_method_availability_to_customers = True
        self.pos.save()

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        with override_eppo_feature_flag({PromoteTTPAndBCRPaymentsToCustomersFlag.flag_name: True}):
            resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        appt_id = resp.json['appointment']['appointment_uid']
        task_id = (
            'booking_changed:customer_tap_to_pay_before_appointment_started_reminder:'
            f'appointment_id={appt_id}'
        )
        reminders = NotificationSchedule.objects.filter(task_id=task_id)

        self.assertEqual(len(reminders), 1)
        self.assertEqual(reminders[0].task_id, task_id)

        ttp_notifications = PMAvailabilityNotificationRecord.objects.filter(user_id=self.user.id)
        self.assertEqual(ttp_notifications.count(), 1)

    def test_handler_creates_appointment__tap_to_pay_reminder__no_pos_flag(self):
        self.pos.tap_to_pay_enabled = True
        self.pos.tap_to_pay_status = TapToPayStatus.ENABLED
        self.pos.promote_payment_method_availability_to_customers = (
            False  # should disable the feature
        )
        self.pos.save()

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        with override_eppo_feature_flag({PromoteTTPAndBCRPaymentsToCustomersFlag.flag_name: True}):
            resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        appt_id = resp.json['appointment']['appointment_uid']
        task_id = (
            'booking_changed:customer_tap_to_pay_before_appointment_started_reminder:'
            f'appointment_id={appt_id}'
        )
        reminders = NotificationSchedule.objects.filter(task_id=task_id)

        self.assertEqual(len(reminders), 0)

        ttp_notifications = PMAvailabilityNotificationRecord.objects.filter(user_id=self.user.id)
        self.assertEqual(ttp_notifications.count(), 0)

    def test_handler_creates_appointment__tap_to_pay_reminder__amount_per_user_exceeded(self):
        self.pos.tap_to_pay_enabled = True
        self.pos.tap_to_pay_status = TapToPayStatus.ENABLED
        self.pos.promote_payment_method_availability_to_customers = True
        self.pos.save()

        baker.make(NotificationSchedule, task_id="task1", state=ScheduleState.SUCCESS)
        baker.make(PMAvailabilityNotificationRecord, task_id="task1", user_id=self.user.id)

        baker.make(NotificationSchedule, task_id="task2", state=ScheduleState.RECEIVED)
        baker.make(PMAvailabilityNotificationRecord, task_id="task2", user_id=self.user.id)

        baker.make(NotificationSchedule, task_id="task3", state=ScheduleState.STARTED)
        baker.make(PMAvailabilityNotificationRecord, task_id="task3", user_id=self.user.id)
        # there should be no more than 3 notifications sent

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        with override_eppo_feature_flag({PromoteTTPAndBCRPaymentsToCustomersFlag.flag_name: True}):
            resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        appt_id = resp.json['appointment']['appointment_uid']
        task_id = (
            'booking_changed:customer_tap_to_pay_before_appointment_started_reminder:'
            f'appointment_id={appt_id}'
        )
        reminders = NotificationSchedule.objects.filter(task_id=task_id)

        self.assertEqual(len(reminders), 0)

        ttp_notifications = PMAvailabilityNotificationRecord.objects.filter(user_id=self.user.id)
        self.assertEqual(ttp_notifications.count(), 3)

    def test_handler_creates_appointment__tap_to_pay_reminder__amount_per_user_not_yet_exceeded(
        self,
    ):
        self.pos.tap_to_pay_enabled = True
        self.pos.tap_to_pay_status = TapToPayStatus.ENABLED
        self.pos.promote_payment_method_availability_to_customers = True
        self.pos.save()

        baker.make(NotificationSchedule, task_id="task1", state=ScheduleState.SUCCESS)
        baker.make(PMAvailabilityNotificationRecord, task_id="task1", user_id=self.user.id)

        baker.make(NotificationSchedule, task_id="task2", state=ScheduleState.RECEIVED)
        baker.make(PMAvailabilityNotificationRecord, task_id="task2", user_id=self.user.id)

        baker.make(NotificationSchedule, task_id="task3", state=ScheduleState.SKIPPED)
        baker.make(PMAvailabilityNotificationRecord, task_id="task3", user_id=self.user.id)
        # there should be no more than 3 notifications sent
        # (here only 2 were sent cause there is 1 with skipped state)

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        with override_eppo_feature_flag({PromoteTTPAndBCRPaymentsToCustomersFlag.flag_name: True}):
            resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        appt_id = resp.json['appointment']['appointment_uid']
        task_id = (
            'booking_changed:customer_tap_to_pay_before_appointment_started_reminder:'
            f'appointment_id={appt_id}'
        )
        reminders = NotificationSchedule.objects.filter(task_id=task_id)

        self.assertEqual(len(reminders), 1)
        self.assertEqual(reminders[0].task_id, task_id)

        ttp_notifications = PMAvailabilityNotificationRecord.objects.filter(user_id=self.user.id)
        self.assertEqual(ttp_notifications.count(), 4)


@pytest.mark.django_db
class CreateCustomerAppointmentSemiAutoTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        self.pos = pos_recipe.make(business=self.business)

    def test_handler_custappt_create_single(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # BookingChanges and NotificationSchedules
        appt_id = resp.json['appointment']['appointment_uid']
        assert (
            BookingChange.objects.filter(
                appointment_id=appt_id,
            ).count()
            == 1
        )

        appointment_id = resp.json['appointment']['appointment_uid']
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:business_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()

    def test_handler_custappt_create_multi(self):
        self.create_extra_staffer()  # for autoassign to work
        notifications_count = NotificationSchedule.objects.count()

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=None,  # no needed in dry_run
        )
        # add second booking
        body['subbookings'].append(
            {
                'booked_from': None,  # multimatcher should assign it
                'service_variant': (
                    {
                        'mode': SVMode.VARIANT,
                        'id': self.gap_hole_variant.id,
                    }
                ),
                'staffer_id': -1,  # autoassign
            }
        )
        body['dry_run'] = True

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert SubBooking.objects.filter(appointment__business=self.business).count() == 0
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        assert resp.json['meta'] is None

        assert BookingChange.objects.count() == 0
        assert NotificationSchedule.objects.count() == notifications_count

        # this time make
        body = resp.json['appointment']
        body['dry_run'] = False
        body['recurring'] = False
        body['bci_agreements'] = {
            agreement['name']: agreement['value']
            for agreement in resp.json['appointment']['bci_agreements']
        }

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert SubBooking.objects.filter(appointment__business=self.business).count() == 2
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2
        assert not resp.json['appointment']['subbookings'][0]['autoassign']
        assert resp.json['appointment']['subbookings'][1]['autoassign']

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # BookingChanges and NotificationSchedules
        assert (
            BookingChange.objects.filter(
                appointment_id=resp.json['appointment']['appointment_uid'],
            ).count()
            == 2
        )

        appointment_id = resp.json['appointment']['appointment_uid']
        assert NotificationSchedule.objects.filter(
            task_id=(
                f'booking_changed:business_booking_confirmation:appointment_id={appointment_id}'
            ),
        ).exists()

    def test_handler_custappt_create_with_deposit(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '10.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']

        deposit = Transaction.objects.get(
            appointment_id=resp.json['appointment']['appointment_uid'],
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        assert deposit_info
        assert deposit_info['id'] == deposit.id
        self.assertEqual(
            deposit.latest_receipt.status_code, receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

    def test_handler_custappt_create_with_prepayment(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '9.99'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info is None  # It is not cancellation f

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        txn = Transaction.objects.get(
            appointment_id=resp.json['appointment']['appointment_uid'],
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        assert transaction_info
        assert transaction_info['id'] == txn.id
        self.assertEqual(
            txn.latest_receipt.status_code, receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
        )

    def test_handler_custappt_create_with_both(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('80.17'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '80.17'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        deposit = Transaction.objects.get(
            appointment_id=resp.json['appointment']['appointment_uid'],
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        assert deposit_info
        assert deposit_info['id'] == deposit.id
        self.assertEqual(
            deposit.latest_receipt.status_code, receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']

        txn = Transaction.objects.get(
            appointment_id=resp.json['appointment']['appointment_uid'],
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        assert transaction_info
        assert transaction_info['id'] == txn.id
        self.assertEqual(
            txn.latest_receipt.status_code, receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
        )

    def test_handler_multibooking_with_deposit(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        deposit = Transaction.objects.get(
            appointment_id=resp.json['appointment']['appointment_uid'],
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        assert deposit_info
        assert deposit_info['id'] == deposit.id
        self.assertEqual(
            deposit.latest_receipt.status_code, receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None

    def test_handler_custappt_create_with_none_deposit(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        sv1 = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        sv1.add_staffers([self.staffer])

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=sv1,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info is None

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None

    def test_handler_custappt_create_with_prepayment_old_app(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        del body['compatibilities']
        resp = self.fetch(url, method='POST', body=body)

        # There is no prepayment compatibilities required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['field'] == 'prepayment_needed'
        assert resp.json['errors'][0]['code'] == 'prepayment_not_supported'

    def test_handler_custappt_create_with_deposit_old_app(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        del body['compatibilities']
        resp = self.fetch(url, method='POST', body=body)

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # There is no deposit compatibilities required
        assert resp.code == status.HTTP_201_CREATED

    @patch.object(segment_api_appointment_booked_task, 'delay')
    def test_handler_custappt_create_single_with_subdomain(
        self,
        appt_booked_mock,
    ):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['_from_subdomain'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        call_args = appt_booked_mock.call_args
        assert call_args[0][0] == resp.json['appointment']['subbookings'][0]['id']


@pytest.mark.django_db
class CreateCustomerAppointmentGooglePayTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        self.pos = pos_recipe.make(business=self.business)

    @responses.activate
    @override_settings(POS__GOOGLE_PAY=True)
    def test_create_with_prepayment_auto(self):
        resp, body, url = self._test_create_with_prepayment()
        self._test_tokenized_payments(
            resp_json=resp.json,
            appointment_status=Appointment.STATUS.ACCEPTED,
            apple_pay_available=False,
            google_pay_available=True,
        )
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '9.99'

        # add a GooglePay Token
        body['external_payment_method'] = {
            'partner': 'google_pay',
            'token': {'keys': '123123123'},
        }
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    @responses.activate
    @override_settings(POS__GOOGLE_PAY=True)
    def test_create_with_prepayment_semi_auto(self):
        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        resp, body, url = self._test_create_with_prepayment()
        self._test_tokenized_payments(
            resp_json=resp.json,
            appointment_status=Appointment.STATUS.UNCONFIRMED,
            apple_pay_available=False,
            google_pay_available=False,
        )
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '9.99'

        # add a GooglePay Token
        body['external_payment_method'] = {
            'partner': 'google_pay',
            'token': {'keys': '123123123'},
        }
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json['errors'][0],
            {
                'field': 'external_payment_method.non_field_errors',
                'description': 'Payment is not possible',
                'code': 'payment_not_possible',
            },
        )

    def _test_create_with_prepayment(self):
        responses.add(
            'POST',
            settings.ADYEN_AUTH_URL,
            json={
                'authCode': '7079',
                'resultCode': 'Authorised',
                'pspReference': '8835295822602224',
            },
            status=200,
        )

        responses.add(
            'POST',
            settings.ADYEN_CAPTURE_URL,
            json={"pspReference": "8835295844953019", "response": "[capture-received]"},
            status=200,
        )

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        return resp, body, url

    @responses.activate
    @override_settings(POS__GOOGLE_PAY=True)
    def test_create_with_deposit_auto(self):
        self._test_create_with_deposit()

    @responses.activate
    @override_settings(POS__GOOGLE_PAY=True)
    def test_create_with_deposit_semi_auto(self):
        self._test_create_with_deposit()

    @responses.activate
    @override_settings(POS__GOOGLE_PAY=True)
    def _test_create_with_deposit(self):
        responses.add(
            'POST',
            settings.ADYEN_AUTH_URL,
            json={
                'authCode': '7079',
                'resultCode': 'Authorised',
                'pspReference': '8835295822602224',
            },
            status=200,
        )

        responses.add(
            'POST',
            settings.ADYEN_CAPTURE_URL,
            json={"pspReference": "8835295844953019", "response": "[capture-received]"},
            status=200,
        )

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._test_tokenized_payments(
            resp_json=resp.json,
            appointment_status=Appointment.STATUS.ACCEPTED,
            apple_pay_available=False,
            google_pay_available=False,  # because of deposit
        )
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a GooglePay Token
        body['external_payment_method'] = {
            'partner': 'google_pay',
            'token': {'keys': '123123123'},
        }
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'payment_not_possible'

    @responses.activate
    @override_settings(POS__GOOGLE_PAY=True)
    def test_create_with_both(self):
        responses.add(
            'POST',
            settings.ADYEN_AUTH_URL,
            json={
                'authCode': '7079',
                'resultCode': 'Authorised',
                'pspReference': '8835295822602224',
            },
            status=200,
        )

        responses.add(
            'POST',
            settings.ADYEN_CAPTURE_URL,
            json={"pspReference": "8835295844953019", "response": "[capture-received]"},
            status=200,
        )

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        with_prepayment.add_staffers([self.staffer])

        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('80.17'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '80.17'

        # add a GooglePay Token
        body['external_payment_method'] = {
            'partner': 'google_pay',
            'token': {'keys': '123123123'},
        }

        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'payment_not_possible'


@pytest.mark.django_db
class CreateCustomerAppointmentApplePayTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        self.pos = pos_recipe.make(business=self.business)
        self.url = self.appointments_url(self.business.id)

    @responses.activate
    @override_settings(POS__APPLE_PAY=True)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_create_with_prepayment(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        resp, body = self._test_create_with_prepayment()
        assert resp.code == status.HTTP_201_CREATED
        self._test_tokenized_payments(
            resp_json=resp.json,
            appointment_status=Appointment.STATUS.ACCEPTED,
            apple_pay_available=True,
            google_pay_available=False,
        )
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '9.99'

        # add a ApplePay Token
        body['external_payment_method'] = {
            'partner': 'apple_pay',
            'token': {'keys': '123123123'},
        }
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

        assert analytics_track_mock.call_args_list[2][1]['event'] == 'Payment_Transaction_Completed'
        assert (
            analytics_identify_mock.call_args_list[2][1]['traits']['event_name']
            == 'python.analytics_payment_transaction_completed_task'
        )

    @responses.activate
    @override_settings(POS__APPLE_PAY=True)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @patch('webapps.pos.provider.proxy.TransactionService.get_fees_and_initialize_payment')
    @patch('webapps.payment_gateway.ports.WalletService.get_wallet')
    @patch('webapps.point_of_sale.adapters.PaymentGatewayPort.get_customer_wallet')
    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.initialize_payment')
    @patch('webapps.pos.provider.proxy.synchronize_adyen_payment')
    @patch(
        'webapps.stripe_integration.services.SynchronizeService.synchronize_stripe_payment_intent'
    )
    @patch('webapps.payment_gateway.ports.BalanceTransactionService')
    @patch('webapps.payment_providers.ports.payment_ports.Payment')
    @patch('webapps.payment_providers.services.common.CommonPaymentServices.update_status')
    @patch('webapps.payment_providers.services.common.StripePaymentServices.authorize_payment')
    @patch('webapps.pos.serializers.TransactionService.check_prepayment_auth_success')
    def test_create_with_prepayment_internal_status_validation(
        self,
        check_prepayment_auth_success_mock,
        authorize_payment_mock,
        update_status_mock,
        payment_class_mock,
        balance_transaction_service_mock,
        synchronize_stripe_payment_intent_mock,
        synchronize_adyen_payment_mock,
        get_fees_and_initialize_payment_mock,
        initialize_payment_mock,
        get_customer_wallet_mock,
        get_wallet_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        stripe_payment_mock = MagicMock()
        stripe_payment_mock.external_id = str(uuid.uuid4())
        payment_mock = MagicMock()
        payment_mock.payment_method = PaymentMethodType.APPLE_PAY
        payment_mock.status = PaymentStatus.NEW
        payment_mock.provider_code = PaymentProviderEnum.STRIPE_PROVIDER
        payment_mock.stripe_payment = stripe_payment_mock
        authorize_payment_mock.return_value = stripe_payment_mock, None
        payment_class_mock.objects.get.return_value = payment_mock
        balance_transaction_mock = MagicMock()
        balance_transaction_mock.transaction_type = BalanceTransactionType.PAYMENT
        balance_transaction_mock.payment_method = PaymentMethodType.APPLE_PAY
        balance_transaction_mock.external_id = str(uuid.uuid4())
        balance_transaction_mock.id = str(uuid.uuid4())
        balance_transaction_service_mock.get_balance_transaction.return_value = (
            balance_transaction_mock
        )
        wallet_mock = MagicMock()
        wallet_mock.id = str(uuid.uuid4())
        get_customer_wallet_mock.return_value = wallet_mock
        reciever_wallet = MagicMock()
        reciever_wallet.owner_type = WalletOwnerType.BUSINESS
        sender_wallet = MagicMock()
        sender_wallet.owner_type = WalletOwnerType.CUSTOMER
        get_wallet_mock.side_effect = [
            sender_wallet,
            reciever_wallet,
        ]
        payment_entity_mock = MagicMock()
        payment_entity_mock.id = str(uuid.uuid4())
        initialize_payment_mock.return_value = payment_entity_mock
        posplan = posplan_recipe.make(
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(posplan)
        self.pos.pos_refactor_stage2_enabled = True
        self.pos.save()

        resp, body = self._test_create_with_prepayment()
        assert resp.code == status.HTTP_201_CREATED
        self._test_tokenized_payments(
            resp_json=resp.json,
            appointment_status=Appointment.STATUS.ACCEPTED,
            apple_pay_available=True,
            google_pay_available=False,
        )
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '9.99'

        # add a ApplePay Token
        body['external_payment_method'] = {
            'partner': 'apple_pay',
            'token': '123123123',
        }
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        # now it works!
        self._check_appointment_and_transaction_info(resp, self.business.id)

    @responses.activate
    @override_settings(POS__APPLE_PAY=True)
    def test_create_with_prepayment_semi_auto(self):
        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        resp, body = self._test_create_with_prepayment()
        self._test_tokenized_payments(
            resp_json=resp.json,
            appointment_status=Appointment.STATUS.UNCONFIRMED,
            apple_pay_available=False,
            google_pay_available=False,
        )
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '9.99'

        # add a ApplePay Token
        body['external_payment_method'] = {
            'partner': 'apple_pay',
            'token': {'keys': '123123123'},
        }
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json['errors'][0],
            {
                'field': 'external_payment_method.non_field_errors',
                'description': 'Payment is not possible',
                'code': 'payment_not_possible',
            },
        )

    def _test_create_with_prepayment(self):
        responses.add(
            'POST',
            settings.ADYEN_AUTH_URL,
            json={
                'authCode': '7079',
                'resultCode': 'Authorised',
                'pspReference': '8835295822602224',
            },
            status=200,
        )

        responses.add(
            'POST',
            settings.ADYEN_CAPTURE_URL,
            json={"pspReference": "8835295844953019", "response": "[capture-received]"},
            status=200,
        )

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        return resp, body

    @responses.activate
    @override_settings(POS__APPLE_PAY=True)
    def test_create_with_deposit(self):
        responses.add(
            'POST',
            settings.ADYEN_AUTH_URL,
            json={
                'authCode': '7079',
                'resultCode': 'Authorised',
                'pspReference': '8835295822602224',
            },
            status=200,
        )

        responses.add(
            'POST',
            settings.ADYEN_CAPTURE_URL,
            json={"pspReference": "8835295844953019", "response": "[capture-received]"},
            status=200,
        )

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a ApplePay Token
        body['external_payment_method'] = {
            'partner': 'apple_pay',
            'token': {'keys': '123123123'},
        }
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'payment_not_possible'

    @responses.activate
    @override_settings(POS__APPLE_PAY=True)
    def test_create_with_both(self):
        responses.add(
            'POST',
            settings.ADYEN_AUTH_URL,
            json={
                'authCode': '7079',
                'resultCode': 'Authorised',
                'pspReference': '8835295822602224',
            },
            status=200,
        )

        responses.add(
            'POST',
            settings.ADYEN_CAPTURE_URL,
            json={"pspReference": "8835295844953019", "response": "[capture-received]"},
            status=200,
        )

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])
        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        with_prepayment.add_staffers([self.staffer])
        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('80.17'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body2 = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '80.17'

        # add a ApplePay Token
        body['external_payment_method'] = {
            'partner': 'apple_pay',
            'token': {'keys': '123123123'},
        }

        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'payment_not_possible'


@pytest.mark.django_db
class CreateCustomerAppointmentTrustedClients(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.pos = pos_recipe.make(business=self.business)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        assert self.business.pos_pay_by_app_enabled
        self.with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        self.with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=self.with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        self.with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        self.with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=self.with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('7.23'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        self.url = self.appointments_url(self.business.id)

        # add a card
        self.card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

    def test_create_new_client_deposit(self):
        """Checks if new client who wants to book will pay CF."""
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        body = build_custappt_data(
            variant=self.with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '10.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # Without dry_run
        body['payment_method'] = self.card.id
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']

        deposit = Transaction.objects.get(
            appointment_id=resp.json['appointment']['appointment_uid'],
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        assert deposit_info
        assert deposit_info['id'] == deposit.id
        self.assertEqual(
            deposit.latest_receipt.status_code, receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

    def test_create_existing_client_deposit(self):
        """Checks if existing untrusted client who wants to book will pay CF."""
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        baker.make(BusinessCustomerInfo, user=self.user, business=self.business)

        body = build_custappt_data(
            variant=self.with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '10.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # Without dry_run
        body['payment_method'] = self.card.id
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']

        deposit = Transaction.objects.get(
            appointment_id=resp.json['appointment']['appointment_uid'],
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )
        assert deposit_info
        assert deposit_info['id'] == deposit.id
        self.assertEqual(
            deposit.latest_receipt.status_code, receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

    def test_create_trusted_deposit(self):
        """Checks if trusted client who wants to book will pay CF."""
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=self.business,
            trusted=True,
        )

        body = build_custappt_data(
            variant=self.with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # Without dry_run
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        assert resp.json['appointment']['payment_info']['deposit_info'] is None

    def test_create_new_client_prepayment(self):
        """Checks if new client who wants to book will pay PP."""
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        body = build_custappt_data(
            variant=self.with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '7.23'

        # Without dry_run
        body['payment_method'] = self.card.id
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_create_existing_client_prepayment(self):
        """Checks if existing untrusted client who wants to book will pay CF."""
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        baker.make(BusinessCustomerInfo, user=self.user, business=self.business)

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        body = build_custappt_data(
            variant=self.with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '7.23'

        # Without dry_run
        body['payment_method'] = self.card.id
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)
        self._check_appointment_and_transaction_info(resp, self.business.id)

    def test_create_trusted_prepayment(self):
        """Checks if trusted client who wants to book will pay CF."""
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=self.business,
            trusted=True,
        )

        body = build_custappt_data(
            variant=self.with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # Without dry_run
        body['payment_method'] = self.card.id
        body['dry_run'] = False
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info is None  # It is not cancellation f

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None


@pytest.mark.django_db
class CreateAppointmentTestPriceVariations(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.pos = pos_recipe.make(business=self.business)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        assert self.business.pos_pay_by_app_enabled

    def make_request(self, type_="DEPOSIT"):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=self.service,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        if type_ == ServiceVariantPayment.CANCELLATION_FEE_TYPE:
            assert appointment_payment['cancellation_fee_total'] == '10.00'
            assert appointment_payment['prepayment_total'] == '0.00'
        else:
            assert appointment_payment['cancellation_fee_total'] == '0.00'
            assert appointment_payment['prepayment_total'] == '10.00'
        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        transaction_info = resp.json['appointment']['payment_info']['transaction_info']

        if type_ == ServiceVariantPayment.CANCELLATION_FEE_TYPE:
            assert deposit_info
            assert (
                deposit_info['id']
                == Transaction.objects.get(
                    appointment_id=resp.json['appointment']['appointment_uid'],
                    transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
                ).id
            )

            assert transaction_info is None
        else:
            assert deposit_info is None  # It is not cancellation fee

            assert transaction_info
            assert (
                transaction_info['id']
                == Transaction.objects.get(
                    appointment_id=resp.json['appointment']['appointment_uid'],
                    transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
                ).id
            )

    def make_test(self, noshow_type, price_type, saving_type):
        self.service = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=price_type,
            price=17.30,
        )
        self.service.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=self.service,
            payment_type=noshow_type,
            payment_amount=10.0,
            saving_type=saving_type,
        )
        self.make_request(type_=noshow_type)

    def test_cf_fixed_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            price_type=PriceType.FIXED,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_cf_fixed_percentage(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            price_type=PriceType.FIXED,
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

    def test_cf_varies_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            price_type=PriceType.VARIES,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_cf_dontshow_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            price_type=PriceType.DONT_SHOW,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_cf_free_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            price_type=PriceType.FREE,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_cf_startsat_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            price_type=PriceType.STARTS_AT,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_cf_startsat_percentage(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            price_type=PriceType.STARTS_AT,
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

    def test_pp_fixed_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            price_type=PriceType.FIXED,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_pp_fixed_percentage(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            price_type=PriceType.FIXED,
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

    def test_pp_varies_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            price_type=PriceType.VARIES,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_pp_dontshow_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            price_type=PriceType.DONT_SHOW,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_pp_free_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            price_type=PriceType.FREE,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_pp_startsat_amount(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            price_type=PriceType.STARTS_AT,
            saving_type=ServiceVariantPayment.AMOUNT,
        )

    def test_pp_startsat_percentage(self):
        self.make_test(
            noshow_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            price_type=PriceType.STARTS_AT,
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )


@pytest.mark.freeze_time(TEST_DATETIME)
@pytest.mark.django_db
class CreateAppointmentTestConsentForms(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()

        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.consent_form_1 = baker.make(
            ConsentForm,
            business=self.business,
            services=[self.variant.service],
        )
        self.consent_form_2 = baker.make(
            ConsentForm,
            business=self.business,
            services=[self.variant.service],
        )
        self.customer = baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=self.business,
        )

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def test_consent_forms(self):
        booked_from = self._dt_from_hour(time(10, 0))
        consent_1 = baker.make(
            Consent,
            form=self.consent_form_1,
            customer=self.customer,
            signed=TEST_DATETIME,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=booked_from,
            staffer=self.staffer,
            recurring=False,
        )
        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': self.customer.id,
        }
        body['dry_run'] = True

        # test with dry-run
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        result_forms = resp.json['appointment']['consent_forms']
        self.assertEqual(len(result_forms), 2)
        if result_forms[0]['id'] != self.consent_form_1.id:
            result_forms = [result_forms[1], result_forms[0]]

        self.assertTrue(
            is_dict_subset_of_dict(
                result_forms[0],
                {
                    'id': self.consent_form_1.id,
                    'consent_uuid': str(consent_1.uuid),
                    'consent_is_signed': True,
                },
            )
        )
        self.assertTrue(
            is_dict_subset_of_dict(
                result_forms[1],
                {
                    'id': self.consent_form_2.id,
                    'consent_uuid': None,
                    'consent_is_signed': False,
                },
            )
        )

        # test without dry-run
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        result_forms = resp.json['appointment']['consent_forms']
        self.assertEqual(len(result_forms), 2)
        if result_forms[0]['id'] != self.consent_form_1.id:
            result_forms = [result_forms[1], result_forms[0]]

        # expect second consent to be created with booking,
        consent_2 = Consent.objects.get(
            form=self.consent_form_2,
            customer=self.customer,
        )

        self.assertTrue(
            is_dict_subset_of_dict(
                result_forms[0],
                {
                    'id': self.consent_form_1.id,
                    'consent_uuid': str(consent_1.uuid),
                    'consent_is_signed': True,
                },
            )
        )
        self.assertTrue(
            is_dict_subset_of_dict(
                result_forms[1],
                {
                    'id': self.consent_form_2.id,
                    'consent_uuid': str(consent_2.uuid),
                    'consent_is_signed': False,
                },
            )
        )

    @patch('webapps.booking.models.Appointment.is_crossing', return_value=True)
    @patch('webapps.booking.models.Appointment.is_first_crossing', return_value=True)
    @patch('webapps.notification.tasks.push.send_push_notification')
    def test_notifications_order(self, send_push_mock, *_mocks):
        booked_from = self._dt_from_hour(time(10, 0))

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            self.variant,
            booked_from=booked_from,
            staffer=self.staffer,
            recurring=False,
        )
        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': self.customer.id,
        }
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        # check push notifications
        consent_notification = GenericPopUpNotificationModel.objects.get(
            user=self.customer.user,
            notification_type=GenericPopUpNotificationModel.TYPE_CONSENTS,
        )
        targets = [
            call_kwargs['target'] for call_args, call_kwargs in send_push_mock.call_args_list
        ]
        self.assertListEqual(targets, [('open_pop_up_notification', str(consent_notification.id))])

        # check pop-ups
        customer_notification_types = list(
            GenericPopUpNotificationModel.objects.filter(
                user=self.customer.user,
                notification_type__in=(GenericPopUpNotificationModel.CUSTOMER_NOTIFICATIONS),
            ).values_list('notification_type', flat=True)
        )
        self.assertListEqual(
            customer_notification_types,
            [
                GenericPopUpNotificationModel.TYPE_CONSENTS,
            ],
        )


@pytest.mark.django_db
class CreateCustomerAppointmentAddonsTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.pos = pos_recipe.make(
            business=self.business,
            tips_enabled=True,
        )

        baker.make(Tip, pos=self.pos, rate=0, default=True)
        baker.make(Tip, pos=self.pos, rate=10)
        baker.make(Tip, pos=self.pos, rate=30)

        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def test_handler_multibooking_with_addons_deposit(self):
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=0),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        addon_1 = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price=12.50,
            price_type=PriceType.FIXED,
            services=[self.service],
            duration=relativedelta(minutes=20),
            max_allowed_quantity=10,
        )
        addon_2 = baker.make(
            ServiceAddOn,
            name='addon 2',
            business=self.business,
            price=14.50,
            price_type=PriceType.FIXED,
            services=[self.service],
            duration=relativedelta(minutes=10),
            max_allowed_quantity=10,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',  # 1h
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0',
            price=100.99,
        )
        service2.add_staffers([self.staffer])
        addon_3 = baker.make(
            ServiceAddOn,
            name='addon 3',
            business=self.business,
            price=16.50,
            price_type=PriceType.FIXED,
            services=[service2],
            duration=relativedelta(minutes=5),
            max_allowed_quantity=10,
        )
        addon_4 = baker.make(
            ServiceAddOn,
            name='addon 4',
            business=self.business,
            price=11.00,
            price_type=PriceType.DONT_SHOW,
            services=[service2],
            duration=relativedelta(minutes=5),
            max_allowed_quantity=10,
        )

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        # addons duration 40min
        body['subbookings'][0]['addons'] = [
            {
                'id': addon_1.id,
                'quantity': 11,
            },
            {
                'id': addon_2.id,
                'quantity': 2,
            },
        ]

        resp = self.fetch(
            f'/customer_api/me/appointments/business/{self.business.id}/dry_run/',
            method='POST',
            body=body,
        )
        self.assertEqual(resp.code, 400)

        body['subbookings'][0]['addons'] = [
            {
                'id': addon_1.id,
                'quantity': 1,
            },
            {
                'id': addon_2.id,
                'quantity': 2,
            },
        ]
        body2 = build_custappt_data(
            variant=sv2,
            booked_from=self._dt_from_hour(time(12, 00)),
            staffer=self.staffer,
            recurring=False,
        )
        # addons duration 15min
        body2['subbookings'][0]['addons'] = [
            {
                'id': addon_3.id,
                'quantity': 3,
            },
            {
                'id': addon_4.id,
                'quantity': 0,
            },
        ]

        body['subbookings'] = body['subbookings'] + body2['subbookings']
        body['dry_run'] = True

        resp = self.fetch(
            f'/customer_api/me/appointments/business/{self.business.id}/dry_run/',
            method='POST',
            body=body,
        )
        self.assertEqual(resp.code, 201)

        booked_time = datetime.datetime.strptime(
            resp.json['appointment']['booked_till'], '%Y-%m-%dT%H:%M'
        ) - datetime.datetime.strptime(resp.json['appointment']['booked_from'], '%Y-%m-%dT%H:%M')

        expected_time = datetime.datetime.strptime("2:50:00", "%H:%M:%S")
        expected_time_delta = datetime.timedelta(
            hours=expected_time.hour,
            minutes=expected_time.minute,
            seconds=expected_time.second,
        )

        self.assertEqual(resp.json['appointment']['total_value'], 209.29)
        self.assertEqual(booked_time, expected_time_delta)

    def test_change_ordering_subbooking(self):
        staffer_2 = baker.make(Resource, business=self.business, type=Resource.STAFF)

        service_1 = baker.make(Service, business=self.business)
        sv_1 = baker.make(
            ServiceVariant,
            duration='2400',  # 40 min
            service=service_1,
            time_slot_interval='0',
        )
        service_1.add_staffers([self.staffer])
        service_1.add_staffers([staffer_2])
        addon_1 = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price=16.50,
            price_type=PriceType.FIXED,
            services=[service_1],
            duration=relativedelta(minutes=5),
            max_allowed_quantity=10,
        )

        service_2 = baker.make(Service, business=self.business)
        sv_2 = baker.make(
            ServiceVariant,
            duration='3600',  # 1h
            service=service_2,
            time_slot_interval='0',
        )
        service_2.add_staffers([self.staffer])
        service_2.add_staffers([staffer_2])

        service_3 = baker.make(Service, business=self.business)
        sv_3 = baker.make(
            ServiceVariant,
            duration='3600',  # 1h
            service=service_3,
            time_slot_interval='0',
        )
        service_3.add_staffers([self.staffer])
        service_3.add_staffers([staffer_2])

        tz = self.business.get_timezone()
        business_hours = {
            DayOfWeek.monday: [(time(9, 0), time(17, 0))],
            DayOfWeek.tuesday: [(time(9, 0), time(17, 0))],
            DayOfWeek.wednesday: [(time(9, 0), time(17, 0))],
            DayOfWeek.thursday: [(time(9, 0), time(17, 0))],
            DayOfWeek.friday: [(time(9, 0), time(17, 0))],
            DayOfWeek.saturday: [(time(9, 0), time(17, 0))],
            DayOfWeek.sunday: [(time(9, 0), time(17, 0))],
        }
        staffer_hours = {
            DayOfWeek.monday: [(time(12, 0), time(17, 0))],
            DayOfWeek.tuesday: [(time(12, 0), time(17, 0))],
            DayOfWeek.wednesday: [(time(12, 0), time(17, 0))],
            DayOfWeek.thursday: [(time(12, 0), time(17, 0))],
            DayOfWeek.friday: [(time(12, 0), time(17, 0))],
            DayOfWeek.saturday: [(time(12, 0), time(17, 0))],
            DayOfWeek.sunday: [(time(12, 0), time(17, 0))],
        }

        BusinessHours.set_hours(
            business_id=self.business.id,
            hours=business_hours,
            tz=tz,
        )
        ResourceHours.set_many_resources_hours(
            business_id=self.business.id,
            resource_ids=[self.staffer.id],
            hours=staffer_hours,
            tz=tz,
        )
        ResourceHours.set_many_resources_hours(
            business_id=self.business.id,
            resource_ids=[staffer_2.id],
            hours=business_hours,
            tz=tz,
        )

        body = build_custappt_data(
            variant=sv_2,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=AnyResource,
            recurring=False,
        )

        body_1 = build_custappt_data(
            variant=sv_1,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body_1['subbookings'][0]['addons'] = [
            {
                'id': addon_1.id,
                'quantity': 1,
            },
        ]

        body_2 = build_custappt_data(
            variant=sv_3,
            booked_from=self._dt_from_hour(time(11, 45)),
            staffer=AnyResource,
            recurring=False,
        )

        body['subbookings'].append(body_1['subbookings'][0])
        body['subbookings'].append(body_2['subbookings'][0])

        resp = self.fetch(
            f'/customer_api/me/appointments/business/{self.business.id}/dry_run/',
            method='POST',
            body=body,
        )
        self.assertListEqual(
            resp.json['appointment']['subbookings'][0]['addons'],
            [],
        )
        self.assertListEqual(
            resp.json['appointment']['subbookings'][1]['addons'],
            [],
        )
        self.assertEqual(
            resp.json['appointment']['subbookings'][2]['service_variant']['id'],
            sv_1.id,
        )
        self.assertEqual(
            resp.json['appointment']['subbookings'][2]['addons'][0]['id'],
            addon_1.id,
        )

        booked_time = datetime.datetime.strptime(
            resp.json['appointment']['subbookings'][2]['booked_till'], '%Y-%m-%dT%H:%M'
        ) - datetime.datetime.strptime(
            resp.json['appointment']['subbookings'][2]['booked_from'], '%Y-%m-%dT%H:%M'
        )
        self.assertEqual(booked_time, datetime.timedelta(minutes=45))
        self.assertIn(
            str(staffer_2.id),
            resp.json['appointment']['subbookings'][2]['_availability']['staffers'],
        )


@pytest.mark.django_db
class CreateCustomerPrepaymentAppointmentAddonsTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
        )

        baker.make(Tip, pos=self.pos, rate=0, default=True)
        baker.make(Tip, pos=self.pos, rate=10)
        baker.make(Tip, pos=self.pos, rate=30)

        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        self.with_prepayment.add_staffers([self.staffer])

        self.addon_1 = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price=10.00,
            price_type=PriceType.STARTS_AT,
            services=[self.service],
            duration=relativedelta(minutes=5),
            max_allowed_quantity=10,
        )

        self.url = self.appointments_url(self.business.id)
        self.body = build_custappt_data(
            variant=self.with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        self.body['subbookings'][0]['addons'] = [
            {
                'id': self.addon_1.id,
                'quantity': 1,
            },
        ]

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        self.body['payment_method'] = card.id
        self.body['dry_run'] = False

    def test_addons_with_cancellation_fee(self):
        baker.make(
            ServiceVariantPayment,
            service_variant=self.with_prepayment,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=9.99,
        )

        resp = self.fetch(self.url, method='POST', body=self.body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment_payment']['cancellation_fee_total'] == '15.77'
        assert resp.json['appointment_payment']['cancellation_fee'] == '15.77'
        assert resp.json['appointment_payment']['prepayment_total'] == '0.00'
        assert resp.json['appointment']['payment_info']['deposit_info']['payment_rows'][0][
            'amount'
        ] == float(15.77)
        assert resp.json['appointment']['subbookings'][0]['addons'] != []
        assert resp.json['appointment']['subbookings'][0]['service_variant'] != []
        assert resp.json['appointment']['total_value'] == 27.3

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body = resp.json['appointment']
        body_to_send = {
            # pylint: disable=protected-access
            # the lack of 'traveling'-key in data affects AppointmentTravelingMixin.save:
            # 'traveling': None,
            '_version': body['_version'],
            "payment_method": card.id,
            "subbookings": [
                {
                    "addons": [
                        {
                            "quantity": 5,
                            "price_type": body['subbookings'][0]['addons'][0]["price_type"],
                            "price_description": "",
                            "max_allowed_quantity": (
                                body['subbookings'][0]['addons'][0]["max_allowed_quantity"]
                            ),
                            "id": body['subbookings'][0]['addons'][0]["id"],
                            "name": body['subbookings'][0]['addons'][0]["name"],
                            "price": body['subbookings'][0]['addons'][0]["price"],
                            "duration": body['subbookings'][0]['addons'][0]["duration"],
                        }
                    ],
                    "id": body['subbookings'][0]['id'],
                    "booked_from": body['subbookings'][0]['booked_from'],
                    "staffer_id": body['subbookings'][0]['staffer_id'],
                    "service_variant": {
                        "mode": "variant",
                        "id": body['subbookings'][0]['service_variant']['id'],
                    },
                }
            ],
            "booked_from": body['booked_from'],
        }

        modify_url = f"/customer_api/me/appointments/{body['appointment_uid']}/"
        resp = self.fetch(modify_url, method='PUT', body=body_to_send)
        assert resp.code == status.HTTP_200_OK, resp.json
        assert resp.json['appointment']['total_value'] == 67.3
        dict_assert(
            resp.json['appointment_payment']['external_partners'],
            {
                'apple_pay': False,
                'google_pay': False,
            },
        )

    def test_addons_with_cancellation_fee_customer_create_business_edit(self):
        baker.make(
            ServiceVariantPayment,
            service_variant=self.with_prepayment,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=9.99,
        )

        resp = self.fetch(self.url, method='POST', body=self.body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment_payment']['cancellation_fee'] == '15.77'

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body = resp.json['appointment']
        body_to_send = {
            '_version': body['_version'],
            "payment_method": card.id,
            "subbookings": [
                {
                    "addons": [],
                    "id": body['subbookings'][0]['id'],
                    "booked_from": body['subbookings'][0]['booked_from'],
                    "booked_till": body['subbookings'][0]['booked_from'][:14] + '55',
                    "staffer_id": body['subbookings'][0]['staffer_id'],
                    "appliance_id": -1,
                    "service_variant": {
                        "mode": "variant",
                        "id": body['subbookings'][0]['service_variant']['id'],
                    },
                }
            ],
            "booked_from": body['booked_from'],
            "customer": {
                'id': self.user.business_customer_infos.first().id,
                'mode': 'customer-card',
            },
            "overbooking": False,
            "_notify_about_reschedule": False,
        }

        modify_url = (
            f"/business_api/me/businesses/{self.business.id}/"
            f"appointments/{body['appointment_uid']}/"
        )
        resp = self.fetch(modify_url, method='PUT', body=body_to_send)
        assert resp.code == status.HTTP_200_OK, resp.json
        assert resp.json['appointment']['payment_info']['deposit_info']['total'] == '$9.99'
        assert resp.json['appointment']['payment_info']['deposit_info']['already_paid'] == '$15.77'
        assert resp.json['appointment']['payment_info']['deposit_info']['remaining'] == '-$5.78'

    def test_addons_with_prepayment(self):
        baker.make(
            ServiceVariantPayment,
            service_variant=self.with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=9.99,
        )

        resp = self.fetch(self.url, method='POST', body=self.body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment_payment']['cancellation_fee_total'] == '0.00'
        assert resp.json['appointment_payment']['prepayment_total'] == '15.77'
        assert resp.json['appointment']['payment_info']['transaction_info']['payment_rows'][0][
            'amount'
        ] == float(15.77)
        assert resp.json['appointment']['subbookings'][0]['addons'] != []
        assert resp.json['appointment']['subbookings'][0]['service_variant'] != []

    def test_combos_addons_with_prepayment(self):
        self.service.combo_type = ComboType.PARALLEL
        self.service.save()

        serv_variant_2 = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        serv_variant_2.add_staffers(serv_variant_2.service.active_staffers)
        ComboMembership.objects.create(
            combo=self.with_prepayment,
            child=serv_variant_2,
            order=1,
        )

        resp = self.fetch(self.url, method='POST', body=self.body)
        assert resp.code == status.HTTP_201_CREATED

        assert (
            len(
                [
                    subbooking['combo_children']
                    for subbooking in resp.json['appointment']['subbookings']
                ]
            )
            == 1
        )

        get_appointment_url_with_combos = (
            f"/customer_api/me/appointments/"
            f"{resp.json['appointment']['appointment_uid']}/?with_combos=1"
        )
        resp = self.fetch(get_appointment_url_with_combos, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert (
            len(
                [
                    subbooking['combo_children']
                    for subbooking in resp.json['appointment']['subbookings']
                ]
            )
            == 1
        )

        get_appointment_url_without_combos = (
            f"/customer_api/me/appointments/{resp.json['appointment']['appointment_uid']}/"
        )
        resp = self.fetch(get_appointment_url_without_combos, method='GET')
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_tips_for_addons_with_prepayment(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('9.99'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        addon_2 = baker.make(
            ServiceAddOn,
            name='addon 2',
            business=self.business,
            price=14.50,
            price_type=PriceType.FIXED,
            services=[self.service],
            duration=relativedelta(minutes=10),
            max_allowed_quantity=10,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )

        body['dry_run'] = True
        body['subbookings'][0]['addons'] = [
            {
                'id': self.addon_1.id,
                'quantity': 3,
            },
            {
                'id': addon_2.id,
                'quantity': 2,
            },
        ]

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self.assertEqual(resp.json['appointment_payment']['cancellation_fee_total'], '0.00')
        assert resp.json['appointment_payment']['prepayment_total'] == '9.99'
        assert resp.json['appointment_payment']['tip_choices'] == [
            {
                'rate': '0.00',
                'type': 'P',
                'label': 'No Tip',
                'amount': '$0.00',
                'amount_unformatted': '0.00',
                'selected': True,
                'default': True,
                'disabled': False,
                'main': True,
            },
            {
                'rate': '10.00',
                'type': 'P',
                'label': '10%',
                'amount': '$7.63',
                'amount_unformatted': '7.63',
                'selected': False,
                'default': False,
                'disabled': False,
                'main': True,
            },
            {
                'rate': '30.00',
                'type': 'P',
                'label': '30%',
                'amount': '$22.89',
                'amount_unformatted': '22.89',
                'selected': False,
                'default': False,
                'disabled': False,
                'main': True,
            },
        ]


@pytest.mark.django_db
class CreateCustomerAppointmentPeakHoursTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.pos = pos_recipe.make(
            business=self.business,
            tips_enabled=True,
        )

        baker.make(Tip, pos=self.pos, rate=0, default=True)
        baker.make(Tip, pos=self.pos, rate=10)
        baker.make(Tip, pos=self.pos, rate=30)

        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert self.business.pos_pay_by_app_enabled

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        self.variant.type = PriceType.FIXED
        self.variant.price = Decimal(50)
        self.variant.save()

        self.second_variant = baker.make(
            ServiceVariant,
            service=self.service,
            active=True,
            duration=relativedelta(minutes=20),
            time_slot_interval=relativedelta(minutes=self.INTERVAL),
            type=PriceType.FIXED,
            price=Decimal(30),
        )
        self.second_variant.add_staffers([self.staffer])

        self.peak_hour_service = PeakHourServiceFactory.get_service()
        self.peak_hour_service.enable(
            self.business.id,
            [
                PeakHour(
                    business_id=self.business.id,
                    day_of_week=day_of_week,
                    service_variants=[
                        PremiumServicesService(
                            elevation_rate=Decimal(20),
                            hour_from=time(10, 0),
                            hour_till=time(14, 0),
                            service_variant_id=variant.id,
                        ),
                    ],
                )
                for day_of_week in PremiumServicesDayOfWeek
                for variant in (self.variant, self.second_variant)
            ],
        )

    def test_handler_multibooking_with_peak_hours(self):
        baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
            discount=0,
        )

        body = build_custappt_data(
            variant=self.variant,
            booked_from=self._dt_from_hour(time(12, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['subbookings'].extend(
            build_custappt_data(
                variant=self.second_variant,
                booked_from=self._dt_from_hour(time(12, 30)),
                staffer=self.staffer,
                recurring=False,
            )['subbookings']
        )

        response = self.fetch(
            f'/customer_api/me/appointments/business/{self.business.id}/dry_run/',
            method='POST',
            body=body,
        )
        self.assertEqual(response.code, 201)
        self.assertEqual(response.json['appointment']['total_value'], 96.0)
        self.assertDictEqual(
            response.json['appointment']['subbookings'][0]['surcharge'],
            {
                'type': 'PH',
                'rate': '20',
                'amount': '10.00',
            },
        )
        self.assertEqual(response.json['appointment']['subbookings'][0]['service_price'], '$60.00')
        self.assertDictEqual(
            response.json['appointment']['subbookings'][1]['surcharge'],
            {
                'type': 'PH',
                'rate': '20',
                'amount': '6.00',
            },
        )
        self.assertEqual(response.json['appointment']['subbookings'][1]['service_price'], '$36.00')

        response = self.fetch(
            f'/customer_api/me/appointments/business/{self.business.id}/',
            method='POST',
            body=body,
        )
        self.assertEqual(response.code, 201)
        self.assertEqual(response.json['appointment']['total_value'], 96.0)
        self.assertDictEqual(
            response.json['appointment']['subbookings'][0]['surcharge'],
            {
                'type': 'PH',
                'rate': '20',
                'amount': '10.00',
            },
        )
        self.assertEqual(response.json['appointment']['subbookings'][0]['service_price'], '$60.00')
        self.assertDictEqual(
            response.json['appointment']['subbookings'][1]['surcharge'],
            {
                'type': 'PH',
                'rate': '20',
                'amount': '6.00',
            },
        )
        self.assertEqual(response.json['appointment']['subbookings'][1]['service_price'], '$36.00')
