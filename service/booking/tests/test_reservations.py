from datetime import datetime

import pytest
from django.utils.timezone import make_aware

from lib.test_utils import create_subbooking
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment, BookingChange
from webapps.booking.models import Resource, SubBooking


@pytest.mark.django_db
class BusinessCreateReservationsHandlerTestCase(BaseAsyncHTTPTest):
    url_fnc = '/business_api/me/businesses/{}/reservations/'.format

    def test_post_invalid_id(self):
        url = self.url_fnc(997)
        resp = self.fetch(url, method='POST', body={})

        assert resp.code == 404

    def test_post_invalid(self):
        url = self.url_fnc(self.business.id)

        resp = self.fetch(url, method='POST', body={})

        assert resp.code == 400
        assert resp.json == dict(
            errors=[
                dict(
                    type='validation',
                    description='Resources is required.',
                    code='required',
                    field='resources',
                ),
                dict(
                    type='validation',
                    description='Reserved from is required.',
                    code='required',
                    field='reserved_from',
                ),
                dict(
                    type='validation',
                    description='Reserved till is required.',
                    code='required',
                    field='reserved_till',
                ),
            ]
        )

    def test_post(self):
        url = self.url_fnc(self.business.id)
        body = dict(
            overbooking=False,
            reason='reason',
            reserved_from='2020-09-15T12:00',
            reserved_till='2020-09-15T12:30',
            resources=[self.owner.id],
        )

        resp = self.fetch(url, method='POST', body=body)

        booking = SubBooking.objects.select_related('appointment').first()
        assert resp.code == 201
        assert resp.json == dict(
            reservation=dict(
                id=booking.id,
                _version=booking.appointment._version,  # pylint: disable=protected-access
                type=Appointment.TYPE.RESERVATION,
                reserved_from='2020-09-15T12:00',
                reserved_till='2020-09-15T12:30',
                reason='reason',
                resources=[
                    dict(
                        active=True,
                        description=None,
                        id=self.owner.id,
                        name=self.owner.name,
                        staff_user_exists=True,
                        type=Resource.STAFF,
                        visible=self.owner.visible,
                        position='Senior Barber',
                        is_invited=False,
                        partner_app_data={},
                    )
                ],
            ),
        )


@pytest.mark.django_db
class BusinessReservationsHandlerTestCase(BaseAsyncHTTPTest):
    url_fnc = '/business_api/me/reservations/{}/'.format

    def test_delete_invalid(self):
        url = self.url_fnc(997)
        resp = self.fetch(url, method='DELETE')

        assert resp.code == 404

    def test_delete(self):
        status = Appointment.STATUS.ACCEPTED
        booking = create_subbooking(
            business=self.business,
            booking_kws=dict(
                status=status,
                type=Appointment.TYPE.RESERVATION,
            ),
        )[0]
        url = self.url_fnc(booking.id)

        resp = self.fetch(url, method='DELETE')

        assert resp.code == 200
        assert resp.json == {}
        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.CANCELED
        assert BookingChange.objects.filter(subbooking=booking).first().created > booking.updated

    def test_get_invalid(self):
        url = self.url_fnc(997)
        resp = self.fetch(url, method='GET')

        assert resp.code == 404

    def test_get(self):
        booking = create_subbooking(
            business=self.business,
            booking_kws=dict(
                status=Appointment.STATUS.ACCEPTED,
                type=Appointment.TYPE.RESERVATION,
            ),
        )[0]
        url = self.url_fnc(booking.id)

        resp = self.fetch(url, method='GET')

        assert resp.code == 200
        tz = booking.appointment.business.get_timezone()
        assert resp.json == dict(
            reservation=dict(
                id=booking.id,
                _version=booking.appointment._version,  # pylint: disable=protected-access
                type=Appointment.TYPE.RESERVATION,
                reserved_from=booking.booked_from.astimezone(
                    tz,
                ).isoformat()[:16],
                reserved_till=booking.booked_till.astimezone(
                    tz,
                ).isoformat()[:16],
                resources=[],
                reason=None,
            ),
        )

    def test_put_invalid(self):
        url = self.url_fnc(997)
        resp = self.fetch(url, method='PUT', body={})

        assert resp.code == 404

    def test_put(self):
        booked_from = datetime(2021, 9, 15)
        booking = create_subbooking(
            business=self.business,
            booking_kws=dict(
                status=Appointment.STATUS.ACCEPTED,
                type=Appointment.TYPE.RESERVATION,
                booked_from=make_aware(booked_from),
            ),
        )[0]
        url = self.url_fnc(booking.id)

        resp = self.fetch(
            url,
            method='PUT',
            body=dict(
                reserved_from='2030-09-15T13:00',
                reserved_till='2030-09-15T13:30',
                overbooking=False,
                reason='some_reason',
                resources=[self.owner.id],
            ),
        )

        booking.refresh_from_db()
        assert resp.code == 200
        assert resp.json == dict(
            reservation=dict(
                id=booking.id,
                _version=booking.appointment._version,  # pylint: disable=protected-access
                type=Appointment.TYPE.RESERVATION,
                reserved_from='2030-09-15T13:00',
                reserved_till='2030-09-15T13:30',
                reason='some_reason',
                resources=[
                    dict(
                        active=True,
                        description=None,
                        id=self.owner.id,
                        name=self.owner.name,
                        staff_user_exists=True,
                        type=Resource.STAFF,
                        visible=self.owner.visible,
                        position='Senior Barber',
                        is_invited=False,
                        partner_app_data={},
                    )
                ],
            ),
        )
