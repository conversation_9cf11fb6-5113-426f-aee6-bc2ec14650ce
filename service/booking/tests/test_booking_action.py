import datetime
from decimal import Decimal
from unittest.mock import patch

import responses
import mock
import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core import mail
from django.test.utils import override_settings
from freezegun import freeze_time
from mock import MagicMock
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC
from segment.analytics import Client

from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.booking import NoShowConfirmationExperiment
from lib.feature_flag.feature.booksy_pay import BooksyPayRefundOnCxCancellationFlag
from lib.feature_flag.feature.payment import PrepaymentsForBusinessAppointmentEnabled
from lib.test_utils import create_subbooking
from lib.test_utils.booksy_gift_cards import create_test_transaction_with_booksy_gift_card
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from lib.tools import tznow, id_to_external_api
from service.booking.tests.test_create_appointment._base import (
    BaseAppointmentWithPrepaymentTestCase,
)
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentStatus
from webapps.booking.enums import BookingAction
from webapps.booking.models import (
    Appointment,
    BookingChange,
    BookingResource,
    BookingSources,
    SubBooking,
)
from webapps.booking.notifications.business_ready import (
    CustomerNoShowNotification,
    NoShowConfirmationNotification,
)
from webapps.booking.tests.test_appointment_base import BaseTestAppointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType, CustomData
from webapps.business.models import Business, Resource, ServiceVariant, Service
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.category import BusinessCategory
from webapps.business.baker_recipes import service_variant_recipe
from webapps.consts import ANDROID, WEB
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.models import NotificationSchedule
from webapps.pos.deposit import create_cancellation_fee_transaction
from webapps.pos.enums import receipt_status, PaymentTypeEnum, PaymentProviderEnum
from webapps.pos.models import (
    Tip,
    Transaction,
    PaymentType,
    PaymentRow,
    POS,
    Receipt,
)
from webapps.pos.typing import RefundEligibility
from webapps.register.models import Register
from webapps.search_engine_tuning.models import BusinessCustomerTuning
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import DeviceTypeName, BooksyAppVersions
from webapps.segment.utils import get_device_type_name_from_api_key
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User
from webapps.pos.baker_recipes import (
    pos_recipe,
    receipt_recipe,
    transaction_recipe,
    payment_row_recipe,
)


class BookingActionTestsHelper:
    @staticmethod
    def create_transaction(booking: SubBooking, payment_type: PaymentTypeEnum) -> Transaction:
        _receipt_status = {
            PaymentTypeEnum.BOOKSY_PAY: receipt_status.BOOKSY_PAY_SUCCESS,
            PaymentTypeEnum.PREPAYMENT: receipt_status.PREPAYMENT_SUCCESS,
        }[payment_type]
        pos = baker.make(POS, business=booking.appointment.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment.id,
        )
        payment_type = baker.make(PaymentType, pos=pos, code=payment_type)
        receipt = baker.make(
            Receipt,
            payment_type=payment_type,
            status_code=_receipt_status,
            transaction=txn,
            already_paid=100,
        )
        txn.latest_receipt = receipt
        txn.save()
        baker.make(
            PaymentRow,
            amount=100,
            payment_type=payment_type,
            status=_receipt_status,
            receipt=receipt,
        )

        return txn


@pytest.mark.django_db
class TestBusinessActions(BaseAsyncHTTPTest, BaseTestAppointment):
    """
    Tests for BusinessAppointmentActionsHandler
    """

    def setUp(self):
        super().setUp()
        self.user.save()
        self.business.package = Business.Package.PRO
        self.business.owner.email = '<EMAIL>'
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.owner.save()
        self.business.save()
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        NotificationHistoryDocument.tasks_clear()

    @staticmethod
    def generate_url(booking, business_id):
        """
        Return BusinessAppointmentActionsHandler url
        """
        url = (
            f'/business_api/me/businesses/{business_id}'
            f'/appointments/{booking.appointment.id}/action'
        )
        return url

    @patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_noshow_appointment(self, analytics_track_mock, analytics_identify_mock):
        self.business.primary_category = baker.make(BusinessCategory)
        self.biz_booking_src.name = WEB
        self.business.save()
        self.biz_booking_src.save()

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            price=99,
            type=PriceType.FIXED,
            service=baker.make(
                Service,
                business=self.business,
                name='new',
                color=3,
            ),
        )

        # creating booking with  status=Appointment.STATUS.NOSHOW, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.CUSTOMER,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )
        # creating booking with  status=Appointment.STATUS.NOSHOW and
        # booking_type=Appointment.TYPE.BUSINESS, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.BUSINESS,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )

        creation_time = datetime.datetime(2020, 10, 10, 10, 0, tzinfo=UTC)
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_type=Appointment.TYPE.CUSTOMER,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=get_or_create_booking_source(
                        name=ANDROID,
                        app_type=BookingSources.CUSTOMER_APP,
                    ),
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() - relativedelta(days=1),
                    service_variant=variant,
                ),
            )
        booking, update_time = self._no_show_data_setup()
        bci = booking.appointment.booked_for

        BusinessCustomerTuning.update_multiple_tunings([bci.id])

        assert bci.search_tuning.any_mobile_customer_appointments is True
        no_show_body = self._get_no_show_payload(booking)

        noshow_url = self.generate_url(booking, self.business.id)

        with freeze_time(update_time):
            noshow_resp = self.fetch(noshow_url, method='POST', body=no_show_body)

        assert noshow_resp.code == 200

        assert set(noshow_resp.json['appointment']['meta'].keys()).issuperset(
            {'energy_booking', 'booking_score'}
        )

        self.business.events.refresh_from_db()
        assert self.business.events.first_no_show is True

        CustomerNoShowNotification(booking.appointment).schedule_record.assert_success()
        NoShowConfirmationNotification(
            booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        ).schedule_record.assert_pending()

        # Make sure booking change object is created (with proper date)
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=AppointmentStatus.NOSHOW,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == update_time
        bci.search_tuning.refresh_from_db()
        assert bci.search_tuning.any_mobile_customer_appointments is False
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': '1st_No_Show_For_Business',
                'properties': {
                    'user_id': id_to_external_api(self.business.owner.id),
                    'service_id': [booking.service_variant.id],
                    'business_id': id_to_external_api(self.business.id),
                    'business_primary_category': self.business.primary_category.internal_name,
                    'price': booking.service_variant.price,
                    'booking_id': id_to_external_api(booking.id),
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                    'device_type': get_device_type_name_from_api_key(self.biz_booking_src.api_key),
                },
            },
        )

        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': 'CB_No_Show_For_Business',
                'properties': {
                    'user_id': id_to_external_api(self.user.id),
                    'service_id': [booking.service_variant.id],
                    'business_id': id_to_external_api(self.business.id),
                    'business_primary_category': self.business.primary_category.internal_name,
                    'price': booking.service_variant.price,
                    'booking_id': id_to_external_api(booking.id),
                    'device_type': get_device_type_name_from_api_key(self.biz_booking_src.api_key),
                },
            },
        )

        dict_assert(
            analytics_identify_mock.call_args_list[0][1]['traits'],
            {
                'user_id': id_to_external_api(self.user.id),
                'email': self.user.email,
                'user_role': UserRoleEnum.OWNER,
                'country': settings.API_COUNTRY,
                'offer_type': Business.Package(
                    self.business.package
                ).label,  # pylint: disable=no-value-for-parameter
                'phone': self.user.cell_phone,
                'app_version': BooksyAppVersions.B30,
                'device_type': DeviceTypeName.DESKTOP,
                'business_CB_no_show_count': 2,
            },
        )

        assert analytics_identify_mock.call_count == 1
        assert analytics_track_mock.call_count == 2

    @patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_noshow_appointment_with_reminder_hour_change_value(
        self, _analytics_track_mock, _analytics_identify_mock
    ):
        self.business.primary_category = baker.make(BusinessCategory)
        self.business.custom_data[CustomData.REMINDER_HOUR_CHANGE_OLD_VALUE] = {
            tznow().isoformat(): 24,
        }
        self.business.save()
        self.biz_booking_src.name = WEB
        self.biz_booking_src.save()

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            price=99,
            type=PriceType.FIXED,
            service=baker.make(
                Service,
                business=self.business,
                name='new',
                color=3,
            ),
        )

        creation_time = datetime.datetime(2020, 10, 10, 10, 0, tzinfo=UTC)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_type=Appointment.TYPE.CUSTOMER,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=get_or_create_booking_source(
                        name=ANDROID,
                        app_type=BookingSources.CUSTOMER_APP,
                    ),
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() - relativedelta(days=1),
                    service_variant=variant,
                ),
            )
        booking, update_time = self._no_show_data_setup()
        bci = booking.appointment.booked_for

        BusinessCustomerTuning.update_multiple_tunings([bci.id])

        assert bci.search_tuning.any_mobile_customer_appointments is True
        no_show_body = self._get_no_show_payload(booking)

        noshow_url = self.generate_url(booking, self.business.id)

        with freeze_time(update_time):
            noshow_resp = self.fetch(noshow_url, method='POST', body=no_show_body)

        assert noshow_resp.code == 200

        assert set(noshow_resp.json['appointment']['meta'].keys()).issuperset(
            {'energy_booking', 'booking_score'}
        )

        self.business.events.refresh_from_db()
        assert self.business.events.first_no_show is True

        CustomerNoShowNotification(
            booking.appointment,
        ).schedule_record.assert_success()
        NoShowConfirmationNotification(
            booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        ).schedule_record.assert_pending()

        # Make sure booking change object is created (with proper date)
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=AppointmentStatus.NOSHOW,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == update_time
        bci.search_tuning.refresh_from_db()
        assert bci.search_tuning.any_mobile_customer_appointments is False

    @patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_noshow_bb_appointment(self, analytics_track_mock, analytics_identify_mock):
        '''
        Test anolgous to 'test_noshow_appointment' but test for BB instead of CB
        '''
        self.business.primary_category = baker.make(BusinessCategory)
        self.business.save()
        self.biz_booking_src.name = WEB
        self.biz_booking_src.save()

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            price=99,
            type=PriceType.FIXED,
            service=baker.make(
                Service,
                business=self.business,
                name='new',
                color=3,
            ),
        )
        # creating booking with  status=Appointment.STATUS.NOSHOW, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.CUSTOMER,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )
        # creating booking with  status=Appointment.STATUS.NOSHOW and
        # booking_type=Appointment.TYPE.BUSINESS, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.BUSINESS,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )
        creation_time = datetime.datetime(2020, 10, 10, 10, 0, tzinfo=UTC)
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_type=Appointment.TYPE.BUSINESS,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=get_or_create_booking_source(
                        name=ANDROID,
                        app_type=BookingSources.CUSTOMER_APP,
                    ),
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() - relativedelta(days=1),
                    service_variant=variant,
                ),
            )

        BusinessCustomerTuning.update_multiple_tunings([bci.id])
        # Cancel appointment payload
        cancel_body = {
            'action': 'no_show',
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            '_notify_about_no_show': True,
        }

        noshow_url = self.generate_url(booking, self.business.id)

        with freeze_time(update_time):
            noshow_resp = self.fetch(noshow_url, method='POST', body=cancel_body)

        assert noshow_resp.code == 200

        assert set(noshow_resp.json['appointment']['meta'].keys()).issuperset(
            {'energy_booking', 'booking_score'}
        )

        self.business.events.refresh_from_db()
        assert self.business.events.first_no_show is True

        CustomerNoShowNotification(
            booking.appointment,
        ).schedule_record.assert_success()
        NoShowConfirmationNotification(
            booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        ).schedule_record.assert_pending()

        # Make sure booking change object is created (with proper date)
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=AppointmentStatus.NOSHOW,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == update_time
        bci.search_tuning.refresh_from_db()
        assert bci.search_tuning.any_mobile_customer_appointments is False

        assert analytics_identify_mock.call_count == 1
        assert analytics_track_mock.call_count == 2  # 1st_no_show_task is also called

        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': 'BB_No_Show_For_Business',
                'properties': {
                    'user_id': id_to_external_api(self.user.id),
                    'service_id': [booking.service_variant.id],
                    'business_id': id_to_external_api(self.business.id),
                    'business_primary_category': self.business.primary_category.internal_name,
                    'price': booking.service_variant.price,
                    'booking_id': id_to_external_api(booking.id),
                    'device_type': DeviceTypeName.DESKTOP,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1]['traits'],
            {
                'user_id': id_to_external_api(self.user.id),
                'email': self.user.email,
                'user_role': UserRoleEnum.OWNER,
                'country': settings.API_COUNTRY,
                'offer_type': Business.Package(
                    self.business.package
                ).label,  # pylint: disable=no-value-for-parameter
                'phone': self.user.cell_phone,
                'app_version': BooksyAppVersions.B30,
                'device_type': DeviceTypeName.DESKTOP,
                'business_BB_no_show_count': 2,
            },
        )

    @patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_noshow_bb_no_show_for_business_killswitch(
        self, analytics_track_mock, analytics_identify_mock
    ):
        '''
        Test anolgous to 'test_noshow_appointment' but test for BB instead of CB
        '''

        from webapps.kill_switch.models import KillSwitch

        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BB_NO_SHOW_FOR_BUSINESS,
            is_killed=True,
        )

        self.business.primary_category = baker.make(BusinessCategory)
        self.business.save()

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            price=99,
            type=PriceType.FIXED,
            service=baker.make(
                Service,
                business=self.business,
                name='new',
                color=3,
            ),
        )
        # creating booking with  status=Appointment.STATUS.NOSHOW, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.CUSTOMER,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )
        # creating booking with  status=Appointment.STATUS.NOSHOW and
        # booking_type=Appointment.TYPE.BUSINESS, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.BUSINESS,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )
        creation_time = datetime.datetime(2020, 10, 10, 10, 0, tzinfo=UTC)
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_type=Appointment.TYPE.BUSINESS,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=get_or_create_booking_source(
                        name=ANDROID,
                        app_type=BookingSources.CUSTOMER_APP,
                    ),
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() - relativedelta(days=1),
                    service_variant=variant,
                ),
            )

        BusinessCustomerTuning.update_multiple_tunings([bci.id])
        # Cancel appointment payload
        cancel_body = {
            'action': 'no_show',
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            '_notify_about_no_show': True,
        }

        noshow_url = self.generate_url(booking, self.business.id)

        with freeze_time(update_time):
            noshow_resp = self.fetch(noshow_url, method='POST', body=cancel_body)

        assert noshow_resp.code == 200

        assert set(noshow_resp.json['appointment']['meta'].keys()).issuperset(
            {'energy_booking', 'booking_score'}
        )

        self.business.events.refresh_from_db()
        assert self.business.events.first_no_show is True

        CustomerNoShowNotification(
            booking.appointment,
        ).schedule_record.assert_success()
        NoShowConfirmationNotification(
            booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        ).schedule_record.assert_pending()

        # Make sure booking change object is created (with proper date)
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=AppointmentStatus.NOSHOW,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == update_time
        bci.search_tuning.refresh_from_db()
        assert bci.search_tuning.any_mobile_customer_appointments is False

        assert analytics_identify_mock.call_count == 0
        assert analytics_track_mock.call_count == 1  # 1st_no_show_task is also called

    @patch('webapps.segment.tasks.DISBALED_DURING_PYTESTS', False)
    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_noshow_cb_no_show_for_business_killswitch(
        self, analytics_track_mock, analytics_identify_mock
    ):
        from webapps.kill_switch.models import KillSwitch

        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.CB_NO_SHOW_FOR_BUSINESS,
            is_killed=True,
        )
        self.business.primary_category = baker.make(BusinessCategory)
        self.business.save()

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            price=99,
            type=PriceType.FIXED,
            service=baker.make(
                Service,
                business=self.business,
                name='new',
                color=3,
            ),
        )

        # creating booking with  status=Appointment.STATUS.NOSHOW, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.CUSTOMER,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )
        # creating booking with  status=Appointment.STATUS.NOSHOW and
        # booking_type=Appointment.TYPE.BUSINESS, to check no_show_counter
        create_subbooking(
            business=self.business,
            booking_type=Appointment.TYPE.BUSINESS,
            booking_kws=dict(
                booked_for=bci,
                updated_by=baker.make(User),
                source=get_or_create_booking_source(
                    name=ANDROID,
                    app_type=BookingSources.CUSTOMER_APP,
                ),
                status=Appointment.STATUS.NOSHOW,
            ),
        )

        creation_time = datetime.datetime(2020, 10, 10, 10, 0, tzinfo=UTC)
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_type=Appointment.TYPE.CUSTOMER,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=get_or_create_booking_source(
                        name=ANDROID,
                        app_type=BookingSources.CUSTOMER_APP,
                    ),
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() - relativedelta(days=1),
                    service_variant=variant,
                ),
            )

        BusinessCustomerTuning.update_multiple_tunings([bci.id])

        assert bci.search_tuning.any_mobile_customer_appointments is True

        # Cancel appointment payload
        cancel_body = {
            'action': 'no_show',
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            '_notify_about_no_show': True,
        }

        noshow_url = self.generate_url(booking, self.business.id)

        with freeze_time(update_time):
            noshow_resp = self.fetch(noshow_url, method='POST', body=cancel_body)

        assert noshow_resp.code == 200

        assert set(noshow_resp.json['appointment']['meta'].keys()).issuperset(
            {'energy_booking', 'booking_score'}
        )

        self.business.events.refresh_from_db()
        assert self.business.events.first_no_show is True

        CustomerNoShowNotification(
            booking.appointment,
        ).schedule_record.assert_success()
        NoShowConfirmationNotification(
            booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        ).schedule_record.assert_pending()

        # Make sure booking change object is created (with proper date)
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=AppointmentStatus.NOSHOW,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == update_time
        bci.search_tuning.refresh_from_db()
        assert bci.search_tuning.any_mobile_customer_appointments is False
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': '1st_No_Show_For_Business',
                'properties': {
                    'user_id': id_to_external_api(self.business.owner.id),
                    'service_id': [booking.service_variant.id],
                    'business_id': id_to_external_api(self.business.id),
                    'business_primary_category': self.business.primary_category.internal_name,
                    'price': booking.service_variant.price,
                    'booking_id': id_to_external_api(booking.id),
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                },
            },
        )

        assert NotificationSchedule.objects.filter(
            task_id=CustomerNoShowNotification(
                booking.appointment,
            ).schedule_record.task_id,
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=NoShowConfirmationNotification(
                booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
            ).schedule_record.task_id,
        ).exists()

        assert analytics_identify_mock.call_count == 0
        assert analytics_track_mock.call_count == 1

    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    def test_cancel_appointment(self):
        bci_user: User = baker.make(
            User,
            email='<EMAIL>',
            first_name='A',
            last_name='B',
        )
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=bci_user,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
            cell_phone='+***********',
        )
        staffer = baker.make(
            Resource,
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            business=self.business,
            staff_user=bci_user,
        )
        service = baker.make(
            Service,
            business=self.business,
            active=True,
        )
        sv = baker.make(
            ServiceVariant,
            service=service,
            active=True,
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
            price=100,
            type=PriceType.FIXED,
        )
        service.add_staffers([staffer])

        creation_time = tznow()
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=self.customer_booking_src,
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() + relativedelta(days=10),
                    service_variant=sv,
                ),
            )

        # Cancel appointment payload
        cancel_body = {
            'action': 'cancel',
            # pylint: disable=protected-access
            '_version': booking.appointment._version,
            '_notify_about_cancel_email': False,
            '_notify_about_cancel_sms': False,
        }

        action_url = self.generate_url(booking, self.business.id)

        with freeze_time(update_time):
            action_resp = self.fetch(action_url, method='POST', body=cancel_body)

        assert action_resp.code == 200

        # Make sure notifications have been sent
        assert NotificationHistoryDocument.task_count() == 2
        notifications = NotificationHistoryDocument.tasks_get(
            customer_id=bci_user.id,
        )
        assert notifications[0].task_id.startswith('booking_changed:customer_booking_cancel')
        assert notifications[0].recipient_email == bci.email
        assert notifications[1].task_id.startswith('booking_changed:customer_booking_cancel')
        assert notifications[1].recipient_phone == bci.cell_phone
        assert len(mail.outbox) == 1

        # Make sure booking change object has been created (with proper date)
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=AppointmentStatus.CANCELED,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == update_time
        assert booking_change_entries[0].service_price == sv.price
        assert booking_change_entries[0].service_price_type == sv.type

        # no no-show notification because it's cancel action
        assert not NotificationSchedule.objects.filter(
            task_id=CustomerNoShowNotification(
                booking.appointment,
            ).schedule_record.task_id,
        ).exists()
        assert not NotificationSchedule.objects.filter(
            task_id=NoShowConfirmationNotification(
                booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
            ).schedule_record.task_id,
        ).exists()

    def test_cancel_appointment_invisible_bci_without_user_doesnt_crash(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
            cell_phone='+***********',
            visible_in_business=False,
        )

        creation_time = tznow()
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=self.customer_booking_src,
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() + relativedelta(days=10),
                ),
            )

        # Cancel appointment payload
        cancel_body = {
            'action': 'cancel',
            # pylint: disable=protected-access
            '_version': booking.appointment._version,
        }

        action_url = self.generate_url(booking, self.business.id)

        with freeze_time(update_time):
            action_resp = self.fetch(action_url, method='POST', body=cancel_body)
        self.assertEqual(action_resp.code, 200)

    @parameterized.expand(
        [
            (
                PaymentTypeEnum.PREPAYMENT,
                receipt_status.PREPAYMENT_SUCCESS,
                receipt_status.PAYMENT_SUCCESS,
            ),
            (
                PaymentTypeEnum.BOOKSY_PAY,
                receipt_status.BOOKSY_PAY_SUCCESS,
                receipt_status.PAYMENT_SUCCESS,
            ),
        ]
    )
    def test_checkout_transaction_while_canceling_appointment(
        self,
        payment_type: PaymentTypeEnum,
        expected_receipt_status_before: str,
        expected_receipt_status_after: str,
    ) -> None:
        creation_time = tznow()
        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business, booking_kws={'status': Appointment.STATUS.ACCEPTED}
            )
        txn = BookingActionTestsHelper.create_transaction(booking, payment_type)
        cancel_body = {
            'action': 'cancel',
            # pylint: disable=protected-access
            '_version': booking.appointment._version,
        }
        action_url = self.generate_url(booking, self.business.id)
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(update_time):
            self.fetch(action_url, method='POST', body=cancel_body)

        assert txn.latest_receipt.status_code == expected_receipt_status_before
        assert txn.latest_receipt.payment_rows.last().status == expected_receipt_status_before

        txn.refresh_from_db()

        assert txn.latest_receipt.status_code == expected_receipt_status_after
        assert txn.latest_receipt.payment_rows.last().status == expected_receipt_status_after

    def _no_show_data_setup(self):
        self.business.primary_category = baker.make(BusinessCategory)
        self.business.save()

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )

        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            price=99,
            type=PriceType.FIXED,
            service=baker.make(
                Service,
                business=self.business,
                name='new',
                color=3,
            ),
        )

        creation_time = datetime.datetime(2020, 10, 10, 10, 0, tzinfo=UTC)
        update_time = creation_time + datetime.timedelta(days=3)

        with freeze_time(creation_time):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_type=Appointment.TYPE.CUSTOMER,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=get_or_create_booking_source(
                        name=ANDROID,
                        app_type=BookingSources.CUSTOMER_APP,
                    ),
                    status=Appointment.STATUS.FINISHED,
                    booked_from=tznow() - relativedelta(days=1),
                    service_variant=variant,
                ),
            )
        return booking, update_time

    def _get_no_show_payload(self, booking):
        return {
            'action': 'no_show',
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            '_notify_about_no_show': False,  # deprecated, backend is overriding this value
        }


@mock.patch('service.customer.my_booksy.is_in_experiment', return_value=False)
@pytest.mark.django_db
@override_feature_flag({PrepaymentsForBusinessAppointmentEnabled: True})
class TestBusinessActionsWithPrepaymentTestCase(BaseAppointmentWithPrepaymentTestCase):
    def setUp(self):
        super().setUp()
        (
            self.booked_from,
            self.booked_till,
            self.variant,
            self.staffer,
        ) = self._standard_setup_for_prepayment()
        baker.make(Tip, pos=self.business.pos, rate=0, default=True)
        baker.make(Tip, pos=self.business.pos, rate=10, default=False)

    @responses.activate
    @patch('webapps.pos.provider.proxy.TransactionService.create_basket_from_cf_auth')
    @patch('webapps.pos.provider.proxy.get_customer_wallet_id_adapter')
    @patch('webapps.pos.provider.proxy.BasketPaymentService.authorize_payment')
    @patch('webapps.pos.provider.proxy.BasketPaymentAnalyticsService.create_analytics_data')
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    def test_flow_business_noshow_business_booking__stripe_account(
        self,
        mock_create_analytics_data,
        mock_authorize_payment,
        mock_get_customer_wallet_id_adapter,
        mock_create_basket_from_cf_auth,
    ):
        mock_create_basket = MagicMock()

        mock_create_basket_from_cf_auth.return_value = mock_create_basket
        mock_get_customer_wallet_id_adapter.return_value = 123

        self.pba = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.PAY_BY_APP,
        )
        self.cash = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.CASH,
        )

        self._prepare_stripe_account()
        baker.make(
            Register,
            is_open=True,
            pos=self.pos,
            opened_by=self.user,
        )

        _, __, variant, ___ = self._standard_setup_for_cancellation_fee()
        appointment, transaction = self.create_appointment_with_transaction_with_deposit(
            variant=variant,
            payment_type=self.pba,
        )
        assert (
            transaction.latest_receipt.status_code == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

        appointment_action_path = self.get_path_appointment__business_action(
            self.business.id,
            appointment.id,
        )
        request_body = {
            'action': BookingAction.NO_SHOW,
            'business_note': '',
            '_version': appointment._version,  # pylint: disable=protected-access
        }
        action_resp = self.fetch(
            body=request_body,
            method='POST',
            path=appointment_action_path,
        )

        assert action_resp.code == 200
        assert action_resp.json['appointment']['status'] == Appointment.STATUS.NOSHOW
        assert not action_resp.json['appointment']['payment_info']['payable']
        assert action_resp.json['appointment']['payment_info']['transaction_info'] is None
        transaction_from_db = Transaction.objects.get(appointment_id=appointment.id)
        assert transaction_from_db.latest_receipt.status_code == (
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        )

        assert mock_authorize_payment.call_count == 1
        assert mock_create_analytics_data.call_count == 1

    def test_booksy_pay_transaction_after_no_show(self, _):
        self.service = baker.make(Service, business=self.business)
        self.service_variant = service_variant_recipe.make(
            service=self.service,
            price=Decimal('100.00'),
            type=PriceType.FIXED,
        )
        appointment = create_appointment(
            [
                {'service_variant': self.service_variant},
            ],
            total_type=PriceType.FIXED,
            status=Appointment.STATUS.ACCEPTED,
            business=self.business,
            booked_for=self.bci,
        )
        transaction = transaction_recipe.make(
            appointment=appointment,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            total=100,
        )
        payment_type = baker.make(
            'pos.PaymentType',
            pos=self.business.pos,
            code=PaymentTypeEnum.BOOKSY_PAY,
            default=True,
        )
        receipt = receipt_recipe.make(
            transaction=transaction,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            payment_type=payment_type,
            already_paid=100,
        )
        transaction.latest_receipt = receipt
        transaction.save()
        payment_row_recipe.make(
            receipt=receipt,
            payment_type=receipt.payment_type,
            status=receipt_status.BOOKSY_PAY_SUCCESS,
        )

        appointment_action_path = self.get_path_appointment__business_action(
            self.business.id,
            appointment.id,
        )
        request_body = {
            'action': BookingAction.NO_SHOW,
            'business_note': '',
            '_version': appointment._version,  # pylint: disable=protected-access
        }
        action_resp = self.fetch(
            body=request_body,
            method='POST',
            path=appointment_action_path,
        )

        assert action_resp.code == 200
        assert action_resp.json['appointment']['status'] == Appointment.STATUS.NOSHOW
        assert not action_resp.json['appointment']['payment_info']['payable']
        transaction_from_db = Transaction.objects.get(appointment_id=appointment.id)
        assert transaction_from_db.latest_receipt.status_code == (receipt_status.BOOKSY_PAY_SUCCESS)

    def create_appointment_with_transaction_with_deposit(
        self,
        variant: ServiceVariant,
        payment_type: PaymentType,
    ):
        appointment = create_appointment(
            [
                dict(service_variant=variant),
            ],
            business=self.business,
            booked_for=self.bci,
        )
        transaction = create_cancellation_fee_transaction(
            checkout=AppointmentWrapper(subbookings=list(appointment.subbookings)).checkout,
            pos=self.pos,
        )
        latest_receipt = transaction.latest_receipt
        transaction.latest_receipt.id = None
        latest_receipt.status_code = receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        latest_receipt.save()
        transaction.latest_receipt = latest_receipt
        transaction.save()
        baker.make(
            PaymentRow,
            amount=Decimal('10'),
            payment_type=payment_type,
            receipt=latest_receipt,
            provider=PaymentProviderEnum.STRIPE_PROVIDER.value,
        )
        return appointment, transaction


@pytest.mark.django_db
class TestCancelAppointmentCustomer(BaseAsyncHTTPTest, BaseTestAppointment):
    """
    Tests for BusinessAppointmentActionsHandler
    """

    def setUp(self):
        super().setUp()
        self.user.save()
        self.business.package = Business.Package.PRO
        self.business.owner.email = '<EMAIL>'
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.owner.save()
        self.business.save()
        NotificationHistoryDocument.tasks_clear()

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
            cell_phone='+***********',
        )
        self.creation_time = tznow()
        with freeze_time(self.creation_time):
            self.booking, *_ = create_subbooking(
                business=self.business,
                booking_kws=dict(
                    booked_for=bci,
                    updated_by=baker.make(User),
                    source=self.customer_booking_src,
                    status=Appointment.STATUS.ACCEPTED,
                    booked_from=tznow() + relativedelta(days=10),
                ),
            )
            owner_resource = Resource.objects.get(staff_user=self.business.owner)
            BookingResource.objects.create(subbooking=self.booking, resource=owner_resource)

    @staticmethod
    def generate_url(booking):
        """
        Return CustomerAppointmentActionsHandler url
        """
        return f'/customer_api/me/appointments/{booking.appointment.id}/action'

    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_eppo_feature_flag(
        {
            NoShowConfirmationExperiment.flag_name: ExperimentVariants.VARIANT_A.value,
        }
    )
    def test_cancel_appointment(self):
        # Cancel appointment payload
        cancel_body = {
            'action': 'cancel',
            # pylint: disable=protected-access
            '_version': self.booking.appointment._version,
        }
        update_time = self.creation_time + datetime.timedelta(days=3)
        action_url = self.generate_url(self.booking)

        with freeze_time(update_time):
            action_resp = self.fetch(action_url, method='POST', body=cancel_body)

        assert action_resp.code == 200

        # Make sure notifications have been sent
        assert NotificationHistoryDocument.task_count() == 2
        notifications = NotificationHistoryDocument.tasks_get(
            business_id=self.business.id,
        )
        assert notifications[0].title == 'SubBookingCancelNotificationWithPush'
        assert "Don't forget to process refund" not in notifications[0].content
        assert 'cancelled appointment on:' in notifications[1].title
        assert len(mail.outbox) == 1

        # Make sure booking change object has been created (with proper date)
        booking_change_entries = BookingChange.objects.filter(
            subbooking=self.booking,
            status=AppointmentStatus.CANCELED,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == update_time

        assert NotificationSchedule.objects.filter(
            task_id__contains='business_booking_cancel'
        ).exists()

        # no no-show notification because it's cancel action
        assert not NotificationSchedule.objects.filter(
            task_id=CustomerNoShowNotification(
                self.booking.appointment,
            ).schedule_record.task_id,
        ).exists()
        assert not NotificationSchedule.objects.filter(
            task_id=NoShowConfirmationNotification(
                self.booking.appointment, experiment_variant=ExperimentVariants.VARIANT_A
            ).schedule_record.task_id,
        ).exists()

    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(POS__PREPAYMENTS=True)
    @patch('webapps.pos.tasks.BooksyGiftCardsClient.reissue_booksy_gift_card')
    def test_customer_cancel_proposed_appointment(
        self,
        reissue_booksy_gift_card_mock: MagicMock,
    ):
        # set a proper appointment status for this test case
        self.booking.appointment.status = Appointment.STATUS.PROPOSED
        self.booking.appointment.is_booksy_gift_card_appointment = True
        self.booking.appointment.save()
        # create related transaction
        pos = pos_recipe.make(business=self.booking.appointment.business)
        _, gift_cards = create_test_transaction_with_booksy_gift_card(
            appointment_id=self.booking.appointment.id,
            pos=pos,
        )
        gift_card = gift_cards[0]
        # Cancel appointment payload
        cancel_body = {
            'action': 'cancel',
            # pylint: disable=protected-access
            '_version': self.booking.appointment._version,
        }
        update_time = self.creation_time + datetime.timedelta(days=3)
        action_url = self.generate_url(self.booking)

        with freeze_time(update_time):
            action_resp = self.fetch(
                action_url,
                method='POST',
                body=cancel_body,
            )

        assert action_resp.code == 200
        assert reissue_booksy_gift_card_mock.call_args[0][0][0] == gift_card.external_id

    @parameterized.expand(
        [
            (
                PaymentTypeEnum.PREPAYMENT,
                receipt_status.PREPAYMENT_SUCCESS,
                receipt_status.PAYMENT_SUCCESS,
            ),
        ]
    )
    def test_checkout_transaction_while_canceling_appointment(
        self,
        payment_type: PaymentTypeEnum,
        expected_receipt_status_before: str,
        expected_receipt_status_after: str,
    ) -> None:
        cancel_body = {
            'action': 'cancel',
            # pylint: disable=protected-access
            '_version': self.booking.appointment._version,
        }
        update_time = self.creation_time + datetime.timedelta(days=3)
        action_url = self.generate_url(self.booking)
        txn = BookingActionTestsHelper.create_transaction(self.booking, payment_type)

        with freeze_time(update_time):
            self.fetch(action_url, method='POST', body=cancel_body)

        assert txn.latest_receipt.status_code == expected_receipt_status_before
        assert txn.latest_receipt.payment_rows.last().status == expected_receipt_status_before

        txn.refresh_from_db()

        assert txn.latest_receipt.status_code == expected_receipt_status_after
        assert txn.latest_receipt.payment_rows.last().status == expected_receipt_status_after

    @parameterized.expand(
        [
            (
                True,
                'SubbookingAutoRefundNotification',
            ),
            (
                False,
                'SubbookingLateCancellationRefundNotification',
            ),
        ]
    )
    @override_eppo_feature_flag(
        {
            BooksyPayRefundOnCxCancellationFlag.flag_name: True,
        }
    )
    @patch('service.booking.booking_actions.auto_refund_transaction.si')
    @patch('service.booking.booking_actions.get_refund_eligibility')
    def test_cancel_appointment_with_booksy_pay_refund_reminder(
        self,
        is_auto_refund_possible,
        notification_title,
        get_refund_eligibility_info_mock,
        auto_refund_transaction_mock,
    ):
        get_refund_eligibility_info_mock.return_value = RefundEligibility(
            is_auto_refund_possible=is_auto_refund_possible,
            refundable=True,
        )
        BookingActionTestsHelper.create_transaction(self.booking, PaymentTypeEnum.BOOKSY_PAY)
        # Cancel appointment payload
        cancel_body = {
            'action': 'cancel',
            # pylint: disable=protected-access
            '_version': self.booking.appointment._version,
        }
        update_time = self.creation_time + datetime.timedelta(days=3)
        action_url = self.generate_url(self.booking)

        with freeze_time(update_time):
            action_resp = self.fetch(action_url, method='POST', body=cancel_body)

        if is_auto_refund_possible:
            auto_refund_transaction_mock.assert_called_once()
        else:
            auto_refund_transaction_mock.assert_not_called()

        assert action_resp.code == 200

        # Make sure notifications have been sent
        assert NotificationHistoryDocument.task_count() == 2
        notifications = NotificationHistoryDocument.tasks_get(
            business_id=self.business.id,
        )
        assert notifications[0].title == notification_title
