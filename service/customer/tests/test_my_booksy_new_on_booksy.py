from datetime import datetime, timedelta

import pytest
import pytz
from django.utils.translation import gettext as _
from mock import patch

from conftest_helpers import clean_elastisearch_index_helper
from lib.elasticsearch.consts import ESAvailabilityNature, ESIndex
from lib.tools import tznow
from service.customer.my_booksy import GalleriesType
from service.tests import ElasticBaseTestCase
from webapps.business.baker_recipes import make_es_indexable_business, treatment_recipe
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.elasticsearch.tools import format_availability_doc
from webapps.business.models.category import BusinessCategory
from webapps.experiment_v3.exp import (
    NewOnBooksyGalleryExperiment,
    UTT2InSearch,
)
from webapps.experiment_v3.models import Experiment
from webapps.images.baker_recipes import cover_photo_recipe, image_recipe
from webapps.images.elasticsearch import ImageDocument


@pytest.mark.django_db
class ExperimentBaseTestCase(ElasticBaseTestCase):
    url = '/customer_api/my_booksy/galleries/'
    fingerprint = '1'
    test_datetime = datetime(2021, 8, 25, 9)

    def setUp(self, **kwargs):
        super().setUp(**kwargs)
        self.prevent_errors_from_unrelated_experiments()
        self.initialize_experiment()

    def tearDown(self):
        self.clean_business_index()
        super().tearDown()

    @staticmethod
    def clean_business_index():
        return clean_elastisearch_index_helper(ESIndex.BUSINESS)

    def prevent_errors_from_unrelated_experiments(self):
        UTT2InSearch.initialize()
        UTT2InSearch(self.user.id).set_variant(UTT2InSearch.v.control)

    @staticmethod
    def initialize_experiment():
        with patch.dict(NewOnBooksyGalleryExperiment.config, {'active': True}):
            NewOnBooksyGalleryExperiment.initialize()

    def get_headers(self, path):
        headers = super().get_headers(path)
        headers['X-Fingerprint'] = self.fingerprint
        return headers

    @pytest.mark.freeze_time(test_datetime)
    def get(self, del_args=None):
        args = {
            'geo_location': '1.0,-70.0',
        }
        if del_args:
            for name in del_args:
                del args[name]
        return super().fetch(self.url, args=args)


class ExperimentSlotTestCase(ExperimentBaseTestCase):
    def test_ok(self):
        experiment = Experiment.objects.get(name=NewOnBooksyGalleryExperiment.name)
        assert experiment.slots.count() == 0

        response = self.get()
        assert response.code == 200
        assert experiment.slots.count() == 1

        slot = experiment.slots.first()
        assert slot.extra_data['geo_location'] == {'lat': 1.0, 'lon': -70.0}

    def test_disabled_without_geo(self):
        response = self.get(del_args=['geo_location'])
        assert response.code == 200

        experiment = Experiment.objects.get(name=NewOnBooksyGalleryExperiment.name)
        assert experiment.slots.count() == 0

    def test_with_disabled_slot_creation(self):
        experiment = Experiment.objects.get(name=NewOnBooksyGalleryExperiment.name)
        experiment.parameters['create_new_slots'] = False
        experiment.save()

        response = self.get()
        assert response.code == 200
        assert experiment.slots.count() == 0


class NewOnBooksyExperimentTestCase(ExperimentBaseTestCase):
    def setUp(self, **kwargs):
        super().setUp(**kwargs)
        self.set_variant(NewOnBooksyGalleryExperiment.Variant.TEST)

    def test_ok(self):
        self.create_businesses()
        response = self.get()
        assert response.code == 200
        assert self.has_new_gallery(response)
        assert self.has_other_galleries(response)
        assert self.new_gallery_is_first(response)

    def test_gallery_not_generated(self):
        experiment = Experiment.objects.get(name=NewOnBooksyGalleryExperiment.name)
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)
        assert experiment.slots.first().extra_data.get('gallery_not_generated') == 1

        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)
        assert experiment.slots.first().extra_data.get('gallery_not_generated') == 2

    def test_no_cover(self):
        self.create_businesses(with_cover=False)
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_not_recent(self):
        self.create_businesses(
            add_kwargs={
                'active_from': (datetime.now() - timedelta(days=60)).isoformat(timespec='seconds'),
            },
        )
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_exclusion(self):
        experiment = Experiment.objects.get(name=NewOnBooksyGalleryExperiment.name)
        experiment.parameters['excluded_business_ids'] = [1]
        experiment.save()

        self.create_businesses()
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_not_available(self):
        self.create_businesses(del_kwargs=['availability'])
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_not_close(self):
        self.create_businesses(
            add_kwargs=dict(
                business_location=dict(
                    city='Foo, FL',
                    coordinate=dict(lat=0.0, lon=0.0),
                ),
            )
        )
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_null_reviews_count(self):
        self.create_businesses(
            add_kwargs=dict(
                reviews_count=None,
            ),
            del_kwargs=['reviews_rank'],
        )
        response = self.get()
        assert response.code == 200
        assert self.has_new_gallery(response)

    def test_no_reviews(self):
        self.create_businesses(
            add_kwargs=dict(
                reviews_count=0,
            ),
            del_kwargs=['reviews_rank'],
        )
        response = self.get()
        assert response.code == 200
        assert self.has_new_gallery(response)

    def test_low_reviews_rank(self):
        self.create_businesses(
            add_kwargs=dict(
                reviews_rank=2,
            ),
        )
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_not_enough_treatments(self):
        self.create_businesses(
            add_kwargs=dict(
                treatment_count=1,
            ),
        )
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_treatment_weights(self):
        score_for_2 = self.get_score_for_treatment_count(2)
        score_for_3 = self.get_score_for_treatment_count(3)
        assert score_for_3 - score_for_2 == 15

        max_score = self.get_score_for_treatment_count(12)
        score_for_13 = self.get_score_for_treatment_count(13)
        assert score_for_13 == max_score

    def get_score_for_treatment_count(self, value):
        self.create_businesses(
            add_kwargs={
                'treatment_count': value,
            }
        )
        response = self.get()
        assert response.code == 200
        assert self.has_new_gallery(response)
        return self.get_first_score_in_new_gallery(response)

    def test_photo_weights(self):
        score_for_1 = self.get_score_for_non_cover_image_limited_count(1)
        score_for_2 = self.get_score_for_non_cover_image_limited_count(2)
        assert score_for_2 - score_for_1 == 15

        score_for_0 = self.get_score_for_non_cover_image_limited_count(0)
        max_score = self.get_score_for_non_cover_image_limited_count(15)
        assert max_score - score_for_0 == 225

    def test_without_new_fields(self):
        self.create_businesses(del_kwargs=['active_from', 'treatment_count'])
        response = self.get()
        assert response.code == 200
        assert not self.has_new_gallery(response)

    def test_only_boost(self):
        experiment = Experiment.objects.get(name=NewOnBooksyGalleryExperiment.name)
        experiment.parameters['only_boost'] = True
        experiment.save()

        index = self.clean_business_index()
        self.create_business(0, add_kwargs=None, del_kwargs=None, with_cover=True)
        self.create_business(1, add_kwargs=None, del_kwargs=None, with_cover=True)
        self.create_business(2, add_kwargs=None, del_kwargs=['promoted'], with_cover=True)
        self.create_business(3, add_kwargs=None, del_kwargs=['promoted'], with_cover=True)
        index.refresh()

        response = self.get()
        assert response.code == 200
        assert self.has_new_gallery(response)

        new_gallery = self.get_new_gallery(response)
        ids = set(b['id'] for b in new_gallery['businesses'])
        assert ids == {0, 1}

    def get_score_for_non_cover_image_limited_count(self, value):
        self.create_businesses(
            add_kwargs={
                'non_cover_image_limited_count': value,
            }
        )
        response = self.get()
        assert response.code == 200
        assert self.has_new_gallery(response)
        return self.get_first_score_in_new_gallery(response)

    def set_variant(self, variant):
        NewOnBooksyGalleryExperiment(
            relation_id=self.fingerprint,
            user_pseudo_id=None,
            geo_location=None,
        ).set_variant(variant)

    @staticmethod
    def has_new_gallery(response):
        return GalleriesType.NEW_ON_BOOKSY.value in (v['type'] for v in response.json.values())

    @staticmethod
    def has_other_galleries(response):
        expected_types = {
            GalleriesType.SPECIAL_OFFERS.value,
            GalleriesType.RECOMMENDED.value,
            GalleriesType.NEAR_ME.value,
        }
        types = {v['type'] for v in response.json.values()}
        return expected_types.issubset(types)

    @staticmethod
    def new_gallery_is_first(response):
        first_gallery = sorted(response.json.items())[0][1]
        return first_gallery['type'] == GalleriesType.NEW_ON_BOOKSY.value

    def create_businesses(self, add_kwargs=None, del_kwargs=None, with_cover=True):
        index = self.clean_business_index()
        for i in range(2):
            self.create_business(i, add_kwargs, del_kwargs, with_cover)
        index.refresh()

    @staticmethod
    def create_business(i, add_kwargs, del_kwargs, with_cover):
        in_3_days = tznow().replace(hour=0) + timedelta(days=3)
        business_kwargs = dict(
            id=i,
            _id=f'business:{i}',
            join='business',
            visible=True,
            is_b_listing=False,
            promoted=True,
            promotions_profitability=1,
            business_location=dict(
                city='Foo, FL',
                coordinate=dict(lat=1.0, lon=-70.0),
            ),
            active_from=datetime.now().isoformat(timespec='seconds'),
            availability=format_availability_doc(
                treatment_type=BusinessCategory.CATEGORY,
                treatment_id=-1,
                nature=ESAvailabilityNature.REAL,
                lower=in_3_days + timedelta(hours=8),
                upper=in_3_days + timedelta(hours=16),
                tz=pytz.utc,  # noqa
            ),
            reviews_count=1,
            reviews_rank=3,
            treatment_count=2,
            non_cover_image_limited_count=1,
        )
        if add_kwargs:
            business_kwargs.update(add_kwargs)
        if del_kwargs:
            for name in del_kwargs:
                del business_kwargs[name]

        BusinessDocument(**business_kwargs).save()

        if with_cover:
            ImageDocument(
                image_id=i,
                business_id=i,
                is_cover_photo=True,
                join=dict(name='image', parent=f'business:{i}'),
                meta=dict(
                    routing=f'{i}',
                ),
            ).save()

    @staticmethod
    def get_new_gallery(response):
        return [v for v in response.json.values() if v['label'] == _('New on Booksy')][0]

    @classmethod
    def get_first_score_in_new_gallery(cls, response):
        new_gallery = cls.get_new_gallery(response)
        return new_gallery['businesses'][0]['meta']['score']


@pytest.mark.django_db
def test_treatments_count_field():
    business = make_es_indexable_business()
    doc = business.get_document()
    assert doc.treatment_count == 0

    treatment = treatment_recipe.make()
    business.treatments.add(treatment)
    business.reindex()
    doc = business.get_document()
    assert doc.treatment_count == 1


@pytest.mark.django_db
def test_non_cover_image_limited_count():
    business = make_es_indexable_business()
    doc = business.get_document()
    assert doc.non_cover_image_limited_count == 0

    image_recipe.make(
        business=business,
    )
    business.reindex()
    doc = business.get_document()
    assert doc.non_cover_image_limited_count == 1

    cover_photo_recipe.make(
        business=business,
    )
    business.reindex()
    doc = business.get_document()
    assert doc.non_cover_image_limited_count == 1


@pytest.mark.django_db
def test_active_from_field():
    business = make_es_indexable_business(active_from=tznow())
    doc = business.get_document()
    assert doc.active_from is not None
