import logging

from dateutil.tz import gettz
from django.db import transaction
from rest_framework import serializers

from lib.feature_flag.feature.analytics import FacebookAnalyticsCustomerBookingTrackingFlag
from lib.tools import (
    external_api_to_id,
    extract_internal_id,
    camel_to_snakecase_dict,
    firstof,
    snake_to_camelcase_dict,
    id_to_external_api,
)

from webapps.booking.enums import WhoMakesChange
from webapps.booking.events import customer_appointment_created_event
from webapps.booking.models import (
    BookingSources,
    Appointment,
    BookingChange,
    SubBooking,
)
from webapps.booking.serializers.appointment import (
    PartnerCustomerAppointmentSerializer,
)
from webapps.business.models import (
    ServiceVariant,
    Resource,
    Business,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.serializers import SerializerBusinessMixin
from webapps.consts import PARTNERS_GROUPON
from webapps.feeds.groupon.enums import (
    APPOINTMENT_STATUS_TO_REDEMPTION_STATUS,
    GrouponBookingTypeChoices,
    GrouponBookingType,
)
from webapps.feeds.models import GrouponBooking
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_booking import (
    BookingChangedScenario,
)
from webapps.segment.tasks import (
    segment_api_appointment_booked_task,
    send_analytics_1st_cb_created_for_business_to_facebook,
)
from webapps.user.tools import get_system_user
from webapps.zoom.models import ZoomMeeting
from service.partners.serializers import (
    PartnersBaseErrorSerializer,
    PartnersBaseSerializer,
    PartnersBaseRerouterSerializer,
)

logger = logging.getLogger('booksy.groupon.v1')

GROUPON_DATETIME_FORMAT = '%Y-%m-%dT%H:%M:%SZ'


class GrouponBaseSerializer(PartnersBaseSerializer):
    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    @property
    def normalized_data(self):
        return camel_to_snakecase_dict(self.initial_data)

    def to_internal_value(self, data):
        data = camel_to_snakecase_dict(data)  # merchantId => merchant_id
        return super().to_internal_value(data)


class GrouponIdSerializer(GrouponBaseSerializer):
    id = serializers.CharField(required=True)


class GrouponBookingIdSerializer(GrouponBaseSerializer):
    groupon_booking_id = serializers.CharField(required=True)


class GrouponCustomerSerializer(GrouponBaseSerializer):
    first_name = serializers.CharField(required=False, allow_blank=True)
    last_name = serializers.CharField(required=False, allow_blank=True)
    email = serializers.EmailField(required=False, allow_blank=True)
    phone = serializers.CharField(required=False, allow_blank=True)


class GrouponBusinessSerializer(
    SerializerBusinessMixin,
    GrouponBaseSerializer,
):
    merchant_id = serializers.CharField(required=True)

    def validate(self, attrs):
        if not self.business_id or not self.context.get('instance_business'):
            raise serializers.ValidationError({'merchant_id': 'does_not_exist'})
        return super().validate(attrs)

    @property
    def business_id(self):
        try:
            params = external_api_to_id(self.normalized_data.get('merchant_id'))
            if params:
                return params['business_id']
        except (AttributeError, KeyError):
            pass

    def _set_instance_business(self, data):
        if self.business_id:
            try:
                self.context['instance_business'] = Business.objects.get(
                    id=self.business_id, active=True
                )
            except Business.DoesNotExist:
                pass


# BookingCreation
class GrouponCreationAvailabilitySerializer(GrouponBaseSerializer):
    id = serializers.CharField(required=False, allow_null=True)
    available_at = serializers.DateTimeField(required=True, default_timezone=gettz('UTC'))
    available_until = serializers.DateTimeField(required=True, default_timezone=gettz('UTC'))
    attribute_ids = serializers.PrimaryKeyRelatedField(
        queryset=Resource.objects.filter(active=True),
        required=False,
        default=[],
        many=True,
    )
    service_id = serializers.PrimaryKeyRelatedField(
        queryset=ServiceVariant.objects.filter(active=True),
        required=True,
    )

    def to_internal_value(self, data):
        data = camel_to_snakecase_dict(data)
        service_id = data.get('service_id')
        if service_id:
            data['service_id'] = extract_internal_id(service_id)
        attribute_ids = data.get('attribute_ids')
        if attribute_ids:
            data['attribute_ids'] = [
                extract_internal_id(attribute_id) for attribute_id in attribute_ids if attribute_id
            ]
        return super().to_internal_value(data)


class GrouponCreationBookingSerializer(GrouponBaseSerializer):
    groupon_booking_id = serializers.CharField(required=True)
    availability = GrouponCreationAvailabilitySerializer(many=True)


class GrouponBookingCreationRequestSerializer(GrouponBusinessSerializer):
    groupon_customer_service_id = serializers.CharField(
        required=False,
        allow_blank=True,
    )
    bookings = GrouponCreationBookingSerializer(
        many=True,
        required=True,
    )
    purchaser_details = GrouponCustomerSerializer(required=False, allow_null=True)
    booking_type = serializers.ChoiceField(
        choices=GrouponBookingTypeChoices,
        required=True,
    )

    def __init__(self, **kwargs):
        self._appointments = []
        super().__init__(**kwargs)

    @property
    def customer_data(self):
        return self.normalized_data.get('purchaser_details') or {}

    def _validate_appointments(self, data):
        for idx, booking in enumerate(data.get('bookings')):
            for availability in booking.get('availability'):
                appointment_data = {
                    'subbookings': [
                        {
                            'booked_from': availability.get('available_at').isoformat(),
                            'booked_till': availability.get('available_until').isoformat(),
                            'service_variant': {
                                'id': availability.get('service_id').id,
                                'mode': 'variant',
                            },
                            'staffer_id': next(
                                iter(
                                    [
                                        s.id
                                        for s in availability.get('attribute_ids')
                                        if s.type == Resource.STAFF
                                    ]
                                ),
                                None,
                            )
                            or -1,
                            'appliance_id': next(
                                iter(
                                    [
                                        s.id
                                        for s in availability.get('attribute_ids')
                                        if s.type == Resource.APPLIANCE
                                    ]
                                ),
                                None,
                            )
                            or -1,
                        }
                    ],
                    'dry_run': False,
                }
                customer = self.customer_data
                if customer:
                    customer_name = BusinessCustomerInfo.build_full_name(
                        customer.get('first_name'),
                        customer.get('last_name'),
                    )
                    appointment_data['customer_name'] = customer_name
                    appointment_data['customer_email'] = customer.get('email')
                    appointment_data['customer_phone'] = customer.get('phone')

                appointment = PartnerCustomerAppointmentSerializer(
                    instance=None,
                    data=appointment_data,
                    context={
                        'business': self.context['instance_business'],
                        'user': get_system_user(),
                        'type': Appointment.TYPE.CUSTOMER,
                        'source': BookingSources.get_cached(name=PARTNERS_GROUPON),
                        'client_type': BusinessCustomerInfo.CLIENT_TYPE__GROUPON_PARTNER,
                    },
                )

                if appointment.is_valid():
                    self._appointments.append(appointment)
                else:
                    key = f'bookings.{idx}.id'
                    raise serializers.ValidationError({key: 'Invalid appointment'})

    def validate(self, attrs):
        ret = super().validate(attrs)
        self._validate_appointments(attrs)
        return ret

    def create(self, validated_data):
        ret, subbookings = [], []

        with transaction.atomic():
            for idx, booking in enumerate(validated_data.get('bookings')):
                try:
                    groupon_booking = GrouponBooking.objects.filter(
                        groupon_booking_id=booking.get('groupon_booking_id')
                    ).get()
                    ret.append(groupon_booking)
                except GrouponBooking.DoesNotExist:
                    appointment_serializer = self._appointments[idx]
                    appointment = appointment_serializer.save()

                    for subbooking in appointment.subbookings:
                        groupon_booking = GrouponBooking.objects.create(
                            booking=subbooking,
                            business=appointment.business,
                            service_id=subbooking.service_variant_id,
                            staffer_id=subbooking.staffer_id,
                            appliance_id=subbooking.appliance_id,
                            groupon_customer_service_id=validated_data.get(
                                'groupon_customer_service_id'
                            ),
                            groupon_booking_id=booking.get('groupon_booking_id'),
                            booking_type=validated_data.get('booking_type'),
                            booked_from=subbooking.booked_from,
                            booked_till=subbooking.booked_till,
                            first_name=self.customer_data.get('first_name'),
                            last_name=self.customer_data.get('last_name'),
                            email=self.customer_data.get('email'),
                            telephone=self.customer_data.get('phone'),
                        )
                        subbookings.append(subbooking)
                        ret.append(groupon_booking)

            transaction.on_commit(lambda: self._after_create(appointment))
        return ret

    @staticmethod
    def _after_create(appointment):
        user = get_system_user()
        for subbooking in appointment.subbookings:
            customer_appointment_created_event.send(subbooking.appointment)
            start_scenario(
                BookingChangedScenario,
                appointment_id=subbooking.appointment_id,
                action=BookingChangedScenario.CUSTOMER_CREATED,
            )
            BookingChange.add(
                subbooking,
                changed_by=BookingChange.BY_CUSTOMER,
                changed_user=user,
                metadata={
                    'reason': 'webapps.feeds.groupon:_create_booking',
                },
            )
            ZoomMeeting.create_meeting_for_booking_if_necessary(subbooking)
            segment_api_appointment_booked_task.delay(subbooking.id)

        if FacebookAnalyticsCustomerBookingTrackingFlag():
            send_analytics_1st_cb_created_for_business_to_facebook.delay(
                appointment_id=appointment.id
            )


# BookingCancellation
class GrouponCancellationIdSerializer(serializers.Serializer):
    id = serializers.PrimaryKeyRelatedField(
        queryset=GrouponBooking.objects.all(),
        required=True,
    )

    def to_internal_value(self, data):
        data = camel_to_snakecase_dict(data)
        data_id = data.get('id')
        if data_id:
            data['id'] = extract_internal_id(data_id)
        return super().to_internal_value(data)


class GrouponBookingCancellationRequestSerializer(GrouponBaseSerializer):
    bookings = GrouponCancellationIdSerializer(many=True)

    def _validate_appointments(self, data):
        for idx, booking in enumerate(data.get('bookings')):
            groupon_booking = booking['id']
            appointment = groupon_booking.booking.appointment

            if not appointment.is_active() or any(sb.has_ended() for sb in appointment.subbookings):
                key = f'bookings.{idx}.id'
                raise serializers.ValidationError({key: 'Could not cancel appointment'})

    def validate(self, attrs):
        ret = super().validate(attrs)
        self._validate_appointments(attrs)
        return ret

    def save(self, **kwargs):
        ret, appointments = [], []
        user = get_system_user()

        with transaction.atomic():
            for booking in self.validated_data.get('bookings'):
                groupon_booking = booking['id']
                appointment = groupon_booking.booking.appointment

                appointment.update_appointment(
                    updated_by=user,
                    status=Appointment.STATUS.CANCELED,
                    who_makes_change=WhoMakesChange.CUSTOMER,
                )
                groupon_booking.booking_type = GrouponBookingType.CANCELLED
                groupon_booking.save(update_fields=['booking_type'])
                appointments.append(appointment)
                ret.append(groupon_booking)

            transaction.on_commit(lambda: self._after_save(appointments))
        return ret

    def _after_save(self, appointments):
        for appointment in appointments:
            start_scenario(
                BookingChangedScenario,
                appointment=appointment,
                action=BookingChangedScenario.CUSTOMER_CANCEL,
            )


class SubBookingGrouponRedemptionSerializer(serializers.Serializer):
    id = serializers.SerializerMethodField()
    merchant_id = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    updated_at = serializers.DateTimeField(
        required=True,
        source='groupon_booking.updated',
        format=GROUPON_DATETIME_FORMAT,
        default_timezone=gettz('UTC'),
    )

    @staticmethod
    def get_id(instance: SubBooking):
        return id_to_external_api(instance.groupon_booking.id)

    @staticmethod
    def get_merchant_id(instance: SubBooking):
        return id_to_external_api(instance.groupon_booking.business_id)

    @staticmethod
    def get_status(instance: SubBooking):
        return APPOINTMENT_STATUS_TO_REDEMPTION_STATUS.get(instance.appointment.status).value

    def to_representation(self, instance: SubBooking):
        ret = super().to_representation(instance)
        return snake_to_camelcase_dict(ret)


class AppointmentGrouponRedemptionSerializer(serializers.Serializer):
    bookings = SubBookingGrouponRedemptionSerializer(many=True)


# Re-routers


class GrouponBaseRerouterSerializer(
    GrouponBaseSerializer,
    PartnersBaseRerouterSerializer,
):
    pass


class GrouponMerchantRerouterSerializer(GrouponBaseRerouterSerializer):
    merchant_id = serializers.CharField(required=True)

    @property
    def country_code(self):
        try:
            params = external_api_to_id(self.normalized_data.get('merchant_id'))
            if params:
                return params['country_code']
        except (AttributeError, KeyError):
            pass


class GrouponMerchantsListRerouterSerializer(GrouponBaseRerouterSerializer):
    ids = serializers.CharField(required=True)

    @property
    def country_code(self):
        try:
            country_codes = set(
                external_api_to_id(b)['country_code']
                for b in self.normalized_data['ids'].split(',')
                if b and external_api_to_id(b)
            )
            # only supports bookings via one country
            if len(country_codes) == 1:
                return firstof(country_codes)
        except (AttributeError, KeyError):
            pass


class GrouponBookingsListRerouterSerializer(
    GrouponBaseRerouterSerializer,
):
    bookings = GrouponIdSerializer(many=True)

    @property
    def country_code(self):
        try:
            country_codes = set(
                external_api_to_id(b['id'])['country_code']
                for b in self.normalized_data['bookings']
                if b['id'] and external_api_to_id(b['id'])
            )
            # only supports bookings via one country
            if len(country_codes) == 1:
                return firstof(country_codes)
        except (AttributeError, KeyError):
            pass


# Responses


class GrouponBookingResponseSerializer(serializers.Serializer):
    id = serializers.CharField(required=True)
    groupon_booking_id = serializers.CharField(required=True)
    created_at = serializers.DateTimeField(
        required=False,
        allow_null=True,
        source='created',
        format=GROUPON_DATETIME_FORMAT,
        default_timezone=gettz('UTC'),
    )
    updated_at = serializers.DateTimeField(
        required=True,
        source='updated',
        format=GROUPON_DATETIME_FORMAT,
        default_timezone=gettz('UTC'),
    )
    cancellation_deadline = serializers.DateTimeField(
        required=False,
        allow_null=True,
        format=GROUPON_DATETIME_FORMAT,
        default_timezone=gettz('UTC'),
    )
    status = serializers.ChoiceField(
        choices=GrouponBookingTypeChoices,
        required=True,
        source='booking_type',
    )

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['id'] = id_to_external_api(ret['id'])
        return snake_to_camelcase_dict(ret)


# Errors
class GrouponBaseErrorSerializer(PartnersBaseErrorSerializer):
    pass


class GrouponBookingCreationErrorSerializer(
    GrouponBaseErrorSerializer,
):
    message = serializers.CharField(allow_null=True)
    bookings = GrouponBookingIdSerializer(required=True, many=True)

    def to_internal_value(self, data):
        data = camel_to_snakecase_dict(data)
        return super().to_internal_value(data)

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        return snake_to_camelcase_dict(ret)


class GrouponBookingCancellationErrorSerializer(GrouponBaseErrorSerializer):
    message = serializers.CharField(allow_null=True)
    bookings = GrouponIdSerializer(required=True, many=True)
