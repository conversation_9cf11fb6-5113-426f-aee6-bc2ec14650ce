import datetime
from decimal import Decimal

import pytest
import pytz
from dateutil.relativedelta import relativedelta
from mock import patch
from model_bakery import baker

from lib.feature_flag.feature import BooksyGiftcardsEnabledFlag
from lib.feature_flag.feature.business import RemoveNoneShortStatusLabelFlag
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Resource,
    Service,
    ServiceVariant,
)
from webapps.pos.baker_recipes import booksy_gift_card_recipe
from webapps.pos.booksy_gift_cards.models import BooksyGiftCard
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    PaymentRow,
    PaymentType,
    POS,
    Receipt,
    TaxRate,
    Tip,
    Transaction,
)
from webapps.user.models import User


@pytest.mark.django_db
@override_feature_flag({BooksyGiftcardsEnabledFlag: True})
class BusinessCheckoutGiftCardTestCase(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()

        pos = baker.make(POS, business=self.business, active=True, tips_enabled=True)
        baker.make(Tip, pos=pos, default=True, rate=10)
        baker.make(Tip, pos=pos, rate=20)
        baker.make(TaxRate, pos=pos, default_for_service=True, rate=20)

        self.staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=baker.make(User),
        )

        baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=self.business.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.business.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.SPLIT, pos=self.business.pos)

        payment_type_bgc = baker.make(
            PaymentType,
            code=PaymentTypeEnum.BOOKSY_GIFT_CARD,
            pos=self.business.pos,
        )
        self.service_1 = baker.make(Service, business=self.business)
        service_2 = baker.make(Service, business=self.business)

        self.service_variant = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )
        service_variant_2 = baker.make(
            ServiceVariant,
            service=service_2,
            duration=relativedelta(minutes=15),
            price=100,
            type=PriceType.FIXED,
        )
        booked_from1 = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        booked_from2 = datetime.datetime(2019, 1, 1, 11, 0, tzinfo=pytz.UTC)
        self.appointment = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + datetime.timedelta(minutes=15),
                    'service_variant': self.service_variant,
                },
                {
                    'booked_from': booked_from2,
                    'booked_till': booked_from2 + datetime.timedelta(minutes=15),
                    'service_variant': service_variant_2,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
            is_booksy_gift_card_appointment=True,
        )
        total_services = sum(x.service_price.value for x in self.appointment.subbookings)
        total_products = Decimal('0')
        total = total_services + total_products
        self.txn = baker.make(
            Transaction,
            appointment=self.appointment,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            business_name=self.business.name,
            customer_data='',
            subtotal_services=total_services,
            subtotal_products=total_products,
            taxed_subtotal_services=total_services,
            taxed_subtotal_products=total_products,
            total=total,
            service_fee=Decimal('0.00'),
            currency_symbol='USD',
            service_tax_mode=POS.POS_TAX_MODE__INCLUDED,
            product_tax_mode=POS.POS_TAX_MODE__INCLUDED,
            pos=self.business.pos,
        )
        receipt = baker.make(
            Receipt,
            transaction=self.txn,
            status_code=receipt_status.GIFT_CARD_DEPOSIT,
            payment_type=payment_type_bgc,
            card_type=None,
            card_last_digits=None,
            already_paid=total,
            provider=None,
        )
        self.payment_row_bgc = baker.make(
            PaymentRow,
            amount=total,
            payment_type=payment_type_bgc,
            receipt=receipt,
            status=receipt_status.GIFT_CARD_DEPOSIT,
            tip_amount=Decimal('0.00'),
        )
        self.bgc = booksy_gift_card_recipe.make(payment=self.payment_row_bgc)
        self.txn.latest_receipt = receipt
        self.txn.save()
        self.body = {
            'id': self.txn.id,
            'transaction_type': 'P',
            'discount_rate': 0,
            'tip': {
                'already_paid': '$0.00',
                'already_paid_unformatted': '0.00',
                'amount_remaining': 0,
                'rate': '0.00',
                'type': 'P',
                'label': 'No Tip',
                'amount': '$0.00',
                'amount_unformatted': '0.00',
            },
            'booking': None,
            'multibooking': self.appointment.id,
            'payment_rows': [
                {
                    'id': self.payment_row_bgc.id,
                    'basket_payment_id': 'afaa8e2e-533d-4d63-9604-9503aa09f87d',
                    'created': '2024-03-04T10:41',
                    'amount': '225.00',
                    'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                    'locked': True,
                    'label': 'Booksy Gift Card',
                    'amount_text': '$225.00',
                    'tip_amount': '0.00',
                    'tip_amount_text': '$0.00',
                    'service_amount': '225.00',
                    'service_amount_text': '$225.00',
                    'voucher_id': None,
                    'voucher_service_id': None,
                    'voucher_service_price': None,
                    'voucher_code': None,
                }
            ],
            'issuing_staffer': self.staffer.id,
            'confirming_staffer': None,
            'note': None,
            'commodity_usage': [],
            'bookings': [
                {
                    'booking_id': booking.id,
                }
                for booking in self.appointment.subbookings
            ],
            'travel_fee': None,
            'vouchers': [],
            'addons': [],
            'products': [],
            'force_customer': True,
            'dry_run': True,
            'selected_register_id': None,
            'compatibilities': {
                'stripe_terminal': True,
                'square': True,
                'split': True,
                'prepayment': True,
                'new_checkout': True,
                'membership': True,
                'egift_card': True,
                'package': True,
            },
        }

        self.url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{self.txn.id}'
        self.update_url = (
            f'/business_api/me/businesses/{self.business.id}/appointments/{self.appointment.id}'
        )

    @override_eppo_feature_flag({RemoveNoneShortStatusLabelFlag.flag_name: True})
    def test_get_200_gift_card_checkout_flag_true(self):
        resp = self._base_get_test_part()
        assert resp.json['transaction']['receipts'][0]['short_status_label'] == ''

    @override_eppo_feature_flag({RemoveNoneShortStatusLabelFlag.flag_name: False})
    def test_get_200_gift_card_checkout_flag_false(self):
        resp = self._base_get_test_part()
        assert resp.json['transaction']['receipts'][0]['short_status_label'] == 'None'

    def test_get_gift_card_is_not_refundable(self):
        resp = self._base_get_test_part()
        assert resp.json['transaction']['receipts'][0]['payment_rows'][0]['refundable'] is False

    def _base_get_test_part(self):
        resp = self.fetch(self.url, method='GET')
        assert Transaction.objects.exclude(id=self.txn.id).count() == 0
        assert set(resp.json.keys()).issuperset(
            {
                'transaction',
                'register',
            }
        )
        assert set(resp.json['transaction'].keys()).issuperset(
            {
                'empty_checkout',
                'transaction_type',
                'tip',
                'discount_rate',
                'commodity_usage',
                'issuing_staffer',
                'confirming_staffer',
                'note',
                'id',
                'customer_id',
                'created',
                'updated',
                'payment_rows',
                'rows',
                'tax_summary',
                'summaries',
                'total',
                'total_unformatted',
                'receipts',
                'actions',
                'business_name',
                'business_address',
                'parent_txn',
                'deposit_info',
                'charge_date',
                'operator',
                'lock',
                'receipt_footer_line_1',
                'receipt_footer_line_2',
                'tax_id',
                'total_wo_prepayment',
                'total_wo_prepayment_w_remaining_tip',
                'default_commodity_usage',
                'three_d_data',
                'customer_invoice',
                'customer_invoice_number',
                'can_generate_invoice',
                'fast_pay_by_app_available',
                'is_family_and_friends',
                'force_stripe_pba',
                'deposit_policy',
                'booking',
                'appointment_uid',
                'multibooking',
                'customer_card_id',
                'customer_data',
                'appointment_status',
                'appointment_datetime',
                'appointment_customer_name',
                'customer_info',
                'appointment_services',
                'commissions_enabled',
                'amount_without_tip',
                'remaining_amount_with_tip',
                'remaining_amount_without_tip',
            }
        )
        assert isinstance(resp.json['transaction']['receipts'], list)
        assert len(resp.json['transaction']['receipts']) == 1
        assert resp.json['transaction']['receipts'][0]['short_status'] == ''
        assert resp.json['transaction']['receipts'][0]['short_status_description'] == ''
        return resp

    @patch('webapps.pos.tasks.BooksyGiftCardsClient.handle_no_show_gift_card')
    def test_gift_card_checkout_completed(self, handle_no_show_gift_card_mock):
        assert Transaction.objects.exclude(id=self.txn.id).count() == 0
        self.body['dry_run'] = False
        resp = self.fetch(self.url, method='PUT', body=self.body)
        assert Transaction.objects.exclude(id=self.txn.id).count() == 1
        self.txn.refresh_from_db()
        self.txn.latest_receipt.refresh_from_db()
        assert self.txn.latest_receipt.status_code == receipt_status.GIFT_CARD_DEPOSIT
        new_txn = Transaction.objects.filter(id=resp.json['transaction']['id']).first()
        assert new_txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert new_txn.latest_receipt.total == self.txn.latest_receipt.total
        assert new_txn.lock

        assert PaymentRow.objects.count() == 2
        self.payment_row_bgc.refresh_from_db()
        assert self.payment_row_bgc.status == receipt_status.GIFT_CARD_DEPOSIT
        new_payment_row = PaymentRow.objects.filter(
            parent_payment_row=self.payment_row_bgc,
        ).first()
        assert new_payment_row.status == receipt_status.PAYMENT_SUCCESS
        handle_no_show_gift_card_mock.assert_not_called()

    def test_gift_card_checkout_completed_with_tip_added(self):
        tip_amount = Decimal('22.50')
        assert Transaction.objects.exclude(id=self.txn.id).count() == 0
        self.body['tip'] = {
            'rate': '10.00',
            'type': 'P',
            'label': '10%',
            'amount': '$22.50',
            'amount_unformatted': '22.50',
            "selected": False,
            "default": True,
            "disabled": False,
            "main": True,
        }
        self.body['dry_run'] = True
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.body['payment_rows'].append(
            {
                'basket_payment_id': None,
                'amount': '22.50',
                'payment_type_code': PaymentTypeEnum.CASH,
                'mode': 'C',
                'label': PaymentTypeEnum.CASH.label,
                'amount_text': '$22.50',
                'tip_amount': None,
                'tip_amount_text': None,
                'service_amount': None,
                'service_amount_text': None,
                'voucher_id': None,
                'voucher_service_id': None,
                'voucher_service_price': None,
                'voucher_code': None,
            }
        )
        self.body['dry_run'] = False
        resp = self.fetch(self.url, method='PUT', body=self.body)
        assert Transaction.objects.exclude(id=self.txn.id).count() == 1
        self.txn.refresh_from_db()
        self.txn.latest_receipt.refresh_from_db()
        assert self.txn.latest_receipt.status_code == receipt_status.GIFT_CARD_DEPOSIT
        new_txn = Transaction.objects.filter(id=resp.json['transaction']['id']).first()
        assert new_txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert new_txn.latest_receipt.total == self.txn.latest_receipt.total + tip_amount
        assert new_txn.latest_receipt.already_paid == self.txn.latest_receipt.total + tip_amount
        assert new_txn.lock

        assert PaymentRow.objects.count() == 3
        self.payment_row_bgc.refresh_from_db()
        assert self.payment_row_bgc.status == receipt_status.GIFT_CARD_DEPOSIT
        new_payment_row = PaymentRow.objects.filter(
            parent_payment_row=self.payment_row_bgc,
        ).first()
        assert new_payment_row.status == receipt_status.PAYMENT_SUCCESS

        tip_payment_row = new_txn.latest_receipt.payment_rows.exclude(id=new_payment_row.id).first()
        assert tip_payment_row.amount == tip_payment_row.tip_amount == tip_amount
        assert tip_payment_row.status == receipt_status.PAYMENT_SUCCESS

    def test_block_last_possible_booking_removal_from_checkout(self):
        self.body['bookings'].pop()
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 200)

        self.body['bookings'] = []
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 400)

    def test_block_appointment_removal_from_checkout(self):
        self.body['bookings'] = []
        self.body['multibooking'] = None
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 400)

    def test_allow_adding_extra_services_during_checkout(self):
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 200)
        self.assertEqual(len(resp.json['transaction']['rows']), 2)
        self.assertEqual(len(resp.json['transaction']['payment_type_choices']), 3)
        self.assertEqual(resp.json['transaction']['payment_type_choices'][-1]['code'], 'split')
        self.assertEqual(resp.json['transaction']['payment_type_choices'][-1]['selected'], False)

        self.body['bookings'].append(
            {
                'service_variant_id': self.service_variant.id,
                'quantity': 1,
                'commision_staffer_id': self.staffer.id,
                'discount_rate': 0,
                'service_name': self.service_variant.service.name,
                'item_price': self.service_variant.price,
            }
        )
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 200)
        self.assertEqual(len(resp.json['transaction']['rows']), 3)
        self.assertEqual(len(resp.json['transaction']['payment_type_choices']), 3)
        self.assertEqual(resp.json['transaction']['payment_type_choices'][-1]['code'], 'split')
        # split True because we add new services during checkout and need to pay more than booksy
        # gift card frozen value
        self.assertEqual(resp.json['transaction']['payment_type_choices'][-1]['selected'], True)

    def test_block_checkout_if_total_negative_after_discount(self):
        self.body['discount_rate'] = 80
        self.body['payment_rows'].append(
            {"payment_type_code": "pay_by_app", "amount": None, "voucher_id": None, "mode": "C"}
        )
        resp = self.fetch(self.url, method='PUT', body=self.body)

        self.body['payment_rows'] = resp.json['transaction']['payment_rows']
        self.body['dry_run'] = False
        resp = self.fetch(self.url, method='PUT', body=self.body)
        self.assertEqual(resp.code, 400)

    @patch('webapps.pos.tasks.BooksyGiftCardsClient.handle_no_show_gift_card')
    def test_update_appt_from_calendar_transaction_update(self, handle_no_show_gift_card_mock):
        self.assertEqual(Transaction.objects.all().count(), 1)
        self.assertEqual(PaymentRow.objects.all().count(), 1)
        booking_id = self.appointment.subbookings[0].id

        body = {
            "subbookings": [
                {
                    "booked_from": "2019-01-01T10:00",
                    "booked_till": "2019-01-01T11:00",
                    "staffer_id": self.staffer.id,
                    "appliance_id": None,
                    "service_variant": {"id": self.service_variant.id, "mode": "variant"},
                    "id": booking_id,
                    "service": {
                        "id": self.service_1.id,
                        "variant": {
                            "id": self.service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 125,
                        },
                    },
                    "staffer": {"id": self.staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    '_version': self.appointment._version,  # pylint: disable=protected-access
                }
            ],
            "customer": {
                "mode": 'walk-in',
            },
            '_version': self.appointment._version,  # pylint: disable=protected-access
            'dry_run': False,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        resp = self.fetch(self.update_url, method='PUT', body=body)
        transaction_id = resp.json['appointment']['payment_info']['transaction_id']
        self.appointment.refresh_from_db()
        self.assertTrue(self.appointment.is_booksy_gift_card_appointment)
        self.assertEqual(Transaction.objects.all().count(), 2)
        self.assertEqual(PaymentRow.objects.all().count(), 3)
        new_transaction = Transaction.objects.get(id=transaction_id)
        self.assertEqual(new_transaction.total, self.service_variant.service_price.value)
        self.assertTrue(
            PaymentRow.objects.filter(
                status=receipt_status.ARCHIVED, payment_type__code=PaymentTypeEnum.BOOKSY_GIFT_CARD
            ).exists()
        )
        self.assertNotEqual(
            PaymentRow.objects.filter(
                parent_payment_row=self.payment_row_bgc, status=receipt_status.GIFT_CARD_DEPOSIT
            )
            .first()
            .booksy_gift_cards.all()
            .first(),
            self.bgc,
        )
        self.assertEqual(BooksyGiftCard.objects.all().count(), 3)
        for pr in PaymentRow.objects.all():
            self.assertEqual(pr.booksy_gift_cards.first().external_id, self.bgc.external_id)
        handle_no_show_gift_card_mock.assert_not_called()
