from decimal import Decimal

import pytest
from django.test import override_settings
from django.utils.translation import gettext as _
from model_bakery import baker
from rest_framework import status

from country_config import Country
from lib.payments.enums import PaymentProviderCode
from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps.business.models import Business
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.payment_gateway.models import (
    WalletFeeSettings,
)
from webapps.pos.enums import POSPlanPaymentTypeEnum
from webapps.pos.models import (
    POS,
    POSPlan,
)


@pytest.mark.django_db
class BusinessPOSPlanInfoHandlerTestCase(BaseAsyncHTTPTest):
    def setUp(self, **kwargs):
        super().setUp(**kwargs)

        baker.make(
            POSPlan,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
            provision=0.1234,
            txn_fee=2.22,
            chargeback_txn_fee=66.77,
        )
        baker.make(
            POSPlan,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_TERMINAL,
            provision=0.4321,
            txn_fee=3.33,
            chargeback_txn_fee=99.11,
        )
        baker.make(
            POSPlan,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.FAST_PAYOUT,
            txn_fee=1.00,
            provision=0.01,
        )
        self.stripe_wallet_fee_settings = baker.make(
            WalletFeeSettings,
            default=True,
            payment_provider_code=PaymentProviderCode.STRIPE,
            fast_payout_provision_percentage=Decimal(2.00),
            fast_payout_provision_fee=137,
        )

        self.business = baker.make(Business, active=True, owner=self.user)
        self.pos = baker.make(POS, business=self.business, active=True, marketpay_enabled=True)
        self.pos.recalculate_pos_plans()
        self.url = '/business_api/me/businesses/{}/pos/pos_plan_info/?pos_plan_type={}'

    def test_get_fail(self):
        resp = self.fetch(self.url.format(self.business.id, "incorrect"))
        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(resp.json, {"errors": [{"field": "pos_plan_type"}]})

    def test_get_adyen(self):
        resp = self.fetch(
            self.url.format(self.business.id, POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)
        )
        assert resp.code == status.HTTP_200_OK
        dict_assert(
            resp.json,
            {
                "fee_description": _("Fee per mobile payment and refund transaction"),
                "terms_and_conditions": _("By enabling you agree to"),
                "rates": "12.34% + $2.22",
            },
        )
        assert "charged a $66.77 non-refundable" in resp.json['chargeback_fee_description']

    def test_get_adyen_marketpay_disabled(self):
        self.pos.marketpay_enabled = False
        self.pos.save()

        resp = self.fetch(
            self.url.format(self.business.id, POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)
        )
        assert resp.code == status.HTTP_200_OK
        dict_assert(
            resp.json,
            {
                "fee_description": _("Fee per mobile payment transaction"),
                "terms_and_conditions": _("By enabling you agree to"),
                "rates": "12.34% + $2.22",
                "chargeback_fee_description": None,
            },
        )

    def test_get_stripe(self):
        resp = self.fetch(self.url.format(self.business.id, POSPlanPaymentTypeEnum.STRIPE_TERMINAL))
        assert resp.code == status.HTTP_200_OK
        dict_assert(
            resp.json,
            {
                "fee_description": _("Fee per Booksy Card Reader payment and refund transaction"),
                "terms_and_conditions": _("By enabling you agree to"),
                "rates": "43.21% + $3.33",
            },
        )
        assert "charged a $99.11 non-refundable" in resp.json['chargeback_fee_description']

    @french_certification_enabled(certification_enabled=True)
    def test_get_stripe_fr(self):
        fc_seller_recipe.make(business=self.business)
        resp = self.fetch(self.url.format(self.business.id, POSPlanPaymentTypeEnum.STRIPE_TERMINAL))
        assert resp.code == status.HTTP_200_OK
        dict_assert(
            resp.json,
            {
                "fee_description": _(
                    "Fee per Booksy Card Reader payment and refund transaction (excluding tax)"
                ),
                "terms_and_conditions": _("By enabling you agree to"),
                "rates": "36.01% + $2.78",
            },
        )
        assert "charged a $99.11 non-refundable" in resp.json['chargeback_fee_description']

    def _test_get_fast_payout(self, rates: str):
        resp = self.fetch(self.url.format(self.business.id, POSPlanPaymentTypeEnum.FAST_PAYOUT))
        assert resp.code == status.HTTP_200_OK
        dict_assert(
            resp.json,
            {
                "fee_description": _("Fee per Fast Payout"),
                "terms_and_conditions": _("By enabling you agree to"),
                "rates": rates,
            },
        )

    def test_get_fast_payout(self):
        self._test_get_fast_payout("2% + $1.37")

    @override_settings(API_COUNTRY=Country.PL)
    def test_get_no_pos_plan(self):
        resp = self.fetch(self.url.format(self.business.id, POSPlanPaymentTypeEnum.TAP_TO_PAY))
        assert resp.code == status.HTTP_404_NOT_FOUND
        dict_assert(
            resp.json,
            {
                "errors": [
                    {
                        "type": "invalid",
                        "code": "not_found",
                        "description": "Pos Plan doesn\'t exist",
                    }
                ]
            },
        )

    @french_certification_enabled(certification_enabled=True)
    def test_french_certification_requires_seller_data(self):
        resp = self.fetch(self.url.format(self.business.id, POSPlanPaymentTypeEnum.STRIPE_TERMINAL))
        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json,
            {
                "errors": [
                    {
                        'code': 'missing_seller_data',
                        'type': 'validation',
                        'field': 'non_field_errors',
                        'description': _(
                            "You need to provide your tax info.\n"
                            "Please open Booksy in a browser and add missing information."
                        ),
                    }
                ]
            },
        )
