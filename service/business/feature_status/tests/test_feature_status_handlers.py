import datetime

import pytest
from dateutil.tz import UTC
from django.test import override_settings
from model_bakery import baker
from rest_framework import status

from lib.feature_flag.feature.boost import BoostBanBackendFlag, BoostPreSuspensionWarningBackendFlag
from lib.feature_flag.feature.promotions import ServicePromotionsAggerationOptimisationFlag
from lib.tests.utils import override_eppo_feature_flag
from service.tests import BaseAsyncHTTPTest
from settings.boost import BoostConfig
from webapps.boost.enums import <PERSON>ostBanLength, BoostBanStatus, BoostBanType, BoostFraudWarningType
from webapps.boost.models import BoostBan, BoostFraudSuspicion
from webapps.business.models.bci import BusinessCustomerInfo


@pytest.mark.django_db
@override_eppo_feature_flag({ServicePromotionsAggerationOptimisationFlag.flag_name: True})
class BusinessFeaturesStatusHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/features_status/'

    def test_get_social_media_status(self):
        self.business.integrations['df_last_share'] = datetime.datetime(
            2020, 9, 1, 10, 30, tzinfo=UTC
        ).isoformat()
        self.business.save(update_fields=['integrations'])

        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200

        assert 'social_media' in resp.json
        self.assertDictEqual(
            resp.json['social_media'],
            {
                'status': 'shared',
                'status_color': 'gray',
                'label': 'Shared',
                'last_shared_date': '2020-09-01',
            },
        )

    def test_get_invite_customers(self):
        url = self.url.format(self.business.id)

        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            email='<EMAIL>',
            invited=False,
            user_id=None,
            deleted=None,
            visible_in_business=True,
        )
        self.bci.reindex(refresh_index=True)

        resp = self.fetch(url, method='GET')
        assert resp.code == 200
        assert 'invite_customers' in resp.json
        self.assertDictEqual(
            resp.json['invite_customers'],
            {
                'status': 'inactive',
                'status_color': 'orange',
                'label': '1 Client to invite',
            },
        )

    def test_service_promotions(self):
        url = self.url.format(self.business.id)

        resp = self.fetch(url, method='GET')
        assert resp.code == 200
        assert 'service_promotions' in resp.json
        self.assertDictEqual(
            resp.json['service_promotions'],
            {
                'status': 'inactive',
                'status_color': 'gray',
                'label': 'Inactive',
            },
        )

    def test_boost_status(self):
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200

        assert 'boost' in resp.json
        self.assertDictEqual(
            resp.json['boost'],
            {
                'status': 'disabled',
                'label': 'Disabled',
                'status_color': 'gray',
                'auto_suspension': True,
            },
        )

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostBanBackendFlag.flag_name: True})
    def test_boost_status_with_ban__ff_enabled(self):
        baker.make(
            BoostBan,
            business=self.business,
            type=BoostBanType.SUSPENSION,
            status=BoostBanStatus.ACTIVE,
            length=BoostBanLength.DAYS_30,
            active_from=datetime.date(2024, 10, 1),
            active_till=datetime.date(2024, 10, 30),
            invoice_required=True,
        )

        response = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn('ban', response.json['boost'])
        self.assertDictEqual(
            response.json['boost']['ban'],
            {
                'type': 'suspension_with_invoice',
                'active_from': '2024-10-01',
                'active_till': '2024-10-30',
            },
        )

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostBanBackendFlag.flag_name: False})
    def test_boost_status_with_ban__ff_disabled(self):
        baker.make(
            BoostBan,
            business=self.business,
            type=BoostBanType.SUSPENSION,
            status=BoostBanStatus.ACTIVE,
            length=BoostBanLength.DAYS_30,
            active_from=datetime.date(2024, 10, 1),
            active_till=datetime.date(2024, 10, 30),
            invoice_required=True,
        )

        response = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertNotIn('ban', response.json['boost'])

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: True})
    def test_boost_status_with_warning__ff_enabled(self):
        boost_fraud_suspicion = baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=True,
            warning_type=BoostFraudWarningType.PRICE_MANIPULATIONS,
        )

        response = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn('warning', response.json['boost'])
        self.assertDictEqual(
            response.json['boost']['warning'],
            {
                'type': 'pre_suspension',
                'alert_text_full': (
                    "Your account maintains a <b>high number of appointments with modified service"
                    " prices</b>, which may indicate non-compliance with the"
                    " Boost Terms & Conditions and result in Boost suspension."
                ),
                'alert_text_short': "A high number of appointments with modified service prices.",
            },
        )

        boost_fraud_suspicion.warning_visible = False
        boost_fraud_suspicion.save()
        response = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn('warning', response.json['boost'])
        self.assertIs(response.json['boost']['warning'], None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: False})
    def test_boost_status_with_warning__ff_disabled(self):
        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=True,
        )

        response = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertNotIn('warning', response.json['boost'])

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag(
        {
            BoostBanBackendFlag.flag_name: True,
            BoostPreSuspensionWarningBackendFlag.flag_name: True,
        }
    )
    def test_boost_status_with_warning_and_ban(self):
        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=True,
        )
        baker.make(
            BoostBan,
            business=self.business,
            type=BoostBanType.SUSPENSION,
            status=BoostBanStatus.ACTIVE,
            length=BoostBanLength.DAYS_30,
            active_from=datetime.date(2024, 10, 1),
            active_till=datetime.date(2024, 10, 30),
            invoice_required=True,
        )

        response = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn('ban', response.json['boost'])
        self.assertDictEqual(
            response.json['boost']['ban'],
            {
                'type': 'suspension_with_invoice',
                'active_from': '2024-10-01',
                'active_till': '2024-10-30',
            },
        )
        self.assertIs(response.json['boost']['warning'], None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=False))
    @override_eppo_feature_flag(
        {
            BoostBanBackendFlag.flag_name: True,
            BoostPreSuspensionWarningBackendFlag.flag_name: True,
        }
    )
    def test_boost_bans_disabled(self):
        baker.make(
            BoostBan,
            business=self.business,
            type=BoostBanType.SUSPENSION,
            status=BoostBanStatus.ACTIVE,
            length=BoostBanLength.DAYS_30,
            active_from=datetime.date(2024, 10, 1),
            active_till=datetime.date(2024, 10, 30),
            invoice_required=True,
        )

        response = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIn('ban', response.json['boost'])
        self.assertIs(response.json['boost']['ban'], None)
        self.assertIs(response.json['boost']['warning'], None)


@pytest.mark.django_db
class FeatureStatusServicePromotionsDetailsTest(BaseAsyncHTTPTest):
    @property
    def url(self):
        return (
            f'/business_api/me/businesses/{self.business.id}/features_status/'
            f'service_promotions/'
        )

    def test_endpoint_exists(self):
        response = self.fetch(self.url)
        assert response.code == status.HTTP_200_OK

    def test_response_json(self):
        url = self.url

        resp = self.fetch(url, method='GET')
        self.assertDictEqual(
            resp.json,
            {
                'flash_sale': {
                    'label': 'Inactive',
                    'status': 'inactive',
                    'status_color': 'gray',
                },
                'happy_hours': {
                    'label': 'Inactive',
                    'status': 'inactive',
                    'status_color': 'gray',
                },
                'last_minute': {
                    'label': 'Inactive',
                    'status': 'inactive',
                    'status_color': 'gray',
                },
                'service_categories': [],
            },
        )


@pytest.mark.django_db
class BoostDetailsTest(BaseAsyncHTTPTest):
    @property
    def url(self):
        return f'/business_api/me/businesses/{self.business.id}/features_status/boost/'

    def test_endpoint_exists(self):
        response = self.fetch(self.url)
        assert response.code == status.HTTP_200_OK

    def test_response_json(self):
        url = self.url

        resp = self.fetch(url, method='GET')
        self.assertDictEqual(
            resp.json,
            {
                'boost_status': {
                    'status': 'disabled',
                    'label': 'Disabled',
                    'status_color': 'gray',
                    'auto_suspension': True,
                },
                'commission_val': None,
            },
        )
