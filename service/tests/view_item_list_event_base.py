from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

import pytest
import pytz
from django.conf import settings
from mock import patch
from rest_framework import status

from conftest_helpers import clean_elastisearch_index_helper
from lib.elasticsearch.consts import ESAvailabilityNature, ESDocType, ESIndex
from lib.tools import id_to_external_api, tznow
from service.tests import dict_assert
from webapps.business.elasticsearch.tools import (
    format_availability_doc,
)
from webapps.business.models.category import BusinessCategory
from webapps.consts import WEB
from webapps.experiment_v3.exp import (
    BoundingBoxExperiment,
    NewOnBooksyGalleryExperiment,
    UTT2InSearch,
)
from webapps.segment.enums import BooksyAppVersions, DeviceTypeName
from webapps.segment.utils import get_user_role_by_id
from webapps.structure.models import get_operating_country
from webapps.user.const import Gender


class ViewItemListEventTestCaseBaseMixin:
    TEST_DATETIME = datetime(2021, 8, 25, 9)

    fingerprint = '1'
    pseudo_id = '2.2'

    url: str
    item_list_id: str
    item_list_name: str
    request_args: dict
    expected_extra_event_params: Optional[dict] = None

    def setUp(self):
        # pylint: disable=invalid-name
        super().setUp()
        self.prevent_errors_from_unrelated_experiments()
        self.set_booking_source()

    def tearDown(self):
        # pylint: disable=invalid-name
        self.clean_business_index()
        super().tearDown()

    def test_with_user(self):
        self._test()

    def test_without_user(self):
        self._test(anonymous=True)

    def test_empty(self):
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.get()
        assert self._get_status_code_from_response(response) == status.HTTP_200_OK
        assert not self.results_are_not_empty(response)
        dict_assert(
            request_api_mock.call_args[1],
            self.get_request_api_args(
                payload=self.get_payload(with_user=True, items_start_from=0, items_count=0),
            ),
        )

    def prevent_errors_from_unrelated_experiments(self):
        self.fix_region()
        UTT2InSearch.initialize()
        UTT2InSearch(self.user.id).set_variant(UTT2InSearch.v.control)
        NewOnBooksyGalleryExperiment.initialize()
        BoundingBoxExperiment.initialize()

    def fix_region(self):
        region = get_operating_country()
        region.latitude = 0.0
        region.longitude = 0.0
        region.save()
        region.reindex()
        self.region = region

    def set_booking_source(self):
        self.customer_booking_src.name = WEB
        self.customer_booking_src.save()

    @staticmethod
    def clean_business_index():
        return clean_elastisearch_index_helper(ESIndex.BUSINESS)

    def _test(self, anonymous=False, business_count=2, extra_args=None, items_start_from=0):
        self.create_businesses(count=business_count)
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.get(anonymous=anonymous, extra_args=extra_args)
        assert self._get_status_code_from_response(response) == status.HTTP_200_OK
        assert self.results_are_not_empty(response)

        dict_assert(
            request_api_mock.call_args[1],
            self.get_request_api_args(
                payload=self.get_payload(
                    with_user=not anonymous, items_start_from=items_start_from
                ),
            ),
        )

    def create_businesses(self, count):
        index = self.clean_business_index()
        for id_ in range(count):
            self.create_business(id_, manual_boost_score=count - id_)
        index.refresh()

    def create_business(self, id_, manual_boost_score=0):
        in_3_days = tznow().replace(hour=0) + timedelta(days=3)
        self.create_doc(
            ESDocType.BUSINESS,
            id=id_,
            _id=f'business:{id_}',
            join='business',
            visible=True,
            is_b_listing=False,
            promoted=True,
            promotions_profitability=1,
            business_location={
                'city': 'Foo, FL',
                'coordinate': {'lat': 1.0, 'lon': -70.0},
            },
            availability=format_availability_doc(
                treatment_type=BusinessCategory.CATEGORY,
                treatment_id=-1,
                nature=ESAvailabilityNature.REAL,
                lower=in_3_days + timedelta(hours=8),
                upper=in_3_days + timedelta(hours=16),
                tz=pytz.utc,  # noqa
            ),
            manual_boost_score=manual_boost_score,
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    def get(self, extra_args=None, anonymous=False, **kwargs):
        raise NotImplementedError

    def results_are_not_empty(self, response):
        return self._get_response_json(response)

    @staticmethod
    def _get_response_json(response):
        raise NotImplementedError

    @staticmethod
    def get_request_api_args(payload):
        return {
            'endpoint': '/p/collect',
            'method': 'post',
            'payload': payload,
        }

    def get_payload(self, with_user, items_start_from, items_count=2):
        params = {
            'item_list_id': self.item_list_id,
            'items': self.get_payload_items(count=items_count, start_from=items_start_from),
            'fingerprint': self.fingerprint,
            'screen_name': self.item_list_id,
        }
        if self.item_list_name:
            params['item_list_name'] = self.item_list_name
        if with_user:
            params['email'] = self.user.email
        if self.expected_extra_event_params:
            params.update(self.expected_extra_event_params)

        payload = {
            'events': [
                {
                    'name': 'view_item_list',
                    'params': params,
                },
            ],
            'firebase_auth': {
                'client_id': self.pseudo_id,
            },
            'user_properties': {
                'app_version': {'value': BooksyAppVersions.B30},
                'country': {'value': settings.API_COUNTRY},
                'device_type': {'value': DeviceTypeName.DESKTOP},
                'gender_code': {'value': Gender.Both.value},
            },
        }
        if with_user:
            payload['user_id'] = id_to_external_api(self.user.id)
            payload['user_properties'].update(
                {
                    'control_group': {'value': not bool(self.user.id % 100)},
                    'phone': {'value': self.user.cell_phone or ''},
                    'user_role': {'value': get_user_role_by_id(self.user.id)},
                }
            )
        return payload

    @staticmethod
    def get_payload_items(count, start_from):
        return [
            {'item_id': f'dev-us-{i}', 'index': i} for i in range(start_from, start_from + count)
        ]

    @staticmethod
    def _get_status_code_from_response(response):
        raise NotImplementedError


@pytest.mark.django_db
class ViewItemListEventTestCaseMixin(ViewItemListEventTestCaseBaseMixin):
    def get_headers(self, path):
        headers = super().get_headers(path)
        headers['X-Fingerprint'] = self.fingerprint
        headers['X-User-Pseudo-ID'] = self.pseudo_id
        headers['X-API-KEY'] = self.customer_booking_src.api_key
        return headers

    def get(self, extra_args=None, anonymous=False, **kwargs):
        args = {**self.request_args}
        if extra_args:
            args.update(extra_args)

        headers = self.get_headers(self.url)
        if anonymous:
            headers.pop('X-ACCESS-TOKEN')

        return super().fetch(self.url, args=args, headers=headers, **kwargs)

    @staticmethod
    def _get_response_json(response):
        return bool(response.json)

    @staticmethod
    def _get_status_code_from_response(response):
        return response.code
