import pytest
from model_bakery import baker
from rest_framework import status

from lib.feature_flag.feature import (
    EnablePBAOnlyAfterAcceptingFeesFlag,
)
from lib.tests.utils import override_feature_flag
from service.tests import BaseAsyncHTTPTest
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import AccountHolder, StripeAccountHolderSettings
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS, PaymentType
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.stripe_integration.models import StripeAccount


# pylint: disable=protected-access
@pytest.mark.django_db
@override_feature_flag(
    {
        EnablePBAOnlyAfterAcceptingFeesFlag: True,
    }
)
class PBAHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/market_pay/account/turn_on_pba'

    def test_stripe(self):
        pos = baker.make(
            POS,
            business=self.business,
            pos_refactor_stage2_enabled=True,
            _force_stripe_pba=False,
        )

        baker.make(
            StripeAccount,
            pos=pos,
            status=StripeAccountStatus.VERIFIED,
        )

        baker.make(
            AccountHolder,
        )
        business_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )
        stripe_account_holder_settings = StripeAccountHolderSettings.objects.get(
            account_holder_id=business_wallet.account_holder_id,
        )
        assert stripe_account_holder_settings.pba_fees_accepted is False

        resp = self.fetch(self.url.format(self.business.id), body={}, method='POST')
        assert resp.code == status.HTTP_400_BAD_REQUEST

        pos._force_stripe_pba = True
        pos.save(update_fields=['_force_stripe_pba'])

        resp = self.fetch(self.url.format(self.business.id), body={}, method='POST')
        assert resp.code == status.HTTP_200_OK

        assert (
            PaymentType.all_objects.filter(
                code=PaymentTypeEnum.PAY_BY_APP,
                pos=pos,
                deleted__isnull=True,
            ).exists()
            is False
        )

        stripe_account_holder_settings.pba_fees_accepted = True
        stripe_account_holder_settings.save(update_fields=['pba_fees_accepted'])

        resp = self.fetch(self.url.format(self.business.id), body={}, method='POST')
        assert resp.code == status.HTTP_200_OK

        assert (
            PaymentType.all_objects.filter(
                code=PaymentTypeEnum.PAY_BY_APP,
                pos=pos,
                deleted__isnull=True,
            ).exists()
            is True
        )
