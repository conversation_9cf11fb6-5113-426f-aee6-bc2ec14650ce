from datetime import timedelta, datetime
from unittest.mock import patch

import pytest
from pytz import UTC
from rest_framework.status import HTTP_200_OK

from lib.enums import StrEnum
from lib.tools import tznow
from service.experiment_v3.business_experiment import BusinessExperimentHandler
from service.tests import BaseAsyncHTTPTest
from webapps.experiment_v3.exp.cx_onboarding_welcome_modal import CxOnboardingWelcomeModalExperiment
from webapps.experiment_v3.exp.base import BaseExperiment
from webapps.experiment_v3.exp.hint_and_walkthrough import HintAndWalkthroughExperiment
from webapps.experiment_v3.exp.onboarding_address_and_location import (
    OnboardingAddressAndLocationExperiment,
)
from webapps.experiment_v3.models import (
    ExperimentSlot,
)

WALKTHROUGH_ON_WEB_TEST_TIME = datetime(2023, 7, 3, 12, tzinfo=UTC)


# pylint: disable=abstract-method
class ExampleExperiment(BaseExperiment):
    class Variants(StrEnum):
        CONTROL = 'control'
        TEST_GROUP = 'test_group'

    name = 'example_name'
    config = {
        'variants': [
            {
                'name': Variants.CONTROL,
                'weight': 0.5,
            },
            {
                'name': Variants.TEST_GROUP,
                'weight': 0.5,
            },
        ]
    }


@pytest.mark.django_db
class BusinessExperimentHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/experiment_variant/{}/?'.format
    TEST_EXPERIMENT_CLASS = ExampleExperiment

    example_extra_data = {
        'registration_email': '<EMAIL>',
    }

    def setUp(self):
        super().setUp()
        with patch.dict(self.TEST_EXPERIMENT_CLASS.config, {'active': True}):
            BusinessExperimentHandler.valid_experiments[self.TEST_EXPERIMENT_CLASS.name] = (
                self.TEST_EXPERIMENT_CLASS
            )
            self.TEST_EXPERIMENT_CLASS.initialize()

    def test_get_experiment_variant_valid_experiment_basic(self):
        resp_0 = self.fetch(self.url(self.business.id, self.TEST_EXPERIMENT_CLASS.name))
        assert resp_0.code == 200
        assert 'selected_variant' in resp_0.json
        assert resp_0.json['selected_variant'] in self.TEST_EXPERIMENT_CLASS.Variants.values()

    def test_update_experiment_slot_invalid_relation_id(self):
        resp_0 = self.fetch(
            self.url(self.business.id, self.TEST_EXPERIMENT_CLASS.name),
            method='PUT',
            body={
                'extra_data': self.example_extra_data,
            },
        )
        assert resp_0.code == 400
        assert not ExperimentSlot.objects.filter(
            relation_id=self.business.id,
            variant__experiment__name=self.TEST_EXPERIMENT_CLASS.name,
        ).exists()

    def test_update_experiment_slot_invalid_experiment_name(self):
        self.TEST_EXPERIMENT_CLASS(self.business.id).get_variant()

        resp_0 = self.fetch(
            self.url(self.business.id, 'some_non_random_invalid_experiment_name'),
            method='PUT',
            body={
                'extra_data': self.example_extra_data,
            },
        )
        assert resp_0.code == 400
        assert not ExperimentSlot.objects.filter(
            relation_id=self.business.id,
            variant__experiment__name='some_non_random_invalid_experiment_name',
        ).exists()

    def test_incorrect_access_token(self):
        path = self.url(self.business.id, self.TEST_EXPERIMENT_CLASS.name)

        headers = self.get_headers(path)
        headers['X-ACCESS-TOKEN'] = 'iiiiiiinvalidaccesstoken_______&^%'

        resp_0 = self.fetch(path, headers=headers)
        assert resp_0.code == 403
        error = resp_0.json['errors'][0]
        assert error['code'] == 'unauthorized'
        assert error['field'] == 'access_token'


class OnboardingAddressAndLocationExperimentTestCase(BusinessExperimentHandlerTestCase):
    TEST_EXPERIMENT_CLASS = OnboardingAddressAndLocationExperiment


class HintAndWalkthroughExperimentTestCase(BusinessExperimentHandlerTestCase):
    TEST_EXPERIMENT_CLASS = HintAndWalkthroughExperiment

    def test_old_business_should_not_be_in_experiment(self):
        self.business.created = tznow() - timedelta(days=6, minutes=10)
        self.business.save()
        self.assertFalse(
            HintAndWalkthroughExperiment.is_business_created_in_past_6_days(self.business)
        )

        resp_0 = self.fetch(self.url(self.business.id, self.TEST_EXPERIMENT_CLASS.name))
        self.assertEqual(HTTP_200_OK, resp_0.code)
        self.assertIn('selected_variant', resp_0.json)
        self.assertIsNone(resp_0.json['selected_variant'])


class CxOnboardingWelcomeModalExperimentTestCase(BusinessExperimentHandlerTestCase):
    TEST_EXPERIMENT_CLASS = CxOnboardingWelcomeModalExperiment
