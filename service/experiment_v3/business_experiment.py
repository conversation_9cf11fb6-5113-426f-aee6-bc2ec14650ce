from service.experiment_v3.common import BaseExperimentHandler
from service.tools import (
    session,
    json_request,
)
from webapps.experiment_v3.exp import (
    BoostAppointmentCancellation,
    ChurnReasonExperiment,
    EasierAccessToPopularCalendarFiltersExperiment,
    TrialSubscriptionMessageExperiment,
    ProfileDelayDays,
)
from webapps.experiment_v3.exp.hint_and_walkthrough import HintAndWalkthroughExperiment
from webapps.experiment_v3.exp.onboarding_address_and_location import (
    OnboardingAddressAndLocationExperiment,
)


class BusinessExperimentHandler(BaseExperimentHandler):  # pylint: disable=too-many-ancestors
    valid_experiments = {
        OnboardingAddressAndLocationExperiment.name: OnboardingAddressAndLocationExperiment,
        HintAndWalkthroughExperiment.name: HintAndWalkthroughExperiment,
        ChurnReasonExperiment.name: ChurnReasonExperiment,
        EasierAccessToPopularCalendarFiltersExperiment.name: EasierAccessToPopularCalendarFiltersExperiment,  # pylint: disable=line-too-long
        TrialSubscriptionMessageExperiment.name: TrialSubscriptionMessageExperiment,
        ProfileDelayDays.name: ProfileDelayDays,
        BoostAppointmentCancellation.name: BoostAppointmentCancellation,
    }

    @session(login_required=True)
    def get(self, business_id, experiment_name):  # pylint: disable=arguments-renamed
        """
        swagger:
          summary: Get experiment variant
          parameters:
            - name: business_id
              description: currently logged in business id.
              type: integer
              paramType: path
            - name: experiment_name
              description: Name of the experiment.
              type: integer
              paramType: path
          type: ExperimentResponse
        :swagger
        """
        self.business_with_staffer(business_id, __check_region=False)
        return super().get(experiment_name, business_id)

    @session(login_required=True)
    @json_request
    def put(self, business_id, experiment_name):  # pylint: disable=arguments-renamed
        """
        swagger:
          parameters:
            - name: business_id
              description: currently logged in business id.
              type: integer
              paramType: path
            - name: experiment_name
              description: Name of the experiment.
              type: integer
              paramType: path
            - name: partial
              description: Perform partial update.
              required: False
              paramType: query
              type: integer
              enum:
                - 0
                - 1
            - name: body
              paramType: body
              required: true
              type: ExperimentSlotExtraBody
          type: ExperimentResponse
        :swagger
        """
        self.business_with_manager(business_id, __check_region=False)
        return super().put(experiment_name, business_id)
